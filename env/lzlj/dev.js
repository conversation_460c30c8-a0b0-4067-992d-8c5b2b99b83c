// 开发环境
module.exports = {
    envName: 'dev',
    baseURL: 'https://msptest.lzlj.com/hydev',                                                                            // 企微测试环境
    appURL:'https://msptest1.lzlj.com/test',                                                                                // 销售助手后端地址
    appImageURL: 'https://msptest1.lzlj.com/share',                                                                         // 智零生成图片路径域名
    dmpURL: 'https://msptest1.lzlj.com/dmpunion',                                                                          // 销售助手DMP后端地址
    fanweiURL: 'https://xtpttest.lzlj.com/papi/open/singleSignon',                                                             // 泛微地址
    agentId: '1000031',                                                                                                     // 企业微信的应用id
    baiduMapKey: '',                                                                                                        // 百度地图key
    imageAssetPath: 'https://xtzh-public-1256376813.cos.ap-chengdu.myqcloud.com/static',                                                       // 静态图片地址前缀
    encryptKey: '684620d4',                                                                                                 // 加密key
    cosBucket: 'xtzh-test-1256376813',                                                                                      // cos存储桶名
    cosRegion: 'ap-chengdu',                                                                                                // cos地域
    cosUploadUrl: 'https://xtzh-test-1256376813.cos.ap-chengdu.myqcloud.com/',                                              // cos上传域名
    wxAppId: 'wxb53fa92a05b4df3e',                                                                                          // 小程序id
    zlAppId: 'wx6b0d6a51598875ca' ,                                                                                          // 零售测试小程序id  跳转到会员注册页面时使用
    nationalPitAppId:'wxf5fc8b228c33876c',                                                                                  //国窖测试小程序id   跳转到会员注册页面时使用
    secretKey: '',
    secretId: '',
    qqMapKey: 'ZUSBZ-ONLLZ-HXLXY-7DGXM-DLL2E-D4FNG', //使用在腾讯位置服务申请的key
    wechatUpdate: true,

    useIncrementRequestId: true,
    useRequestIdAppendToHttpResult: true,
    useDialogShowErrorInsteadOfMessage: true,
    getWayFlag: true, // 是否开启接入waf网关
    domain: 'a1d5d7df3-wx621112590b635086.sh.wxgateway.com' //waf网关域名
};
