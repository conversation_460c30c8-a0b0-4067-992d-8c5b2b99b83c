<template>
    <view class="perform-link-img">
        <view class="pic-v">
            <view class="item-header">
                <view class="title">{{moduleType | lov('FEEDBACK_PHOTO_TYPE')}}</view>
                <view style="color: #dd524d;float: left" v-if="required">*</view>
                <view class="iconfont icon-info-circle" style="float: right;margin-right: 10px;font-size: 14px;color: #8C8C8C;"
                      v-if="$utils.isNotEmpty(valuesPlaceholder)||$utils.isNotEmpty(basePlaceholder)" @tap="showPlaceholder"></view>
                <view v-if="involveFlag&&!operationFlag" style="float: right;margin-right: 10px;font-size: 14px;color: #8C8C8C;"><link-checkbox v-model="showInvolve" disabled :trueValue="true" :falseValue="false"/>是否不涉及</view>
                <view v-if="involveFlag&&operationFlag" style="float: right;margin-right: 10px;font-size: 14px;color: #8C8C8C;"><link-checkbox v-model="InvolvedBinding" @tap="checkItem()" :trueValue="true" :falseValue="false"/>是否不涉及</view>
            </view>
            <view class="img-v" v-show="!showInvolved">
              <lnk-img-watermark :parentId="activityId"
                                 :moduleType="moduleType"
                                 :delFlag="operationFlag"
                                 :newFlag="operationFlag"
                                 :continueFlag="true"
                                 :drawWatermarkCancleFlag="true"
                                 watermarkText="现场拍照"
                                 :useModuleName="useModuleName"
                                 :marketActivityFlag = "true"
                                 :moduleName="moduleName"
                                 :dataSourceShowFlag="true"
                                 :createdShowFlag="true"
                                 :picTypeList="picTypeList"
                                 :aiUse="aiUse"
                                 @imgUploadSuccess="imageArrLength"
                                 @imgDeleteSuccess="deleteImageArrLength"
                                 ref="img"
                                 v-if="refreshFlag"
              >
              </lnk-img-watermark>
              <view style="width: 100%;height: 118px" v-if="!refreshFlag"></view>
            </view>
            <view class="img-v involve" v-if="showInvolved">
                <view style="width: 70px; height: 70px;background: #f2f2f2;text-align: center;"> <link-icon  style="color:#BFBFBF;height: 70px;font-size: 42px;left: 48px;" icon="icon-jinyong"/></view>
                <view style="width: 70px; line-height: 14px; font-size: 14px;padding-top: 10px;">此项不涉及</view>
            </view>
        </view>
        <link-dialog ref="confirmDialog" disabledHideOnClickMask title="提示" v-model="showDialog" :initial="true">
            {{'切换后将清除该项资料，且不可恢复，是否继续?'}}
            <link-button slot="foot" @tap="cancelInvolved" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="confirmInvolved">确定</link-button>
        </link-dialog>
    </view>
</template>

<script>
    import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';

    export default {
        name: "perform-link-img",
        components: {
            LnkImgWatermark
        },
        props: {
            objectType: {
                type: String,
            },
            objectTypeOneIndex: {
                type: Number,
            },
            objectTypeTwoIndex: {
                type: Number,
            },
            required: {
                type: Boolean,
            },
            activityId: {
                type: String,
            },
            cameraRefresh: {
                type: Boolean,
                default: false
            },
            moduleType: {
                type: String,
            },
            useModuleName: {
                type: String,
            },
            moduleName: {
                type: String,
            },
            operationFlag: {
                type: Boolean,
                default: false
            },
            basePlaceholder: {
                type: String,
            },
            valuesPlaceholder: {
                type: String,
            },
            picTypeList: {
                type: Array,
            },
            aiUse:{
                type: Boolean,
                default: false,
            },
            involveFlag:{
                type:Boolean,
                default: false,
            }
        },
        data(){
          return{
              refreshFlag:true,
              delAllFlag:false,
              once:0,
              picList:[],
              showDialog:false,
              imgLength:0,
              InvolvedBinding:false,
              showInvolved: false,
          }
        },
        computed:{
            showInvolve(){
                return !this.imgLength
            }
        },
       async created() {
               // setTimeout(async () => {
               //     if(this.$utils.isNotEmpty(this.$refs.img)) {
               //         this.$refs.img.emitImgUploadSuccess()
               //     }
               // }, 2100)
        },
        mounted() {
            // this.$refs.confirmDialog.show();
        },
        methods:{
            refresh(){
                this.refreshFlag =  !this.refreshFlag;
                setTimeout(()=>{
                    this.once=0;
                    this.refreshFlag =  !this.refreshFlag;
                },100)
            },
            updatePic(All){
                if(this.$utils.isNotEmpty(All)){
                    if (All==='delAll'){
                        this.delAllFlag = true;
                    }
                }
                this.refresh();
            },
           async confirmInvolved(){
                await this.$refs.img.delALLTypeImg();
                this.InvolvedBinding = true;
                this.showInvolved = true;
                this.$emit('involveFun',this.objectType,this.objectTypeOneIndex,this.objectTypeTwoIndex,this.InvolvedBinding,this.moduleType,this._uid)
                this.showDialog=false;
            },
            cancelInvolved(){
                this.showInvolved = false;
                this.InvolvedBinding = false;
                this.showDialog=false;
            },
            //todo 这里写子组件的接收方法
            repeatInvolvedDispose(involveValue){
                if(involveValue){
                    this.InvolvedBinding = true;
                    this.showInvolved = true;
                }else{
                    this.InvolvedBinding = false;
                    this.showInvolved = false;
                }
            },

           async checkItem(){
               this.$utils.showLoading();
                setTimeout(()=>{
                if(this.InvolvedBinding){
                    this.showDialog=true;
                }else{
                    this.showInvolved = false;
                    this.InvolvedBinding = false;
                    this.$emit('involveFun',this.objectType,this.objectTypeOneIndex,this.objectTypeTwoIndex,this.InvolvedBinding,this.moduleType,this._uid)
                }
                 this.$utils.hideLoading();
                },50)
            },
            imageArrLength(param) {
                if(!this.$utils.isEmpty(param)){
                    this.imgLength = param.length;
                    this.showInvolved = this.involveFlag && !this.operationFlag && !this.imgLength;
                    if(this.once>0){
                        this.$emit('repeatFun',this.moduleType,this._uid);
                    }
                }else{
                    this.imgLength = 0;
                    this.showInvolved = this.involveFlag && !this.operationFlag && !this.imgLength;
                }
                if(this.delAllFlag){
                    this.showInvolved = true;
                    this.delAllFlag = false;
                }
                this.once++;
            },
            deleteImageArrLength(imgList,ALL){
                if(this.$utils.isNotEmpty(ALL)){
                    if (ALL==='delAll'){
                        this.$emit('repeatFun',this.moduleType,this._uid,ALL);
                    }
                }else{
                    this.$emit('repeatFun',this.moduleType,this._uid)
                }
            },
            showPlaceholder() {
                /*
                * 照片上传说明取值顺序
                * a:优先使用小程序模板配置的模板控件上的说明
                * b:如果a情况没有值，则使用模板控件上的说明
                * c:如果a、b情况都没有值 那么照片类型旁边的说明图标就隐藏。
                * */
                let msg = "";
                // 模板控件中的图片上传说明
                if(this.$utils.isNotEmpty(this.basePlaceholder)){
                    msg = this.basePlaceholder
                }
                // 优先使用小程序模板中的图片上传说明
                if(this.$utils.isNotEmpty(this.valuesPlaceholder)){
                    msg = this.valuesPlaceholder
                }
                this.$message({message: msg})
            },
        }
    }
</script>

<style lang="scss">
    .perform-link-img {
        .pic-v {
            background: white;
            .item-header {
                height: 88px;
                padding-left: 32px;
                font-size: 28px;
                line-height: 88px;
                color: #262626;
                letter-spacing: 0;
                border-bottom: 2px solid #F2F2F2;

                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #595959;
                    letter-spacing: 0;
                    line-height: 88px;
                    float: left;
                    margin-right: 20px
                }
            }

            .img-v {
                padding-bottom: 24px;
            }
            .involve{
                margin-left: 32px;
                font-size: 32px;
                margin-top: 20px;
            }
        }
    }
</style>
