<!--
总部活动-宴席活动详情
<AUTHOR>
@date 2023-06-28
@file banquet-activity-detail-page
-->
<template>
  <link-page class="banquet-activity-detail-page">
    <lnk-taps :taps="tapsOptions" v-model="tapsActive" @switchTab="switchTab"></lnk-taps>
    <!--基础信息-->
    <banquet-basic-info :banquetId="id" :banquetItem="banquetItem" :editButtonFlag="editButtonFlag" :editBasicFlag="editBasicFlag"
                        id="basicInfo" :templateList="templateList"></banquet-basic-info>
    <!--执行反馈-->
    <banquet-feedback :banquetId="id" :banquetItem="banquetItem" id="feedback"></banquet-feedback>
    <!--出库明细-->
    <banquet-outbound-details :banquetItem="banquetItem" id="OutboundDetails"></banquet-outbound-details>
    <banquet-inbound-details :banquetItem="banquetItem" id="inboundDetails"></banquet-inbound-details>
    <!--消费者开瓶明细-->
    <banquet-consumer-open :feedbackCode="feedbackCode" id="consumerOpen"></banquet-consumer-open>
    <!--联系人明细-->
    <banquet-forward-contact-list :banquetItemId="banquetItem.id" id="forwardContact"></banquet-forward-contact-list>
    <link-fab-group v-if="fabGroupFlag">
      <link-fab-item v-if="feedbackFlag" icon="icon-plus" label="执行反馈" @tap-icon="()=>clickFeedback()"/>
      <link-fab-item v-if="reportFlag" icon="icon-yijianbaobei1" label="一键报备" @tap-icon="()=>clickReport()"/>
      <link-fab-item v-if="forwardFlag" icon="icon-fenxiang" label="一键转发" @tap-icon="()=>clickForward()"/>
    </link-fab-group>
    <banquet-share-dialog :banquetItem="banquetItem" @shareFlag="shareFlag" v-if="isShareDialog"></banquet-share-dialog>
  </link-page>
</template>

<script>
import lnkTaps from '../core/lnk-taps/lnk-taps';
import banquetBasicInfo from './components/banquet-basic-info.vue';
import banquetFeedback from './components/banquet-feedback.vue';
import banquetOutboundDetails from './components/banquet-outbound-details.vue';
import banquetInboundDetails from './components/banquet-inbound-details.vue';
import banquetForwardContactList from './components/banquet-forward-contact-list.vue';
import banquetConsumerOpen from './components/banquet-consumer-open.vue';
import banquetShareDialog from './components/banquet-share-dialog.vue'
import Taro from '@tarojs/taro'

definePageConfig({
    navigationBarTitleText: '宴席单详情'
});

export default {
    name: 'banquet-activity-detail-page',
    components: { 
        lnkTaps,
        banquetBasicInfo, 
        banquetFeedback, 
        banquetOutboundDetails, 
        banquetInboundDetails, 
        banquetForwardContactList, 
        banquetConsumerOpen, 
        banquetShareDialog
    },
    data() {
        const tapsOptions = [
            {name: '基础信息', seq: '1', val: 'basicInfo'},
            {name: '执行反馈', seq: '2', val: 'feedback'},
            {name: '出库明细', seq: '3', val: 'OutboundDetails'},
            {name: '退货明细', seq: '4', val: 'inboundDetails'},
            {name: '消费者开瓶明细', seq: '5', val: 'consumerOpen'},
            {name: '转发明细', seq: '6', val: 'forwardContact'}
        ];
        const tapsActive = 0;       // 选项卡默认选中
        const banquetItem = {};    // 宴席信息
        return {
        tapsOptions,
        tapsActive,    // 选项卡默认选中
        banquetItem,   // 宴席信息
        templateList: [], // 宴席模板
        currentIndex: 0,
        fabGroupFlag: false,     //悬浮按钮
        feedbackFlag: false,
        reportFlag: false,
        forwardFlag: false,
        editBasicFlag: false,    // 是否可编辑时间信息，默认不编辑，满足状态可
        editButtonFlag: true,    // 显示编辑按钮
        id:undefined,//活动id
        userInfo: this.$taro.getStorageSync('token').result,
        isShareDialog: false,
        noticeItem: {}, // 宴席未执行反馈消息提醒对象
        feedbackCode: ''
        }
    },
    async created() {
        // 默认隐藏右上角转发功能
        Taro.hideShareMenu();
        this.tapsActive = this.tapsOptions[0];
        if (this.pageParam.data) {
            this.banquetItem = this.pageParam.data
            this.feedbackCode = this.pageParam.data.feedbackCode
            this.id = this.banquetItem.id
        }
        // 页面来源为宴席未执行反馈消息提醒
        if (this.pageParam.messageInfo) {
            this.noticeItem = this.pageParam.messageInfo;
            this.id = this.noticeItem.banquetFeedbackId;
            await this.queryBasicInfo();
        }
        let sceneObj = await this.$scene.ready();

        if (this.$utils.isNotEmpty(sceneObj.query['id']) && !this.pageParam.data) {
            this.id = sceneObj.query['id'];
            await this.queryBasicInfo()
        } 
        // 若当前消息为未读消息，变更为已读
        if (this.noticeItem.isRead === 'N') {
            await this.changeReadStatus();
        }
        await this.queryTemplate();
        await this.setFabButton();
        await this.switchRegistList();
        //  --- 处理基础信息编辑按钮 ---
        if (['Submitted', 'Processing'].includes(this.banquetItem.status) && ['Approved', 'FeedbackRefuse'].includes(this.banquetItem.approveStatus)) {
        this.editBasicFlag = true;
        }
        this.$bus.$on('refreshFabButton', async (data) => {
            this.banquetItem.approveStatus = data.data.approveStatus;
            this.banquetItem.status = data.data.status;
            await this.setFabButton();
        });
    },
    methods: {
        /**
         * 将未读的消息变更为已读
         * @author: 谭少奇
         * @date: 2023-11-01
         */
        async changeReadStatus () {
            const url = 'action/link/pushNotice/setReadedById';
            const param = {id: this.noticeItem.id};
            await this.$http.get(url, param);
            await this.$utils.handleTODOListNumber('del', 'unReadMessage');
        },
        shareFlag() {
            this.isShareDialog = false;
            this.fabGroupFlag = true;
        },
        /**
         * 获取宴席模板
         * <AUTHOR>
         * @date	2023/12/21 16:06
         */
        async queryTemplate() {
            try {
                const {success, result} = await this.$utils.getQwMpTemplate('BanquetTemplate');
                if (success) {
                    let resultOpt = JSON.parse(result);
                    this.templateList = JSON.parse(resultOpt.conf);
                }
            } catch (e) {
                console.log('e: 获取宴席模板失败', e);
            }
        },
        /**
         *  @desc 设置悬浮按钮逻辑
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        async setFabButton() {
            if (['New', 'Approving', 'Refuse'].includes(this.banquetItem.approveStatus)) {
                this.fabGroupFlag = false;
            } else {
                this.fabGroupFlag = true;
            }
            // 执行反馈审批通过
            if (this.banquetItem.approveStatus === 'Approved' && ['Submitted', 'Closed', 'Abnormal'].includes(this.banquetItem.status)) {
                this.reportFlag = true;
                this.feedbackFlag = false;
                this.forwardFlag = false;
            }
            if (this.banquetItem.approveStatus === 'Approved' && this.banquetItem.status === 'Processing') {
                this.reportFlag = true;
                this.feedbackFlag = true;
                this.forwardFlag = true;
            }
            //lzljqw-004-2651执行反馈审批通过&&活动状态为已结束
            if (this.banquetItem.approveStatus === 'Approved' && this.banquetItem.status === 'End') {
                this.reportFlag = true;
                this.feedbackFlag = true;
                this.forwardFlag = false;
            }
            // 执行反馈审批中/执行反馈审批通过
            if (['FeedbackApproving', 'FeedbackApproved'].includes(this.banquetItem.approveStatus) && ['End', 'Processing', 'Submitted'].includes(this.banquetItem.status)) {
                this.reportFlag = true;
                this.feedbackFlag = false;
                this.forwardFlag = false;
            }
            // 执行反馈审批驳回
            if (this.banquetItem.approveStatus === 'FeedbackRefuse' && ['Processing', 'Submitted'].includes(this.banquetItem.status)) {
                this.reportFlag = true;
                this.feedbackFlag = true;
                this.forwardFlag = true;
            }
            if (this.banquetItem.approveStatus === 'FeedbackRefuse' && this.banquetItem.status === 'End') {
                this.reportFlag = true;
                this.feedbackFlag = true;
                this.forwardFlag = false;
            }
            if (this.banquetItem.approveStatus === 'FeedbackRefuse' && this.banquetItem.status === 'Closed') {
                this.reportFlag = true;
                this.feedbackFlag = false;
                this.forwardFlag = false;
            }
        },
        /**
         * 登记要求（字符串转换为数组）
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        switchRegistList() {
            // 获取模版子类型，将返回的登记要求字符串转化为数组
            if(this.banquetItem.registerRequest) {
                const regRegStr = this.banquetItem.registerRequest.replace(/"/g, '');
                if (this.banquetItem.registerRequest.charAt(0) === "[") {
                this.banquetItem.regReqList = regRegStr.slice(1, -1).split(',');
                } else {
                this.banquetItem.regReqList = regRegStr.slice(1, -1);
                }
            }
        },
        /**
         * tab页切换
         *  <AUTHOR>
         *  @date 2023-06-29
         **/
        switchTab(val, key) {
            this.currentIndex = parseInt(key);
            this.tapsActive = val;
            wx.pageScrollTo({
                selector: `#${val.val}`,
                duration: 500,
            })
        },
        /**
         * 点击执行反馈
         * <AUTHOR>
         *  @date 2023-06-29
         */
        clickFeedback() {
            this.$nav.push('/pages/headquarters-activity/banquet-feedback-page.vue',{
                data: this.banquetItem
            })
        },
        /**
         * 点击一键报备
         * <AUTHOR>
         *  @date 2023-06-29
         */
        clickReport() {
            this.$nav.push('/pages/headquarters-activity/banquet-report-share-page.vue',{
                data: this.banquetItem
            })
        },
        /**
         * 点击一键转发
         * <AUTHOR>
         *  @date 2023-07-04
         */
        async clickForward() {
            const data = await this.$utils.getCfgProperty('NEW_BANQUET_SHARE');
            if (data.includes(this.userInfo.coreOrganizationTile.brandCompanyCode)) {
                this.fabGroupFlag = false;
                this.isShareDialog = true;
            } else {
                this.$nav.push('/pages/headquarters-activity/banquet-forward-contact-page.vue', {
                    banquetItem: this.banquetItem
                });
            }
        },
        /**
         * @description: 通过id查询活动详情
         * @author: 邓佳柳
         * @Date: 2024-10-18 09:33:22
         */    
        async queryBasicInfo() {
            try {
                const data = await this.$http.post('action/link/headquarterFeedback/queryById',{
                    id: this.id
                })
                if (data.success) {
                    this.banquetItem = data.result;
                    this.feedbackCode = data.result.feedbackCode
                } else {
                    this.$message.warn("查询该活动失败，请稍后重试！");
                }
            } catch (e) {
                this.$message.warn("查询该活动失败，请稍后重试！" );
            }
        },
            
    },
    onShareAppMessage(res) {
        // 自定义按钮与小程序提供分享按钮，均跳转到相同界面
        // 构建带参数的 path 使用encodeUrlComponent转译用户token
        const pagePath = '/pages/headquarters-activity/banquet-new-share-page';
        const query = `?banquetId=${this.banquetItem.id}&token=${this.$taro.getStorageSync('token').token}&scene=skipLogin`
        return {
            title: '分享宴席执行反馈',
            path: pagePath + query,
            imageUrl: this.$imageAssets.banquetForwardImage
        }
    }
}
</script>

<style lang="scss">
.banquet-activity-detail-page {

}
</style>
