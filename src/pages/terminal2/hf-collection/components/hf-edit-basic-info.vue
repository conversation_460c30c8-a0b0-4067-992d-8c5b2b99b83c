<!--
 * @Description: 高频点编辑表单组件
-->

<template>
    <view class="hf-edit-basic-info">
        <view class="card-content">
            <link-form ref="editForm" :value="formData" :rules="formRules">
                <!-- 高频点类型选择 -->
                <link-form-item label="高频点类型" required>
                    <link-lov
                        v-model="formData.pointType"
                        type="HIGH_FREQUENCY_POINT_TYPE"
                        @change="onTypeChange"
                        placeholder="请选择高频点类型"
                    />
                </link-form-item>

                <!-- 动态表单字段 -->
                <template v-if="currentFields.length > 0">
                    <link-form-item
                        v-for="field in currentFields"
                        :key="field.key"
                        :label="field.label"
                        :required="field.required"
                        :field="field.key"
                        :vertical="field.type === 'textarea'"
                    >
                        <!-- 输入框字段 -->
                        <link-input
                            v-if="field.type === 'input'"
                            v-model="formData[field.key]"
                            :disabled="field.readonly"
                            :placeholder="field.placeholder"
                        />

                        <!-- 文本域字段 -->
                        <link-textarea
                            v-if="field.type === 'textarea'"
                            v-model="formData[field.key]"
                            :placeholder="field.placeholder"
                            :height="144"
                            :nativeProps="{maxlength: field.maxLength || 300}"
                            mode="textarea"
                        />

                        <!-- 值列表字段 -->
                        <link-lov
                            v-if="field.type === 'lov'"
                            v-model="formData[field.key]"
                            :type="field.lovType"
                            :placeholder="field.placeholder"
                            :excludeLovs="field.excludeLovs"
                        />

                        <!-- 选择器字段 -->
                        <link-select
                            v-if="field.type === 'select'"
                            v-model="formData[field.key]"
                        >
                            <link-select-option
                                v-for="(option, optionIndex) in field.options"
                                :key="optionIndex"
                                :label="option"
                                :val="option"
                            />
                        </link-select>

                        <!-- 日期字段 -->
                        <link-date
                            v-if="field.type === 'date'"
                            v-model="formData[field.key]"
                            view="YMD"
                            :placeholder="field.placeholder"
                            displayFormat="YYYY年MM月DD日"
                            valueFormat="YYYY-MM-DD"
                        />

                        <!-- 日期时间字段 -->
                        <link-date
                            v-if="field.type === 'datetime'"
                            v-model="formData[field.key]"
                            view="YMDHm"
                            :placeholder="field.placeholder"
                            displayFormat="YYYY年MM月DD日 HH时mm分"
                            valueFormat="YYYY-MM-DD HH:mm"
                        />

                        <!-- 终端选择字段 -->
                        <view
                            v-if="field.type === 'terminal'"
                            class="terminal-selector"
                            @tap="openTerminalDialog"
                        >
                            <view class="terminal-content">
                                <text
                                    v-if="formData.accntCode"
                                    class="terminal-name"
                                >
                                    {{ formData.accntCode }}
                                </text>
                                <text v-else class="terminal-placeholder">
                                    请选择终端
                                </text>
                            </view>
                            <view
                                class="iconfont icon-right arrow-right"
                            ></view>
                        </view>

                        <!-- 地址选择字段 -->
                        <template v-if="field.type === 'address'">
                            <template slot="title">
                                <text>{{ field.label }}</text>
                                <view
                                    class="iconfont icon-location location-icon"
                                    @tap="triggerLocation"
                                ></view>
                            </template>
                            <view
                                class="address-content"
                                @tap="triggerLocation"
                            >
                                <text
                                    v-if="formData.address"
                                    class="address-text"
                                >
                                    {{ formData.address }}
                                </text>
                                <text v-else class="address-placeholder">
                                    {{ field.placeholder }}
                                </text>
                            </view>
                        </template>
                    </link-form-item>
                </template>

                <!-- 附件 -->
                <view v-if="formData.pointType" class="attachment-container">
                    <link-form-item
                        label="附件"
                        :required="false"
                        field="attachments"
                        :vertical="true"
                        :customWrapper="true"
                    >
                        <lnk-img
                            ref="attachmentComponent"
                            :parentId="formData.id"
                            moduleType="HIGH_FREQUENCY_COLLECTION"
                            :delFlag="true"
                            :newFlag="true"
                        />
                    </link-form-item>
                </view>
            </link-form>
        </view>

        <!-- 终端选择弹窗 -->
        <choose-related-terminal
            v-if="showTerminalDialog"
            @choose="onTerminalChoose"
            @close="closeTerminalDialog"
        />

        <!-- 定位选择组件 -->
        <LocationSelector ref="locationSelector" @change="onLocationChange" />
    </view>
</template>

<script>
import { getFieldsByType } from "../data/field-config";
import chooseRelatedTerminal from "./choose-related-terminal";
import LnkImg from "../../../core/lnk-img/lnk-img";
import LocationSelector from "./location-selector";
import { reverseTMapGeocoder } from "@/utils/locations-tencent";

export default {
    name: "hf-edit-basic-info",
    components: {
        chooseRelatedTerminal,
        LnkImg,
        LocationSelector,
    },
    data() {
        return {
            currentFields: [],
            formRules: {
                pointType: [{ required: true, message: "请选择高频点类型" }],
            },
            showTerminalDialog: false,
            selectedTerminal: null,
        };
    },
    props: {
        formData: {
            type: Object,
            default: () => ({}),
        },
    },
    watch: {
        "formData.pointType": {
            handler(newVal) {
                if (newVal) {
                    this.updateCurrentFields();
                    this.updateFormRules();
                }
            },
            immediate: true,
        },
    },
    computed: {
        // 判断是否有地址数据
        hasAddressData() {
            return (
                !this.$utils.isEmpty(this.formData.province) ||
                !this.$utils.isEmpty(this.formData.city) ||
                !this.$utils.isEmpty(this.formData.district)
            );
        },
    },
    mounted() {
        this.updateCurrentFields();
        this.updateFormRules();
    },
    methods: {
        /**
         * 处理采集点类型变更
         */
        onTypeChange() {
            this.$emit("resetFormData");

            this.updateCurrentFields();
            this.updateFormRules();
        },

        /**
         * 更新当前表单字段
         * @description 获取选中类型的字段配置
         */
        updateCurrentFields() {
            if (this.formData.pointType) {
                this.currentFields = getFieldsByType(this.formData.pointType);

                // 初始化新字段值
                this.currentFields.forEach((field) => {
                    if (this.formData[field.key] === void 0) {
                        this.$set(this.formData, field.key, "");
                    }
                });
            } else {
                this.currentFields = [];
            }
        },

        /**
         * 更新表单验证规则
         */
        updateFormRules() {
            const newRules = {
                pointType: this.Validator.required("请选择高频点类型"),
            };

            // 为当前字段添加验证规则
            this.currentFields.forEach((field) => {
                if (field.required) {
                    const operation = ["input", "textarea"].includes(field.type)
                        ? "输入"
                        : "选择";
                    newRules[field.key] = this.Validator.required(
                        `请${operation}${field.label}`
                    );
                }
            });

            this.formRules = newRules;
        },

        /**
         * 验证表单
         */
        async validate() {
            try {
                await this.$refs.editForm.validate();
                return true;
            } catch (error) {
                console.error("表单验证失败:", error);

                if (error?.label) {
                    this.$message.warn(`请完善基础信息-${error.label}`);
                } else {
                    this.$message.warn(error.message || "请完善基础信息");
                }

                return false;
            }
        },

        /**
         * 打开终端选择弹窗
         * @description 显示终端选择对话框
         */
        openTerminalDialog() {
            this.showTerminalDialog = true;
        },

        /**
         * 关闭终端选择弹窗
         * @description 隐藏终端选择对话框
         */
        closeTerminalDialog() {
            this.showTerminalDialog = false;
        },

        /**
         * 处理终端选择
         */
        onTerminalChoose(terminal) {
            if (terminal) {
                this.$set(this.formData, "accntCode", terminal.acctCode);
            }
            this.closeTerminalDialog();
        },

        /**
         * 处理定位数据变化
         */
        onLocationChange(addressData) {
            const keys = [
                "province",
                "city",
                "district",
                "street",
                "address",
                "detailAddress",
                "latitude",
                "longitude",
            ];
            keys.forEach((key) => {
                this.$set(this.formData, key, addressData[key]);
            });
        },

        /**
         * 触发定位
         */
        async triggerLocation() {
            if (this.$refs.locationSelector) {
                await this.$refs.locationSelector.triggerLocation();
            }
        },

        /**
         * 设置位置数据（供父组件调用）
         */
        async setLocationData(location) {
            if (!location || this.$utils.isEmpty(location)) {
                return;
            }

            try {
                const newAddressData = {};

                newAddressData.latitude = location.latitude.toString();
                newAddressData.longitude = location.longitude.toString();

                const addressRes = await reverseTMapGeocoder(
                    location.latitude,
                    location.longitude,
                    "高频点定位"
                );

                if (addressRes?.originalData?.result) {
                    const addressComponent =
                        addressRes.originalData.result.addressComponent;

                    newAddressData.province = addressComponent.province || "";
                    newAddressData.city = addressComponent.city || "";
                    newAddressData.district = addressComponent.district || "";
                    newAddressData.street = addressComponent.street || "";

                    const addressParts = [
                        addressComponent.province,
                        addressComponent.city,
                        addressComponent.district,
                        addressComponent.street,
                    ].filter((part) => part?.trim());
                    newAddressData.address = addressParts.join("/");

                    let detailAddress =
                        location.name ||
                        addressRes.originalData.result.formatted_address ||
                        "";

                    [
                        addressComponent.province,
                        addressComponent.city,
                        addressComponent.district,
                    ]
                        .filter(Boolean)
                        .forEach((region) => {
                            detailAddress = detailAddress.replace(region, "");
                        });

                    newAddressData.detailAddress = detailAddress.trim();
                } else {
                    this.$refs.locationSelector.showLocationFailDialog();
                }

                this.onLocationChange(newAddressData);
            } catch (error) {
                console.error("处理位置选择结果失败:", error);
                this.$message.error("地址解析失败，请手动输入地址信息");
            }
        },
    },
};
</script>

<style lang="scss">
.hf-edit-basic-info {
    .card-content {
        padding-bottom: 12px;
    }
    .attachment-container {
        .lnk-img {
            margin-left: 12px;
        }
    }

    .terminal-selector {
        display: flex;
        align-items: center;

        .terminal-content {
            flex: 1;

            .terminal-name {
                color: #3b4144;
            }

            .terminal-placeholder {
                color: #e0e0e0;
            }
        }

        .arrow-right {
            font-size: 32px;
            color: #e0e0e0;
        }

        &:active {
            background-color: #e9ecef;
        }
    }

    .location-icon {
        font-size: 32px;
        color: #2f69f8;
        margin-left: 4px;
        display: inline-block;
    }

    .address-content {
        width: 100%;
        text-align: right;

        .address-text {
            color: #333;
        }

        .address-placeholder {
            color: #e0e0e0;
        }

        &:active {
            background-color: #f8f9fa;
        }
    }
}
</style>
