<!--
 * @Description: 高频点详情
-->

<template>
    <link-page class="hf-detail-page">
        <!--顶部背景-->
        <view class="top-container">
            <navigation-bar
                :backVisible="true"
                :navBarAllHeight="fixTop"
                :backgroundImg="$imageAssets.homeMenuBgImage"
                :title="navigationBarTitle"
                :titleColor="navigationBarTitleColor"
                :navBackgroundColor="navBackgroundColor"
            >
                <view slot="rightIcon" class="edit-button" @tap="goEditPage">
                    <link-icon icon="icon-edit" class="edit-icon"></link-icon>
                </view>
                <view class="top-content">
                    <view class="hf-image">
                        <image :src="$imageAssets.terminalDefaultImage"></image>
                    </view>
                    <view class="hf-content-cover">
                        <view class="title-level-code">
                            <view class="hf-content-top">
                                <view class="hf-title">{{
                                    details.pointName
                                }}</view>
                            </view>
                        </view>
                        <view class="hf-content-middle">
                            <view class="hf-type" v-if="details.pointType">
                                {{
                                    details.pointType
                                        | lov("HIGH_FREQUENCY_POINT_TYPE")
                                }}
                            </view>
                            <link-icon
                                icon="icon-dizhi1"
                                class="location-icon"
                                :style="{
                                    color: getCollectionPointColor(
                                        details.pointType
                                    ),
                                }"
                            />
                        </view>
                    </view>
                </view>
            </navigation-bar>
        </view>
        <view class="content">
            <view
                class="top-blank"
                v-if="tapsFix"
                :style="{
                    height: duration + 'px',
                    'line-height': duration + 'px',
                    'padding-top': statusBarHeight + 'rpx',
                }"
            >
                {{ details.acctName }}
            </view>
            <!--状态栏-->
            <link-sticky
                top
                :duration="
                    this.$device.isIphoneX
                        ? duration * 2 -
                          12 +
                          this.$device.systemInfo.statusBarHeight
                        : duration * 2 + 16
                "
            >
                <view class="tap-container">
                    <view class="lnk-tabs">
                        <view
                            class="lnk-tabs-item"
                            :class="{
                                active:
                                    tab.values.code === tapsActive.values.code,
                            }"
                            v-for="(tab, index) in templateData"
                            :key="index"
                            @tap="switchTab(tab, 'status')"
                        >
                            <view class="label-name" style="min-width: 67px">
                                {{ tab.base.label }}
                            </view>
                            <view
                                class="line"
                                v-if="
                                    tab.values.code === tapsActive.values.code
                                "
                            ></view>
                        </view>
                    </view>
                    <link-dropdown class="dropdown" v-model="showDropdownFlag">
                        <view
                            class="iconfont dropdown-icon"
                            :class="
                                showDropdownFlag ? 'icon-close' : 'icon-apphf'
                            "
                        ></view>
                        <view slot="dropdown" class="dropdown-container">
                            <view
                                v-for="(item, index) in templateData"
                                :key="index"
                                class="menu-item"
                                @tap="switchTab(item, 'dropdown')"
                            >
                                <view
                                    class="iconfont menu-icon"
                                    :class="item.values.iconPath"
                                ></view>
                                <view class="menu-name">{{
                                    item.base.label
                                }}</view>
                            </view>
                        </view>
                    </link-dropdown>
                </view>
            </link-sticky>

            <!--基础信息-->
            <view v-if="tapsActive.values.code === 'basicInfo'">
                <view class="edit-button-container">
                    <view class="edit-text" @tap="goEditBasicInfo">编辑</view>
                </view>

                <basic-info :details="details"></basic-info>
            </view>

            <!--联系人-->
            <view
                v-if="tapsActive.values.code === 'contacts'"
                class="financial-components"
            >
                <view class="edit-button-container">
                    <view class="edit-text" @tap="goEditPage">添加</view>
                </view>

                <contacts ref="contacts" :detailData="details"></contacts>
            </view>
        </view>
    </link-page>
</template>

<script lang="jsx">
import navigationBar from "link-taro-component";
import basicInfo from "./components/hf-basic-info";
import contacts from "./components/hf-contacts";
import { getCollectionPointColor } from "./data/collection-point-config";

export default {
    name: "hf-detail-page",
    components: {
        navigationBar, // 页面top背景栏
        basicInfo, // 基础信息
        contacts, // 联系人
    },
    data() {
        return {
            showDropdownFlag: false,
            tapsFix: false,
            tapsActive: { values: { code: "basicInfo" } },
            fixTop: 200,

            details: this.pageParam.data,

            duration: this.$device.isIphoneX ? 88 : 64,
            statusBarHeight: this.$device.systemInfo.statusBarHeight,
            navigationBarTitle: "高频点详情",
            navigationBarTitleColor: "#ffffff",
            navBackgroundColor: "transparent",
            templateData: [
                {
                    base: { label: "基础信息" },
                    values: { code: "basicInfo", iconPath: "icon-user" },
                },
                {
                    base: { label: "联系人" },
                    values: { code: "contacts", iconPath: "icon-contacts" },
                },
            ],
        };
    },
    /**
     * 监听页面滚动函数
     * @param e 距离顶部距离
     */
    onPageScroll(e) {
        this.tapsFix = e.scrollTop >= this.fixTop - this.duration;
        this.showDropdownFlag = false;
    },

    methods: {
        /**
         * 导航栏切换
         */
        async switchTab(val, flag) {
            switch (flag) {
                case "status":
                    this.tapsActive = val;
                    break;
                case "dropdown":
                    this.tapsActive = val;
                    this.showDropdownFlag = false;
                    break;
            }
        },
        /**
         * 获取采集点类型对应的颜色
         */
        getCollectionPointColor,

        /**
         * 跳转到编辑页面
         */
        goEditPage() {
            this.$nav.push(
                "/pages/terminal2/hf-collection/hf-collection-edit-page",
                {
                    editFlag: "edit",
                    data: this.details,
                }
            );
        },

        /**
         * 编辑基础信息
         */
        goEditBasicInfo() {
            this.goEditPage();
        },

        /**
         * 添加联系人
         */
        goEditContacts() {
            this.$nav.push(
                "/pages/terminal2/edit-construction/edit-contacts-page",
                {
                    flag: "add",
                    title: "添加联系人",
                    acctData: this.details,
                    editFlag: "edit",
                }
            );
        },

        /**
         * 处理从编辑页面返回时的回调
         */
        onBack(data) {
            if (data?.refreshFlag) {
                this.$refs.contacts?.refreshContacts();
                if (data?.newDetailData) {
                    this.details = data.newDetailData;
                }
            }
        },
    },
};
</script>

<style lang="scss">
.hf-detail-page {
    .top-container {
        .comp-navbar {
            width: 100vw;
            .placeholder-bar {
                background-color: transparent;
                width: 100%;
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                -webkit-box-pack: start;
                -ms-flex-pack: start;
                justify-content: flex-start;
                -webkit-box-align: center;
                -ms-flex-align: center;
                align-items: center;
                .icon-left {
                    width: 10%;
                    font-size: 34px;
                    color: #ffffff;
                    padding-left: 24px;
                }
                .navigator-back {
                    width: 46px;
                    height: 46px;
                    padding-left: 24px;
                }
                .bar-title {
                    width: 82%;
                    font-size: 34px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    text-align: center;
                }
            }
            .edit-button {
                position: absolute;
                right: 24px;
                top: 50%;
                transform: translateY(-50%);
                .edit-icon {
                    font-size: 32px;
                    color: #ffffff;
                }
            }
        }
        .top-content {
            @include flex;
            .hf-image {
                margin-left: $margin-normal;
                width: 128px;
                height: 128px;
                border-radius: 16px;
                overflow: hidden;
                box-shadow: 0 7px 49px 0 rgba(20, 28, 51, 0.39);
                image {
                    width: 100%;
                    height: 100%;
                }
            }
            .hf-content-cover {
                width: 80%;
                .hf-content-code {
                    margin-left: 24px;
                    color: #fff;
                    font-size: 28px;
                    line-height: 28px;
                }
                .title-level-code {
                    @include flex-start-center;
                    @include space-between;
                    width: 100%;
                    .hf-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .hf-title {
                            font-family: PingFangSC-Semibold, serif;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 0;
                            line-height: 40px;
                            max-width: 370px;
                        }
                        .hf-level {
                            width: 120px;
                            height: 44px;
                            margin-left: 12px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                }
                .hf-content-middle {
                    @include flex-start-center;
                    height: 60px;
                    line-height: 60px;
                    margin-left: 24px;
                    flex-wrap: wrap;
                    .hf-type {
                        border: 1px solid #ffffff;
                        border-radius: 8px;
                        font-size: 20px;
                        padding-left: 18px;
                        padding-right: 18px;
                        line-height: 36px;
                        color: #ffffff;
                        margin-right: 20px;
                        margin-top: 10px;
                    }
                    .location-icon {
                        font-size: 32px;
                        margin-top: 10px;
                    }
                }
            }
        }
    }
    .content {
        .top-blank {
            width: 100%;
            background: $color-primary;
            position: fixed;
            top: 0;
            font-family: PingFangSC-Semibold, serif;
            font-size: 34px;
            color: #ffffff;
            letter-spacing: 0;
            text-align: center;
            z-index: 9999;
        }
        .tap-container {
            width: 100%;
            display: flex;
            height: 92px;
            overflow: hidden;
            .lnk-tabs::-webkit-scrollbar {
                display: none;
            }
            .lnk-tabs {
                overflow-x: scroll;
                white-space: nowrap;
                border-top: 1px solid #f2f2f2;
                display: flex;
                background-color: #fff;
                color: #595959;
                width: 670px;
                z-index: 9999;
                &.marginTop {
                    margin-top: 80px;
                }
                .active {
                    color: $color-primary;
                }
                .lnk-tabs-item {
                    height: 92px;
                    line-height: 92px;
                    text-align: center;
                    .label-name {
                        width: 100%;
                        font-size: 28px;
                        margin-left: 10px;
                    }
                    .line {
                        height: 8px;
                        width: 56px;
                        border-radius: 16px 16px 0 0;
                        background-color: $color-primary;
                        box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                        margin: -12px auto auto auto;
                    }
                }
            }
            .dropdown {
                right: 0;
                position: relative;
                width: 92px;
                height: 92px;
                background: #ffffff;
                box-shadow: -3px 0 28px 0 rgba(7, 44, 105, 0.16) !important;
                .dropdown-icon {
                    line-height: 92px;
                    text-align: center;
                    font-size: 36px;
                    color: #595959;
                }
                /*deep*/
                .link-dropdown-reference {
                    box-shadow: -3px 0 28px 0 rgba(7, 44, 105, 0.16) !important;
                }
                /*deep*/
                .link-dropdown-content {
                    border-radius: 0 0 32px 32px;
                }
                /*deep*/
                .link-dropdown {
                    box-shadow: -3px 0 28px 0 rgba(7, 44, 105, 0.16) !important;
                }
                .dropdown-container {
                    @include flex-start-center();
                    @include wrap();
                    border-radius: 0 0 32px 32px;
                    padding-bottom: 28px;
                    .menu-item {
                        width: 25%;
                        @include flex-center-center();
                        @include direction-column();
                        .menu-icon {
                            color: $color-primary;
                            font-size: 48px;
                            padding-top: 28px;
                            padding-bottom: 30px;
                        }
                        .menu-name {
                            font-family: PingFangSC-Regular, serif;
                            font-size: 28px;
                            color: #595959;
                            letter-spacing: 0;
                            text-align: center;
                            line-height: 28px;
                            padding-bottom: 28px;
                        }
                    }
                }
            }
        }
        .financial-components {
            padding-bottom: 68px;
        }

        .edit-button-container {
            padding: 0 24px;

            .edit-text {
                padding: 32px 24px 32px 0;
                text-align: right;
                font-size: 28px;
                color: #2f69f8;
                line-height: 28px;

                &:active {
                    opacity: 0.7;
                }
            }
        }
    }
}
</style>
