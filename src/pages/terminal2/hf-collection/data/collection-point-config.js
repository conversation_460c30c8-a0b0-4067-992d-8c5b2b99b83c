import { $imageAssets } from "@/utils/image-assess";

/**
 * 高频点采集点类型配置
 */
export const COLLECTION_POINT_CONFIG = {
    // 采集点类型和颜色映射
    TYPE_COLOR_MAP: {
        GIFT_RECYCLING_POINT: "#FF6B6B", // 红色 - 礼品回收点
        WASTE_RECYCLING_POINT: "#4CAF50", // 绿色 - 废品回收点
        PRIVATE_DINING_CLUB: "#FF9800", // 橙色 - 私人餐饮会所
        UNIT_CANTEEN: "#2196F3", // 蓝色 - 单位食堂
        COMMUNITY: "#9C27B0", // 紫色 - 社区
        TRADE_AREA: "#FF5722", // 深橙色 - 商圈
        POPULAR_CATERING: "#795548", // 棕色 - 大众餐饮
        DEFAULT: "#757575", // 灰色 - 默认
    },

    // 采集点类型和图标映射
    TYPE_ICON_MAP: {
        GIFT_RECYCLING_POINT: $imageAssets.markerRed, // 红色 - 礼品回收点
        WASTE_RECYCLING_POINT: $imageAssets.markerGreen, // 绿色 - 废品回收点
        PRIVATE_DINING_CLUB: $imageAssets.markerOrange, // 橙色 - 私人餐饮会所
        UNIT_CANTEEN: $imageAssets.markerBlue, // 蓝色 - 单位食堂
        COMMUNITY: $imageAssets.markerPurple, // 紫色 - 社区
        TRADE_AREA: $imageAssets.markerDeepOrange, // 深橙色 - 商圈
        POPULAR_CATERING: $imageAssets.markerBrown, // 棕色 - 大众餐饮
        DEFAULT: $imageAssets.markerGray, // 灰色 - 默认
    },
};

/**
 * 获取采集点类型对应的颜色
 * @param {string} type 采集点类型值
 * @returns {string} 对应的颜色值
 */
export function getCollectionPointColor(type) {
    return (
        COLLECTION_POINT_CONFIG.TYPE_COLOR_MAP[type] ||
        COLLECTION_POINT_CONFIG.TYPE_COLOR_MAP.DEFAULT
    );
}

/**
 * 获取采集点类型对应的图标路径
 * @param {string} type 采集点类型值
 * @returns {string} 对应的图标路径
 */
export function getCollectionPointIcon(type) {
    return (
        COLLECTION_POINT_CONFIG.TYPE_ICON_MAP[type] ||
        COLLECTION_POINT_CONFIG.TYPE_ICON_MAP.DEFAULT
    );
}
