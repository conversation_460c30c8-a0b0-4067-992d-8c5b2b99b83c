<template>
    <link-page class="activity-detail-add-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <view v-if="!$utils.isEmpty(approvalId)">
            <view class="zero-view"></view>
            <view style="width: 94%;margin-left: 3%">
                <view class="form-column">
                    <view v-for="item in details" :key="item.label" :class="item.type?'item-textarea':'item-input' ">
                        <view class="label">{{ item.label }}</view>
                        <view class="text">{{ item.text }}</view>
                    </view>
                </view>
            </view>
            <view class="zero-view"></view>
            <view class="act-rule">
                <view class="act-rule-title">活动参与条件</view>
                <view class="view-btn" @tap="ruleDlg = true">查看</view>
            </view>
            <link-dialog v-model="ruleDlg"
                         position="poster">
                <view class="act-rule-con">
                    <view class="act-rules">
                        <view class="act-rules-title">总部规则</view>
                        <view v-if="marketRules.length > 0">
                            <view class="rule-item" v-for="(item, index) in marketRules">
                                <view v-if="item.showContent">
                                    {{item.fieldName}}{{item.judgeName}}{{item.showContent}}
                                </view>
                                <view v-else>
                                    {{item.fieldName}}{{item.judgeName}}{{item.judgeContent}}
                                </view>
                            </view>
                        </view>
                        <view v-else class="rule-item">无</view>
                    </view>
                    <view class="zero-view"></view>
                    <view class="act-rules">
                        <view class="act-rules-title">名单提报规则</view>
                        <view v-if="listRules.length > 0">
                            <view class="rule-item" v-for="(item, index) in listRules">
                                <view v-if="item.showContent">
                                    {{item.fieldName}}{{item.judgeName}}{{item.showContent}}
                                </view>
                                <view v-else>
                                    {{item.fieldName}}{{item.judgeName}}{{item.judgeContent}}
                                </view>
                            </view>
                        </view>
                        <view v-else class="rule-item">无</view>
                    </view>
                </view>
            </link-dialog>
            <view class="zero-view"></view>
        </view>
        <link-form :option="option" ref="form" class="mb40" :value="option.formData"
                   :readonly="!editable"
                   :rules="formRules" hideSaveButton>
            <link-form-item vertical label="审核意见" v-if="option.formData.auditOpinion" :readonly="true">
                <link-textarea v-model="option.formData.auditOpinion"/>
            </link-form-item>
            <link-form-item :key="index" :label="item.values.fieldName" v-for="(item,index) in componentList"
                            :vertical="item.ctrlCode === 'link-textarea' ? true : false"
                            :readonly="item.values.readonlyFlag"
                            :required="item.values.require"
            >
                <view v-if="['consigneeProvince', 'consigneeCity', 'consigneeDistrict', 'consigneeStreet'].includes(item.values.field)"
                      @tap.stop="getLocation()"
                      style="width: 100%; text-align: right; display: flex; align-items: center; justify-content: flex-end">
                    <view style="flex: 1">{{option.formData[item.values.field]}}</view>
<!--                    <link-input v-model="option.formData[item.values.field]"-->
<!--                                placeholder="请选择所在地区"-->
<!--                                disabled/>-->
                    <link-icon icon="icon-location"
                               class="link-location"
                               v-if="editable"
                               style="color: #2A55EC; margin-left: 8px"/>
                </view>
                <view v-else>
                    <activity-detail-add-jsx-component :option="item" :formData="option.formData"
                                                       :listTagParam="listTagParam"/>
                </view>
            </link-form-item>
            <link-form-item label="浪潮投放次数" v-if="!$utils.isEmpty(approvalId) && activityListItem.listRewardType !== 'Equity'">
                <view>{{interListTimes}}</view>
            </link-form-item>
        </link-form>
        <view class="zero-view" v-if="$utils.isEmpty(approvalId)"></view>
        <view class="custom-tab" v-if="!$utils.isEmpty(approvalId) && activityListItem.listRewardType !== 'Equity'">
            <view class="tab-btn" :class="tabActive === 'rollingYear' ? 'tab-btn-active' : ''"
                  @tap="tabActive = 'rollingYear'">滚动年
            </view>
            <view class="tab-btn" :class="tabActive === 'total' ? 'tab-btn-active' : ''" @tap="tabActive = 'total'">合计
            </view>
        </view>
        <view class="consumer-data" v-if="!$utils.isEmpty(approvalId) && tabActive === 'total' && activityListItem.listRewardType !== 'Equity'">
            <view class="consumer-data-item" v-for="item in consumerActData">
                <view class="number">{{item.val}}</view>
                <view class="label">{{item.label}}</view>
            </view>
        </view>
        <view v-if="!$utils.isEmpty(approvalId) && tabActive === 'rollingYear' && activityListItem.listRewardType !== 'Equity'"
              style="margin: 0px 10px 10px 20px;"
        >滚动年截止：{{activityListItem.touchEndTime ? activityListItem.touchEndTime : ''}}</view>
        <view class="consumer-data" v-if="!$utils.isEmpty(approvalId) && tabActive === 'rollingYear' && activityListItem.listRewardType !== 'Equity'">
            <view class="consumer-data-item" v-for="item in consumerRollingYearActData">
                <view class="number">{{item.val}}</view>
                <view class="label">{{item.label}}</view>
            </view>
        </view>
        <view class="blank-short"></view>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
        <!-- 从名单行详情进入pageParam.data.listRewardType === 'Liqueur'-->
        <!-- 新增消费者后跳转pageParam.lineDataType === 'Liqueur'-->
        <link-sticky v-if="pageParam.editable && editable && $utils.isEmpty(approvalId) && componentList.length > 0 && (pageParam.data.listRewardType === 'Liqueur' || pageParam.lineDataType === 'Liqueur')">
            <link-button mode="stroke" v-show="LangChao25BtnShow" label="取消" block @tap="$nav.back()"/>
            <link-button label="保存" v-show="LangChao25BtnShow" block @tap="commit"/>
        </link-sticky>
        <link-sticky>
<!--            v-if="(activityType === 'LangChaoJiHua' || activityType === 'QiTaXiaoJiuTiBao') && activityStatus === 'Approved' && lineStatus === 'Approved' && ($utils.isEmpty(approvalId) || logistics)">-->
            <link-button mode="stroke" label="补发详情" block @tap="reissueApplication(false)"
                         v-if="((activityType === 'LangChaoJiHua' || activityType === 'QiTaXiaoJiuTiBao'))
                         && (lineStatus === 'Approved' || lineStatus === 'CrowdConfirmed') &&
                         (lineData.logStatus === 'ReSubmitted' || lineData.logStatus === 'Reissued' || lineData.logStatus === 'ReissueSuccess')"/>
            <link-button mode="stroke" label="补发申请" block @tap="reissueApplication(true)"
                            v-show="LangChao25BtnShow"
                         v-if="((activityType === 'LangChaoJiHua' || activityType === 'QiTaXiaoJiuTiBao'))
                         && (lineStatus === 'Approved' || lineStatus === 'CrowdConfirmed') &&
                         (lineData.logStatus === 'Delivered'||lineData.logStatus === 'ReissueRefused') && approval_from != 'qw'"/>
            <link-button label="查看物流" v-if="($utils.isEmpty(approvalId) || logistics) && checkLogFlag" block @tap="checkLogistics"/>
        </link-sticky>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
        </link-sticky>
        <!-- 权益类型为圈选时 -->
        <link-sticky class="button-bottom"
            v-show="LangChao25BtnShow" style="bottom: 20px !important;" v-if="pageParam.circleButtonFlag && pageParam.data.listRewardType === 'Crowd' && $utils.isEmpty(approvalId) && activityStatus === 'Confirm' && lineStatus=== 'CrowdConfirming'">
            <link-button block @tap="putIn('normal')">确认投放</link-button>
            <link-button block @tap="putIn('no')">不投放</link-button>
        </link-sticky>
        <link-sticky v-show="LangChao25BtnShow" class="text-bottom" v-if="pageParam.circleButtonFlag && pageParam.data.listRewardType === 'Crowd' && $utils.isEmpty(approvalId) && activityStatus === 'Confirm' && lineStatus=== 'CrowdConfirming'">
            <text>注意：提报结束时间到期时，待确认的会自动确认投放</text>
        </link-sticky>
    </link-page>
</template>

<script>
    import ActivityDetailAddJsxComponent from "./activity-detail-add-jsx-component";
    import {ROW_STATUS} from "../../../utils/constant";
    import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
    import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import {reverseTMapGeocoder} from "../../../utils/locations-tencent";

    export default {
        name: "guest-detail-add-page",
        components: {
            ApprovalOperator,
            ApprovalHistoryPoint,
            ActivityDetailAddJsxComponent,
            LnkTaps
        },
        data() {
            let config = {
                data: {},
            };
            const option = new this.FormOption(this, {
                ...config,
                operator: 'NEW'
            });
            const LangChao25BtnShow = this.pageParam.LangChao25BtnShow;

            const activityType = this.pageParam.activityType;
            const lineStatus = this.pageParam.lineStatus;
            const activityStatus = this.pageParam.activityStatus;
            const brand = this.pageParam.brand;
            const lineData = this.pageParam.data;
            const companyId = this.pageParam.companyId || ''
            const orderFlag = this.pageParam.orderFlag || 'Y'
            return {
                checkLogFlag: false,           // 名单行的订单id如果存在，则显示查看物流按钮
                orderFlag,
                LangChao25BtnShow,
                consumerData: {}, // 从消费者新建页带过来的消费者信息
                isGuoJiao: false,
                pageFrom: '',
                companyId,
                companyName: '',
                tabActive: 'rollingYear',
                consumerActData: [
                    {label: '活动参与次数', field: 'activitiesNum', val: '-'},
                    {label: '拜访次数', field: 'visitsNum', val: '-'},
                    {label: '动销次数', field: 'movingPinTimes', val: '-'},
                    {label: '动销金额', field: 'movingPinAmount', val: '-'},
                    {label: '互动扫码次数', field: 'scanningCodeNum', val: '-'},
                    {label: '礼赠次数', field: 'giftNum', val: '-'},
                    {label: '动销瓶数', field: 'movingPinNum', val: '-'},
                    {label: '三节浪潮触达', field: 'interTouchNum', val: '-'},
                    // {label: '消费者触达次数', field: 'interTouchNum', val: '-'},
                    {label: '最近一次参与活动', field: 'latelyActivities', val: '-'},
                    {label: '最近一次礼赠', field: 'latelyGift', val: '-'},
                    {label: '最近一次动销', field: 'latelyMovingPin', val: '-'},
                    {label: '最近一次拜访', field: 'latelyVisits', val: '-'}
                ],
                consumerRollingYearActData: [
                    {label: '三节浪潮触达', field: 'interTouchNum', val: '-'},
                    {label: '开瓶瓶数', field: 'scanTimes', val: '-'},
                    {label: '动销金额', field: 'movingPinAmount', val: '-'},
                    {label: '线下活动参与次数', field: 'offlineTimes', val: '-'},
                    {label: '线上活动参与次数', field: 'onlineTimes', val: '-'},
                    {label: '下单次数', field: 'orderTimes', val: '-'}
                ],
                listApplyId: null,
                approvalId: null,
                activityListItem: {},           //名单提报对象
                marketRules: [],      // 营销规则
                listRules: [],        // 名单提报规则
                details: [],                   //名单提报头显示字段
                sequence: 0,                  // 排序
                lineData,                      // 名单行数据
                lineStatus,                    // 名单行状态
                activityStatus,               // 名单头状态
                extFieldType: [],            // 组件类型值列表
                tableFields: [],             // 字段值列表
                interactionFields: [],        // 互动平台字段
                activityType,
                brand,
                componentsMap: {             // 组件映射,将后台配置的值列表转换成小程序组件
                    'link-table-column-input': {component: {ctrlCode: 'link-input', values: {}}},
                    'link-table-column-lov': {component: {ctrlCode: 'link-lov', values: {}}},
                    'link-table-column': {component: {ctrlCode: 'link-input', values: {readonlyFlag: true}}},
                    'link-table-column-input-number': {component: {ctrlCode: 'link-number', values: {}}},
                    'link-table-column-datepicker': {component: {ctrlCode: 'link-date', values: {}}},
                    'link-table-column-object': {component: {ctrlCode: 'link-object', values: {}}},
                    'link-table-column-address': {component: {ctrlCode: 'link-address', values: {}}},
                    'link-table-column-textarea': {component: {ctrlCode: 'link-textarea', values: {}}}
                },
                guestFields: [],
                editable: true,
                approval_from: '',
                ifNew: true,
                formRules: {
                    phone: this.Validator.phone(), // 电话号码校验
                    testReg: this.Validator.regexp({reg: /^1[3456789]\d{9}$/, msg: '请输入正确的电话号码'}),  // 正则校验
                },
                componentList: [],
                option,
                interListTimes: '-',
                ruleDlg: false,
                logistics: false,       // 企微消息（物流退回）进来
                listTagParam: {},       // 名单那提报规则配置了产品时产品查询参数
                invalidateLocString: '' // 区域限制字符串
            }
        },
        async created() {
            this.$locations.QQClearLocation();
            let config = {
                data: {},
            };
            // approval从审批页面来/accountApply从消费者新建页面（申请跟进并选择此客户/申请跟进此客户）-消费者分配申请页面来/oldCustomerItem从消费者新建页面来/无值则是从名单提详情来
            const source = this.pageParam.source;
            this.pageFrom = this.pageParam.source;
            this.editable = this.pageParam.editable;
            if (source === 'accountApply' || source === 'oldCustomerItem') {
                const consumerItem = this.pageParam.data;
                this.consumerData = consumerItem;
                this.companyId = consumerItem.belongToCompanyId;
                // 消费者
                let consumerObj = {
                    accntChannelId: consumerItem.id,
                    phone: consumerItem.phoneNumber,
                    row_status: ROW_STATUS.NEW,
                    acctName: consumerItem.name,
                    gender: consumerItem.gender,
                    birthDate: consumerItem.birthDate,
                    company: consumerItem.companyName,
                    birthDateType: consumerItem.birthType,
                    idCardNumber: consumerItem.idCardNumber,
                    deliveryChannel: consumerItem.accntSourceFrom,
                    jobTitle: consumerItem.position,
                    // 行业性质
                    companyType: consumerItem.companyProperty,
                    //所属圈层
                    circle: consumerItem.belongToCircleLayer,
                    salemanId: consumerItem.salemanId,
                    saleman: consumerItem.saleman,
                    mcInterListId: this.pageParam.mcInterListId,
                    activityType: this.pageParam.activityType,
                    activityName: this.pageParam.activityName,
                    consigneeProvince: consumerItem.province,
                    consigneeCity: consumerItem.city,
                    consigneeDistrict: consumerItem.county,
                    consigneeStreet: consumerItem.street,
                    consigneeAddress: consumerItem.detailedAddress,
                    source: consumerItem.sourceFrom,
                    subAcctType: consumerItem.type,
                    loyaltyLevel: consumerItem.loyaltyLevel,
                    certify: consumerItem.certify,
                    bringInto: consumerItem.bringInto
                }
                config = {
                    data: consumerObj
                }
            } else {
                // 名单提报详情-完善消费者信息
                config = {
                    data: this.pageParam.data
                }
            }
            this.option = new this.FormOption(this, {
                ...config,
                operator: 'NEW'
            });
            let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
            const approval_from = sceneObj.query['approval_from'];
            this.approval_from = approval_from;
            if (this.$store.getters['listTag/getListTagFlag'] && source !== 'approval') {
                // 标准接口查询消费者信息 accntChannelId
                await this.getConsumerData(this.pageParam.data.accntChannelId || this.pageParam.data.id);
                // 名单那提报规则配置了产品时的赋值逻辑
                await this.setDefaultListTagProd(config.data);
            } else {
                // 判断是否从品鉴卡活动来的,品鉴卡活动无需默认赋值产品数据
                const tastingCardFlag = this.$store.getters['tastingCard/getTastingCardFlag'];
                console.log('tastingCard', this.$store.getters['tastingCard/getTastingCardFlag']);
                // 默认赋值产品数据
                if (!tastingCardFlag && this.$store.getters['listTag/getProdPick'] !== 'Y' && !(source === 'approval' || approval_from === 'qw')) {
                    await this.setDefaultProd(config.data);
                }
            }
            if (source === 'approval') {
                this.editable = false;
                this.approvalId = this.pageParam.data.id;//审批传过来的审批数据id
                this.listApplyId = this.pageParam.data.flowObjId.split('_')[0];//审批传过来的名单提报对象ID
                if (this.$utils.isNotEmpty(this.listApplyId)) {
                    await this.queryInterListLineNew();
                } else {
                    this.$utils.showAlert('请联系管理员，未获取到名单提报信息！', {icon: 'none'});
                    return
                }
            } else {
                if (approval_from === 'qw' && this.pageParam.sourceFrom !== 'order') { //从小程序审批消息而来
                    this.editable = false
                    this.approvalId = sceneObj.query['approval_id'];//审批传过来的审批数据id
                    this.logistics = sceneObj.query['logistics'] === '1';
                    this.listApplyId = sceneObj.query['flowObjId'].split('_')[0];//审批传过来的名单提报对象ID
                    if (this.$utils.isNotEmpty(this.listApplyId)) {
                        await this.queryInterListLineNew();
                    } else {
                        this.$utils.showAlert('请联系管理员，未获取到名单提报信息！', {icon: 'none'});
                        return
                    }
                }
                // 企微预警卡片
                if (this.pageParam.sourceFrom === 'order') {
                    this.listApplyId = this.pageParam.mcInterListId;
                    this.option.formData = this.pageParam.data;
                    this.lineData = this.pageParam.data;
                    this.activityType = this.pageParam.activityType;
                    this.lineStatus = this.pageParam.lineStatus;
                    this.activityStatus = this.pageParam.activityStatus;
                    await this.initDataOrder();
                }
            }
            if(!!this.lineData.orderId){
                this.checkLogFlag = true;
            }
            // 获取值列表
            this.extFieldType = await this.$lov.getLovByType('EXT_FIELD_TYPE');
            this.tableFields = await this.$lov.getLovByType('TABLE_FIELD');
            this.interactionFields = await this.$lov.getLovByType('INTERACTION_FIELD');
            await this.getGuestFieldsConfig()
            this.invalidateLocString = await this.$utils.getCfgProperty('REGIONAL_RESTRICTION');
        },
        async onShow(){
            // 根据定位信息反解析地址信息
            const location = this.$locations.QQGetLocation();
            if(location){
                let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '名单新增');
                // todo 是否存经纬度
                // this.$set(this.formData, 'attr01', location.longitude);
                // this.$set(this.formData, 'attr02', location.latitude);
                try {
                    this.$utils.showLoading()
                    const addrCode = addressInfo['originalData'].result.addressComponent.adcode;
                    const data = await this.$http.post(this.$env.appURL +'/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode: addrCode})
                    if(data.success) {
                        if(data.adcodeFlag){
                            this.$set(this.option.formData, 'consigneeProvince', data.province);
                            this.$set(this.option.formData, 'consigneeCity', data.city);
                            this.$set(this.option.formData, 'consigneeDistrict', data.district);
                            this.$set(this.option.formData, 'consigneeStreet', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                            this.$utils.hideLoading();
                        }else {
                            this.$set(this.option.formData, 'consigneeProvince', addressInfo['originalData'].result.addressComponent.province)
                            this.$set(this.option.formData, 'consigneeCity', addressInfo['originalData'].result.addressComponent.city)
                            this.$set(this.option.formData, 'consigneeDistrict', addressInfo['originalData'].result.addressComponent.district)
                            this.$set(this.option.formData, 'consigneeStreet', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                            this.$utils.hideLoading();
                        }
                        // 判断是否在限制地区（参数配置）
                        if(this.invalidateLocString.indexOf(this.option.formData.consigneeProvince) !== -1){
                            this.$message.warn('该地区尚未开通配送服务，请切换其他地区选择！');
                        }
                    }
                } catch (e) {
                    this.$utils.hideLoading();
                }
            }
        },
        methods: {
            /**
             * @desc 确认投放
             * <AUTHOR>
             * @date 2024-07-03
             **/
            putIn(type) {
                // 判断是否在限制地区（参数配置）
                if(this.invalidateLocString.indexOf(this.option.formData.consigneeProvince) !== -1){
                    this.$message.warn('该地区尚未开通配送服务，请切换其他地区选择！');
                    return;
                }
                const typeList = {
                    normal: '确认投放',
                    no: '不投放'
                };
                this.$dialog({
                    title: '提示',
                    content: `是否${typeList[type]}，确认后不可修改`,
                    cancelButton: true,
                    confirmButton: true,
                    onConfirm: () => {
                        switch (type) {
                            case 'normal':
                                this.normalPutIn();
                                break;
                            case 'no':
                                this.noPutIn();
                                break;
                        }
                    }
                });
            },
            /**
             * @desc 确认投放（自定义个数）投放成功后
             * <AUTHOR>
             * @date 2024-07-03
             **/
            async normalPutIn() {
                try{
                    const id = this.lineData.id;
                    const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/confirmInterLineDeploy', {
                        ids: [id]
                    });
                    if (data.success) {
                        // 将当前名单行的待确认（CrowdConfirming）的名单行更新成已确认（CrowdConfirmed）
                        this.$set(this, 'lineStatus', 'CrowdConfirmed');
                        this.$message.success('确认投放成功！');
                    } else {
                        this.$message.error('确认投放失败，' + data.result);
                    }
                } catch (e) {
                    console.error(e)
                }
            },
            /**
             * @desc 不投放
             * <AUTHOR>
             * @date 2024-07-03
             **/
            async noPutIn() {
                try {
                    const id = this.lineData.id;
                    const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/noInterLineDeploy', {
                        ids: [id]
                    });
                    if (data.success) {
                        // 将当前名单行的待确认（CrowdConfirming）的名单行更新成不投放（CrowdRefused）
                        this.$set(this, 'lineStatus', 'CrowdRefused');
                        this.$message.success('不投放成功！');
                    } else {
                        this.$message.error('不投放失败，' + data.result);
                    }
                } catch (e) {
                    console.error(e)
                }
            },
            /**
             * @desc 查询消费者触达信息相关内容
             * <AUTHOR>
             * @date 2022/4/14 11:00
             **/
            async queryInterTouch() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', {
                    dmpSrUrl: '/link/CsmListSubmission/queryCsmListSubmission',
                    belongToCompanyId: this.activityListItem.companyId,
                    phoneNumber: this.option.formData.phone
                });
                if (data.rows && data.rows.length > 0) {
                    this.consumerActData.forEach((item) => {
                        if (!this.$utils.isUndefined(data.rows[0][item.field])) {
                            item.val = data.rows[0][item.field];
                        }
                    });
                }
            },
            /**
             * @desc 查询消费者触达信息相关内容
             * <AUTHOR>
             * @date 2022/4/14 11:00
             **/
            // async queryFiscalYearInterTouch() {
            //     const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', {
            //         dmpSrUrl: '/link/CsmListSubmission/queryCsmListSubmission',
            //         belongToCompanyId: this.activityListItem.companyId,
            //         phoneNumber: this.option.formData.phone,
            //         businessDate: `${new Date().getFullYear()}-${new Date().getMonth() + 1}-${new Date().getDate()}`
            //     });
            //     if (data.rows && data.rows.length > 0) {
            //         this.consumerFiscalYearActData.forEach((item) => {
            //             if (!this.$utils.isUndefined(data.rows[0][item.field])) {
            //                 item.val = data.rows[0][item.field];
            //             }
            //         });
            //     }
            // },
            /**
             * @desc 查询滚动年
             * <AUTHOR>
             * @date 2024-04-08
             **/
            async queryRollingYearInterTouch() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', {
                    dmpSrUrl: '/link/CsmListSubmission/queryRollingYearCsmListSubmission',
                    belongToCompanyId: this.activityListItem.companyId,
                    phoneNumber: this.option.formData.phone,
                    touchEndTime: this.activityListItem.touchEndTime
                });
                if (data.rows && data.rows.length > 0) {
                    this.consumerRollingYearActData.forEach((item) => {
                        if (!this.$utils.isUndefined(data.rows[0][item.field])) {
                            item.val = data.rows[0][item.field];
                        }
                    });
                }
            },
            /**
             * @desc 根据ID查询嘉宾名单行信息
             * <AUTHOR>
             * @date 2022/4/14 10:07
             **/
            async queryInterListLineNew() {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/queryById', {id: this.listApplyId});
                this.option.formData = data.result;
                // 浪潮投放次数
                if (data.result && (data.result.interListTimes || data.result.interListTimes === 0)) {
                    this.interListTimes = data.result.interListTimes;
                }
                this.lineData = data.result;
                console.log('lineData', this.lineData);
                this.activityType = data.result.activityType;
                this.lineStatus = data.result.status;
                this.activityStatus = data.result.headStatus;
                await this.initData();
            },
            /**
             * @desc 查询参数配置
             * <AUTHOR>
             * @date 2022/6/7 17:21
             **/
            async queryCfg() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'GUOJIAO_ACCT_MEMBER_LEVEL'});
                if (data.success) {
                    const companyIds = data.value.split(',');
                    this.isGuoJiao = companyIds.indexOf(this.activityListItem.companyId) !== -1
                }
            },
            /**
             * @desc 查询活动参与条件
             * <AUTHOR>
             * @date 2022/4/13 16:04
             **/
            async queryMcRule() {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/marketActivityRule/queryByExamplePage', {
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    oauth: 'ALL',
                    filtersRaw: [{"id": "mcActId", "property": "mcActId", "value": this.activityListItem.mcActId}]
                });
                if (data.rows && data.rows.length > 0) {
                    const otherRules = data.rows.filter(item => !item.fieldLovType);
                    const consumerGradeData = data.rows.filter(item => item.fieldColumnName === 'loyaltyLevel' && item.fieldLovType);
                    for (const item of consumerGradeData) {
                        const lovData = item.judgeContent.split(',');
                        let judgeContent = '/';
                        for (let i = 0; i < lovData.length; i++) {
                            if (this.isGuoJiao) {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeNameParentTypeParentVal(item.fieldLovType, lovData[i], 'ACCT_MEMBER_LEVEL_COMPANY', this.activityListItem.companyId) + '/';
                            } else {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovData[i]) + '/';
                            }
                        }
                        item['showContent'] = judgeContent.substring(1, judgeContent.lastIndexOf('/'));
                    }
                    const lovRuleData = data.rows.filter(item => !!item.fieldLovType && item.fieldColumnName !== 'loyaltyLevel');
                    for (const item of lovRuleData) {
                        const lovRuleItemData = item.judgeContent.split(',');
                        let ruleItemJudgeContent = '/';
                        for (let i = 0; i < lovRuleItemData.length; i++) {
                            ruleItemJudgeContent = ruleItemJudgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovRuleItemData[i]) + '/';
                        }
                        item['showContent'] = ruleItemJudgeContent.substring(1, ruleItemJudgeContent.lastIndexOf('/'));
                    }
                    this.marketRules = otherRules.concat(consumerGradeData).concat(lovRuleData);
                }
            },
            /**
             * @desc 查询活动参与条件
             * <AUTHOR>
             * @date 2022/4/13 16:04
             **/
            async queryRule() {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/marketActivityRule/queryByExamplePage', {
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    oauth: 'ALL',
                    filtersRaw: [{"id": "mcActId", "property": "mcActId", "value": this.activityListItem.id}]
                });
                if (data.rows && data.rows.length > 0) {
                    const otherRules = data.rows.filter(item => !item.fieldLovType);
                    const consumerGradeData = data.rows.filter(item => item.fieldColumnName === 'loyaltyLevel' && item.fieldLovType);
                    for (const item of consumerGradeData) {
                        const lovData = item.judgeContent.split(',');
                        let judgeContent = '/';
                        for (let i = 0; i < lovData.length; i++) {
                            if (this.isGuoJiao) {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeNameParentTypeParentVal(item.fieldLovType, lovData[i], 'ACCT_MEMBER_LEVEL_COMPANY', this.activityListItem.companyId) + '/';
                            } else {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovData[i]) + '/';
                            }
                        }
                        item['showContent'] = judgeContent.substring(1, judgeContent.lastIndexOf('/'));
                    }
                    const lovRuleData = data.rows.filter(item => !!item.fieldLovType && item.fieldColumnName !== 'loyaltyLevel');
                    for (const item of lovRuleData) {
                        const lovRuleItemData = item.judgeContent.split(',');
                        let ruleItemJudgeContent = '/';
                        for (let i = 0; i < lovRuleItemData.length; i++) {
                            ruleItemJudgeContent = ruleItemJudgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovRuleItemData[i]) + '/';
                        }
                        item['showContent'] = ruleItemJudgeContent.substring(1, ruleItemJudgeContent.lastIndexOf('/'));
                    }
                    this.listRules = otherRules.concat(consumerGradeData).concat(lovRuleData);
                }
            },
            /**
             * @desc 卡片预警-查看名单行详情
             * <AUTHOR>
             * @date 2023-04-18
             **/
            async initDataOrder() {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListNew/queryById', {
                    id: this.option.formData.mcInterListId
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询名单提报头数据失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.activityListItem = data.result;
                    this.brand = data.result.brand;
                }
            },
            /**
             * @desc 初始化数据
             * <AUTHOR>
             * @date 2022/4/2 10:04
             **/
            async initData() {
                this.$utils.showLoading();
                if (!this.$utils.isEmpty(this.approvalId) || this.logistics) {
                    const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListNew/queryById', {
                        id: this.option.formData.mcInterListId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError('查询名单提报头数据失败！' + response.result);
                        }
                    });
                    if (data.success) {
                        this.$utils.hideLoading();
                        this.activityListItem = data.result;
                        this.brand = data.result.brand;
                    }
                }
                // 详情头初始化
                this.details = [
                    {label: '活动名称', text: this.activityListItem.interListName || this.activityListItem.activityName},
                    {
                        label: '活动类型',
                        text: await this.$lov.getNameByTypeAndVal('MC_TYPE', this.activityListItem.activityType)
                    },
                    {
                        label: '活动开始时间',
                        text: this.$date.filter(this.activityListItem.actStartTime, 'YYYY-MM-DD HH:mm:ss')
                    },
                    {label: '活动结束时间', text: this.$date.filter(this.activityListItem.actEndTime, 'YYYY-MM-DD HH:mm:ss')},
                    {
                        label: '状态',
                        text: await this.$lov.getNameByTypeAndVal('LIST_STATUS', this.activityListItem.status)
                    },
                    // {label: '提报名额', text: this.activityListItem.submitPlaces},
                    {label: '活动对接人', text: this.activityListItem.actListOwner}
                ]
                this.companyId = this.pageParam.companyId || this.activityListItem.companyId
                await this.queryInterTouch();
                // 本财年
                await this.queryRollingYearInterTouch();
                await this.queryCfg();
                if (this.activityListItem.mcActId) {
                    await this.queryMcRule();
                }
                await this.queryRule();
            },
            /**
             * @desc 补发申请按钮
             * <AUTHOR>
             * @date 2022/4/1 11:38
             **/
            reissueApplication(editable) {
                if (this.lineData.logStatus === 'ReissueRefused' || this.lineData.logStatus === 'Delivered') {
                    this.$nav.redirect('/pages/lj-consumers/list-report/reissue-application-page', {
                        interListLine: this.option.formData,
                        editable: (!!(editable || this.lineData.logStatus === 'ReissueRefused'))&&this.LangChao25BtnShow,
                        pageFrom: 'reissueApplication',
                        invalidateLocString: this.invalidateLocString,
                        "LangChao25BtnShow":this.LangChao25BtnShow,
                        callback: async () => {
                        }
                    });
                } else {
                    this.$nav.push('/pages/lj-consumers/list-report/reissue-application-page', {
                        interListLine: this.option.formData,
                        editable: (!!(editable || this.lineData.logStatus === 'ReissueRefused'))&&this.LangChao25BtnShow,
                        pageFrom: 'reissueApplication',
                        invalidateLocString: this.invalidateLocString,
                        "LangChao25BtnShow":this.LangChao25BtnShow,
                        callback: async () => {
                        }
                    });
                }
            },
            /**
             * @desc 查看物流
             * <AUTHOR>
             * @date 2022/4/1 15:18
             **/
            checkLogistics() {
                this.$nav.push('/pages/lj-consumers/list-report/check-logistics-page', {
                    interListLine: this.option.formData,
                    orderFlag: this.orderFlag,
                    callback: async () => {
                    }
                });
            },
            /**
             * @desc 查询嘉宾名单相关配置字段
             * <AUTHOR>
             * @date 2022/3/31 15:38
             **/
            async getGuestFieldsConfig() {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interCustExt/queryByExamplePage', {
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    filtersRaw: [
                        {id: 'companyId', property: 'companyId', value: this.companyId, operator: '='},
                        {id: 'activityType', property: 'activityType', value: this.activityType, operator: '='},
                        {id: 'setType', property: 'setType', value: 'McInterCust', operator: '='}
                    ],
                    sort: 'sequence',
                    order: 'ASC'
                });
                if (!!data && data.success) {
                    const configList = data.rows || [];
                    // 240523 item.pageType为空或者配置了AppListLine的才展示
                    let configListData = configList.filter(item => item.extFieldType && item.extFieldType !== 'link-table-column-address' && (item.pageType === '' || item.pageType.indexOf('AppListLine') > -1));
                    configListData.forEach((item, index) => {
                        let component = this.$utils.deepcopy(this.componentsMap[item.extFieldType].component);
                        // 如果【是否扩展字段】为Y，则先取【自定义显示名称】字段显示值显示为字段名称。如果没有才会显示营销平台字段。
                        if (item.fieldType === 'Y') {
                            component.values['fieldName'] = item.custColumnName;
                        } else {
                            let field;
                            if (item.pageType === 'LinkSpot') {
                                field = this.interactionFields.filter((fieldItem) => {
                                    return fieldItem.val === item.interactionField;
                                })[0];
                            } else {
                                field = this.tableFields.filter((fieldItem) => {
                                    return fieldItem.val === item.objectField;
                                })[0];
                            }
                            component.values['fieldName'] = !!field && field.name;
                        }
                        // 是否必输
                        component.values['require'] = item.necFlag === 'Y';
                        // 对应字段
                        component.values['field'] = item.objectField;
                        // 如果是值列表类型,则获取值列表
                        if (!!item.valueList && component.ctrlCode === 'link-lov') {
                            component.values['lovType'] = item.valueList;
                        }
                        if (component.ctrlCode === 'link-object' && component.values['field'] === 'prodId') {
                            component.values['option'] = 'productOption';
                            component.values['showField'] = 'prodName';
                            if (this.pageParam.source === 'approval') {
                                // 从审批页面来时产品只可查看
                                component.values.readonlyFlag = true;
                            } else {
                                if (this.pageParam.editable) {
                                    if (this.$store.getters['listTag/getListTagFlag']) {
                                        // 名单那提报规则配置了产品时不禁用
                                        component.values.readonlyFlag = false;
                                    } else {
                                        component.values.readonlyFlag = this.$store.getters['listTag/getProdPick'] !== 'Y';
                                    }
                                } else {
                                    component.values.readonlyFlag = true;
                                }
                            }
                        }
                        if (component.values['field'] === 'consigneeAddress') {
                            this.sequence = index;
                        }
                        if (component.values['field'] === 'bringInto' || component.values['field'] === 'certify' || component.values['field'] === 'acctName' || component.values['field'] === 'phone' || component.values['field'] === 'source' || component.values['field'] === 'subAcctType' || component.values['field'] === 'loyaltyLevel') {
                            component.values.readonlyFlag = true
                        }
                        this.componentList.push(component);
                    });
                    let address = configList.filter((item) => item.extFieldType && item.extFieldType === 'link-table-column-address');
                    if (address.length > 0) {
                        let componentData = {
                            ctrlCode: 'link-address',
                            values: {fieldName: '收货地址-省市县', require: address[0].necFlag === 'Y'}
                        }
                        address.forEach((item) => {
                            if (item.objectField === 'consigneeProvince') {
                                componentData.values.province = item.objectField;
                                componentData.values.field = item.objectField;
                            }
                            if (item.objectField === 'consigneeCity') {
                                componentData.values.city = item.objectField;
                            }
                            if (item.objectField === 'consigneeDistrict') {
                                componentData.values.district = item.objectField;
                            }
                            if (item.objectField === 'consigneeStreet') {
                                componentData.values.street = item.objectField;
                            }
                        })
                        this.componentList.splice(this.sequence, 0, componentData);
                    }
                    this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.companyId);
                }
            },
            /**
             * @desc 提交数据
             * <AUTHOR>
             * @date 2022/4/7 11:22
             **/
            async commit() {
                for (let i = 0; i < this.componentList.length; i++) {
                    const data = this.componentList[i];
                    // 如果必输,则校验必输的值是否为空
                    if (!!data.values['require']) {
                        if (!this.option.formData[data.values['field']]) {
                            const prefix = data['ctrlCode'] === 'link-input' ? '请输入' : '请选择';
                            this.$message.warn(`${prefix}${data.values.fieldName}`);
                            return false;
                        }
                    }
                    if (data.values.hasOwnProperty('fieldType')) {
                        if (data.values.fieldType === 'phone') {
                            const reg = /^1[3456789]\d{9}$/
                            const flag = reg.test(this.option.formData[data.values['field']])
                            if (!flag) {
                                this.$message.warn(`请输入正确的${data.values.fieldName}`);
                                return false;
                            }
                        }
                        if (data.values.fieldType === 'number') {
                            if (typeof (Number(this.option.formData[data.values['field']])) !== 'number') {
                                this.$message.warn(`${data.values.fieldName}只能为数字，请重新输入`);
                                return false;
                            }
                        }
                    }
                }
                if (this.consumerData.empFlag === 'Y') {
                    this.$message.warn('当前消费者为内部员工，不允许提报！');
                    return;
                }
                if (this.consumerData.terminalFlag === 'Y') {
                    this.$message.warn('当前消费者为终端员工，不允许提报！');
                    return;
                }
                if(this.invalidateLocString.indexOf(this.option.formData.consigneeProvince) !== -1) {
                    this.$message.warn('该地区尚未开通配送服务，请切换其他地区选择！');
                    return;
                }
                this.$utils.showLoading();
                if (this.option.formData.status === 'Improved' || this.option.formData.status === 'Refused' || this.option.formData.status === 'QwRefused') {
                    this.option.formData.status = 'New';
                }
                const source = this.pageParam.source
                let submitData = this.option.formData
                let url = '/marketactivity/link/interListLineNew/upsert'
                if (source === 'oldCustomerItem' || source === 'accountApply') {
                    delete this.option.formData.source;
                    delete this.option.formData.loyaltyLevel;
                    delete this.option.formData.subAcctType;
                    url = '/marketactivity/link/interListLineNew/batchAdd'
                    submitData = [this.option.formData]
                }
                const data = await this.$http.post(this.$env.appURL + url, submitData, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('完善嘉宾信息失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    if (source === 'oldCustomerItem' || source === 'accountApply') {
                        if (data.result) {
                            this.$taro.showModal({
                                title: '提示',
                                content: '添加嘉宾名单失败！' + data.result,
                                showCancel: false,
                                success: async (res) => {
                                    if (res.confirm) {
                                        if (source === 'oldCustomerItem') {
                                            this.$nav.back(null, 2)
                                        } else if (source === 'accountApply') {
                                            this.$nav.back(null, 3)
                                        }
                                    }
                                }
                            });
                            return;
                        }
                    }
                    this.$utils.showAlert('提交成功！', {icon: 'none'});
                    if (source === 'oldCustomerItem') {
                        this.$nav.back(null, 2)
                    } else if (source === 'accountApply') {
                        this.$nav.back(null, 3)
                    } else {
                        this.$nav.back();
                    }

                }
            },
            /**
             * 选择所属终端
             * <AUTHOR>
             * @date 2021-09-13
             * */
            async pickTerminal() {
                this.terminalOption.option.param['attr2'] = this.pageParam.salesCityId;
                const terminalData = await this.$object(this.terminalOption, {multiple: false, pageTitle: "请选择所属终端"});
                if (terminalData.acctType === 'Terminal') {
                    this.$set(this.option.formData, 'terminalId', terminalData.id);
                    this.$set(this.option.formData, 'terminal', terminalData.acctName);
                    this.$set(this.option.formData, 'terminalCode', terminalData.acctCode);
                } else if (terminalData.acctType === 'Distributor') {
                    this.$set(this.option.formData, 'terminalId', terminalData.id);
                    this.$set(this.option.formData, 'terminal', terminalData.billTitle);
                    this.$set(this.option.formData, 'terminalCode', terminalData.acctCode);
                }
            },
            /**
             * 选择所属经销商
             * <AUTHOR>
             * @date 2021-09-13
             * */
            async pickDealer() {
                this.dealerOption.option.param['attr2'] = this.pageParam.salesCityId;
                const dealerData = await this.$object(this.dealerOption, {multiple: false, pageTitle: "请选择所属经销商"});
                this.$set(this.option.formData, 'dealerId', dealerData.id);
                this.$set(this.option.formData, 'dealer', dealerData.billTitle);
                this.$set(this.option.formData, 'dealerCode', dealerData.acctCode);
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/11/08
             * @methods: setDefaultProd
             * @para:
             * @description: 当名单头prodPick不为Y时默认赋值产品
             **/
            async setDefaultProd(obj) {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListProd/queryValidProd',
                    {
                        mcInterListId: this.pageParam.mcInterListId,
                        attr1: obj.accntChannelId,     // 消费者id，筛选标签用
                        // typeLevel: obj.subAcctType,
                        sort: 'created',
                        filtersRaw: [{
                            id: 'tnterProdType',
                            value: 'SubmitProducts',
                            property: 'tnterProdType',
                            operator: '='
                        }]
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError('产品查询失败！' + response.result);
                        }
                    });
                if (data.success) {
                    let ret = {};
                    const defaultProd = data.rows.find((item) => item.defaultFlag === 'Y');
                    if (defaultProd) {
                        ret = defaultProd;
                    } else {
                        ret = data.rows[0];
                    }
                    if (ret) {
                        if (!this.option.formData.prodId) {
                            this.$set(this.option.formData, 'prodId', ret.prodId);
                            this.$set(this.option.formData, 'prodName', ret.prodShowName);
                            this.$set(this.option.formData, 'attr1', ret.id);
                        }
                    } else {
                        this.$message.error('未查到符合条件的产品，请联系管理员!');
                    }
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/12/12
             * @methods: getConsumerData
             * @para:
             * @description: 根据消费者id查询消费者
             **/
            async getConsumerData(consumerId) {
                const data = await this.$http.post(this.$env.appURL + '/link/sendDmp/send',
                    {
                        dmpUrl: '/link/cdcPubConsumer/queryById',
                        id: consumerId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError('查询消费者！' + response.result);
                        }
                    });
                if (data.success) {
                    this.listTagParam = {
                        companyId: data.result.belongToCompanyId,
                        prodTag: data.result.vipNumber
                    };
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/12/12
             * @methods: setDefaultListTagProd
             * @para:
             * @description: 名单那提报规则配置了产品时的赋值逻辑
             **/
            async setDefaultListTagProd(obj) {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/ruleTagProd/queryByExamplePage',
                    {
                        sort: 'created',
                        filtersRaw: [
                            {id: 'companyId', property: 'companyId', value: this.listTagParam.companyId, operator: '='},
                            {id: 'tag', property: 'tag', value: this.listTagParam.prodTag, operator: '='},
                            {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}
                        ]
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError('产品查询失败！' + response.result);
                        }
                    });
                if (data.success) {
                    let ret = {};
                    if (data.rows[0]) {
                        ret = data.rows[0];
                    } else {
                        console.log('未查询到符合条件的数据，请检查！')
                    }
                    if (!this.option.formData.prodId) {
                        this.$set(this.option.formData, 'prodId', ret.prodId);
                        this.$set(this.option.formData, 'prodName', ret.prodName);
                        this.$set(this.option.formData, 'attr1', ret.id);
                    }
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/03/27
             * @methods: getLocation
             * @para:
             * @description: 获取定位信息
             **/
            async getLocation() {
                if (!this.editable) {
                    return
                }
                this.$utils.showLoading()
                const addressInfo = await this.$locations.getAddress();
                this.$utils.hideLoading();
                await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
            }
        }
    }
</script>

<style lang="scss">
    .activity-detail-add-page {
        .mb40{
            margin-bottom: 40px;
        }
        .blank-short {
            width: 100%;
            height: 80px;
            background: #F2F2F2;
        }
        .blank {
            width: 100%;
            height: 352px;
            background: #F2F2F2;
        }

        .zero-view {
            width: 100%;
            height: 30px;
        }

        .form-column {
            margin: auto;
            border-radius: 16px;
            padding: 40px 28px 4px 28px;
            background: white;
            font-family: PingFangSC-Regular;

            .item-input {
                line-height: 30px;
                margin-bottom: 30px;
                width: 100%;
                display: flex;

                .label {
                    align-self: flex-start;
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                }

                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                    text-align: right;
                }
            }

            .item-textarea {
                line-height: 30px;
                width: 100%;
                margin-bottom: 30px;

                .label {
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                    margin-bottom: 18px;
                }

                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                }
            }
        }

        .act-rule {
            width: 87%;
            border-radius: 16px;
            margin: auto;
            background: white;
            letter-spacing: 0;
            font-size: 28px;
            padding: 32px 24px;
            color: #262626;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .act-rule-title {
                letter-spacing: 0;
                font-size: 28px;
            }

            .view-btn {
                color: #2F69F8;
            }
        }

        .act-rule-con {
            background-color: #d9d9d9;
            padding: 24px;

            .zero-view {
                width: 100%;
                height: 30px;
            }

            .act-rules {
                /*width: 87%;*/
                max-height: 500px;
                overflow: scroll;
                margin: auto;
                border-radius: 16px;
                background: white;
                letter-spacing: 0;
                font-size: 28px;
                padding: 32px 24px;
                color: #262626;

                .act-rules-title {
                    border-bottom: 2px solid #f2f2f2;
                    letter-spacing: 0;
                    font-size: 28px;
                    padding: 0 0 20px;
                    margin-bottom: 10px;
                }

                .rule-item {
                    width: 100%;
                    line-height: 50px;
                }
            }
        }

        .custom-tab {
            height: 100px;
            display: flex;
            align-items: center;
            width: 93%;
            margin: 0 auto;

            .tab-btn {
                width: 140px;
                height: 50px;
                line-height: 50px;
                text-align: center;
                font-size: 24px;
                background-color: white;
                color: #2b2b2b;
                border-radius: 25px;
                margin-right: 40px;
            }

            .tab-btn-active {
                background-color: #1F74FF;
                color: white;
            }
        }

        .consumer-data {
            display: flex;
            flex-wrap: wrap;
            justify-content: space-between;
            width: 93%;
            margin: 0 auto 40px;
            font-size: 24px;
            color: #262626;

            .consumer-data-item {
                width: 30%;
                background-color: white;
                border-radius: 20px;
                height: 160px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                margin-bottom: 20px;
                line-height: 40px;
                text-align: center;

                .number {
                    font-weight: bold;
                }
            }
        }

        .data-name {
            color: #262626;
        }

        .button-bottom {
            width: 100%;
            display: flex;
            position: fixed;
            bottom: 32px;
            left: 0;
            z-index: 999;
        }

        .text-bottom {
            width: 100%;
            height: 32px;
            font-size: 24px;
            padding: 12px;
            color: #8C8C8C;
            display: flex;
            background: #f2f2f2;
            position: fixed;
            bottom: 0;
            left: 0;
            z-index: 999;

        }
    }

</style>
