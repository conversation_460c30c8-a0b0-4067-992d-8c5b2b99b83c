<template>
  <link-page class="log-ditail-page">
    <scroll-view scroll-y="true" class="scroll-view">
      <view v-for="(ite,i) in logList" :key="i" class="info-card">
        <view class="card-left">
            <view :class="['line', i&&'col-g']"></view>
            <view :class="['circle',!i&&'bigger']"></view>
            <view class="line col-g"></view>
        </view>
        <view :class="[i===0&&'c-gray','card-right']">
            {{ ite.content||'-' }}
            <view class="log-time">
                {{ ite.time }}
            </view>
        </view>
      </view>
      <view v-if="!logList.length" class="no-inf">暂无物流信息</view>
    </scroll-view>
  </link-page>
</template>

  <script>
export default {
  name: "log-ditail-page",
  data () {
    const id = this.pageParam.id;
    const type = this.pageParam.type;
    return {
      logList: [],
      id,
      type,
    }
  },
  created(){
    this.queryLog()
  },
  methods: {
    async queryLog () {
      this.$utils.showLoading();
      const data = await this.$http.post(this.$env.appURL + '/order/link/express/query', { no: this.id, type: this.type }, {
        autoHandleError: false,
        handleFailed: (response) => {
          this.$utils.hideLoading();
          this.$showError('物流数据查询失败！' + response.result);
        }
      });
      if (data.success) {
        this.$utils.hideLoading();
        this.logList = data.result&& data.result.list||[];
        console.log('查物流数据成功', this.logList);
      }
    },
  }
}
  </script>

  <style lang="scss">
.log-ditail-page {
    padding:28px;
    .info-card{
        display: flex;
        .card-left{
            width: 58px;
            display: flex;
            flex-direction: column;
            align-items: center;
            .circle{
                width: 12px;
                height: 12px;
                border-radius: 50%;
                background: gray;
                &.bigger{
                    width: 18px;
                    height: 18px;
                }
            }
            .line{
                flex: 1;
                width: 2px;
                // margin-left: 6px;
                &.col-g{
                    background: #b8b5b5;
                }
            }
        }
        .card-right{
            font-size: 28px;
            color: #aaa;
            flex: 1;
            padding: 12px 0;
            &.c-gray{
                color:#666666
            }
            .log-time{
                font-size: 24px;
            }
        }
    }
    .no-inf{
        font-size: 24px;
        text-align: center;
        color:#666666
    }
}
</style>
