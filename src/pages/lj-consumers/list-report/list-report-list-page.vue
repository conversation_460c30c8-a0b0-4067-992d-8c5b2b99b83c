<template>
    <link-page class="list-report-list-page">
        <lnk-taps :taps="statusOptions" v-model="visitStatusActive" @switchTab="onTap"></lnk-taps>
        <view class="blank"></view>
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'活动名称/活动对接人'}}">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item"
                      style="padding: 20px 0 20px 12px;" @tap="gotoItem(data)">
                    <view slot="note">
                        <view class="media-list">
                            <view class="media-top">
                                <view class="num-view">
                                    <view class="num">{{ data.interListName || data.activityName }}</view>
                                </view>
                                <status-button>{{ data.status | lov('LIST_STATUS') }}</status-button>
                            </view>
                        </view>
                        <view class="content-dead-time">
                            <view class="lable-name">
                                提报开始时间
                            </view>
                            <view class="time">
                                {{ data.startTime }}
                            </view>
                        </view>
                        <view class="content-dead-time">
                            <view class="lable-name">
                                提报结束时间
                            </view>
                            <view class="time">
                                {{ data.endTime }}
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="lable-name">名单下发人</view>
                            <view class="name">{{ data.actListOwner }}</view>
                        </view>
                        <view class="content-middle">
                            <view class="lable-name">权益类型</view>
                            <view class="name">{{ data.listRewardType | lov('LIST_REWARD_TYPE') }}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import StatusButton from "../../lzlj/components/status-button";
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    export default {
        name: "activity-list-page",
        data() {
        const pageOauthList = this.pageParam.secMenus || [{securityMode: 'MY_POSTN_ONLY', name: '我的数据'}];
        const userInfo = this.$taro.getStorageSync('token').result;
        let pageOauth = 'MY_POSTN_ONLY';
        let pageOauthName = '我的数据';
        if (pageOauthList.length > 0) {
            pageOauth = pageOauthList[0].securityMode;
            pageOauthName = pageOauthList[0].name;
        }
            return {
                pageOauthList,
                pageOauth,
                pageOauthName,
                statusOptions: [
                    {
                        defaultValue: "N",
                        id: "222323332756804060",
                        name: "待提报",
                        seq: "1",
                        type: "status",
                        val: "[ForSubmitted,Refused,Submitted,Confirm]"
                    },
                    {
                        defaultValue: "N",
                        id: "222323332756804060",
                        name: "已结束",
                        seq: "2",
                        type: "status",
                        val: "[Approved,EndSubmitted]"
                    }
                ],
                visitStatusActive: {},
                userInfo,
                autoList: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/marketactivity/link/interListNew/queryByLangChaoMpPage',
                    },
                    loadOnStart: false,
                    searchFields: ['activityName','actListOwner', 'interListName'],
                    param: {
                        // oauth: this.pageOauth||pageOauth,
                        notStandardOauth: this.pageOauth||pageOauth,
                        filtersRaw: [
                            {id: 'status', property: 'status', value: '[ForSubmitted,Refused,Submitted,Confirm]', operator: 'IN'},
                            {id: 'listRewardType', property: 'listRewardType', value: '[Liqueur,Crowd]', operator: 'IN'}
                        ]
                    },
                    sort: 'created',
                    sortOptions: [
                        {label: '创建时间', field: 'created', desc: true}
                    ],
                    filterOption: [
                        {
                            label: '活动类型', field: 'activityType', type: 'select', data: []
                        }
                    ],
                    slots: {
                        searchRight: () => (
                            <view class="filter-type-item" style="max-width: 224rpx;height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 30rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;" onTap={this.chooseOauthData}>{this.pageOauthName}<link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/></view>
                        )
                    }
                })
            }
        },
        components: {LnkTaps, StatusButton},
        async created() {
            this.visitStatusActive = this.statusOptions[0];
            await this.fetchFilterData()
            await this.autoList.methods.reload();
        },
        methods: {
             /**
             * @createdBy 曾宇
             * @date 2023/4/11
             * @methods: chooseOauthData
             * @description: 选择页面安全性
             **/
            chooseOauthData() {
                this.$actionSheet(() => (
                    <link-action-sheet title="请选择数据范围" onCancel={() => {}}>
                        {this.pageOauthList.map((item) => {return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>})}
                    </link-action-sheet>
                ));
            },
            /**
             * @createdBy 曾宇
             * @date 2023/4/11
             * @methods: pageOauthChange
             * @para: oauth 安全性
             * @description: 页面安全性切换
             **/
            pageOauthChange(oauth) {
                this.$utils.showLoading();
                this.autoList.list = [];
                this.pageOauth = oauth.securityMode;
                this.pageOauthName = oauth.name;
                this.autoList.option.param.notStandardOauth = oauth.securityMode;
                this.autoList.methods.reload();
                this.$utils.hideLoading();
            },
            /**
             * @desc 监听返回
             * <AUTHOR>
             * @date 2022/4/1 11:19
             **/
            async onBack() {
                await this.autoList.methods.reload();
            },
            /**
             * @desc 切换tab页签
             * <AUTHOR>
             * @date 2022/4/13 10:33
             **/
            async onTap() {
                this.autoList.option.param.filtersRaw.forEach((item)=> {
                    if (item.property === 'status') {
                        item.value = this.visitStatusActive.val;
                    }
                });
                await this.autoList.methods.reload();
            },
            /**
             * @desc 跳转详情
             * <AUTHOR>
             * @date 2022/4/13 10:33
             **/
            gotoItem(data) {
                // 权益类型为圈选
                if(data.listRewardType === 'Crowd') {
                    this.$nav.push('/pages/lj-consumers/list-report/list-report-detail-circle-page', {
                        data: data,
                        "pageOauth":this.pageOauth,
                        source: 'list'
                    })
                } else {
                    // 权益类型为浪潮小酒
                    this.$nav.push('/pages/lj-consumers/list-report/list-report-detail-page', {
                        data: data,
                        "pageOauth":this.pageOauth,
                        source: 'list'
                    })
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/9/23
             * @methods fetchFilterData
             * @para
             * @description 获取筛选数据
             */
            async fetchFilterData() {
                const param = {
                    pageFlag: true,
                    rows: 100,
                    page: 1,
                    filtersRaw: [
                        {id: 'companyId', property: 'companyId', value: this.userInfo.coreOrganizationTile.l3Id, operator: '='}
                    ]
                }
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/actCostMap/queryByExamplePage', param, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询筛选数据失败：${response.result}`);
                    }
                });
                const filterOptionData = []
                const result = data.rows;
                result.forEach((item, index) => {
                    const obj = {name: item.activityTypeName, val: item.activityType}
                    filterOptionData.push(obj)
                })
                this.autoList.option.filterOption[0].data = filterOptionData
            }
        }
    }
</script>

<style lang="scss">
    .list-report-list-page {
        .blank {
            width: 100%;
            height: 96px;
            background: #F2F2F2;
        }
        .perform-case-list-item {
            background: #FFFFFF;
            margin: 24px;
            border-radius: 16px;
        }

        .media-list {
            line-height: 56px;
            height: 56px;
            .media-top {
                width: 100%;
                @include flex-start-center;
                @include space-between;

                .num-view {
                    line-height: 56px;
                    width: 75%;
                    overflow-x: auto;
                    display: flex;
                    .num {
                        flex-shrink: 0;
                        font-family: PingFangSC-Semibold;
                        font-size: 32px;
                        color: #262626;
                        //letter-spacing: 0;
                        line-height: 56px;
                    }
                }

                .status-view {
                    min-width: 120px;
                    height: 36px;
                    line-height: 36px;
                    text-align: center;
                    color: #ffffff;
                    background: #2F69F8;
                    box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                    border-radius: 8px;
                    font-size: 20px;
                    margin-right: 8px;
                    -webkit-transform: skewX(-30deg);
                    -ms-transform: skewX(-30deg);
                    transform: skewX(-30deg);

                    &-refuse {
                        background: #FF5A5A !important;
                        box-shadow: 0 3px 4px 0 rgba(255, 90, 90, 0.35) !important;
                    }

                    .status {
                        transform: skewX(30deg);
                        font-size: 20px;
                        color: #FFFFFF;
                        letter-spacing: 2px;
                        text-align: center;
                        line-height: 36px;
                    }
                }
            }
        }

        .content-dead-time {
            @include flex-start-center;
            font-family: PingFangSC-Regular;
            font-size: 28px;
            letter-spacing: 0;
            line-height: 52px;
            height: 52px;

            .lable-name {
                color: #8C8C8C;
                margin-right: 10px;
                min-width: 180px;
            }

            .time {
                color: #000;
            }
        }

        .content-middle {
            width: 100%;
            @include flex-start-center;
            font-size: 28px;
            letter-spacing: 0;
            font-family: PingFangSC-Regular;
            line-height: 52px;

            .lable-name {
                color: #8C8C8C;
                margin-right: 10px;
                min-width: 180px;
            }

            .name {
                color: #262626;
            }
        }
    }
</style>
