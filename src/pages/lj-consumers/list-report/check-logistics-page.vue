<template>
    <link-page class="check-logistics-page">
        <link-form hideSaveButton hideEditButton>
            <line-title title="提报订单"/>
            <view v-if="orderFlag === 'N' && !this.pageParam.interListLine.orderId" class="check-logistics-old">
                <link-form-item>
                    <item slot="custom" title="提报订单ID" :arrow="false">
                        {{normalLogisticsOrderData.id}}
                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(normalLogisticsOrderData.id)"/>
                    </item>
                </link-form-item>
                <link-form-item>
                    <item slot="custom" title="物流公司" :arrow="false">
                        {{normalLogisticsData.logCompany}}
                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(normalLogisticsData.logCompany)"/>
                    </item>
                </link-form-item>
                <link-form-item>
                    <item slot="custom" title="物流编号" :arrow="false">
                        {{normalLogisticsData.logCode}}
                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(normalLogisticsData.logCode)"/>
                    </item>
                </link-form-item>
                <link-form-item>
                    <item slot="custom" title="物流状态" :arrow="false">
                        {{normalLogisticsData.status | lov('ORDER_STATUS')}}
                    </item>
                </link-form-item>
                <link-form-item v-show="!$utils.isMoreThanAYearOld(normalLogisticsData.created)" @tap='jumpDetail(normalLogisticsData.logCode,normalLogisticsData.logCompany)'>
                    <link-button style="width:100%" block shape="round" mode="stroke">查看物流</link-button>
                </link-form-item>
            </view>
            <view v-if="orderFlag === 'Y' || (orderFlag === 'N' && !!this.pageParam.interListLine.orderId)" class="check-logistics-old">
                <link-form-item>
                    <item slot="custom" title="提报订单ID" :arrow="false">
                        {{normalLogisticsOrderData.id}}
                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(normalLogisticsOrderData.id)"/>
                    </item>
                </link-form-item>
                <link-form-item>
                    <item slot="custom" title="物流公司" :arrow="false">
                        {{normalLogisticsOrderData.logisticsCompany | lov('LOGISTICS_TYPE')}}
                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(normalLogisticsOrderData.logisticsCompany)"/>
                    </item>
                </link-form-item>
                <link-form-item>
                    <item slot="custom" title="物流编号" :arrow="false">
                        {{normalLogisticsOrderData.logisticsNo}}
                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(normalLogisticsOrderData.logisticsNo)"/>
                    </item>
                </link-form-item>
                <link-form-item>
                    <item slot="custom" title="物流状态" :arrow="false">
                        {{normalLogisticsOrderData.status | lov('ORDER_STATUS')}}
                    </item>
                </link-form-item>
                <link-form-item v-show="!$utils.isMoreThanAYearOld(normalLogisticsOrderData.created)" @tap='jumpDetail(normalLogisticsOrderData.logisticsNo,normalLogisticsOrderData.logisticsCompany)'>
                    <link-button style="width:100%" block shape="round" mode="stroke">查看物流</link-button>
                </link-form-item>
            </view>
            <line-title title="补发订单"/>
<!--            <view class="check-logistics-old" v-if="showFlag && orderFlag === 'N'">-->
<!--                <link-form-item>-->
<!--                    <item slot="custom" title="补发物流公司" :arrow="false">-->
<!--                        {{reissueLogisticsData.logCompany}}-->
<!--                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(reissueLogisticsData.logCompany)"/>-->
<!--                    </item>-->
<!--                </link-form-item>-->
<!--                <link-form-item>-->
<!--                    <item slot="custom" title="补发物流编号" :arrow="false">-->
<!--                        {{reissueLogisticsData.logCode}}-->
<!--                        <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(reissueLogisticsData.logCode)"/>-->
<!--                    </item>-->
<!--                </link-form-item>-->
<!--                <link-form-item>-->
<!--                    <item slot="custom" title="补发物流状态" :arrow="false">-->
<!--                        {{reissueLogisticsData.status | lov('ORDER_STATUS')}}-->
<!--                    </item>-->
<!--                </link-form-item>-->
<!--            </view>-->
            <link-auto-list :option="reissueLogisticsDataOption" hideCreateButton v-if="showFlag">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false">
                        <view slot="note" class="reissue-item">
                            <view class="list-item-data">
                                <view class="list-item-data__left">
                                    <view class="title">补发订单ID</view>
                                </view>
                                <view class="list-item-data__right">
                                    {{data.id}}
                                    <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(data.id)"/>
                                </view>
                            </view>
                            <view class="list-item-data">
                                <view class="list-item-data__left">
                                    <view class="title">补发物流公司</view>
                                </view>
                                <view class="list-item-data__right">
                                    {{data.logisticsCompany | lov('LOGISTICS_TYPE')}}
                                    <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(data.logisticsCompany)"/>
                                </view>
                            </view>
                            <view class="list-item-data">
                                <view class="list-item-data__left">
                                    <view class="title">补发物流编号</view>
                                </view>
                                <view class="list-item-data__right">
                                    {{data.logisticsNo}}
                                    <link-button mode="stroke" size="mini" label="复制" icon="icon-yijianbaobei1" class="button" @tap="copyData(data.logisticsNo)"/>
                                </view>
                            </view>
                            <view class="list-item-data">
                                <view class="list-item-data__left">
                                    <view class="title">补发物流状态</view>
                                </view>
                                <view class="list-item-data__right">
                                    {{data.status | lov('ORDER_STATUS')}}
                                </view>
                            </view>
                            <view class="list-item-data" v-show="!$utils.isMoreThanAYearOld(data.created)">
                                <link-button @tap='jumpDetail(data.logisticsNo,data.logisticsCompany)'
                                style="width:100%" block shape="round" mode="stroke">查看物流</link-button>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
            <!--暂无数据-->
            <view v-if="!showFlag" style="height: 160px; display: flex; align-items: center; justify-content: center; color: #999">暂无数据</view>
        </link-form>
    </link-page>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title";
export default {
    name: "check-logistics-page",
    components: {LineTitle},
    data() {
        return {
            reissueLogisticsData: {
                logCompany: '',
                logCode: ''
            },
            orderFlag: '',
            showFlag: false,
            normalLogisticsOrderData: {
                id: '',
                logisticsCompany: '',
                logisticsNo: ''
            },
            normalLogisticsData: {
                logCompany: '',
                logCode: ''
            },           // 物流数据
            reissueLogisticsDataOption: new this.AutoList(this, { //嘉宾名单
                loadOnStart: false,
                url: {
                    queryByExamplePage: this.$env.appURL + '/link/saleorder/queryReOrderPage'
                },
                param: {
                    id: '',
                    oauth: 'ALL',
                    sort: 'status',
                    filtersRaw: [
                        {id: 'orderChildType', property: 'orderChildType', value: 'ReSubmitOrder', operator: '='}
                    ]
                },
                pageSize: 10,
                sortOptions: null,
                hooks: {
                    beforeLoad(option) {
                        if (this.reissueLogisticsData.orderId) {
                            option.param.id = this.reissueLogisticsData.orderId;
                        } else {
                            return Promise.reject('订单ID为空，请检查数据');
                        }
                    }
                }
            })
        }
    },
    async created() {
        this.orderFlag = this.pageParam.orderFlag;
        if (this.orderFlag === 'N') {
            await this.queryLogisticsData(this.pageParam.interListLine.id);
            if(!!this.pageParam.interListLine.orderId) {
                await this.initQuery();
            }
        } else {
            await this.initQuery();
        }
    },
    methods:{
          /**
         * @desc 跳转物流详情
         * <AUTHOR>
         * @date 2025-03-20
         **/
        jumpDetail(id,type){
            this.$nav.push('/pages/lj-consumers/list-report/logistics-detail-page',{id,type});
        },
        /**
         * @desc 初始化查询数据
         * <AUTHOR>
         * @date 2024-07-17
         **/
        async initQuery() {
            await this.queryNewOrderData(this.pageParam.interListLine.orderId);
            if (this.pageParam.interListLine.logStatus === 'ReissueSuccess' || this.pageParam.interListLine.logStatus === 'Reissued') {
                this.showFlag = true;
                await this.queryReissueLogisticsData(this.pageParam.interListLine.id);
                this.reissueLogisticsDataOption.methods.reload();
            }
        },
        /**
         * @desc 查询提报订单数据
         * <AUTHOR>
         * @date 2022/12/6 10:06
         **/
        async queryNewOrderData (orderId) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/link/saleorder/queryById', {id: orderId}, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('物流数据查询失败！' + response.result);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.normalLogisticsOrderData = data.result;
                console.log('查询提报订单数据normalLogisticsOrderData', this.normalLogisticsOrderData);
            }
        },
        /**
         * @desc 复制数据
         * <AUTHOR>
         * @date 2022/4/22 00:38
         **/
        copyData (data) {
            wx.setClipboardData({
                data: data,
                success: function () {
                    // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
                    wx.showToast({
                        title: '复制成功',
                        duration: 3000
                    });
                    wx.getClipboardData({
                        success: function (res) {
                        }
                    })
                }
            })
        },
        /**
         * @desc 查询物流数据
         * <AUTHOR>
         * @date 2022/4/6 11:21
         **/
        async queryLogisticsData (headId) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/logistics/queryByExamplePage',
                {
                    filtersRaw: [
                        {id: 'headId', property: 'headId', value: headId, operator: '='},
                        {id: 'type', property: 'type', value: 'NormalDelivery'}
                    ],
                    pageFlag: true,
                    rows: 1,
                    page: 1
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('物流数据查询失败！' + response.result);
                    }
                });
            if (data.success) {
                this.$utils.hideLoading();
                this.normalLogisticsData = data.rows[0];
                console.log('查询物流数据normalLogisticsData', this.normalLogisticsData);
            }
            if (this.pageParam.interListLine.logStatus === 'ReissueSuccess' || this.pageParam.interListLine.logStatus === 'Reissued') {
                this.showFlag = true;
                await this.queryReissueLogisticsData(headId);
            }
        },
        /**
         * @desc 查询补发物流数据
         * <AUTHOR>
         * @date 2022/4/6 17:10
         **/
        async queryReissueLogisticsData (headId) {
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/logistics/queryByExamplePage',
                {
                    filtersRaw: [
                        {id: 'headId', property: 'headId', value: headId, operator: '='},
                        {id: 'type', property: 'type', value: 'ReDelivery'},
                        {id: 'status', property: 'status', value: this.pageParam.interListLine.logStatus}
                    ],
                    pageFlag: true,
                    rows: 1,
                    page: 1,
                    order: 'DESC',
                    sort: 'created'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('物流数据查询失败！' + response.result);
                    }
                });
            if (data.success) {
                this.$utils.hideLoading();
                this.reissueLogisticsData = data.rows[0];
                console.log('查询补发物流数据reissueLogisticsData', this.reissueLogisticsData);
            }
        }
    }
}
</script>

<style lang="scss">
.check-logistics-page {
    .button{
        margin-left: 20px;
    }
    /*deep*/.line-title{
    margin-bottom: 20px;
            }
    .check-logistics-old{
        /*deep*/ .link-item-body-left{
        flex: 0.35 !important;
    }
    }
    .reissue-item{
        .list-item-data{
            @include flex-center-center();
            @include space-between();
            border-bottom: 1px solid #eff1f3;
            padding: 28px 0;
            .title{
                display: flex;
                color: #333333;
                font-size: 28px;
            }
            &__left{

            }
            &__right {
                font-size: 28px;
            }
        }
    }
}
</style>
