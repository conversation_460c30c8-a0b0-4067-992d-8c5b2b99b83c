<template>
    <link-page class="list-report-detail-page">
        <view class="zero-view"></view>
        <view style="width: 94%;margin-left: 3%">
            <view class="form-column">
                <view v-for="item in details" :key="item.label" :class="item.type?'item-textarea':'item-input' ">
                    <view class="label">{{ item.label }}</view>
                    <view class="text">{{ item.text }}</view>
                </view>
            </view>
        </view>
        <view class="zero-view"></view>
        <view class="act-rule">
            <view class="act-rule-title">活动参与条件</view>
            <view class="view-btn" @tap="ruleDlg = true">查看</view>
        </view>
        <link-dialog v-model="ruleDlg"
                     position="poster">
            <view class="act-rule-con">
                <view class="act-rules">
                    <view class="act-rules-title">总部规则</view>
                    <view v-if="marketRules.length > 0">
                        <view class="rule-item" v-for="(item, index) in marketRules">
                            <view v-if="item.showContent">
                                {{item.fieldName}}{{item.judgeName}}{{item.showContent}}
                            </view>
                            <view v-else>
                                {{item.fieldName}}{{item.judgeName}}{{item.judgeContent}}
                            </view>
                        </view>
                    </view>
                    <view v-else class="rule-item">无</view>
                </view>
                <view class="zero-view"></view>
                <view class="act-rules">
                    <view class="act-rules-title">名单提报规则</view>
                    <view v-if="listRules.length > 0">
                        <view class="rule-item" v-for="(item, index) in listRules">
                            <view v-if="item.showContent">
                                {{item.fieldName}}{{item.judgeName}}{{item.showContent}}
                            </view>
                            <view v-else>
                                {{item.fieldName}}{{item.judgeName}}{{item.judgeContent}}
                            </view>
                        </view>
                    </view>
                    <view v-else class="rule-item">无</view>
                </view>
            </view>
        </link-dialog>
        <view class="zero-view"></view>
        <list class="guest-list">
            <view class="title">
                <view class="left">嘉宾名单</view>
                <!-- ”审批中“、”审批通过“、“审批拒绝“、””提报结束“的时候不可以操作【添加】按钮。-->
<!--                <view class="right" @tap="addGuest" v-if="activityListItem.status !== 'Canceled' && activityListItem.status !== 'Submitted' && activityListItem.status !== 'Approved' && activityListItem.status !== 'EndSubmitted'">-->
<!--                提报开始时间<当前时间<=提报结束时间，且名单头状态为“待提报”或“审批拒绝”时，才显示-->
                <view class="pricolor" @tap="jumpTolastPage" v-if="lastPage&&lastPage>=2">
                    跳转上次浏览第{{ lastPage }}页
                </view>
                <view class="right" v-show="LangChao25BtnShow" @tap="addGuest" v-if="addFlag && ['ForSubmitted', 'Refused'].includes(activityListItem.status)">
                    添加
                </view>
            </view>
            <view class="calcAll">
                <view >
                    合计：<text style="color: #8C8C8C"> {{ guestNum }} </text>
                </view>

                <view :class="{pricolor:isStoreChosen}" @tap="chooseClient">
                    所属客户<link-icon icon="mp-desc"/>
                </view>
                <!--                合计：{{ guestList.totalCount }}-->
            </view>
            <link-auto-list :option="guestList" hideCreateButton>
                <template slot-scope="{data,index}">
                    <link-swipe-action :key="data.id" style="width: 100%;">
                        <link-swipe-option slot="option" @tap="handleTerminalDelete(data,index)"
                            v-show="LangChao25BtnShow"
                            v-if="addFlag && ['ForSubmitted', 'Refused'].includes(activityListItem.status) && (data.status === 'New'|| data.status === 'Refused' || data.status === 'Improved' || data.status === 'QwRefused')">
                            删除
                        </link-swipe-option>
                        <item :key="index" :data="data" style="padding: 0 14px 0 0;margin-left: 14px;" @tap="gotoItem(data)">
                            <view slot="note" class="guest-list-item">
                                <view class="sort-num" :id="`a${data.id}`">{{ Number((data.page - 1) * 10 + (index + 1)) }}</view>
                                <view class="logo-img">
                                    <image class="media-list-logo" :src="data|headImgAccount(data)"/>
                                    <view style="width: 32px;height: 12px; margin-top: -10px;"><image style="width: 100%;height: 100%;" :src="data.certify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image></view>
                                    <view class="time" v-if="data.certify === 'certified' && !$utils.isEmpty(data.certifyTime) && data.certifyTime !== '9999-12-31 23:59:59'">{{ `${data.certifyTime}到期` }}</view>
                                </view>
                                <view class="guest-list-item-content">
                                    <view class="guest-list-item-content-row1">
                                        <view class="left">
                                            <view class="terminal-type" v-if="data.acctName">{{ data.acctName.length < 4? data.acctName :
                                                data.acctName.slice(0,2)+ '...' }}
                                            </view>
                                            <view class="terminal-name">{{ data.phone }}</view>
                                        </view>
                                        <view class="right">
                                            <status-button v-if="data.logStatus" type="other" >{{ data.logStatus | lov('LOGISTICS_STATUS') }}</status-button>
                                            <status-button v-else :type="data.status === 'Improved' ? 'invalid' : 'normal'">{{ data.status | lov('LIST_LINE_STATUS') }}</status-button>
                                        </view>
                                    </view>
                                    <view class="guest-list-item-content-row2">
                                        审核意见
                                        <view class="audit-opinion">{{ data.auditOpinion }}</view>
                                    </view>
                                    <view class="guest-list-item-content-row2">
                                        订单状态
                                        <view class="audit-opinion" :style="{'color':colorMap[data.orderStatus]||'gray'}" >{{ data.orderStatus | lov('ORDER_STATUS') }}</view>
                                    </view>

                                </view>
                            </view>
                        </item>
                        <item :arrow="false" class="touch-item padding0" >
                            <view class="fn24" style="display: inline-block;text-align: left;">
                                <text style="text-align: left">所属客户: </text>
                            </view>
                            <view class="touch-label scroll-x" :key="'a22d3'">
                                <view>{{ data.belongToStore }}</view>
                            </view>
                        </item>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </list>
        <view class="zero-view"></view>
        <link-sticky v-show="LangChao25BtnShow" v-if="addFlag && ['ForSubmitted', 'Refused'].includes(activityListItem.status)">
            <link-button block @tap="commitInterListLine">提交名单</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import {LovService} from "link-taro-component";
    import StatusButton from "../../lzlj/components/status-button";
    import TagInfo from "../account/components/tag-info/tag-info";
    import LnkTaps from "../../core/lnk-taps/lnk-taps";

    export default {
        name: "list-report-detail-page",
        data() {
            const pageOauth = this.pageParam.pageOauth || 'MY_POSTN_ONLY';
            console.log(this.pageParam.pageOauth,'this.pageParam.pageOauth',pageOauth);
            const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
            const filtersRawTemp = [{id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}];
            return {
                pageOauth,
                colorMap:{
                    Returning:'#FFA500',
                    // Receiving:'#FFA500',
                    Returned:'#d83931',
                    Arrive:'#245bdb',
                    Signed:'#00FF00',
                    Withdrawing:'#dc9b04',
                    Shipped:'#ccffcc',
                    Processing:'#ADD8E6',
                    // Delivery:'#808080',
                    Closed:'#006400',
                },
                statusList: [
                    {name: '全部', value: 'ALL', seq: 0, count: ''},
                    {name: '已认证', value: 'certified', seq: 1, count: ''},
                    {name: '未认证', value: 'uncertified', seq: 2, count: ''}
                ],
                statusListActive: {name: '全部', value: 'ALL', seq: 0},
                userInfo,
                filtersRawTemp,
                infoJampId: null, //从消息服务跳转过来的id
                order_status: null,   // 从消息服务跳转过来的状态
                card_post_id: null,   // 从消息服务跳转过来的职位id
                classifyList: [
                    {val: 'ALL', name: '全部', seq: '1', field: ''},
                    {val: 'certified', name: '已认证', seq: '2', field: 'certify'},
                    {val: 'uncertified', name: '未认证', seq: '3', field: 'certify'},
                    {val: 'type', name: 'K序列', seq: '4', field: 'type'},
                    {val: 'ACCT_MEMBER_LEVEL', name: 'V序列', seq: '5', field: 'loyaltyLevel'}
                ],
                classifyListActive: {val: 'ALL', name: '全部', seq: '1', field: ''},
                classifyItemListActive: {},
                classifyItemList: [],
                tagIdList: [],
                tagDialogFlag: false,                     // 标签筛选弹窗
                tagGroupList: [],
                isStoreChosen: false,
                LangChao25BtnShow: false,
                editPath: '/pages/lj-consumers/account/account-item-edit-page',
                isGuoJiao: false,     // 判断是否查询国窖系公司的分类分级值列表
                marketRules: [],      // 营销规则
                listRules: [],        // 名单提报规则
                logisticsStatusArr: [],
                lineStatusArr: [],
                activityListItem: {},//名单提报对象
                details: [],//名单提报头显示字段
                guestList: new this.AutoList(this, { //嘉宾名单
                    module: this.$env.appURL + '/marketactivity/link/interListLineNew',
                    exactSearchFields: [
                        {
                            field: 'acctName',
                            showValue: '姓名',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }, {
                            field: 'phone',
                            showValue: '手机号',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }
                    ],
                    loadOnStart: false,
                    param: {
                        oauth: pageOauth||this.pageOauth,
                        // postnId: this.userInfo.postnId,
                        sort: 'status',
                        filtersRaw: [
                            // {id: 'postnId', property: 'postnId', value: userInfo.postnId || this.userInfo.postnId},
                            {id: 'mcInterListId', property: 'mcInterListId', value: ''},
                            {id: 'status', property: 'status', value: '[Created, Imported]', operator: 'NOT IN'}
                        ]
                    },
                    filterOption: [
                    this.pageParam&&this.pageParam.data && {label: '名单状态', field: 'status', type: 'lov', lov: 'LIST_LINE_STATUS', lovOption: {parentType: 'LIST_REWARD_TYPE', parentVal: this.pageParam.data.listRewardType}, multiple: true},
                        {label: '嘉宾订单状态', field: 'orderStatus', type: 'lov', lov: 'ORDER_STATUS', multiple: true},
                        {label: '发运状态', field: 'logStatus', type: 'lov', lov: 'LOGISTICS_STATUS', multiple: true}
                    ].filter(e=>!!e),
                    pageSize: 10,
                    sortOptions: null,
                    scrollToTop: false,
                    hooks: {
                        beforeLoad(option) {
                            if (!!this.order_status) {
                                const filtersRaw = option.param.filtersRaw;
                                option.param.filtersRaw = filtersRaw.concat({id: 'orderStatus', property: 'orderStatus', value: this.order_status, operator: '='});
                                option.param.cardPostId = this.card_post_id;
                                if(option.param.oauth){ // 卡片不要安全性,有职位id
                                    option.param.oauth = "ALL";
                                }
                                option.url = this.$env.appURL + '/marketactivity/link/interListLineNew/queryQwCardMsgInterLinePage';
                            } else {
                                // 除了卡片跳转，正常进入须加安全性
                                if(!option.param.oauth){
                                    option.param.oauth = 'MY_POSTN_ONLY';
                                }
                            }
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                // 嘉宾订单状态
                                if (option.param.filtersRaw[i].property === 'orderStatus' && option.param.filtersRaw[i].value === 'NULL') {
                                    option.param.filtersRaw[i].value = '';
                                    option.param.filtersRaw[i].operator = 'IS NULL';
                                }
                                // 发运状态
                                if (option.param.filtersRaw[i].property === 'logStatus' && option.param.filtersRaw[i].value === 'NULL') {
                                    option.param.filtersRaw[i].value = '';
                                    option.param.filtersRaw[i].operator = 'IS NULL';
                                }
                            }this.tempParam = option.param;
                        },
                        afterLoad (option) {
                            this.queryListLinePageCount(this.tempParam);
                        }
                    }
                }),
                customerOption: new this.AutoList(this, { // 所属客户
                    module: this.$env.appURL + '/action/link/accnt',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryAccntPage'
                    },
                    param: {
                        postnId: '',
                        oauth: 'MY_POSTN_ONLY'
                    },
                    searchFields: ['acctName'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} className="select-box" arrow="false">
                                <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                                <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                                <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                            </item>)
                    },
                    hooks: {
                        beforeLoad (option) {
                            delete option.param.order;
                            delete option.param.sort;
                            option.param.postnId = this.userInfo.postnId;
                        }
                    }
                }),
                customerOptionNew: new this.AutoList(this, { // 所属客户
                module: this.$env.appURL + '/action/link/accnt',
                url: {
                    queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryInterListLineAccntPage'
                },
                param: {
                    // postnId: '',
                    oauth: pageOauth||this.pageOauth,
                },
                searchFields: ['acctName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} className="select-box" arrow="false">
                            <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                            <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                        </item>)
                },
                hooks: {
                    beforeLoad (option) {
                        delete option.param.order;
                        delete option.param.sort;
                        // option.param.postnId = this.userInfo.postnId;
                        option.param.interListId = this.activityListItem.id;
                        const lineStatusList = this.tempParam.filtersRaw.find(e=>e.property=='status'&&e.operator=='=');
                        const logStatusList = this.tempParam.filtersRaw.find(e=>e.property=='logStatus');
                        const orderStatusList = this.tempParam.filtersRaw.find(e=>e.property=='orderStatus');
                        // const interTouchType = this.tempParam.filtersRaw.find(e=>e.property=='interTouchType');
                        option.param.orderStatusList = orderStatusList&&orderStatusList.value.replace('[','').replace(']','').split(',')||[];
                        option.param.lineStatusList = lineStatusList&&lineStatusList.value.replace('[','').replace(']','').split(',')||[];
                        option.param.logStatusList = logStatusList&&logStatusList.value.replace('[','').replace(']','').split(',')||[];
                        // option.param.interTouchType =interTouchType&& interTouchType.value;
                        if(this.card_post_id){ // 卡片进来特殊处理
                            option.param.cardPostId = this.card_post_id;
                            option.param.oauth = 'ALL';
                        }
                    }
                }
            }),
                consumerOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.dmpURL + '/link/interTouch/queryMpPage'
                    },
                    param: {
                        queryType: '',
                        oauth: 'MY_POSTN',
                        mcActId: '',
                        filtersRaw: [
                            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                        ],
                    },
                    pageSize: 10,
                    hooks: {
                        async beforeLoad (option) {
                            option.param.mcActId = this.activityListItem.mcActId || null;
                            option.param.interListId = this.activityListItem.id;
                            if (option.param['filtersRaw'].length > 0) {
                                for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                    if (option.param.filtersRaw[i].property === 'queryType') {
                                        option.param.queryType = option.param.filtersRaw[i].value
                                        option.param.filtersRaw.splice(i, 1);
                                        break;
                                    }
                                }
                            }
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                if (option.param.filtersRaw[i].property === 'name') {
                                    option.param.filtersRaw[i].operator = 'like';
                                }
                            }
                        }
                    },
                    filterOption: [
                        {label: '消费者范围', field: 'queryType', type: 'lov', default:'NotReported',value:'NotReported', lov: 'QUERY_TYPE', multiple: false},
                    ],
                    sortOptions: null,
                    exactSearchFields: [
                        {
                            field: 'name',
                            showValue: '姓名',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }, {
                            field: 'phoneNumber',
                            showValue: '手机号',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }
                    ],
                    customClass: 'choose-consumer-list-page',
                    slots: {
                        other: () => (
                            <view>
                                {this.addConsumerFlag && <link-fab-button onTap={this.addConsumer}></link-fab-button>}
                                <tag-info show={this.tagDialogFlag} {...{on: {'update:show': val => this.tagDialogFlag = val}}} tagGroupList={this.tagGroupList} {...{on: {'choose': val => {this.chooseTagGroups(val)}}}}/>
                            </view>),
                        top: () => (<view style="position: relative;">
                                <view style="width: 100%;position: absolute;top: -88px;display: flex;justify-content: space-around;flex-wrap: nowrap;">
                                    {this.classifyList.map(tab => {
                                        return (
                                            <view onTap={()=>this.switchTab(tab)} style={tab.seq === this.classifyListActive.seq
                                                ? 'width: 25%;justify-content: center;align-items: center;flex-direction: column;color:#2f69f8; height: 46px;line-height: 46px; display: flex;'
                                                : 'width: 25%;justify-content: center;align-items: center;flex-direction: column; height: 46px;line-height: 46px; display: flex;'}>
                                                <view style="font-size: 14px;margin-left: 5px;display: flex;flex-direction: column;align-items: center;justify-content: center;">
                                                    <text>{tab.name}</text>
                                                    {tab.seq === this.classifyListActive.seq
                                                        ? <view style="height: 4px;width: 14px;border-radius: 8px 8px 0 0;background-color: #2f69f8;box-shadow: 0 1.5px 4px 0 rgba(47,105,248,0.63);margin-top: -4px;"></view>
                                                        : ''
                                                    }
                                                </view>
                                            </view>
                                        )})}
                                </view>
                                {
                                    this.classifyItemList.length > 0?
                                        <view>
                                            <scroll-view scroll-x="true">
                                                <view style="height: 36px; display: flex;align-items: center;">
                                                    {
                                                        this.classifyItemList.map(tabItem => {
                                                            return (
                                                                <view  onTap={() =>this.switchTabItem(tabItem)} style={tabItem.seq === this.classifyItemListActive.seq
                                                                    ? 'min-width: 85px;height: 28px;box-sizing: border-box;margin: 0 0 8px 12px;font-size:12px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 28px; font-weight: 400;border: 0.5px solid rgba(63,102,239,1);border-radius: 14px;'
                                                                    : 'min-width: 85px;height: 28px;box-sizing: border-box;margin: 0 0 8px 12px;font-size:12px;color: #333333;letter-spacing: 0;text-align: center;line-height: 28px; font-weight: 400;border: 0.5px solid rgba(221,221,221,1);border-radius: 14px;'}>
                                                                    { tabItem.name }
                                                                </view>
                                                            )
                                                        })
                                                    }
                                                </view>
                                            </scroll-view>
                                        </view> : ''
                                }
                            </view>
                        ),
                        // 标签筛选暂时注释
                        // <!-- <view onTap={() => {this.tagDialogFlag=true}} style={this.tagIdList.length>0
                        //      ? 'height: 20px;margin: 6px 2px;background: #EDF3FF;background: #EDF3FF;color: #2F69F8;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'
                        //      : 'margin: 7.5px 0;height: 25px; line-height: 25px;border-radius: 3px;padding: 0 10px;max-width: 62px;color: #333333;height: 20px;margin: 6px 2px;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'}>
                        //      标签<link-icon icon="mp-desc" style="width: 8px;height: 6px;color: #CCCCCC;margin-left: 4px;"/>
                        //  </view>
                        //  -->
                        filterGroup: () => (
                            <view style="flex: 1;overflow-x: hidden;">
                                <scroll-view style="width: 100%;">
                                    <view style="display: flex;white-space: nowrap;font-size: 24px;padding: 4px 12px;align-items: center;flex-wrap: nowrap;">
                                        <view onTap={() => this.chooseStoreData()} style={this.isStoreChosen
                                            ? 'width:82px; height: 20px;margin: 6px 2px;background: #EDF3FF;background: #EDF3FF;color: #2F69F8;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'
                                            : 'margin: 7.5px 0;height: 25px; line-height: 25px;border-radius: 3px;padding: 0 10px;max-width: 62px;color: #333333;height: 20px;margin: 6px 2px;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'}>
                                            所属客户<link-icon icon="mp-desc" style="width: 8px;height: 6px;color: #CCCCCC;margin-left: 4px;"/>
                                        </view>
                                        <view style="width: 1px;height: 16px;position: absolute;right: 2px;top: 12px;background-color: #CCCCCC;border: 1px solid rgba(204,204,204,1);"></view>
                                    </view>
                                </scroll-view>
                            </view>
                        )
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false" style="overflow: hidden; margin: 12px;">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view style="position: relative;-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%; padding: 16px 12px 12px 12px;-webkit-flex-direction: column;-ms-flex-direction: column;flex-direction: column;">
                                    <view style="height: 24px;font-family: PingFangSC-Semibold;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;display: flex;">
                                        <view style="overflow: hidden; max-width: 180px;text-overflow:ellipsis; white-space: nowrap;">{ data.name}</view>
                                        {data.certify ? <view style=" margin-left: 10px;width: 60px;height: 24px;"><image style="width: 100%;height: 100%;" src={data.certify === 'certified' ? this.$imageAssets.storeStatusVerifiedImage : this.$imageAssets.storeStatusUnverifiedImage}></image></view> : ''}
                                    </view>
                                    <view>
                                        <view style="overflow: hidden; max-width: 100%;text-overflow:ellipsis; white-space: nowrap;">
                                            {data.certify === 'certified' && !this.$utils.isEmpty(data.certifyTime) && data.certifyTime !== '9999-12-31 23:59:59'? <view style="font-weight: bold;font-size: 24rpx;color: #000000">{ data.certifyTime }认证到期</view>: ''}
                                        </view>
                                    </view>
                                    <view style="height: 18px;margin: 8px 0;display: flex;">
                                        <view style="min-width: 40px;padding: 0 8px;margin-right: 8px;background: #F0F5FF;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 18px;font-weight: 400;">{ LovService.filter(data.type, 'ACCT_SUB_TYPE') }</view>
                                        <view style="min-width: 40px;padding: 0 8px;margin-right: 8px;background: #F0F5FF;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 18px;font-weight: 400;">{ LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL') }</view>
                                        {data.impFlag === 'Y' ?  <view style="width: 56px;margin-right: 8px;background: #FFF1EB;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #FF461E;line-height: 18px;font-weight: 400;text-align: center;">重点客户</view> : '' }
                                    </view>
                                    <view style="display: flex;flex-direction: column;">
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">联系方式</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #317DF7;line-height: 22px;font-weight: 400;">{data.phoneNumber}</view>
                                        </view>
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">单位</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{data.companyName}</view>
                                        </view>
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">职务</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{data.position}</view>
                                        </view>
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">所属客户</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{data.belongToStore}</view>
                                        </view>
                                    </view>
                                </view>
                                <view style="position: absolute; right: -5px;top:0;">
                                    <view style="background: #2F69F8;min-width: 72px;color: white;letter-spacing: 0;text-align: center;text-decoration: none;height: 20px;transform: skew(30deg, 0);display: flex;justify-content: center;align-items: center;border-radius: 3px;padding: 0 8px 0 16px;">
                                        <view style="font-size: 12px; transform: skew(-30deg, 0);color: #FFFFFF;">{ data.salesmanName }跟进</view>
                                    </view>
                                </view>
                            </item>
                        )
                    }
                }),
                ruleDlg: false,
                listTag: false,
                addSuccess: false,
                delflag: false,
                currentTime: '',
                addFlag: true,
                lastPage: '',
                itemBeenWentId: '', // 点击跳转详情的嘉宾id
                guestNum: '',   // 嘉宾数量
                tempParam: {}, //查询合计的临时变量
                addConsumerFlag: true, // 是否可以新建消费者
            }
        },
        components: {
            StatusButton,TagInfo,LnkTaps
        },
        async created() {
            this.$store.commit('listTag/setListTagFlag', false);
            // 从列表界面进入，详情头
            this.activityListItem = this.pageParam.data || {} // 发运消息通知没有默认值到值template会报错,接口会获取activityListItem数据
            this.btnPermission(this.activityListItem.id);
            // 获取当前时间
            this.currentTime = this.$date.format(new Date(Date.now()), 'YYYY-MM-DD HH:mm:ss');
            let sceneObj = await this.$scene.ready(); //消息场景对象
            const approval_from = sceneObj.query['approval_from'];
            this.order_status = sceneObj.query['order_status'];
            this.card_post_id = sceneObj.query['card_post_id'];
            console.log('order_status', this.order_status)
            if(approval_from === 'qw') {
            //发运消息通知 id=805849232973512728&approval_from=qw&order_status=Shipped&card_post_id=618459650769814903
                this.infoJampId = sceneObj.query['id'];
                await this.initQueryHead();
                await this.initQueryItem();
                this.addFlag = false; // 消息通知不展示
            }
            console.log('this.activityListItem', this.activityListItem);
            // 如果提报开始时间<当前时间<=提报结束时间，addFlag为true 11-15改为不限制时间
            {/* if (this.activityListItem.startTime < this.currentTime && this.currentTime <= this.activityListItem.endTime) {
                this.addFlag = true;
            } */}
            this.$store.commit('listTag/setProdPick', this.activityListItem.prodPick);
            this.guestList.option.param.filtersRaw.forEach((item) => {
                if (item.property === 'mcInterListId') item.value = this.activityListItem.id;
            });
            await this.guestList.methods.reload();
            // await this.queryListLinePageCount(this.guestList.option.param);
            //详情头初始化；加载嘉宾数据
            await this.initData();
            // 不展示消费者添加按钮：不在值列表【ASSIGN_POSITION】中，且值列表状态为有效的，其余的职位类型进入【消费者列表】【市场活动添加消费者，新建按钮】【礼赠添加消费者新建】【动销添加消费者新建】【拜访添加消费者新建】【名单提报添加消费者新建】
            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
            const assignList = lovData.map(item => item.val);
            console.log(this.userInfo.positionType,assignList,'----------职位,和允许添加')
            if(!assignList.includes(this.userInfo.positionType)) {
                this.addConsumerFlag = false;
            }
            // 筛选状态字段 嘉宾行状态
            const lineStatusData = await this.$lov.getLovByType('LIST_LINE_STATUS');
            this.lineStatusArr = lineStatusData.filter((item)=> item.val !== 'Created' && item.val !== 'Imported')
            this.lineStatusArr.forEach((item) => {
                this.$set(item, 'checked', false)
            });
            // 筛选状态字段 物流行状态
            this.logisticsStatusArr = await this.$lov.getLovByType('LOGISTICS_STATUS');
            this.logisticsStatusArr.forEach((item) => {
                this.$set(item, 'checked', false)
            });
            await this.queryCfg();
            if (this.activityListItem.mcActId) {
                await this.queryMcRule();
            }
            await this.queryRule();
        },
        // async mounted () {
        //
        // },
        methods: {
            /**
             * 查询消费者数据小计
             * @author:  胡益阳
             * @date:  2024/6/13
             */
            async queryListLinePageCount (e) {
                const param = this.$utils.deepcopy(e)
                param.filtersRaw = param.filtersRaw.filter((item) => item.property !== 'certify');
                try {
                    this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/queryListLinePageCount', param,{
                        autoHandleError: false,
                        handleFailed:(e)=>{
                            if(!e.result.includes('当前数量统计正在进行中')){
                                this.$message.error('查询消费者数据小计失败，' + res.message)
                            }
                            return Promise.reject();
                        }
                    }).then((res) => {
                        if (res.success) {
                            this.guestNum = res.result.allCount;
                        } else {
                            this.$message.error('查询消费者数据小计失败，' + data.result);
                        }
                    })
                } catch (e) {
                    console.error(e)
                }
            },
            /**
             * @desc 从卡片进入，初始化名单头
             * <AUTHOR>
             * @date 2024-04-11
             **/
            async initQueryHead() {
                try {
                    console.log('this.infoJampId', this.infoJampId)
                    const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListNew/queryById', {
                        id: this.infoJampId
                    });
                    if(data.success) {
                        this.$set(this, 'activityListItem', data.result);
                        this.btnPermission(this.activityListItem.id);
                    } else {
                        this.$message.error('查询名单提报头失败，' + data.result);
                    }
                } catch (e) {
                    console.log('e', e)
                }
            },
            /**
             * btnPermission
             * @param id 名单头Id
             */
             async btnPermission(id) {
                console.log(this.activityListItem.permissionType,'permissionType',id)

                if(!id) return;
                if(this.activityListItem.permissionType === 'ByOrg'){
                    // 名单头=按组织下发：（我职位的安全性）提报人安全性下才显示，否则不显示；
                    this.LangChao25BtnShow = this.pageOauth === 'MY_POSTN_ONLY';
                    console.log(this.LangChao25BtnShow,'LangChao25BtnShow')
                    return
                }
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListNew/queryLangChaoButton', {
                   id,
                });
                // 名单头=按职位下发：（我职位的安全性）提报人安全性下且当前职位=这个名单头的名单职位中间表的职位才显示，否则不显示
                if(this.activityListItem.permissionType === 'ByPersonnel'){
                    this.LangChao25BtnShow = !!data.num && this.pageOauth === 'MY_POSTN_ONLY';
                }else{
                }
            },
            /**
             * @desc 从卡片进入，初始化名单行
             * <AUTHOR>
             * @date 2024-04-11
             **/
            async initQueryItem() {
                try {
                    this.guestList.option.param.filtersRaw = [
                        {id: 'mcInterListId', property: 'mcInterListId', value: this.infoJampId},
                        // {id: 'orderStatus', property: 'orderStatus', value: this.order_status, operator: '='}
                    ];
                    this.guestList.option.url = {
                        queryByExamplePage: this.$env.appURL + '/marketactivity/link/interListLineNew/queryQwCardMsgInterLinePage'
                    };
                    await this.guestList.methods.reload();
                } catch (e) {
                    console.log('e', e)
                }
            },
            /**
             * @desc 选择数据
             * <AUTHOR>
             * @date 2023/7/7 14:57
             **/
             async switchTabChoose (tab) {
                 this.statusListActive = tab;
                 let filtersRaw = this.guestList.option.param.filtersRaw;
                 if (tab.value !== 'ALL') {
                     if (!filtersRaw.some((item1) => {
                         return item1.property === 'certify'
                     })) {
                         filtersRaw.push({id: 'certify', property: 'certify', value: tab.value, operator: '='})
                     } else {
                         for (let i = 0; i < filtersRaw.length; i++) {
                             if (filtersRaw[i].property === 'certify') {
                                 filtersRaw[i].value = tab.value;
                                 filtersRaw[i].operator = '=';
                                 break;
                             }
                         }
                     }
                 } else {
                     for (let i = 0; i < filtersRaw.length; i++) {
                         if (filtersRaw[i].property === 'certify') {
                             filtersRaw.splice(i, 1);
                         }
                     }
                 }
                this.guestList.option.param.filtersRaw = filtersRaw;
                await this.guestList.methods.reload();
            },
            /** 根据标签组选择对应的标签值
             * @desc
             * <AUTHOR>
             * @date 2022/8/22 11:14
             **/
            async chooseTagGroups(item) {
                const data = this.$utils.deepcopy(item);
                if (data.length === 0) {
                    this.tagIdList = [];
                } else {
                    this.tagIdList = data.map(i => ({
                        tagGroupId: i.headId,
                        tagId: i.id
                    }));
                }
                if (this.tagIdList.length > 0) {
                    this.consumerOption.option.param['tagIdList'] = this.tagIdList.map((item) => {
                        return item.tagId
                    });
                    this.consumerOption.methods.reload();
                } else {
                    delete this.consumerOption.option.param['tagIdList'];
                    this.consumerOption.methods.reload();
                }
            },
            async chooseClient(){
                if(this.isStoreChosen){
                    this.isStoreChosen = false;
                    delete this.guestList.option.param['belongToStoreIdList'];
                    this.guestList.methods.reload();
                    return
                }
                const data = await this.$object(this.customerOptionNew, {
                    pageTitle: '请选择所属客户',
                    showInDialog: true,
                    multiple: false,
                });
                if (data) {
                    this.isStoreChosen = true;
                    this.guestList.option.param['belongToStoreIdList'] = [data.id];
                    this.guestList.methods.reload();
                } else {
                    this.isStoreChosen = false;
                    delete this.guestList.option.param['belongToStoreIdList'];
                    this.guestList.methods.reload();
                }
                console.log(data,'--')
            },
            /**
             * @desc 选择所属客户数据
             * <AUTHOR>
             * @date 2022/5/27 11:21
             **/
            async chooseStoreList () {
                this.isStoreChosen = !this.isStoreChosen
                if (this.isStoreChosen) {
                    const list = await this.$object(this.customerOption, {
                        pageTitle: '请选择所属客户',
                        showInDialog: true,
                        multiple: false
                    });
                    return list.id;
                } else {
                    return false
                }
            },
            /**
             * @desc 选择所属门店信息
             * <AUTHOR>
             * @date 2022/7/5 09:33
             **/
            async chooseStoreData () {
                const data = await this.chooseStoreList();
                let filtersRaw = this.consumerOption.option.param.filtersRaw;
                if (data) {
                    this.isStoreChosen = true;
                    if (filtersRaw.length === 0) {
                        filtersRaw.push({id: 'belongToStoreId', property: 'belongToStoreId', value: data, operator: '='})
                    } else {
                        if (!filtersRaw.some((item1) => {
                            return item1.property === 'belongToStoreId'
                        })) {
                            filtersRaw.push({id: 'belongToStoreId', property: 'belongToStoreId', value: data, operator: '='})
                        }
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'belongToStoreId') {
                                filtersRaw[i].value = data;
                                filtersRaw[i].operator = '=';
                            }
                        }
                    }
                } else {
                    this.isStoreChosen = false;
                    for (let i = 0; i < filtersRaw.length; i++) {
                        if (filtersRaw[i].property === 'belongToStoreId') {
                            filtersRaw.splice(i, 1);
                        }
                    }
                }
                this.consumerOption.option.param.filtersRaw = filtersRaw;
                this.consumerOption.methods.reload();
            },
            /**
             * @desc 筛选数据
             * <AUTHOR>
             * @date 2022/6/1 10:19
             **/
            switchTabItem(item) {
                this.classifyItemListActive = item;
                let filtersRaw = this.consumerOption.option.param.filtersRaw;
                if (this.classifyListActive.field === 'type') {
                    if (filtersRaw.length === 0) {
                        filtersRaw.push({id: 'type', property: 'type', value: item.type, operator: '='})
                    } else {
                        if (!filtersRaw.some((item1) => {
                            return item1.property === 'type'
                        })) {
                            filtersRaw.push({id: 'type', property: 'type', value: item.type, operator: '='})
                        }
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'type') {
                                filtersRaw[i].value = item.type;
                                filtersRaw[i].operator = '=';
                            }
                            if (filtersRaw[i].property === 'loyaltyLevel') {
                                filtersRaw.splice(i, 1);
                            }
                        }
                    }
                } else if (this.classifyListActive.field === 'loyaltyLevel') {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'loyaltyLevel'
                    })) {
                        filtersRaw.push({id: 'loyaltyLevel', property: 'loyaltyLevel', value: item.val, operator: '='})
                    } else {
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'loyaltyLevel') {
                                filtersRaw[i].value = item.val;
                                filtersRaw[i].operator = '=';
                                break;
                            }
                        }
                    }
                }
                this.consumerOption.list = [];
                this.consumerOption.option.param.filtersRaw = filtersRaw;
                this.consumerOption.methods.reload();
            },
            /**
             * @desc 查询类型数据
             * <AUTHOR>
             * @date 2022/6/1 15:27
             **/
            async queryTypeList() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                    oauth: 'ALL',
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    distinctFields: 'type',
                    filtersRaw: [
                        {
                            id: 'companyId',
                            property: 'companyId',
                            value: this.userInfo.coreOrganizationTile['l3Id'],
                            operator: '='
                        },
                        {id: 'status', property: 'status', value: 'Active', operator: '='}]
                });
                if (data.success) {
                    for (const item of data.rows) {
                        let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                        this.$set(item, 'name', name);
                    }
                    data.rows.forEach((item, index) => {
                        this.$set(item, 'seq', index + 1)
                    });
                    this.classifyItemList = data.rows;
                }
            },
            /**
             * @desc 级联分类分级
             * <AUTHOR>
             * @date 2022/6/1 10:14
             **/
            async switchTab(item) {
                this.classifyListActive = item;
                if (item.val === 'ACCT_MEMBER_LEVEL') {
                    const lovData = await this.$lov.getLovByType(item.val);
                    if (this.isGuoJiao) {
                        this.classifyItemList = await this.$lov.getLovByParentTypeAndValue({
                            type: item.val,
                            parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                            parentVal: this.userInfo.coreOrganizationTile['l3Id']
                        });
                    } else {
                        this.classifyItemList = lovData.filter(item => !item.parentId);
                    }
                    this.classifyItemListActive = {};
                    this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                    this.consumerOption.methods.reload();
                } else if (item.val === 'type') {
                    await this.queryTypeList();
                    this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                    this.consumerOption.methods.reload();
                } else if (item.val === 'certified') {
                    this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                        id: 'certify',
                        property: 'certify',
                        value: 'certified',
                        operator: '='
                    }]);
                    this.consumerOption.list = [];
                    this.consumerOption.methods.reload();
                } else if (item.val === 'uncertified') {
                    this.classifyItemList = [];
                    this.classifyItemListActive = {};
                    this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                        id: 'certify',
                        property: 'certify',
                        value: 'uncertified',
                        operator: '='
                    }]);
                    this.consumerOption.list = [];
                    this.consumerOption.methods.reload();
                } else {
                    this.classifyItemList = [];
                    this.classifyItemListActive = {};
                    this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                    this.consumerOption.methods.reload();
                }
            },
            /**
             * @desc 查询标签组信息
             * <AUTHOR>
             * @date 2022/8/22 10:51
             **/
            async queryTagGroups() {
                const data = await this.$http.post(this.$env.dmpURL + '/link/portalAccntTagGroup/queryTagGroups', {
                    validFlag: 'Y',
                    queryItemFlag: 'Y',         // 查询标签值
                    labelType: 'CustomLabel',
                    labelRange: 'GroupAndBrandLabel',
                    companyId: this.userInfo.coreOrganizationTile['l3Id']
                });
                if (data.rows.length > 0) {
                    data.rows.forEach((item) => {
                        item.checked = true;
                        item.showMore = false;
                        item.editFlag = false;
                        item.selectAllFlag = false;
                        if (!this.$utils.isUndefined(this.tagLabels)) {
                            item.editFlag = true;
                            for (const tagPart of this.labelField) {
                                if(tagPart.id === 'tag' + item.id) item.required = tagPart.required;
                            }
                            if (item.tagItemList.length > 0) {
                                item.tagItemList.forEach((tag) => {
                                    tag.checked = false;
                                    delete tag.id;
                                });
                            }
                            this.tagLabels.forEach((group) => {
                                group.itemTags.forEach((tag) => {
                                    const flag = item.tagItemList.find((label) => label.tagId === tag.tagId);
                                    if(flag) {
                                        flag.checked = true;
                                        flag.id = tag.id;
                                        flag.ifActive = tag.ifActive;
                                    }
                                })
                            });
                        } else {
                            if (item.tagItemList.length > 0) {
                                item.tagItemList.forEach((tag) => {
                                    tag.checked = false;
                                });
                            }
                        }
                    });
                    this.tagGroupList = data.rows;
                }
            },
            /**
             * @desc 查询参数配置
             * <AUTHOR>
             * @date 2022/6/7 17:21
             **/
            async queryCfg () {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', { key: 'GUOJIAO_ACCT_MEMBER_LEVEL'});
                if (data.success) {
                    const companyIds = data.value.split(',');
                    this.isGuoJiao = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
                }
            },
            /**
             * @desc 查询活动参与条件
             * <AUTHOR>
             * @date 2022/4/13 16:04
             **/
            async queryMcRule () {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/marketActivityRule/queryByExamplePage', {
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    oauth: 'ALL',
                    filtersRaw: [{"id":"mcActId","property":"mcActId","value": this.activityListItem.mcActId}]
                });
                if (data.rows && data.rows.length > 0) {
                    const otherRules = data.rows.filter(item => !item.fieldLovType);
                    const consumerGradeData = data.rows.filter(item => item.fieldColumnName === 'loyaltyLevel' && item.fieldLovType);
                    for (const item of consumerGradeData) {
                        const lovData = item.judgeContent.split(',');
                        let judgeContent = '/';
                        for(let i = 0; i < lovData.length; i++) {
                            if (this.isGuoJiao) {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeNameParentTypeParentVal(item.fieldLovType, lovData[i], 'ACCT_MEMBER_LEVEL_COMPANY', this.activityListItem.companyId) + '/';
                            } else {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovData[i]) + '/';
                            }
                        }
                        item['showContent'] = judgeContent.substring(1, judgeContent.lastIndexOf('/') );
                    }
                    const lovRuleData = data.rows.filter(item => !!item.fieldLovType && item.fieldColumnName !== 'loyaltyLevel');
                    for (const item of lovRuleData) {
                        const lovRuleItemData = item.judgeContent.split(',');
                        let ruleItemJudgeContent = '/';
                        for(let i = 0; i < lovRuleItemData.length; i++) {
                            ruleItemJudgeContent = ruleItemJudgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovRuleItemData[i]) + '/';
                        }
                        item['showContent'] = ruleItemJudgeContent.substring(1, ruleItemJudgeContent.lastIndexOf('/') );
                    }
                    this.marketRules = otherRules.concat(consumerGradeData).concat(lovRuleData);
                    const find = this.marketRules.find((item) => item.fieldColumnName === 'vipNumber');
                    if (find) {
                        this.listTag = true;
                        this.$store.commit('listTag/setListTagFlag', true);
                    }
                }
            },
            /**
             * @desc 查询活动参与条件
             * <AUTHOR>
             * @date 2022/4/13 16:04
             **/
            async queryRule () {
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/marketActivityRule/queryByExamplePage', {
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    oauth: 'ALL',
                    filtersRaw: [{"id":"mcActId","property":"mcActId","value": this.activityListItem.id}]
                });
                if (data.rows && data.rows.length > 0) {
                    const otherRules = data.rows.filter(item => !item.fieldLovType);
                    const consumerGradeData = data.rows.filter(item => item.fieldColumnName === 'loyaltyLevel' && item.fieldLovType);
                    for (const item of consumerGradeData) {
                        const lovData = item.judgeContent.split(',');
                        let judgeContent = '/';
                        for(let i = 0; i < lovData.length; i++) {
                            if (this.isGuoJiao) {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeNameParentTypeParentVal(item.fieldLovType, lovData[i], 'ACCT_MEMBER_LEVEL_COMPANY', this.activityListItem.companyId) + '/';
                            } else {
                                judgeContent = judgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovData[i]) + '/';
                            }
                        }
                        item['showContent'] = judgeContent.substring(1, judgeContent.lastIndexOf('/') );
                    }
                    const lovRuleData = data.rows.filter(item => !!item.fieldLovType && item.fieldColumnName !== 'loyaltyLevel');
                    for (const item of lovRuleData) {
                        const lovRuleItemData = item.judgeContent.split(',');
                        let ruleItemJudgeContent = '/';
                        for(let i = 0; i < lovRuleItemData.length; i++) {
                            ruleItemJudgeContent = ruleItemJudgeContent + await this.$lov.getNameByTypeAndVal(item.fieldLovType, lovRuleItemData[i]) + '/';
                        }
                        item['showContent'] = ruleItemJudgeContent.substring(1, ruleItemJudgeContent.lastIndexOf('/') );
                    }
                    this.listRules = otherRules.concat(consumerGradeData).concat(lovRuleData);
                    const find = this.listRules.find((item) => item.fieldColumnName === 'vipNumber');
                    if (find) {
                        this.listTag = true;
                        this.$store.commit('listTag/setListTagFlag', true);
                    }
                }
            },
            /**
             * @desc 确定筛选
             * <AUTHOR>
             * @date 2022/4/1 17:03
             **/
            confirmQuery () {
                this.$utils.showLoading();
                const chooseLineData = this.lineStatusArr.filter((item) => !!item.checked);
                const chooseLogisData = this.logisticsStatusArr.filter((item) => !!item.checked);
                let filtersRaw = [];
                if (chooseLineData.length > 0) {
                    let str = '';
                    chooseLineData.forEach((item) => {
                        str = item.val + ',' + str
                    });
                    str = str.substring(0, str.length - 1);
                    filtersRaw.push({id: 'status', property: 'status', operator: 'IN', value: `[${str}]`})
                }
                if (chooseLogisData.length > 0) {
                    let strTmp = '';
                    chooseLogisData.forEach((item) => {
                        strTmp = item.val + ',' + strTmp
                    });
                    strTmp = strTmp.substring(0, strTmp.length - 1);
                    filtersRaw.push({id: 'logStatus', property: 'logStatus', operator: 'IN', value: `[${strTmp}]`})
                }
                if (this.statusListActive.value !== 'ALL') {
                    filtersRaw.push({id: 'certify', property: 'certify', value: this.statusListActive.value, operator: '='})
                }
                this.guestList.option.param.filtersRaw = filtersRaw.concat({id: 'mcInterListId', property: 'mcInterListId', value: this.activityListItem.id})
                this.guestList.methods.reload();
                this.$utils.hideLoading();
                // this.queryListLinePageCount(this.guestList.option.param);
            },
            /**
             * @desc 选择状态值
             * <AUTHOR>
             * @date 2022/4/1 16:25
             **/
            chooseStatus (item) {
               item.checked = !item.checked;
               this.confirmQuery();
            },
            sleep(millisecond) {
                return new Promise(resolve => {
                    setTimeout(() => {
                        resolve()
                    }, millisecond)
                })
            },
            /**
             * @desc 监听返回
             * <AUTHOR>
             * @date 2022/4/1 11:19
             **/
            async onBack() {
                const guestNum = this.guestNum;
                console.log('----------------------fanhuifanhui');
                !this.itemBeenWentId && setTimeout(async () => {
                    console.log(this.guestNum, guestNum,'----------------------重新查询0');
                    await this.guestList.methods.reload();//重新查询嘉宾行信息,查询后afterload会重新查询guestNum
                    {/* if(!this.guestList.list.length&&this.addSuccess){ // 添加成功后,有概率查不出来,重新查一次
                        this.addSuccess = false;
                        console.log('----------------------重新查询');
                        await this.sleep(1000);
                        await this.guestList.methods.reload();//重新查询嘉宾行信息
                    }else if(this.addSuccess){
                        await this.queryListLinePageCount(this.tempParam);
                        // 添加成功后仍未查出
                        console.log(this.guestNum, guestNum,'----------------------重新查询2');
                        if(this.guestNum==guestNum||this.delflag){
                             await this.sleep(1000);
                             await this.guestList.methods.reload();//重新查询嘉宾行信息
                        }
                    } */}
                    this.addSuccess = false;
                    this.delflag = false;
                }, 500);
                //只更新点击的那条数据,以保证不刷新整个列表
                this.itemBeenWentId && setTimeout(()=>this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/queryByExamplePage',
                {   oauth: "ALL",
                    order: "desc",
                    page: 1,
                    rows: 10,
                    sort: "id",
                    filtersRaw:[
                        {id: 'id', property: 'id', value:this.itemBeenWentId},
                        {id: 'mcInterListId', property: 'mcInterListId', value: this.activityListItem.id},
                    ]}).then(res=>{
                    const ind = this.guestList.list.findIndex(e=>e.id==this.itemBeenWentId);
                    this.guestList.list.splice(ind,1,res.rows[0]);
                    this.itemBeenWentId = '';
                }),800)
            },
            /**
             * @desc 删除名单行数据
             * <AUTHOR>
             * @date 2022/3/31 19:06
             **/
            async handleTerminalDelete(item, index) {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/deleteById', item, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('删除嘉宾名单行数据失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.delflag = true;
                    this.$utils.hideLoading();
                    const delIndex = this.guestList.list.findIndex(e=>e.id==item.id);
                    this.guestList.list.splice(delIndex,1);
                    {/* await this.guestList.methods.reload(); */}
                }
            },
            /**
             * @desc 初始化数据
             * <AUTHOR>
             * @date 2022/4/2 10:04
             **/
            async initData() {
                this.$utils.showLoading();
                // 详情头初始化
                this.details = [
                    {label: '名单提报名称', text: this.activityListItem.interListName || this.activityListItem.activityName},
                    {label: '活动类型', text: await this.$lov.getNameByTypeAndVal('MC_TYPE', this.activityListItem.activityType)},
                    {label: '提报开始时间', text: this.$date.filter(this.activityListItem.actStartTime || this.activityListItem.startTime, 'YYYY-MM-DD HH:mm:ss')},
                    {label: '提报结束时间', text: this.$date.filter(this.activityListItem.actEndTime || this.activityListItem.endTime, 'YYYY-MM-DD HH:mm:ss')},
                    {label: '状态', text: await this.$lov.getNameByTypeAndVal('LIST_STATUS', this.activityListItem.status)},
                    // {label: '提报名额', text: this.activityListItem.submitPlaces},
                    {label: '名单下发人', text: this.activityListItem.actListOwner},
                    {label: '权益类型', text: await this.$lov.getNameByTypeAndVal('LIST_REWARD_TYPE', this.activityListItem.listRewardType)}
                ]
                this.$utils.hideLoading();
            },
            /**
             * @desc 提交名单信息
             * <AUTHOR>
             * @date 2022/4/2 09:11
             **/
            async commitInterListLine() {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/bathCrateFlow', {
                    mcInterListId: this.activityListItem.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('提报名单失败！' + response.result);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$nav.back();
                }
            },
            async jumpTolastPage(){
                this.$utils.showLoading()
                this.guestList.pageSize = this.guestList.option.pageSize = this.lastPage*10;
                await this.guestList.methods.reload();
                if(this.guestList.list.length<=10) {
                    this.$utils.hideLoading();
                    this.lastPage = 1;
                    return
                }
                this.guestList.pageSize =this.guestList.option.pageSize = 10;
                this.guestList.page = this.lastPage;
                await this.guestList.methods.loadMore();
                const id = this.guestList.list[this.guestList.list.length-10].id
                console.log(`${id}`,'id')
                setTimeout(()=>{
                    wx.pageScrollTo({
                        selector: `#a${id}`,
                        duration: 0
                    });
                    this.$utils.hideLoading();
                },400)

            },
            /**
             * @desc 添加嘉宾
             * <AUTHOR>
             * @date 2022/3/31 11:55
             **/
            async addGuest () {
                this.$utils.showLoading()
                this.isStoreChosen = false;
                this.tagIdList = [];
                if (this.consumerOption.option.param.tagIdList) {
                    delete this.consumerOption.option.param.tagIdList;
                }
                await this.queryTagGroups();
                this.$utils.hideLoading()
                let filtersRaw = this.consumerOption.option.param.filtersRaw;
                for (let i = 0; i < filtersRaw.length; i++) {
                    if (filtersRaw[i].property === 'belongToStoreId') {
                        filtersRaw.splice(i, 1);
                    }
                }
                // 遍历filtersRaw，如果没有postnId，则添加一个postnId筛选条件
                if(!filtersRaw.some((item) => item.property === 'postnId')) {
                    filtersRaw = filtersRaw.concat( {id: 'postnId', property: 'postnId', value: this.userInfo.postnId, operator: '='})
                }
                this.consumerOption.option.param.filtersRaw = filtersRaw;
                this.lastPage = this.guestList.page;
                const list = await this.$object(this.consumerOption, {
                    pageTitle: '消费者',
                    multiple: true,
                    beforeConfirm: async (rows) => {
                        const page = (this.consumerOption.list.length||0)/(this.consumerOption.pageSize||10)
                        if (rows.length > 10) {
                            this.$showError('最多不能超过10条数据');
                            // 兼容page会被组件清空导致后端报错问题
                            this.consumerOption.page = Math.ceil(page);
                            return Promise.reject()
                        }
                    }
                });
                let consumerDataList = [];
                consumerDataList = list.map(item => ({
                    mcInterListId: this.activityListItem.id,
                    accntChannelId: item.consumerId,
                    phone: item.phoneNumber,
                    row_status: ROW_STATUS.NEW,
                    acctName: item.name,
                    gender: item.gender,
                    birthDate: item.birthDate,
                    company: item.companyName,
                    birthDateType: item.birthType,
                    deliveryChannel: item.accntSourceFrom,
                    consigneeProvince: item.province,
                    consigneeCity: item.city,
                    consigneeDistrict: item.county,
                    consigneeStreet: item.street,
                    consigneeAddress: item.detailedAddress,
                    jobTitle: item.position,
                }));
                if(!consumerDataList.length){return}
                // this.$utils.showLoading()
                const data = await this.$http.post(this.$env.appURL + '/marketactivity/link/interListLineNew/batchAdd', consumerDataList, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        setTimeout(async()=> {
                            this.addSuccess = true;
                            this.$utils.showLoading();
                            await this.guestList.methods.reload();//重新查询嘉宾行信息,查询后afterload会重新查询guestNum
                            this.$utils.hideLoading();
                            this.$message.error('添加嘉宾名单失败！' + response.result);
                        }, 1000)
                    }
                });
                if (data&&data.success) {
                    this.addSuccess = true;
                    this.$utils.showLoading();
                    await this.guestList.methods.reload();//重新查询嘉宾行信息,查询后afterload会重新查询guestNum
                    this.$utils.hideLoading();
                    if (data.result) {
                        this.$message.warn('添加嘉宾名单失败！' + data.result);
                    }
                }else{
                    await this.guestList.methods.reload();//重新查询嘉宾行信息,查询后afterload会重新查询guestNum
                }
            },
            /**
             * 新增消费者
             * <AUTHOR>
             * @date 2020-08-07
             * */
            async addConsumer() {
                const id = await this.$newId();
                const accountItem = {
                    id: id,
                    row_status: ROW_STATUS.NEW,
                    consumerDataType: 'ChannelConsumer',
                    dataSource: 'MarketingPlatform',
                    dataType: 'Consumer',
                    accntSourceFrom: 'SalesAssistant',
                    orgId: this.userInfo.orgId,
                    fstName: this.userInfo.firstName,
                    postnId: this.userInfo.postnId,
                    belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    type: "ToBeFollowed",
                    birthType: 'Yang',
                    brandPreference: "",
                    hobby: "",
                    terminalFlag : 'N',
                    listOfTags: {
                        accountId: id,
                        list: []
                    }
                };
                this.$nav.push(this.editPath, {
                    "pageOauth":this.pageOauth,
                    "LangChao25BtnShow": this.LangChao25BtnShow,
                    data: accountItem,
                    marketActivityId : this.activityListItem.id,
                    pageBackNum: 1,
                    userInfo: this.userInfo,
                    pageFrom:'ListReport',
                    activityType: this.activityListItem.activityType,
                    brand: this.activityListItem.brand,
                    activityStatus: this.activityListItem.status,
                    activityName: this.activityListItem.interListName || this.activityListItem.activityName,
                    lineDataType: this.activityListItem.listRewardType
                })
            },
            /**
             * @desc 跳转嘉宾详情页进行数据完善
             * <AUTHOR>
             * @date 2022/3/31 15:04
             **/
            gotoItem(item) {
                this.itemBeenWentId = item.id;
                item['row_status'] = ROW_STATUS.UPDATE;
                let editable = ['ForSubmitted', 'Refused'].includes( this.activityListItem.status) &&  ['New','QwRefused','Refused','Improved'].includes(item.status)
                this.$nav.push('/pages/lj-consumers/list-report/guest-detail-add-page', {
                    "pageOauth":this.pageOauth,
                    "LangChao25BtnShow":this.LangChao25BtnShow,
                    data: item,
                    editable: editable&&this.LangChao25BtnShow,
                    sourceFrom: this.order_status ? 'order' : '',
                    activityStatus: this.activityListItem.status,
                    lineStatus: item.status,
                    activityType: this.activityListItem.activityType,
                    brand: this.activityListItem.brand,
                    companyId: this.activityListItem.companyId,
                    orderFlag: this.activityListItem.orderFlag,
                    subAcctType: this.activityListItem.type,
                    prodPick: this.activityListItem.prodPick,
                    mcInterListId: this.activityListItem.id,
                    // listTag: this.listTag    // 规则是否配置了浪潮产品
                });
            }
        }
    }
</script>

<style lang="scss">
    .list-report-detail-page {
        width: 100%;
        margin-bottom: 120px;

        .padding0{
            padding-top:0;
            text-align: left;
            justify-content: start;
            border-bottom: 0.5px solid #f2f2f2;
        }

        .zero-view {
            width: 100%;
            height: 30px;
        }
        .scroll-x{
            display: inline-block;
            color: #8C8C8C;
            width: calc( 100% - 140px);
            flex:1;
            margin-left: 10px;
            flex-shrink: 1;
            white-space: nowrap;
            overflow: auto;
        }
        .form-column {
            margin: auto;
            border-radius: 16px;
            padding: 40px 28px 4px 28px;
            background: white;
            font-family: PingFangSC-Regular;

            .item-input {
                line-height: 30px;
                margin-bottom: 30px;
                width: 100%;
                display: flex;

                .label {
                    align-self: flex-start;
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                }

                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                    text-align: right;
                }
            }

            .item-textarea {
                line-height: 30px;
                width: 100%;
                margin-bottom: 30px;

                .label {
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                    margin-bottom: 18px;
                }

                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                }
            }
        }
        .act-rule{
            width: 87%;
            border-radius: 16px;
            margin: auto;
            background: white;
            letter-spacing: 0;
            font-size: 28px;
            padding: 32px 24px;
            color: #262626;
            display: flex;
            align-items: center;
            justify-content: space-between;
            .act-rule-title{
                letter-spacing: 0;
                font-size: 28px;
            }
            .view-btn {
                color: #2F69F8;
            }
        }
        .act-rule-con {
            background-color: #d9d9d9;
            padding: 24px;
            .zero-view {
                width: 100%;
                height: 30px;
            }
            .act-rules{
                /*width: 87%;*/
                max-height: 500px;
                overflow: scroll;
                margin: auto;
                border-radius: 16px;
                background: white;
                letter-spacing: 0;
                font-size: 28px;
                padding: 32px 24px;
                color: #262626;
                .act-rules-title{
                    border-bottom: 2px solid #f2f2f2;
                    letter-spacing: 0;
                    font-size: 28px;
                    padding: 0 0 20px;
                    margin-bottom: 10px;
                }
                .rule-item {
                    width: 100%;
                    line-height: 50px;
                }
            }
        }
        .guest-list {
            width: 93%;
            margin: auto auto 130px;
            border-radius: 16px;
            background: white;
            .line-status{
                display: flex;
                flex-wrap: wrap;
                border-bottom: 2px solid #f2f2f2;
                justify-content: flex-start;
                align-items: center;
                .line-status-item{
                    border-radius: 10px;
                    height: 30px;
                    line-height: 30px;
                    font-size: 24px;
                    padding: 12px;
                    display: flex;
                    justify-content: center;
                    margin: 10px;
                    min-width: 18%;
                }
                .unchecked {
                    border: 1px solid #E0E4EC;
                    color: #8C8C8C;
                }
                .checked {
                    background: #EDF3FF;
                    color: #2F69F8;
                    border: 1px solid #EDF3FF;
                }
                .confirm{
                    color: #ffffff;
                    background: #2F69F8;
                    width: 80px;
                    border-radius: 13px;
                    display: flex;
                    justify-content: center;
                }
            }

            .title {
                border-bottom: 2px solid #f2f2f2;
                height: 92px;
                line-height: 92px;
                display: flex;
                justify-content: space-between;
                letter-spacing: 0;
                font-size: 28px;
                padding: 0 24px;

                .left {
                    color: #262626;
                }

                .right {
                    color: #2F69F8;
                    width: 30%;
                    text-align: right;
                }
            }

            .guest-list-item-icon {
                width: 80px;
                text-align: center;

                .iconfont {
                    font-size: 40px;
                    color: #bfbfbf;
                }
            }
            .tab-filter{
                .active {
                    color: $color-primary;
                }
                .lnk-tabs-content {
                    display: flex;
                    justify-content: space-around;
                    flex-wrap: nowrap;
                }
                .lnk-tabs-item {
                    height: 92px;
                    line-height: 92px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;
                    .label-name-line {
                        position: relative;
                        font-size: 28px;
                        margin-left: 10px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        .num-tips {
                            position: absolute;
                            top: 0;
                            right: 0;
                            transform: translateX(90%);
                            background-color: #FF5A5A;
                            color: #ffffff;
                            font-size: 24px;
                            border-radius: 50%;
                            padding-left: 16px;
                            padding-right: 16px;
                            height: 40px;
                            line-height: 40px;
                            text-align: center;
                        }
                        .line {
                            height: 8px;
                            width: 56px;
                            border-radius: 16px 16px 0 0;
                            background-color: $color-primary;
                            box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                            margin-top: -8px;
                        }
                    }
                }
            }
            .guest-list-item {
                @include flex;
                @include flex-start-center;
                padding: 34px 16px 34px 0;
                .sort-num {
                    width: 42px;
                }
                .logo-img {
                    display: flex;
                    flex-direction: column;
                    justify-content: center;
                    margin-right: 20px;
                    align-items: center;
                    .time {
                        width: 94px;
                        font-size: 16px;
                    }
                }
                .media-list-logo {
                    height: 94px;
                    width: 94px;

                    image {
                        height: 100%;
                        width: 100%;
                    }
                }
                .guest-list-logo {
                    border-radius: 50%;
                    width: 80px;
                    height: 80px;
                    overflow: hidden;
                }

                .guest-list-item-content {
                    flex: 1;
                    .guest-list-item-content-row1 {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 4px;
                        width: 100%;

                        .left {
                            font-size: 28px;
                            letter-spacing: 0;
                            line-height: 27px;
                            @include flex;
                            @include flex-start-center;

                            .terminal-type {
                                color: #262626;
                                width: 88px;
                            }

                            .terminal-name {
                                font-size: 28px;
                                color: #8C8C8C;
                                font-family: PingFangSC-Regular, serif;
                                padding-left: 16px;
                                width: 180px;
                            }
                        }

                        .right {

                        }

                    }

                    .guest-list-item-content-row2 {
                        font-size: 28px;
                        letter-spacing: 0;
                        @include flex;
                        align-items: center;
                        height: 30px;
                        margin-bottom: 4px;
                        margin-top: 20px;
                        color: #262626;
                        .audit-opinion {
                            display: inline-block;
                            overflow: hidden;
                            text-overflow: ellipsis;
                            display: -webkit-box;
                            -webkit-line-clamp: 1;
                            line-clamp: 1;
                            color: #8C8C8C;
                            -webkit-box-orient: vertical;
                            width: 70%;
                            margin-left: 10px;
                        }
                    }
                }
            }

            .link-auto-list .link-sticky.link-sticky-top .link-sticky-content{
                width: auto;
            }
        }

        .link-auto-list .link-auto-list-top-bar {
            display: flex;
            margin-bottom: 20px;
            border-bottom: 0px !important;
        }
        .link-auto-list .link-auto-list-top-bar .link-search-input.link-search-input-no-padding-bottom {
            padding-bottom: 0;
            width: 80%;
        }
        .link-auto-list .link-auto-list-top-bar .link-auto-list-query-bar {
            display: -webkit-flex;
            display: -ms-flexbox;
            display: flex;
            width: 20%;
            padding-top: 20px;
        }
        .calcAll {
            justify-content: space-between;
            font-size: 28px;
            display: flex;
            align-items: center;
            padding: 0 24px;
            height: 92px;
            border-bottom: 2px solid #f2f2f2;
        }
        .pricolor{
            color: #2f69f8;
        }
    }

</style>
