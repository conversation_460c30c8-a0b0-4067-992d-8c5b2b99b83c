<!--
* consumers-schedule-work-summary-detail-page 排期工作小结详情
* @data: 2025/1/13
* @author: 胡益阳
-->
<template>
    <link-page class="consumers-schedule-work-summary-detail-page">
            <!--排期信息-->
            <line-title title="排期信息"></line-title>
            <view class="zero-view"></view>
            <view class="form">
                <view class="form-item">
                    <view class="item-input">
                        <view class="label">排期ID</view>
                        <view class="text">{{ headData.id }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">排期运营名称</view>
                        <view class="text">{{ headData.scheduleName }}</view>
                    </view>
                </view>
            </view>

            <!--基础信息-->
            <line-title title="基础信息"></line-title>
            <view class="zero-view"></view>
            <view class="form">
                <view class="form-item">
                    <view class="item-input">
                        <view class="label" v-if="customersItem.scheRuleType === 'ScheAction'">动作大类</view>
                        <view class="label" v-if="customersItem.scheRuleType === 'ScheTarget'">目标大类</view>
                        <view class="text">{{ customersItem.planScheType | lov('SCHE_TYPE') }}</view>
                    </view>
                    <view class="item-input" v-if="customersItem.planScheChild">
                        <view class="label" v-if="customersItem.scheRuleType === 'ScheAction'">动作小类</view>
                        <view class="label" v-if="customersItem.scheRuleType === 'ScheTarget'">目标小类</view>
                        <view class="text">{{ customersItem.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">是否需要领导协同</view>
                        <view class="text">{{ customersItem.isLeadership === 'Y' ? '是' : '否'}}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">预估执行开始时间</view>
                        <view class="text">{{ $date.format(customersItem.beginTime, 'YYYY-MM-DD') }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">预估执行结束时间</view>
                        <view class="text">{{ $date.format(customersItem.endTime, 'YYYY-MM-DD') }}</view>
                    </view>
                    <view class="item-textarea" v-if="customersItem.planDesc">
                        <view class="label" v-if="customersItem.scheRuleType === 'ScheAction'">动作计划说明</view>
                        <view class="label" v-if="customersItem.scheRuleType === 'ScheTarget'">目标计划说明</view>
                        <link-textarea style="padding:0px !important;white-space:pre-wrap;" disabled
                                       v-model="customersItem.planDesc" mode="textarea"/>
                    </view>
                </view>
            </view>
            <!--协同领导-->
<!--            <view class="leader-ship" v-if="leaderData.length > 0">-->
            <view class="leader-ship" v-if="customersItem.scheRuleType === 'ScheAction' && leaderData.length > 0">
                <view class="line">
                    <view class="name">{{'协同领导'}}</view>
                </view>
                <view class="leader-ship-data">
                    <view v-for="(item,index) in leaderData" :key="item.id">
                        <view class="leader-ship-item">
                            <text class="num">{{ item.firstName }}</text>
                            <text class="name">{{ item.userName ? item.userName : item.username }}</text>
                        </view>
                    </view>
                </view>
            </view>
            <!--执行明细-->
            <view class="zero-view"></view>
            <implement-summary-detail :rowId="rowId"
                                      ref="implementSummaryDetail"
                                      v-if="!!rowId"></implement-summary-detail>
            <!--工作小结-->
            <view class="summary-title">
                <view class="line">
                    <view class="line-top"></view>
                    <view class="line-bottom"></view>
                </view>
                <view class="title-info">
                    <view class="stair-title">{{'工作小结与评论'}}</view>
                </view>
                <view class="add-button" v-if="!approvalFlag
                                            && ['Execution', 'Executing', 'ExeDelayed', 'ExeEarly', 'Planing', 'Finished', 'Suspend'].includes(customersItem.planDeStatus)
                                            && !['UpdateRevering', 'InvalidRevering'].includes(customersItem.planStatus)
                                            && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)"
                      @tap="openAddSummaryDialog()">
                    <view class="icon-box">
                        <link-icon icon="icon-plus" style="color: #FFFFFF;font-size: 20px;"/>
                    </view>
                    <!-- 排期负责人展示添加小结，其他人展示添加评论 -->
                    <view class="text">{{isPrincipal ? '添加小结' :'添加评论'}}</view>
                </view>
            </view>
            <view class="zero-view"></view>
            <view class="work-summary-container"  :style="scheduleCommentList.length > 0 ? { padding: '20px 14px 2px 14px;' } : {}">
                <view class="work-summary-item" v-for="(item, index) in scheduleCommentList" :key="index">
                    <view class="cell-box">
                        <view class="cell-icon">{{ getLastTwoChars(item.createdName) }}</view>
                        <view class="cell-title" v-if="item.personnelType">{{ item.personnelType | lov('COMMENT_TYPE')}}</view>
<!--                        <view class="cell-desc">{{ item.positionType | lov('ASSIGN_POSITION') }}</view>-->
                        <view class="cell-time" :style="{'max-width': $utils.isNotEmpty(item.personnelType) ? '133px':'183px'}">{{ item.lastUpdated }}</view>
                        <view class="cell-status" :style="getColorByStatus(item.planStatus, item.planDeStatus)">
                            <view class="dot"></view>
                            {{ item.planDeStatus | lov('SCHE_PLAN_STATUS') }}
                        </view>
                    </view>
                    <view class="cell-label">{{ item.comments }}</view>
                </view>
            </view>
<!--        </view>-->
        <view style="height: 70px"></view>
        <!--添加小结弹窗-->
        <link-dialog ref="addSummary" v-model="addSummaryFlag" title="添加小结" width="80%">
            <view class="add-summary">
                <link-form ref="addSummaryForm" :value="formData">
                    <link-form-item label="是否暂缓排期" v-if="showSuspendSchedule && consumerTimeFlag && customersItem.scheRuleType === 'ScheAction'">
                        <link-switch v-model="formData.suspendSchedule"/>
                    </link-form-item>
                    <link-form-item label="总结说明" vertical field="comments">
                        <view class="text-area-box">
                            <view class="text-area-len">{{ `${formData.comments? formData.comments.length : 0}/100` }}</view>
                            <link-textarea :height="144" v-model="formData.comments" style="padding: 0px !important;" placeholder="请填写总结说明" :nativeProps="{maxlength:100}"/>
                        </view>
                    </link-form-item>
                </link-form>
                <view class="button">
                    <link-button class="button-item" mode="stroke" block @tap="cancel">取消</link-button>
                    <link-button class="button-item" block @tap="submit">确认</link-button>
                </view>
            </view>
        </link-dialog>
        <!-- 添加评论弹窗 -->
        <link-dialog ref="addComment" v-model="addCommentFlag" title="添加评论" width="80%">
            <view class="add-summary">
                <link-form ref="addCommentForm" :value="commentData">
                    <link-form-item label="评论说明" vertical field="comments">
                        <view class="text-area-box">
                            <view class="text-area-len">{{ `${commentData.comments? commentData.comments.length : 0}/100` }}</view>
                            <link-textarea :height="144" v-model="commentData.comments"  style="padding: 0px !important;" placeholder="请填写评论说明" :nativeProps="{maxlength:100}"/>
                        </view>
                    </link-form-item>
                </link-form>
                <view class="button">
                    <link-button class="button-item" mode="stroke" block @tap="commentCancel">取消</link-button>
                    <link-button class="button-item" block @tap="commentSubmit">确认</link-button>
                </view>
            </view>
        </link-dialog>
        <link-sticky>
            <!-- 从执行情况进入 -->
            <link-button block v-if="(pageFrom === 'implementation' || pageFrom === 'addConsumer')
                                     && consumerTimeFlag
                                     && !approvalFlag
                                     && isPrincipal
                                     && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(customersItem.planStatus)
                                     && !['Finished', 'Planing', 'Invalid'].includes(customersItem.planDeStatus)
                                     && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)"
                         @tap="editScheduling(customersItem)">编辑
            </link-button>
            <!--1.从排期日历进入 -->
            <!--2.排期计划状态不等于已完成、未完成、已作废-->
            <!--3.排期运营状态不等于已结束、新建、已作废；、-->
            <!--4.排期计划审核状态不等于新建审核中、变更审核中、作废审核中-->
            <!--5.且排期消费者状态不等于已终止-->
            <!--6.且当前时间处于排期消费者的开始时间和结束时间范围内-->
            <!--7.且排期负责人才展示-->
<!--             待联调- 且排期负责人才展示-->
            <link-button block v-if="pageFrom === 'calendar'
                                     && consumerTimeFlag
                                     && !approvalFlag
                                     && isPrincipal
                                     && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(customersItem.planStatus)
                                     && !['Finished', 'Planing', 'Invalid'].includes(customersItem.planDeStatus)
                                     && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)"
                         @tap="editScheduling(customersItem)">编辑
            </link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import {isEmpty} from "../../../components/painter/libs/util";
import LineTitle from "../../lzlj/components/line-title.vue";
import implementSummaryDetail from "./components/implement-summary-detail.vue";
import {getColorByStatus} from "./components/schedule-common";
import {ComponentUtils} from 'link-taro-component';
import Taro from "@tarojs/taro";
export default {
    name: 'consumers-schedule-work-summary-detail-page',
    components: {LineTitle, implementSummaryDetail},
    data() {
        return {
            customersItem: {},         // 小排期数据
            scheduleCommentList: [],    // 排期小结数据
            scheduleItem: {},   // 消费者信息
            scheduleId: '',     // 排期id
            rowId: '',          // 排期消费者行id
            acctId: '',         // 消费者id
            leaderData: [],  // 协同领导数据
            oauth: this.pageParam.oauth,
            headData: this.pageParam.headData ? this.pageParam.headData : {},  // 大排期数据
            showSuspendSchedule: false,       // 是否展示暂缓排期
            addSummaryFlag: false,            // 添加小结的弹窗
            debounceFlag: false,
            formData: {
                suspendSchedule: 'N',
                schedulePlanId: '',
                comments: ''
            },                     // 添加小结的表单
            addCommentFlag: false,            // 添加评论的弹窗
            commentData: {
                schedulePlanId: '',
                comments: ''
            },                     // 添加评论的表单
            pageFrom: this.pageParam.pageFrom,
            consumerTimeFlag: false,
            approvalFlag: false, //从企微预警卡片来的为true，否则为false
            isPrincipal: this.pageParam.isPrincipal ? this.pageParam.isPrincipal : false,
            isTequFlag: false, // 是否为特曲公司
        }
    },
    props: {},
    async created() {
        // 企微卡片进入
        let sceneObj = await this.$scene.ready(); // 消息场景对象
        if (sceneObj.query['scheduleId']) { // 从企微卡片来
            this.approvalFlag = true;
            this.scheduleId = sceneObj.query['scheduleId']
            this.rowId = sceneObj.query['schePlanId']
            this.acctId = sceneObj.query['acctId']
            // 查询小排期详情
            await this.queryDetail(this.rowId);
            // 查询消费者信息
            await this.queryConsumer(this.rowId);
            // 查询大排期
            await this.querySchedule();
            this.timeFlag();
        }
        if (this.pageParam.pageFrom === 'implementation' || this.pageParam.pageFrom === 'addConsumer') {
            this.rowId = this.pageParam.data.customersItem.id
            this.scheduleItem =  this.pageParam.data.scheduleItem
            this.customersItem = this.pageParam.data.customersItem
            this.timeFlag();
        }
        // 日历
        if (this.pageParam.pageFrom === 'calendar') {
            this.acctId = this.pageParam.acctId;
            this.scheduleId = this.pageParam.scheduleId;
            this.scheduleItem = this.pageParam.scheduleItem;
            this.rowId = this.pageParam.rowId;  // 排期消费者行id
            this.consumerTimeFlag = true;
            console.log('小结详情页acctId。scheduleId', this.acctId, this.scheduleId)
            // 查询小排期详情
            await this.queryDetail(this.rowId);
            // 查询是否是负责人
            await this.queryIsPrincipal();
            // 查询消费者信息
            // await this.queryConsumer();
            // 查询大排期
            // await this.querySchedule();
        }
        await this.queryScheduleComment();
        await this.queryLeader();
        this.$bus.$on('refreshWorkSummary', async (scheduleId, rowId, acctId, acctRowId) => {
            this.scheduleId = scheduleId;
            this.acctId = acctId;
            this.rowId = rowId;
            await this.queryDetail(rowId);
            await this.queryConsumer(acctRowId);
            await this.queryLeader();
            this.reloadSummaryDetail(rowId);
        });
        this.userInfo = Taro.getStorageSync('token').result;
        const isTequ = await this.$utils.getCfgProperty('SCHE_TEQU_VAL');
        // 判断是否为特曲公司
        this.isTequFlag = isTequ.split(',').some(item => item === this.userInfo.companyId);
        console.log('小结详情页scheduleItem', this.scheduleItem);
        console.log('小结详情页headData', this.headData);
        console.log('小结详情页customersItem', this.customersItem);
        console.log('小结详情页scheActionType', this.headData.scheActionType);
        console.log('小结详情页scheTargetType', this.headData.scheTargetType);
    },
    methods: {
        /**
         * @desc 刷新【执行明细】
         * <AUTHOR>
         * @date 2025-02-26
         **/
        reloadSummaryDetail(rowId) {
            this.$refs.implementSummaryDetail.reloadSummaryDetailList(rowId);
        },
        /**
         * @desc 查询是否是负责人
         * <AUTHOR>
         * @date 2025-01-16
         **/
        async queryIsPrincipal() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/queryMyPostnType',
                    scheInfoId: this.scheduleItem.id,    //排期消费者行id
                    businessType: 'chargePerson'
                });
                if(data.success && data.rows[0]) {
                    this.isPrincipal = data.rows[0].businessType === 'chargePerson';
                    console.log('是否是负责人1', this.isPrincipal, data.rows[0].businessType);
                } else {
                    this.isPrincipal = false;
                }
                console.log('是否是负责人2', this.isPrincipal);
            } catch (e) {
                this.$message.error('查询负责人失败', e);
                console.log('查询负责人出错', e);
            }
        },
        /**
         * @desc 判断当前时间处于排期消费者的开始时间和结束时间范围内
         * <AUTHOR>
         * @date 2025-01-08
         **/
        timeFlag() {
            console.log('')
            // 250121 因iso版本问题（15以下）会导致时间格式处理出错，iso15以下版本处理时间只能处理2025/01/21这种格式，当前数据格式为2025-01-21，故作处理
            const formatDate = (dateStr) => dateStr.replace(/-/g, '/');
            const currentTime = new Date();
            const startTime = new Date(formatDate(this.pageParam.conStartTime));
            const endTime = new Date(formatDate(this.pageParam.conEndTime));
            this.consumerTimeFlag = currentTime >= startTime && currentTime <= endTime;
            console.log('consumerTimeFlag', this.consumerTimeFlag);
        },
        getColorByStatus,
        /**
         * @desc 编辑排期
         * <AUTHOR>
         * @date 2025-02-12
         **/
        editScheduling() {
            console.log('跳转前this.headData', this.headData)
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-edit-page', {
                editFlag: true,  // 修改
                isAction: this.customersItem.scheRuleType === 'ScheAction',
                editData: this.customersItem,
                scheduleItem: this.scheduleItem,
                headData: this.headData,
                conStartTime: this.pageParam.conStartTime,
                conEndTime: this.pageParam.conEndTime,
                pageFrom: this.pageFrom,
                oauth: this.oauth,
            });
        },
        /**
         * @Description: 只展示创建人的后两位
         * @Author: 胡益阳
         * @Date: 2025/1/15
         */
        getLastTwoChars(str) {
            try {
                if (typeof str!== 'string') {
                    str = String(str)
                }
                if (str.length === 0) {
                    return '';
                } else if (str.length === 1) {
                    return str;
                } else {
                    return str.slice(-2);
                }
            } catch (error) {
                console.error('获取字符串后两位发生错误: ', error);
                return '';
            }
        },
        /**
         * @Description: 查询小排期小结
         * @Author: 胡益阳
         * @Date: 2025/1/13
        */
        async queryScheduleComment () {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleComment/queryScheduleComment',
                    schedulePlanId: this.customersItem.id,
                    sort: 'created',
                    order: 'desc'   // 倒序排序
                });
                if (data.success) {
                    this.scheduleCommentList = data.rows
                } else {
                    this.$message.warn('查询小排期小结失败', data.message);
                }
            } catch (e) {
                this.$message.error('查询小排期小结失败，请联系管理员', e);
            }
        },
        /**
         * @desc 查询运营详情-大排期
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async querySchedule() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: this.scheduleId
                });
                if(data.success) {
                    this.headData = data.result;
                    console.log('查询大排期this.headData', this.headData)
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询运营详情-小排期
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryDetail(rowId) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/queryById',
                    id: rowId
                });
                if(data.success) {
                    this.customersItem = data.result;
                    console.log('查询小排期customersItem', this.customersItem)
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询消费者信息
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryConsumer(acctRowId) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/queryTargetConsumerList',
                    scheInfoId: acctRowId,
                    scheduleId: this.scheduleId
                });
                if(data.success) {
                    this.scheduleItem = data.rows[0];
                    console.log('查询消费者信息scheduleItem', this.scheduleItem)
                } else {
                    this.$message.warn('查询消费者信息异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询消费者信息异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询协同领导
         * <AUTHOR>
         * @date 2025-02-14
         **/
        async queryLeader(rowId) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/queryByExamplePage',
                    scheInfoId: this.scheduleItem.id, //排期消费者行id
                    schePlanId: this.customersItem.id ? this.customersItem.id : rowId,  //排期计划id
                    businessType: 'leadership',
                    status: 'Y'
                });
                if(data.success) {
                    this.leaderData = data.rows;
                    console.log('查询协同领导leaderData', this.leaderData)
                } else {
                    this.$message.warn('查询协同领导信息异常，请联系管理员', data.message);
                }
            } catch (e) {
                console.log('查询协同领导信息异常', e)
                // this.$message.error('查询协同领导信息异常，请联系管理员', e);
            }
        },
        /**
         * @Description: 打开添加小结的弹窗
         * @Author: 胡益阳
         * @Date: 2025/1/10
         */
        openAddSummaryDialog (data) {
            this.showSuspendSchedule = ['Execution', 'Executing', 'ExeDelayed', 'ExeEarly'].includes(this.customersItem.planDeStatus)
        //     判断是负责人/执行人，展示添加小结，否则展示添加评论
            if (this.isPrincipal) {
                this.formData.schedulePlanId = this.customersItem.id;
                this.addSummaryFlag = true
            } else {
                this.commentData.schedulePlanId = this.customersItem.id;
                this.addCommentFlag = true
            }
        },
        /**
         * @desc 取消
         * <AUTHOR>
         * @date 2025-01-09
         **/
        cancel () {
            this.formData = {};
            this.addSummaryFlag = false;
        },
        /**
         * @desc 添加小结-确认
         * <AUTHOR>
         * @date 2025-01-09
         **/
        submit: ComponentUtils.debounce(async function() {
            try {
                if(this.$utils.isEmpty(this.formData.comments)){
                    this.$message.warn('请填写总结说明');
                    return
                }
                if (this.debounceFlag) return;
                if (!this.debounceFlag) {
                    this.debounceFlag = true;
                }
                let params = {
                    dmpUrl: '/link/scheduleComment/addComment',
                    commentType: 'Summary'                            // 小排期类型_小结
                }
                Object.assign(params, this.formData)
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', params);
                if (data.success) {
                    this.debounceFlag = false;
                    this.$message.success('添加小结成功！')
                    this.cancel();
                    await this.queryScheduleComment();
                } else {
                    this.$message.warn('添加小结异常，请联系管理员');
                }
            } catch (err) {
                this.$message.error('添加小结异常，请联系管理员', err);
            }
        }, 500),
        /**
         * @desc 添加评论-确认
         * <AUTHOR>
         * @date 2025-02-18
         **/
        commentSubmit: ComponentUtils.debounce(async function() {
            // 待联调
            try {
                if(this.$utils.isEmpty(this.commentData.comments)){
                    this.$message.warn('请填写评论说明');
                    return
                }
                if (this.debounceFlag) return;
                if (!this.debounceFlag) {
                    this.debounceFlag = true;
                }
                let params = {
                    dmpUrl: '/link/scheduleComment/addComment',
                    commentType: 'OtherReply'                            // 小排期类型
                }
                Object.assign(params, this.commentData)
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', params);
                if (data.success) {
                    this.debounceFlag = false;
                    this.$message.success('添加评论成功！')
                    this.commentCancel();
                    await this.queryScheduleComment();
                } else {
                    this.$message.error('添加评论异常，请联系管理员');
                }
            } catch (err) {
                this.$message.error('添加评论异常，请联系管理员', err);
            }
        }, 500),
        /**
         * @desc 添加评论-取消操作
         * @date 2025-02-18
         **/
        commentCancel() {
            this.commentData = {};
            this.addCommentFlag = false;
        }
    },
    computed: {},
    watch: {},
}
</script>

<style lang="scss">
@import './components/common-styles.scss';
.consumers-schedule-work-summary-detail-page {
    width: 100%;
    .form {
        width: 94%;
        margin: 0 auto;
        .form-item {
            border-radius: 16px;
            padding: 40px 28px 4px 28px;
            background: white;
            font-family: PingFangSC-Regular;
            .item-input {
                line-height: 30px;
                margin-bottom: 30px;
                width: 100%;
                display: flex;
                .label {
                    align-self: flex-start;
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                }
                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                    text-align: right;
                }
            }
            .item-textarea {
                line-height: 30px;
                width: 100%;
                margin-bottom: 30px;
                .label {
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                    margin-bottom: 18px;
                }
                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                }
            }
            .link-item {
                flex-direction: column;
            }
        }
    }
    .leader-ship {
        width: 94%;
        margin: 16px auto;
        .line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 16rpx;
            padding: 24rpx 28rpx;
            background: white;
            .name {
                font-size: 28px;
                color: #262626;
            }
            .add {
                font-size: 28px;
                color: #2F69F8;
            }
        }
        .leader-ship-data {
            display: flex;
            flex-direction: column;
            .leader-ship-item {
                display: flex;
                padding: 24px 28px;
                background: white;
                border-radius: 16px;
                margin-top: 16px;
                font-size: 28px;
                color: #262626;
                .num {
                    width: 30%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .name {
                    width: 65%;
                    margin-left: 12px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
    .work-summary-container {
        box-sizing: border-box;
        width: 94%;
        margin: 0 auto;
        border-radius: 16px;
        background: white;
        .work-summary-item {
            line-height: 30px;
            margin-bottom: 30px;
            width: 100%;
            display: flex;
            flex-direction: column;
        }
        .cell-box {
            width: 100%;
            display: flex;
            align-items: center;
            .cell-icon {
                width: 72px;
                height: 72px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                font-size: 26px;
                background: linear-gradient(to bottom, #80C0FF, #2F68EB);
                color: #FFFFFF;
            }
            .cell-title {
                max-width: 150px;
                margin-left: 16px;
                padding: 5px 12px;
                border-radius: 4px;
                font-size: 24px;
                color: #6A6D75;
                background-color: #eff2f6;
            //    超出隐藏
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .cell-desc {
                margin-left: 16px;
                font-size: 26px;
                color: #6A6D75;
                max-width: 110px;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
            .cell-time {
                color: #9EA1AE;
                font-size: 24px;
                margin: 0 10px;
                max-width: 210px;
                // 超出长度滑动展示
                flex-shrink: 0;
                overflow: auto;
                white-space: nowrap; /* 防止换行 */
            }
            .cell-status {
                display: flex;
                align-items: center;
                padding: 10px;
                max-width: 110px;
                background-color: #20c1bd;
                border-radius: 18px;
                color: #FFFFFF;
                font-size: 20px;
                // 超出长度滑动展示
                flex-shrink: 0;
                overflow: auto;
                white-space: nowrap; /* 防止换行 */
                //放在父盒子的最右边，向右对齐
                margin-left: auto;

                .dot {
                    width: 8px;
                    height: 8px;
                    border-radius: 50%;
                    background-color: #FFFFFF;
                    margin-right: 4px;
                }
            }
        }
        .cell-label {
            padding: 20px 0 20px 90px;
            font-size: 28px;
            color: #212223;
        }
    }

    .summary-title{
        margin-left: 24px;
        padding: 24px 0 0 0;
        display: flex;
        //@include flex-start-center;
        width: calc(100% - 24px);
        .line {
            width: 2%;
            // 垂直居中
            display: flex;
            align-items: center;
            flex-direction: column;
            padding-top: 8px;
            .line-top {
                width: 8px;
                height: 16px;
                background: #3FE0E2;
            }

            .line-bottom {
                width: 8px;
                height: 16px;
                background: #2F69F8;
            }
        }
        .title-info{
            display: flex;
            justify-content: space-between;
            width: 50%;
            align-items: center;
            .stair-title {
                margin-left: 16px;
                font-family: PingFangSC-Semibold, serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 1px;
                line-height: 32px;
                white-space: nowrap;
                min-width: 40%;
                width: auto;
            }
        }
        .add-button {
            display: flex;
            width: 40%;
            color: #2f69f8;
            // 向右对齐
            justify-content: flex-end;
            .icon-box {
                display: flex;
                justify-content: center;
                align-items: center;
                width: 48px;
                height: 48px;
                border-radius: 50%;
                background-color: #2f69f8;
            }
            .text {
                margin-left: 8px;
                font-size: 28px;
            }
        }
    }
    .link-dialog-content {
        border-radius: 24px !important;
    }
    .auto-height {
        .link-textarea-content {
            height: auto !important;
            min-height: auto !important;
        }
    }
    .add-summary {
        width: 100%;
        .text-area-box {
            width: 100%;
            height: 100%;
            position: relative;
            .text-area-len {
                position: absolute;
                right: 10px;
                bottom: 10px;
                font-size: 24px;
                color: #BFC1CA;
            }
            .link-textarea-content {
                padding: 24px 24px 45px;
            }
        }
        .button {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            .link-button {
                margin: 0 !important;
                width: 252px;
                height: 72px;
            }
        }
    }
}
</style>
