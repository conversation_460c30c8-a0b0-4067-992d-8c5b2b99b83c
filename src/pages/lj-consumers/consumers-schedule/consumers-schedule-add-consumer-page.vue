<!--
消费者-消费者排期运营-添加消费者
<AUTHOR>
@date 2025-
@file consumers-schedule-add-consumer-page.vue
-->
<template>
    <link-page class="consumers-schedule-add-consumer-page">
        <!-- 执行情况/添加消费者-顶部消费者展示 -->
        <target-customers :scheduleItem="scheduleItem"
                          :conStartTime="pageParam.data.conStartTime"
                          :conEndTime="pageParam.data.conEndTime"
                          :headData="headData">
        </target-customers>
        <view class="main-content">
            <!-- 动作可选 -->
            <implement-action-option-none v-if="headFlag && scheduleItem.id && scheduleItem.scheduleId && headData.scheActionType !== 'ActionPackageAll'"
                                          :scheduleItem="scheduleItem"
                                          :oauth="oauth"
                                          :headData="headData"
                                          :consumerTimeFlag="consumerTimeFlag"
                                          :isPrincipal="isPrincipal"
                                          :isTequFlag="isTequFlag"
                                          pageFrom="addConsumer"
                                          @editScheduling="editScheduling"
                                          ref="actionList2"
            ></implement-action-option-none>
            <!-- 目标可选 -->
            <implement-target-option-none v-if="headFlag && scheduleItem.id && scheduleItem.scheduleId && headData.scheTargetType !== 'TargetPackageAll'"
                                          :scheduleItem="scheduleItem"
                                          :oauth="oauth"
                                          :headData="headData"
                                          :isPrincipal="isPrincipal"
                                          pageFrom="addConsumer"
                                          :consumerTimeFlag="consumerTimeFlag"
                                          @editScheduling="editScheduling"
                                          ref="targetList2"
            ></implement-target-option-none>
        </view>
        <link-sticky style="width: 100%" v-if="headData.isAddApprove === 'Y' && isPrincipal">
            <link-button block @tap="submit">提交审批</link-button>
        </link-sticky>
        <link-sticky style="width: 100%" v-if="headData.isAddApprove === 'N' && isPrincipal">
            <link-button block @tap="submit">重新添加</link-button>
        </link-sticky>
        <link-fab-button icon="icon-plus" v-if="!(headData.scheActionType === 'ActionPackageAll' && headData.scheTargetType === 'TargetPackageAll') && isPrincipal" label="新建排期" @tap="chooseType"/>
        <link-dialog ref="chooseTypeFlag" v-model="chooseTypeFlag" title="请选择添加计划" width="80%" class="choose-type-flag">
            <view class="choose-type-dialog">
                <link-button class="button" @tap="addSchedule('ScheAction')">添加动作计划</link-button>
                <view class="zero-view"></view>
                <link-button class="button" @tap="addSchedule('ScheTarget')">添加目标计划</link-button>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
import TargetCustomers from "./components/target-customers.vue";
import ImplementActionOptionNone from "./components/implement-action-option-none.vue";
import ImplementTargetOptionNone from "./components/implement-target-option-none.vue";
import Taro from "@tarojs/taro";

export default {
    name: 'consumers-schedule-add-consumer-page',
    components: {ImplementActionOptionNone, TargetCustomers, ImplementTargetOptionNone},
    data() {
        return {
            scheduleItem: {},   // 排期消费者信息
            headData: {},       // 运营详情
            scheInfoId: '',     // 消费者行ID
            scheduleId: '',     // 排期ID
            oauth: '',          // 安全性
            consumerTimeFlag: false, // 当前时间处于排期消费者的开始时间和结束时间范围内
            // addScheduleFlag: false,
            formData: {
                scheduleClassName: '', // 运营大类
                scheduleSmallClassName: '', // 运营小类
                targetNum: 1, // 目标值
                planTimeType: 'ByDay', // 执行时间类型
                beginTime: '', // 预估执行时间
                endTime: '', // 预估执行结束时间
                // reachNum: '' // 运营小类的达成值
            }, // 新增运营排期-表单数据
            // tempPlanNum: 1, // 弹窗可填目标值
            scheduleClassList: [], // 运营排期大类数据
            scheduleSmallList: [], // 运营排期小类数据
            // reachNum: '', // 运营小类的达成值
            smallClassFlag: false, // 运营小类是否显示
            editFlag: false, // 是否可修改
            chooseResult: '', // 选择结果
            chooseTypeFlag: false, // 选择类型弹窗
            headFlag: false, // 头部数据是否加载完成
            isPrincipal: false, // 是否负责人
            isTequFlag: false, // 是否为特曲公司
        }
    },
    async created() {
        this.scheduleId = this.pageParam.scheduleId;
        this.scheInfoId = this.pageParam.scheInfoId;
        this.oauth = this.pageParam.oauth;
        await this.queryDetail(this.scheduleId);
        await this.queryConsumer();
        await this.queryIsPrincipal();
        this.timeFlag();
        this.userInfo = Taro.getStorageSync('token').result;
        const isTequ = await this.$utils.getCfgProperty('SCHE_TEQU_VAL');
        // 判断是否为特曲公司
        this.isTequFlag = isTequ.split(',').some(item => item === this.userInfo.companyId);
        console.log('scheduleItem', this.scheduleItem);
        console.log('headData', this.headData);
        console.log('scheActionType', this.headData.scheActionType);
        console.log('scheTargetType', this.headData.scheTargetType);
        this.$bus.$on('refreshAddList',(data)=>{
            if(data === 'ScheAction') {
                this.$refs.actionList2.reloadActionList();
            } else {
                this.$refs.targetList2.reloadTargetList();
            }
        });
    },
    beforeDestroy() {
        this.$bus.$off('refreshAddList', this.handleRefreshList);
    },
    methods: {
        /**
         * @desc 查询是否是负责人
         * <AUTHOR>
         * @date 2025-01-16
         **/
        async queryIsPrincipal() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/queryMyPostnType',
                    scheInfoId: this.scheduleItem.id,    //排期消费者行id
                    businessType: 'chargePerson',
                    status: 'Y'
                });
                if(data.success && this.$utils.isNotEmpty(data.rows[0])) {
                    this.isPrincipal = data.rows[0].businessType === 'chargePerson';
                } else {
                    this.isPrincipal = false;
                }
                console.log('是否负责人', this.isPrincipal);
            } catch (e) {
                this.$message.error('查询负责人失败', e);
            }
        },
        /**
         * @desc 提交审批
         * <AUTHOR>
         * @date 2024-09-09
         **/
        // async submitApproval() {
        //     this.$dialog({
        //         title: '提示',
        //         content: (h) => {
        //             return (
        //                 <view>
        //                     <view>提交后将进入审批流程，是否确认提交？</view>
        //                 </view>
        //             )
        //         },
        //         confirmText: '提交审批',
        //         cancelButton: true,
        //         onConfirm: () => {
        //             this.submitApprovalData();
        //         }
        //     })
        // },
        async v3AddCusumer(approvalType=''){
            const newId = await this.$newId();
             let flowStartPsnId;
            //获取超高需要的参数
                if(this.pageParam.scheduleCrowd=='HighValueAcct'){
                    flowStartPsnId = await this.getHighPId(this.userInfo.postnId)
                }
            this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                submitData: {  consumerInfoIds: [this.pageParam.data.id],id:newId,
                    positionType: this.userInfo.positionType,flowStartPsnId
                },
                submitUser: this.userInfo,
                flowObjId: newId,
                source: 'AddScheConsumerSubmit',
                // 审批类型编码
                approvalType
            });
        },
        async onBack(param) {
            if (param && param.flag === 'flow') {
                // this.$set(this.formData, 'nodeApprovers', param.nodeDtos);
                if(param.source === 'AddScheConsumerSubmit') {
                    await this.submitApprovalData(param.nodeDtos,param.preApprovalId);
                }
            }
        },
        /**
         * @desc 判断是否是v3需要审批
         * <AUTHOR>
         * @date 2025-03-06
         **/
         async isV3(applyType='AddScheConsumer') {
            return new Promise(async (res, rej) => {
                const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                    companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    applyType
                });
                console.log('result', result)
                if (success && result === 'v3') {
                    res(true);
                } else {
                    res(false);
                }
            });
        },
        async submit(){
            const isV3 = await this.isV3(this.pageParam.scheduleCrowd=='HighValueAcct'?'AddScheHighConsumer':'AddScheConsumer');
            if(isV3)return this.v3AddCusumer(this.pageParam.scheduleCrowd=='HighValueAcct'?'AddScheHighConsumer':'AddScheConsumer');
            this.submitApprovalData()
        },
          // 超高的审批要获取参数给到企微审批流
        async getHighPId(postnId){
           const data = await this.$http.post(this.$env.appURL+'/action/link/identityEmp/queryByPostnId',{postnId,"waitstaffType":"BusinessAgent"});
            if(data.success){
                return data.result&& data.result.headUserId
            } else {
                this.$message.error(data.result);
            }
        },
        /**
         * @desc 提交审批数据接口调用
         * <AUTHOR>
         * @date 2024-09-06
         **/
        async submitApprovalData(nodeDtos,approvalRecordId) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/batchAddApprove',
                    ...(nodeDtos && { nodeApprovers: nodeDtos,approvalRecordId }),
                    consumerInfoIds: [this.pageParam.data.id],
                });
                if (data.success) {
                    console.log('data', data);
                    //重新加载上一个页面的guestList
                    this.$bus.$emit('refreshList');
                    // 250121-因组件底层代码会导致偶发性报错，现改为提交审批后返回上一页面
                    // await this.queryConsumer();
                    // await this.$refs.actionList2.reloadActionList();
                    // await this.$refs.targetList2.reloadTargetList();
                    this.$nav.back();
                    this.$message.success('提交审批成功！');
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        },
        /**
         * @desc 添加排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async addSchedule(scheRuleType) {
            this.chooseResult = scheRuleType;
            this.chooseTypeFlag = false;
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-edit-page', {
                editFlag: false,  // 新增
                isAction: this.chooseResult === 'ScheAction',
                scheduleItem: this.scheduleItem,
                headData: this.headData,
                conStartTime: this.pageParam.data.conStartTime,
                conEndTime: this.pageParam.data.conEndTime,
                pageFrom: 'addConsumer',
            });
        },
        /**
         * @desc 修改排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async editScheduling(data, scheRuleType) {
            this.chooseResult = scheRuleType;
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-edit-page', {
                editFlag: true,  // 修改
                isAction: this.chooseResult === 'ScheAction',
                editData: data,
                scheduleItem: this.scheduleItem,
                headData: this.headData,
                pageFrom: 'addConsumer',
                conStartTime: this.pageParam.data.conStartTime,
                conEndTime: this.pageParam.data.conEndTime,
                oauth: this.oauth,
            });
        },
        /**
         * @desc 添加排期-选项
         * <AUTHOR>
         * @date 2025-01-09
         **/
        async chooseType(scheRuleType) {
            const scheAction = ['ActionPackageOption', 'ActionNone'].includes(this.headData.scheActionType);
            const scheTarget = ['TargetPackageOption', 'TargetNone'].includes(this.headData.scheTargetType);
            if (scheAction && scheTarget) {
                this.chooseTypeFlag = true;
            }
            if(scheAction && !scheTarget) {
               await this.addSchedule('ScheAction');
            }
            if(!scheAction && scheTarget) {
               await this.addSchedule('ScheTarget');
            }
        },
        /**
         * @desc 判断当前时间处于排期消费者的开始时间和结束时间范围内
         * <AUTHOR>
         * @date 2025-01-08
         **/
        timeFlag() {
            // 250121 因iso版本问题（15以下）会导致时间格式处理出错，iso15以下版本处理时间只能处理2025/01/21这种格式，当前数据格式为2025-01-21，故作处理
            const formatDate = (dateStr) => dateStr.replace(/-/g, '/');
            const currentTime = new Date();
            const startTime = new Date(formatDate(this.pageParam.data.conStartTime));
            const endTime = new Date(formatDate(this.pageParam.data.conEndTime));
            this.consumerTimeFlag = currentTime >= startTime && currentTime <= endTime;
        },
        /**
         * @desc 查询消费者信息
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryConsumer() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/queryById',
                    id: this.scheInfoId
                    // scheduleId: this.scheduleId
                });
                if(data.success) {
                    this.scheduleItem = data.result;
                } else {
                    this.$message.warn('查询消费者信息异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询消费者信息异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryDetail() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: this.scheduleId
                });
                if(data.success) {
                    this.headData = data.result;
                    this.headFlag = true;
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 根据当前tab刷新对应的列表
         * <AUTHOR>
         * @date 2025-01-12
         **/
        handleRefreshList() {
            // 根据当前tab刷新对应的列表
            if(this.chooseResult === 'ScheAction') {
                this.$refs.actionList2.reloadActionList();
            } else {
                this.$refs.targetList2.reloadTargetList();
            }
        }
    }
}
</script>

<style lang="scss">
@import './components/common-styles.scss';
.consumers-schedule-add-consumer-page {
    padding-top: 24px;
    .blank {
        width: 100%;
        height: 96px;
        background: #F2F2F2;
    }
    .choose-type-flag {
        border-radius: 16px !important;
        .choose-type-dialog {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
        }
        .button {
            border-radius: 16px;
        }
    }
    .link-textarea {
        background-color: white;
        padding: 0px !important;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }
    .fixed-height .link-textarea-content {
        height: 180px !important;
        min-height: 180px !important;
    }
}
</style>
