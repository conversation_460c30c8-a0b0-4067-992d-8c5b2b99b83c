<!--
消费者-消费者排期运营-详情
<AUTHOR>
@date 2024-07-01
@file consumers-schedule-detail-page.vue
-->
<template>
    <link-page class="consumers-schedule-detail-page">
        <view class="zero-view"></view>
        <!-- 基础数据  -->
        <view style="width: 94%;margin-left: 3%">
            <view class="form-column">
                <view v-for="item in details" :key="item.label" :class="item.type?'item-textarea':'item-input' ">
                    <view class="label">{{ item.label }}</view>
                    <view v-if="item.type!='item-textarea'" class="text">{{ item.text }}</view>
                    <link-textarea style="padding:0px !important;white-space:pre-wrap;" disabled v-if="item.type=='item-textarea'" v-model="item.text" mode="textarea"/>
                </view>
            </view>
        </view>
        <!-- 排期消费者门槛 -->
        <detail-threshold v-if="scheduleId" :scheduleId="scheduleId" :customersItem="customersItem"></detail-threshold>
        <view class="zero-view"></view>
        <!-- 运营明细  -->
        <detail-planning v-if="!!customersItem && !!scheduleId"
                         :customersItem="customersItem"
                         :scheduleId="scheduleId"
        ></detail-planning>
        <view class="zero-view" v-if="customersItem.scheTargetType !== 'TargetNone' || customersItem.scheActionType !== 'ActionNone'"></view>
        <!-- 排期消费者  -->
        <list class="guest-list">
            <view class="title">
                <view class="left">排期消费者</view>
                <view class="right" v-if="customersItem.scheStatus === 'Processing'" @tap="addGuest">
                    添加
                </view>
            </view>
            <view class="tab-filter">
                <view class="lnk-tabs-content">
                    <view class="lnk-tabs-item" :class="{'active': tab.seq === customersStatusActive.seq}"
                          v-for="(tab, index) in customersStatusList" :key="index" @tap="switchTabChoose(tab, index)">
                        <view class="label-name-line">
                            <text class="label-name-text">{{tab.name}}</text>
                            <view class="line" v-if="tab.seq === customersStatusActive.seq"></view>
                        </view>
                    </view>
                </view>
            </view>
            <link-checkbox-group v-model="tempConsumerIdList">
                <link-auto-list :option="guestList" hideCreateButton :searchInputBinding="{props:{placeholder:'消费者姓名/手机号'}}">
                    <template slot-scope="{data,index}">
                        <link-swipe-action>
                            <item :key="index" :data="data" style="border-bottom: 0.5px solid #f2f2f2;"
                                  @tap="gotoCustomers(data)">
                                <link-checkbox :val="data.id" slot="thumb" v-if="data.status === 'New' && data.endAuditStatus !== 'AddReviewing' && userInfo.postnId === data.chargePersonId"/>
                                <view slot="note" class="guest-list-item">
                                    <view class="logo-img">
                                        <image class="media-list-logo" :src="data|headImgAccount(data)"/>
                                        <view style="width: 32px;height: 12px; margin-top: -10px;">
                                            <image style="width: 100%;height: 100%;"
                                                   :src="data.addCertify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image>
                                        </view>
                                    </view>
                                    <view class="guest-list-item-content">
                                        <view class="guest-list-item-content-row1">
                                            <view class="consumer-name" :style="{ maxWidth: (customersItem.isAddApprove === 'Y' && data.endAuditStatus) ? 'calc(100% - 85px)' : '100%' }">
                                                {{data.consumerName ? data.consumerName : ''}}
                                            </view>
                                        </view>
                                        <view class="guest-list-item-content-row2">{{ data.consumerPhone ? data.consumerPhone : '' }}</view>
                                        <view class="guest-list-item-content-row2">
                                            排期时间：{{ data.conStartTime ? (data.conStartTime).substr(0, 10) : '' }} -
                                            {{ data.conEndTime ? (data.conEndTime).substr(0, 10) : '' }}
                                        </view>
                                    </view>
                                    <view class="content-status">
                                        <!--  颜色规则见lzlj-zhyx06-8410  -->
                                        <view v-if="(data.endAuditStatus === 'EndPass' && data.status === 'End') || ($utils.isEmpty(data.endAuditStatus) && data.status === 'New') || ($utils.isEmpty(data.endAuditStatus) && data.status === 'End') || (data.endAuditStatus === 'EndPass' && data.status === 'New') || (data.endAuditStatus === 'AddPass' && data.status === 'End')" :class="data.status === 'End' ? 'endPass' : 'blue'">{{data.status | lov('TARGET_ACCT_STATUS')}}</view>
                                        <view v-else-if="((data.status === 'New' || data.status === 'End') && ['AddReject', 'AddReviewing'].includes(data.endAuditStatus) || (data.status === 'Processing' && data.endAuditStatus === 'EndReviewing'))" class="blue">{{data.endAuditStatus | lov('SCHE_ADUIT_STATUS')}}</view>
                                        <view v-else-if="data.status === 'Processing' && ['AddPass', 'EndRejected'].includes(data.endAuditStatus)" :class="data.targetStatus === 'Finished' ? 'green' : 'red'">{{data.targetStatus | lov('TARGET_STATUS_SCHE')}}</view>
                                        <view v-else-if="data.status === 'Processing' && $utils.isEmpty(data.endAuditStatus)" :class="data.targetStatus === 'Finished' ? 'green' : 'red'">{{data.targetStatus | lov('TARGET_STATUS_SCHE')}}</view>
                                    </view>
                                </view>
                            </item>
                            <link-swipe-option label="终止" @tap="endConsumer(data)"
                                               v-if="data.status === 'Processing'
                                               && !(data.conEndTime < nowTime)
                                               && userInfo.postnId === data.chargePersonId
                                               && !['Closed', 'New', 'InActive'].includes(customersItem.scheStatus)"
                                               slot="option"/>
                            <link-swipe-option label="删除" @tap="deleteConsumer(data)"
                                               v-if="data.status === 'New'
                                               && userInfo.postnId === data.chargePersonId
                                               && ($utils.isEmpty(data.endAuditStatus) || data.endAuditStatus === 'AddReject')
                                               && !['Closed', 'New', 'InActive'].includes(customersItem.scheStatus)"
                                               slot="option"/>
                        </link-swipe-action>
                    </template>
                </link-auto-list>
            </link-checkbox-group>
        </list>
        <view class="bottom-button">
            <view class="text-bottom" v-if="tipsFlag && submitBtnFlag">提示：请进入详情页面维护排期动作计划/排期目标计划后提交审批</view>
            <link-button block slot="foot" @tap="submitApproval"
                         v-if="customersItem.isAddApprove === 'Y' && submitBtnFlag">提交审批
            </link-button>
        </view>
    </link-page>
</template>

<script>
import {$logger} from "@/utils/log/$logger"
import {ROW_STATUS} from "../../../utils/constant";
import {LovService} from "link-taro-component";
import StatusButton from "../../lzlj/components/status-button.vue";
import LnkTaps from "../../core/lnk-taps/lnk-taps";
import detailThreshold from "./components/detail-threshold.vue";
import detailPlanning from "./components/detail-planning.vue";

export default {
    name: 'consumers-schedule-detail-page',
    components: {StatusButton, LnkTaps, detailThreshold, detailPlanning},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        const filtersRawTemp = [{id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}];
        const guestList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send',
            },
            exactSearchFields: [
                {
                    field: 'consumerName',
                    showValue: '消费者姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },
                {
                    field: 'consumerPhone',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            filterOption: [
                {label: '审批状态', field: 'endAuditStatus', type: 'lov', lov: 'SCHE_ADUIT_STATUS', multiple: true},
                {label: '排期消费者状态', field: 'status', type: 'lov', lov: 'TARGET_ACCT_STATUS', multiple: true}
            ],
            scrollToTop: false,
            loadOnStart: false,
            pageSize: 10,
            param: {
                dmpUrl: '/link/consumerScheduleTarget/queryTargetConsumerList',
                filtersRaw: []
            },
            // sort: 'created',
            // order: 'desc',
            sortOptions: null,
            hooks: {
                async beforeLoad (option)  {
                    const filtersRaw = [
                        {id: 'scheduleId', property: 'scheduleId', value: this.scheduleId, operator: '='}
                    ]
                    option.param.filtersRaw = filtersRaw.concat(option.param.filtersRaw);
                    if(this.tempConsumerList.length > 0) {
                        this.submitBtnFlag = this.tempConsumerList.some(item => item.status === 'New');
                    }
                },
                afterLoad (data) {
                     // 遍历data.rows数据，如果数据中有status为New（待提交）的数据，submitBtnFlag为true
                    this.tempConsumerList = this.tempConsumerList.concat(data.rows);
                    if(this.tempConsumerList.length > 0) {
                        this.submitBtnFlag = this.tempConsumerList.some(item => item.status === 'New');
                    }
                }
            },
        });
        const customerOption = new this.AutoList(this, { // 所属客户
            module: this.$env.appURL + '/action/link/accnt',
            url: {
                queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryAccntPage'
            },
            param: {
                postnId: '',
                // oauth: this.pageParam.oauth,
                oauth: 'MY_POSTN_ONLY'
            },
            searchFields: ['acctName'],
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} className="select-box" arrow="false">
                        <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                        <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                        <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                    </item>)
            },
            hooks: {
                beforeLoad (option) {
                    delete option.param.sort;
                    delete option.param.order;
                    option.param.postnId = this.userInfo.postnId;
                    if(Array.isArray(option.param.filtersRaw) && option.param.filtersRaw.length === 0) {
                        delete option.param.filtersRaw;
                    }
                }
            }
        });
        const consumerOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            param: {
                sort: 'created',
                order: 'desc',
                dmpUrl: '/link/consumerScheduleTarget/queryTargetConsumer',
                filtersRaw: []
            },
            pageSize: 10,
            hooks: {
                beforeLoad(option) {
                    option.param.scheduleId = this.scheduleId;
                    option.param.oauth = 'MY_POSTN_ONLY';
                    if(Array.isArray(option.param.filtersRaw) && option.param.filtersRaw.length === 0) {
                        delete option.param.filtersRaw;
                    }
                    // wx.showLoading({
                    //     title: '加载中'
                    // });
                },
                // async afterLoad (option) {
                //     wx.hideLoading();
                // }
            },
            // filterOption: [
            //     {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'},
            //     {label: '消费者生日', field: 'birthdayMd', type: 'date', view: 'MD', format: 'MM-DD'}
            // ],
            sortOptions: null,
            exactSearchFields: [
                {
                    field: 'name',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }, {
                    field: 'phoneNumber',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            customClass: 'choose-consumer-list-page',
            slots: {
                // <tag-info show={this.tagDialogFlag} {...{on: {'update:show': val => this.tagDialogFlag = val}}} tagGroupList={this.tagGroupList} {...{on: {'choose': val => {this.chooseTagGroups(val)}}}}/>
                other: () => (
                    <view>
                        <link-fab-button onTap={this.addConsumer}></link-fab-button>
                    </view>),
                top: () => (<view style="position: relative;">
                        <view
                            style="width: 100%;position: absolute;top: -88px;display: flex;justify-content: space-around;flex-wrap: nowrap;">
                            {this.classifyList.map(tab => {
                                return (
                                    <view onTap={() => this.switchTab(tab)}
                                          style={tab.seq === this.classifyListActive.seq
                                              ? 'width: 25%;justify-content: center;align-items: center;flex-direction: column;color:#2f69f8; height: 46px;line-height: 46px; display: flex;'
                                              : 'width: 25%;justify-content: center;align-items: center;flex-direction: column; height: 46px;line-height: 46px; display: flex;'}>
                                        <view
                                            style="font-size: 14px;margin-left: 5px;display: flex;flex-direction: column;align-items: center;justify-content: center;">
                                            <text>{tab.name}</text>
                                            {tab.seq === this.classifyListActive.seq
                                                ? <view
                                                    style="height: 4px;width: 14px;border-radius: 8px 8px 0 0;background-color: #2f69f8;box-shadow: 0 1.5px 4px 0 rgba(47,105,248,0.63);margin-top: -4px;"></view>
                                                : ''
                                            }
                                        </view>
                                    </view>
                                )
                            })}
                        </view>
                        {
                            this.classifyItemList.length > 0 ?
                                <view>
                                    <scroll-view scroll-x="true">
                                        <view style="height: 36px; display: flex;align-items: center;">
                                            {
                                                this.classifyItemList.map(tabItem => {
                                                    return (
                                                        <view onTap={() => this.switchTabItem(tabItem)}
                                                              style={tabItem.seq === this.classifyItemListActive.seq
                                                                  ? 'min-width: 85px;height: 28px;box-sizing: border-box;margin: 0 0 8px 12px;font-size:12px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 28px; font-weight: 400;border: 0.5px solid rgba(63,102,239,1);border-radius: 14px;'
                                                                  : 'min-width: 85px;height: 28px;box-sizing: border-box;margin: 0 0 8px 12px;font-size:12px;color: #333333;letter-spacing: 0;text-align: center;line-height: 28px; font-weight: 400;border: 0.5px solid rgba(221,221,221,1);border-radius: 14px;'}>
                                                            {tabItem.name}
                                                        </view>
                                                    )
                                                })
                                            }
                                        </view>
                                    </scroll-view>
                                </view> : ''
                        }
                    </view>
                ),
                filterGroup: () => (
                    <view style="flex: 1;overflow-x: hidden;">
                        <scroll-view style="width: 100%;">
                            <view
                                style="display: flex;white-space: nowrap;font-size: 24px;padding: 4px 12px;align-items: center;flex-wrap: nowrap;">
                                <view onTap={() => this.chooseStoreData()} style={this.isStoreChosen
                                    ? 'width:82px; height: 20px;margin: 6px 2px;background: #EDF3FF;background: #EDF3FF;color: #2F69F8;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'
                                    : 'margin: 7.5px 0;height: 25px; line-height: 25px;border-radius: 3px;padding: 0 10px;max-width: 62px;color: #333333;height: 20px;margin: 6px 2px;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'}>
                                    所属客户
                                    <link-icon icon="mp-desc"
                                               style="width: 8px;height: 6px;color: #CCCCCC;margin-left: 4px;"/>
                                </view>
                                <view
                                    style="width: 1px;height: 16px;position: absolute;right: 2px;top: 12px;background-color: #CCCCCC;border: 1px solid rgba(204,204,204,1);"></view>
                            </view>
                        </scroll-view>
                    </view>
                )
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} arrow="false" style="overflow: hidden; margin: 12px;">
                        <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                        <view style="position: relative;-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%; padding: 16px 12px 12px 12px;-webkit-flex-direction: column;-ms-flex-direction: column;flex-direction: column;">
                            <view style="height: 24px;font-family: PingFangSC-Semibold;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;display: flex;">
                                <view style="overflow: hidden; max-width: 180px;text-overflow:ellipsis; white-space: nowrap;">{ data.name}</view>
                                {data.certify ? <view style=" margin-left: 10px;width: 60px;height: 24px;"><image style="width: 100%;height: 100%;" src={data.certify === 'certified' ? this.$imageAssets.storeStatusVerifiedImage : this.$imageAssets.storeStatusUnverifiedImage}></image></view> : ''}
                            </view>
                            <view style="height: 18px;margin: 8px 0;display: flex;">
                                <view
                                    style="min-width: 56px;padding: 0 8px;margin-right: 8px;background: #F0F5FF;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 18px;font-weight: 400;">{LovService.filter(data.type, 'ACCT_SUB_TYPE')}</view>
                                <view
                                    style="min-width: 56px;padding: 0 8px;margin-right: 8px;background: #F0F5FF;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 18px;font-weight: 400;">{LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL')}</view>
                                {data.impFlag === 'Y' ? <view
                                    style="min-width: 56px;width: 56px;margin-right: 8px;background: #FFF1EB;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #FF461E;line-height: 18px;font-weight: 400;text-align: center;">重点客户</view> : ''}
                                {!!data.identityLevel ? <view
                                    style="width: auto; padding: 0 8px; background: #262626; border-radius: 2px; font-size: 11px; color: #F0BE94; line-height: 18px; font-weight: 400; text-align: center; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">{LovService.filter(data.identityLevel, 'ACCT_SUB_TYPE')}</view> : ''}
                            </view>
                            <view style="display: flex;flex-direction: column;">
                                <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                    <view
                                        style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">联系方式</view>
                                    <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #317DF7;line-height: 22px;font-weight: 400;">{data.phoneNumber}</view>
                                </view>
                                <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                    <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">单位</view>
                                    <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{data.companyName}</view>
                                </view>
                                <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                    <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">职务</view>
                                    <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{data.position}</view>
                                </view>
                                <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                    <view style="width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">所属客户</view>
                                    <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;width: 180px;white-space: nowrap;overflow: hidden;text-overflow: ellipsis;">{data.belongToStore}</view>
                                </view>
                            </view>
                        </view>
                        <view style="position: absolute; right: -5px;top:0;">
                            <view style="background: #2F69F8;min-width: 72px;color: white;letter-spacing: 0;text-align: center;text-decoration: none;height: 20px;transform: skew(30deg, 0);display: flex;justify-content: center;align-items: center;border-radius: 3px;padding: 0 8px 0 16px;">
                                <view style="font-size: 12px; transform: skew(-30deg, 0);color: #FFFFFF;">{ data.fstName }跟进</view>
                            </view>
                        </view>
                    </item>
                )
            }
        });
        const customersStatusList = [
            {name: '全部', value: 'ALL', seq: '0'},
            {name: '已达成', value: 'finished', seq: '1'},
            {name: '未达成', value: 'failed', seq: '2'}
        ];
        return {
            details: [],    // 基础字段展示
            customersItem: {},  // 详情数据
            guestList,  // 嘉宾名单
            customersStatusActive: {},
            customerOption,
            consumerOption,
            customersStatusList,
            userInfo,
            maxSel: 10,
            scheduleId: '', //运营id
            classifyList: [
                {val: 'ALL', name: '全部', seq: '1', field: ''},
                {val: 'certified', name: '已认证', seq: '2', field: 'certify'},
                {val: 'uncertified', name: '未认证', seq: '3', field: 'certify'},
                {val: 'type', name: 'K序列', seq: '4', field: 'type'},
                {val: 'ACCT_MEMBER_LEVEL', name: 'V序列', seq: '5', field: 'loyaltyLevel'}
            ],
            classifyListActive: {val: 'ALL', name: '全部', seq: '1', field: ''},
            classifyItemListActive: {},
            classifyItemList: [],
            tagIdList: [],
            tagDialogFlag: false,                     // 标签筛选弹窗
            tagGroupList: [],
            isGuoJiao: false,     // 判断是否查询国窖系公司的分类分级值列表
            isStoreChosen: false,
            editPath: '/pages/lj-consumers/account/account-item-edit-page',
            filtersRawTemp,
            endReasonText: '',    // 终止原因
            consumerInfoId: '',    // 选中的消费者id
            tempConsumerIdList: [],// 选中的消费者id
            oauth: this.pageParam.oauth,
            tipsFlag: false,       // 审批提示语
            submitBtnFlag: false,   // 提交审批按钮，有提交数据时才展示，status为New
            tempConsumerList:[],        // 临时消费者列表
            nowTime: '',
            isGotoPreCreate: 'N',  // 是否跳转审批预览页面
            headUserId: null,
            nodeApprovers: [],
            endScheConsumerIsV3: false,
            endScheConsumerData: {}
        }
    },
    async mounted() {
        this.$bus.$on('queryScheduleConsumer', async () => {
            await this.consumerOption.methods.reload();
        })
        // 变更了执行情况，刷新列表
        this.$bus.$on('refreshList', async () => {
            console.log('详情refreshList');
            await this.guestList.methods.reload();
        })
        this.maxSel = await this.$utils.getCfgProperty('SCHE_APPROVAL_MAX');
    },
    async created() {
        console.log('oauth:', this.oauth);
        // 企微预警
        let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
        if(this.pageParam.source === 'list') {
            this.scheduleId = this.pageParam.data.id;
            this.customersItem = this.pageParam.data ? this.pageParam.data : {};
            console.log('this.customersItem:', this.customersItem);
            this.guestList.option.param.oauth = this.pageParam.oauth;
            this.customersStatusActive = this.customersStatusList[0];
        }
        if(sceneObj.query['scheduleId']) {
            this.scheduleId = sceneObj.query['scheduleId'];
            await this.queryDetail();
            this.customersStatusActive = this.customersStatusList[2];
            this.guestList.option.param.oauth = 'MY_POSTN_ONLY';
            this.guestList.option.param.filtersRaw = [
                {id: 'targetStatus', property: 'targetStatus', value: 'failed', operator: '='}
            ];
        }
        // 获取当前系统时间：当前系统时间大于消费者排期结束时间
        const nowTime = await this.$utils.getServerTime();
        this.nowTime = this.$date.format(new Date(nowTime), 'YYYY-MM-DD HH:mm:ss')
        await this.initData();
        if((['ActionPackageOption', 'ActionNone'].includes(this.customersItem.scheActionType) || ['TargetPackageOption', 'TargetNone'].includes(this.customersItem.scheTargetType)) && this.customersItem.isAddApprove === 'Y') {
            this.tipsFlag = true;
        }
        this.guestList.methods.reload();
    },
    methods: {
        /**
         * @desc 判断是否是v3需要审批
         * <AUTHOR>
         * @date 2025-03-06
         **/
        async isV3(applyType='AddScheConsumer') {
            return new Promise(async (res, rej) => {
                const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                    companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    applyType
                });
                console.log('result', result)
                if (success && result === 'v3') {
                    res(true);
                } else {
                    res(false);
                }
            });
        },
        /**
         * @Author: 胡政民
         * @Date: 2025-05-15 17:43:29
         * @method: queryByPostinId
         * @LastEditTime: Do not edit
         * @return {*}
         * @description:
         */
         async queryByPostinId() {
            const {success, result} = await this.$http.post(this.$env.appURL + '/action/link/identityEmp/queryByPostnId', {
                postnId: this.userInfo.postnId,
                waitstaffType:"BusinessAgent"
            });
            if(success) {
                return result.headUserId;
            } else {
                this.$showError('查询审批人失败！' + result);
            }
         },
        /**
         * @desc 判断是否需要唤起预览页
          * @autho        r 何春霞
         * @date 2025-03-20
         **/
        async gotoPreCreateFlow(data) {
            const {success, result} = await this.$http.post('action/link/flow/v3/preCreateFlow', {
                // 审批对象ID
                objectId: data.id,
                // 审批类型编码
                approvalType: this.customersItem.scheduleCrowd=='HighValueAcct'?'EndScheHighConsumer':'EndScheConsumer',
                // 业务对象JSON字符串
                flowObjDetail: JSON.stringify(data),
                // flowObjId传订单id
                flowObjId: data.id,
                // 提交用户id
                submitUserId: this.userInfo.id,
                // 提交用户职位id
                submitPostnId: this.userInfo.postnId,
                // 提交用户组织id
                submitOrgId: this.userInfo.orgId,
                // 提交审批人id
                flowStartPsnId: this.headUserId
            });
            if(success) {
                return result.jumpApproval;
            } else {
                this.$showError('预生成审批记录失败！' + result);
            }
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryDetail() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: this.scheduleId
                });
                if(data.success) {
                    this.customersItem = data.result;
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 初始化数据
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async initData() {
            this.$utils.showLoading();
            // 详情头初始化
            console.log("this.customersItem:",this.customersItem)
            this.details = [
                {label: '排期运营名称', text: this.customersItem.scheduleName ? this.customersItem.scheduleName : '-'},
                {label: '运营开始时间', text: this.customersItem.startTime ? this.customersItem.startTime : '-'},
                {label: '运营结束时间', text: this.customersItem.endTime ? this.customersItem.endTime : '-'},
                {label: '期限（天）', text: this.customersItem.term ? this.customersItem.term + '天' : '-'},
                {label: '排期导向', text: this.customersItem.scheGuide ? (await Promise.all(this.customersItem.scheGuide.split(',').map(async guideType => await this.$lov.getNameByTypeAndVal('SCHE_GUIDE', guideType)))).join(', ') : '-'},
                {label: '排期人群', text: this.customersItem.scheduleCrowd ?  await this.$lov.getNameByTypeAndVal('SCHEDULE_CROWD', this.customersItem.scheduleCrowd) : '-'},
                {label: '运营动作', text: this.customersItem.scheActionType ?  await this.$lov.getNameByTypeAndVal('SCHEDULE_ACTION_TYPE', this.customersItem.scheActionType) : '-'},
                {label: '运营目标', text: this.customersItem.scheTargetType ?  await this.$lov.getNameByTypeAndVal('SCHEDULE_TARGET_TYPE', this.customersItem.scheTargetType) : '-'},
                {label: '排期运营说明', text: this.customersItem.scheduleDesc ? this.customersItem.scheduleDesc.replace(/↵/g, '\n') : '' ,type:'item-textarea'},
               ]
            this.$utils.hideLoading();
        },
        /**
         * @desc 删除消费者
         * <AUTHOR>
         * @date 2024-12-17
         **/
        async deleteConsumer(data) {
            this.$dialog({
                title: '提示',
                content: '确定删除消费者吗？',
                confirmText: '确定',
                onConfirm: async () => {
                    await this.deleteConsumerInfo(data);
                },
                cancelButton: true
            })
        },
        async deleteConsumerInfo(data) {
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/consumerScheduleTarget/remove',
                id: data.id
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`删除消费者失败：${response.result}`);
                }
            });
            if (res) {
                this.$message.success('删除消费者成功！');
                await this.guestList.methods.reload();
            }
        },
        /**
         * @desc 终止消费者
         * <AUTHOR>
         * @date 2024-07-31
         **/
        async endConsumer(data) {
            if(data.isEndAcct === 'N') {
                // 不需要走审批
                await this.cancelConsumer(data);
            }
            if(data.isEndAcct === 'Y') {
                // 需要走审批

                this.endReasonText = '';
                this.$dialog({
                    title: '提示',
                    content: (h) => {
                        return (
                            <view>
                                <view>终止后代表消费者不再参与排期运营，是否确认终止？</view>
                                <link-form-item required label="终止原因" vertical>
                                    <link-textarea placeholder="请填写申请说明，100字以内" padding-start padding-end
                                                   v-model={this.endReasonText} nativeProps={{maxlength: 100}}>
                                    </link-textarea>
                                </link-form-item>
                            </view>
                        )
                    },
                    confirmText: '提交终止申请',
                    cancelButton: true,
                    onConfirm: async () => {
                        console.log('data2', data);
                        this.endScheConsumerData = data;
                        this.endScheConsumerIsV3 = await this.isV3(this.customersItem.scheduleCrowd=='HighValueAcct'?'EndScheHighConsumer':'EndScheConsumer');
                        console.log('判断审批类型走V3还是V2', this.endScheConsumerIsV3);
                        if(this.endScheConsumerIsV3) {
                            const newId = data.newId = await this.$newId();
                            data.positionType = this.userInfo.positionType;
                            console.log('this.userInfo.positionType', this.userInfo.positionType);
                            this.headUserId = data.flowStartPsnId = await this.queryByPostinId();
                            this.isGotoPreCreate = await this.gotoPreCreateFlow(data);
                            console.log('走V3', this.isGotoPreCreate);
                            if (this.isGotoPreCreate === 'Y') {
                                await this.confirmEndConsumer(data, this.endScheConsumerIsV3);
                            } else {
                                console.log('submitData2', data);
                                this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                                    submitData: data,
                                    submitUser: this.userInfo,
                                    flowObjId: newId,
                                    source: 'EndScheConsumerSubmit',
                                    // 审批类型编码
                                    approvalType: this.customersItem.scheduleCrowd=='HighValueAcct'?'EndScheHighConsumer':'EndScheConsumer'
                                });
                            }
                        } else {
                            await this.confirmEndConsumer(data, this.endScheConsumerIsV3);
                        }
                    }
                })
            }
        },
        /**
         * @desc 调用-终止消费者接口-不需要走审批
         * <AUTHOR>
         * @date 2024-07-31
         **/
        async cancelConsumer(data) {
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/consumerScheduleTarget/cancel',
                id: data.id
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`终止消费者失败：${response.result}`);
                }
            });
            if (res) {
                this.$message.success('终止消费者成功！');
                await this.guestList.methods.reload();
            }
        },
        /**
         * @desc 调用-终止消费者接口-需要走审批
         * <AUTHOR>
         * @date 2024-07-31
         **/
        async confirmEndConsumer(data, isV3) {
            let param = {};
            if(isV3) {
                param = {
                    id: data.id,
                    dmpUrl: '/link/consumerScheduleTarget/terminate',
                    endReason: this.endReasonText,
                    nodeApprovers: this.nodeApprovers,
                    approvalRecordId: data.newId,
                    jumpApproval: this.isGotoPreCreate
                }
            } else {
                param = {
                    id: data.id,
                    dmpUrl: '/link/consumerScheduleTarget/terminate',
                    endReason: this.endReasonText
                }
            }
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`终止消费者失败：${response.result}`);
                }
            });

            if (res) {
                this.$message.success('终止消费者审批提交成功！');
                await this.guestList.methods.reload();
            }
        },
        /**
         * @desc 选择数据
         * <AUTHOR>
         * @date 2023/7/7 14:57
         **/
        async switchTabChoose (tab) {
            this.customersStatusActive = tab;
            this.tempConsumerList = [];
            let filtersRaw = this.$utils.deepcopy(this.guestList.option.param.filtersRaw);
            if (tab.seq === '0') {
                filtersRaw = filtersRaw.filter(item => item.property !== 'targetStatus');
            } else {
                filtersRaw = filtersRaw.filter(item => item.property !== 'targetStatus');
                filtersRaw.push({id: 'targetStatus', property: 'targetStatus', value: tab.value, operator: '='});
            }
            this.guestList.option.param.filtersRaw = filtersRaw;
            await this.guestList.methods.reload();
        },
        /**
         * @desc 添加嘉宾
         * <AUTHOR>
         * @date 2022/3/31 11:55
         **/
        async addGuest () {
            this.$utils.showLoading()
            this.isStoreChosen = false;
            // this.tagIdList = [];
            // if (this.consumerOption.option.param.tagIdList) {
            //     delete this.consumerOption.option.param.tagIdList;
            // }
            // await this.queryTagGroups();
            this.$utils.hideLoading()
            const list = await this.$object(this.consumerOption, {
                pageTitle: '消费者',
                multiple: true,
                beforeConfirm: async (rows) => {
                    if (rows.length > 10) {
                        this.$showError('最多不能超过10条数据')
                        return Promise.reject()
                    }
                }
            });
            let consumerScheduleInfos = [];
            console.log('list', list);
            consumerScheduleInfos = list.map(item => ({
                scheduleId: this.scheduleId,
                addSource: 'SalesmanAdd',
                acctId: item.id
            }));
            // this.$utils.showLoading()
            const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/batchInsert',
                    consumerScheduleInfos
                },
                {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        setTimeout(() => {
                            this.$utils.hideLoading();
                            setTimeout(() => {
                                this.$message.warn('添加排期消费者失败！' + response.result);
                            }, 1000);
                        }, 1000)
                    }
                });
            if (data.success) {
                this.$utils.hideLoading();
                setTimeout(() => {
                    this.$message.success('添加排期消费者成功！');
                }, 1000);
                this.guestList.methods.reload();
            }
        },
        /**
         * @desc 添加排期消费者-调接口
         * <AUTHOR>
         * @date 2025-02-14
         **/
        async addConsumerApi(consumerScheduleInfos) {
            const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/insertWithRules',
                    ...consumerScheduleInfos
                },
                {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        setTimeout(() => {
                            this.$utils.hideLoading();
                            setTimeout(() => {
                                this.$message.warn('添加排期消费者失败！' + response.result);
                            }, 1000);
                            return Promise.reject(response.result);
                        }, 1000)
                    }
                });
            if (data.success) {
                this.$utils.hideLoading();
                setTimeout(() => {
                    this.$message.success('添加排期消费者成功！');
                }, 1000);
                this.guestList.methods.reload();
            }
        },
        /**
         * @desc 查询标签组信息
         * <AUTHOR>
         * @date 2022/8/22 10:51
         **/
        async queryTagGroups() {
            const data = await this.$http.post(this.$env.dmpURL + '/link/portalAccntTagGroup/queryTagGroups', {
                validFlag: 'Y',
                queryItemFlag: 'Y',         // 查询标签值
                labelType: 'CustomLabel',
                labelRange: 'GroupAndBrandLabel',
                companyId: this.userInfo.coreOrganizationTile['l3Id']
            });
            if (data.rows.length > 0) {
                data.rows.forEach((item) => {
                    item.checked = true;
                    item.showMore = false;
                    item.editFlag = false;
                    item.selectAllFlag = false;
                    if (!this.$utils.isUndefined(this.tagLabels)) {
                        item.editFlag = true;
                        for (const tagPart of this.labelField) {
                            if(tagPart.id === 'tag' + item.id) item.required = tagPart.required;
                        }
                        if (item.tagItemList.length > 0) {
                            item.tagItemList.forEach((tag) => {
                                tag.checked = false;
                                delete tag.id;
                            });
                        }
                        this.tagLabels.forEach((group) => {
                            group.itemTags.forEach((tag) => {
                                const flag = item.tagItemList.find((label) => label.tagId === tag.tagId);
                                if(flag) {
                                    flag.checked = true;
                                    flag.id = tag.id;
                                    flag.ifActive = tag.ifActive;
                                }
                            })
                        });
                    } else {
                        if (item.tagItemList.length > 0) {
                            item.tagItemList.forEach((tag) => {
                                tag.checked = false;
                            });
                        }
                    }
                });
                this.tagGroupList = data.rows;
            }
        },
        /**
         * @desc 选择所属客户数据
         * <AUTHOR>
         * @date 2022/5/27 11:21
         **/
        async chooseStoreList () {
            this.isStoreChosen = !this.isStoreChosen
            if (this.isStoreChosen) {
                const list = await this.$object(this.customerOption, {
                    pageTitle: '请选择所属客户',
                    showInDialog: true,
                    multiple: false
                });
                return list.id;
            } else {
                return false
            }
        },
        /**
         * @desc 选择所属门店信息
         * <AUTHOR>
         * @date 2022/7/5 09:33
         **/
        async chooseStoreData () {
            const data = await this.chooseStoreList();
            let filtersRaw = this.consumerOption.option.param.filtersRaw;
            if (data) {
                this.isStoreChosen = true;
                if (filtersRaw.length === 0) {
                    filtersRaw.push({id: 'belongToStoreId', property: 'belongToStoreId', value: data, operator: '='})
                } else {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'belongToStoreId'
                    })) {
                        filtersRaw.push({id: 'belongToStoreId', property: 'belongToStoreId', value: data, operator: '='})
                    }
                    for (let i = 0; i < filtersRaw.length; i++) {
                        if (filtersRaw[i].property === 'belongToStoreId') {
                            filtersRaw[i].value = data;
                            filtersRaw[i].operator = '=';
                        }
                    }
                }
            } else {
                this.isStoreChosen = false;
                for (let i = 0; i < filtersRaw.length; i++) {
                    if (filtersRaw[i].property === 'belongToStoreId') {
                        filtersRaw.splice(i, 1);
                    }
                }
            }
            this.consumerOption.option.param.filtersRaw = filtersRaw;
            this.consumerOption.methods.reload();
        },
        /**
         * @desc 筛选数据
         * <AUTHOR>
         * @date 2022/6/1 10:19
         **/
        switchTabItem(item) {
            this.classifyItemListActive = item;
            let filtersRaw = this.consumerOption.option.param.filtersRaw;
            if (this.classifyListActive.field === 'type') {
                if (filtersRaw.length === 0) {
                    filtersRaw.push({id: 'type', property: 'type', value: item.type, operator: '='})
                } else {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'type'
                    })) {
                        filtersRaw.push({id: 'type', property: 'type', value: item.type, operator: '='})
                    }
                    for (let i = 0; i < filtersRaw.length; i++) {
                        if (filtersRaw[i].property === 'type') {
                            filtersRaw[i].value = item.type;
                            filtersRaw[i].operator = '=';
                        }
                        if (filtersRaw[i].property === 'loyaltyLevel') {
                            filtersRaw.splice(i, 1);
                        }
                    }
                }
            } else if (this.classifyListActive.field === 'loyaltyLevel') {
                if (!filtersRaw.some((item1) => {
                    return item1.property === 'loyaltyLevel'
                })) {
                    filtersRaw.push({id: 'loyaltyLevel', property: 'loyaltyLevel', value: item.val, operator: '='})
                } else {
                    for (let i = 0; i < filtersRaw.length; i++) {
                        if (filtersRaw[i].property === 'loyaltyLevel') {
                            filtersRaw[i].value = item.val;
                            filtersRaw[i].operator = '=';
                            break;
                        }
                    }
                }
            }
            this.consumerOption.list = [];
            this.consumerOption.option.param.filtersRaw = filtersRaw;
            this.consumerOption.methods.reload();
        },
        /**
         * @desc 级联分类分级
         * <AUTHOR>
         * @date 2022/6/1 10:14
         **/
        async switchTab(item) {
            this.classifyListActive = item;
            if (item.val === 'ACCT_MEMBER_LEVEL') {
                const lovData = await this.$lov.getLovByType(item.val);
                if (this.isGuoJiao) {
                    this.classifyItemList = await this.$lov.getLovByParentTypeAndValue({
                        type: item.val,
                        parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                        parentVal: this.userInfo.coreOrganizationTile['l3Id']
                    });
                } else {
                    this.classifyItemList = lovData.filter(item => !item.parentId);
                }
                this.classifyItemListActive = {};
                this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                this.consumerOption.methods.reload();
            } else if (item.val === 'type') {
                await this.queryTypeList();
                this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                this.consumerOption.methods.reload();
            } else if (item.val === 'certified') {
                this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                    id: 'certify',
                    property: 'certify',
                    value: 'certified',
                    operator: '='
                }]);
                this.consumerOption.list = [];
                this.consumerOption.methods.reload();
            } else if (item.val === 'uncertified') {
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                    id: 'certify',
                    property: 'certify',
                    value: 'uncertified',
                    operator: '='
                }]);
                this.consumerOption.list = [];
                this.consumerOption.methods.reload();
            } else {
                this.classifyItemList = [];
                this.classifyItemListActive = {};
                this.consumerOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                this.consumerOption.methods.reload();
            }
        },
        /**
         * @desc 查询类型数据
         * <AUTHOR>
         * @date 2022/6/1 15:27
         **/
        async queryTypeList() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                oauth: 'ALL',
                pageFlag: true,
                rows: 500,
                page: 1,
                distinctFields: 'type',
                filtersRaw: [
                    {
                        id: 'companyId',
                        property: 'companyId',
                        value: this.userInfo.coreOrganizationTile['l3Id'],
                        operator: '='
                    },
                    {id: 'status', property: 'status', value: 'Active', operator: '='}]
            });
            if (data.success) {
                for (const item of data.rows) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                    this.$set(item, 'name', name);
                }
                data.rows.forEach((item, index) => {
                    this.$set(item, 'seq', index + 1)
                });
                this.classifyItemList = data.rows;
            }
        },
        async v3AddCusumer(approvalType=''){
            const newId = await this.$newId();
            if (this.tempConsumerIdList.length > 1) {
                this.submitApprovalData(); // v3多个批量添加不预览
                return;
            }
            let flowStartPsnId;
            //获取超高需要的参数
                if(this.customersItem.scheduleCrowd=='HighValueAcct'){
                    flowStartPsnId = await this.getHighPId(this.userInfo.postnId)
                }
            this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                submitData: {  consumerInfoIds: this.tempConsumerIdList,id: newId,
                    positionType: this.userInfo.positionType,flowStartPsnId
                },
                submitUser: this.userInfo,
                flowObjId: newId,
                source: 'AddScheConsumerSubmit',
                // 审批类型编码
                approvalType
            });
        },
        /**
         * @desc 提交审批
         * <AUTHOR>
         * @date 2024-09-06
         **/
        async submitApproval() {
            console.log('tempConsumerIdList', this.tempConsumerIdList);
            if (this.tempConsumerIdList.length === 0) {
                this.$message.warn('请选择消费者！');
                return;
            }
            console.log(this.maxSel,'this.maxSel')
            if (this.tempConsumerIdList.length > this.maxSel) {
                this.$message.warn(`选择消费者数量不能大于${this.maxSel}！`);
                return;
            }
            this.$dialog({
                title: '提示',
                content: (h) => {
                    return (
                        <view>
                            <view>提交后将进入审批流程，是否确认提交？</view>
                        </view>
                    )
                },
                confirmText: '提交审批',
                cancelButton: true,
                onConfirm: async () => {
                    const isV3 = await this.isV3( this.customersItem.scheduleCrowd=='HighValueAcct'?'AddScheHighConsumer':'AddScheConsumer');
                    if(isV3)return this.v3AddCusumer(this.customersItem.scheduleCrowd=='HighValueAcct'?'AddScheHighConsumer':'AddScheConsumer');
                    await this.submitApprovalData();
                }
            })
        },
        async onBack(param) {
            if (param && param.flag === 'flow') {
                this.$set(this, 'nodeApprovers', param.nodeDtos);
                if(param.source === 'AddScheConsumerSubmit') {
                    await this.submitApprovalData(param.nodeDtos,param.preApprovalId);
                } else if(param.source === 'EndScheConsumerSubmit') {
                    await this.confirmEndConsumer(this.endScheConsumerData, this.endScheConsumerIsV3);
                }
            }
        },
         // 超高的审批要获取参数给到企微审批流
        async getHighPId(postnId){
           const data = await this.$http.post(this.$env.appURL+'/action/link/identityEmp/queryByPostnId',{postnId,"waitstaffType":"BusinessAgent"});
            if(data.success){
                return data.result&& data.result.headUserId
            } else {
                this.$message.error(data.result);
            }
        },
        /**
         * @desc 提交审批数据接口调用
         * <AUTHOR>
         * @date 2024-09-06
         **/
        async submitApprovalData(nodeDtos,approvalRecordId) {
            try {

                this.$utils.showLoading('提交中,请稍后...');
                // nodeDtos有值则是v3审批
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/batchAddApprove',
                    ...(nodeDtos && { nodeApprovers: nodeDtos, approvalRecordId, }),
                    consumerInfoIds: this.tempConsumerIdList
                });
                this.$utils.hideLoading()
                if (data.success) {
                    this.$message.success('提交审批成功！');
                    this.guestList.methods.reload();
                }else{
                    this.guestList.methods.reload();
                }
            } catch (error) {
                this.$utils.hideLoading();
                this.tempConsumerIdList = []
                this.guestList.methods.reload();
                console.log(error,'失败');
            }
        },
        /**
         * 新增消费者
         * <AUTHOR>
         * @date 2020-08-07
         * */
        async addConsumer() {
            const id = await this.$newId();
            const accountItem = {
                id: id,
                row_status: ROW_STATUS.NEW,
                consumerDataType: 'ChannelConsumer',
                dataSource: 'MarketingPlatform',
                dataType: 'Consumer',
                accntSourceFrom: 'SalesAssistant',
                orgId: this.userInfo.orgId,
                fstName: this.userInfo.firstName,
                postnId: this.userInfo.postnId,
                belongToCompanyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                type: "ToBeFollowed",
                birthType: 'Yang',
                brandPreference: "",
                hobby: "",
                terminalFlag : 'N',
                listOfTags: {
                    accountId: id,
                    list: []
                }
            };
            this.$nav.push(this.editPath, {
                data: accountItem,
                pageBackNum: 1,
                userInfo: this.userInfo,
                pageFrom:'scheduleDetail',
                callback: (data) => {
                    const consumerScheduleInfos = {
                        scheduleId: this.scheduleId,
                        addSource: 'SalesmanAdd',
                        acctId: data.id
                    };
                    this.addConsumerApi(consumerScheduleInfos);
                }
            })
        },
        /**
         * @desc 跳转详情-消费者排期运营执行情况
         * <AUTHOR>
         * @date 2024-07-09
         **/
        gotoCustomers(data) {
            // 消费者排期详情埋点：事件编码、排期id、排期消费者id（row_Id）、消费者id、菜单id
            try {
                $logger.info('CONSUMER_SCHEDULE_002', 'Click', `菜单id：consumer_schedule_排期id：${data.scheduleId}_排期消费者行id：${data.id}_消费者id：${data.acctId}`);
            } catch (e) {
                $logger.error('CONSUMER_SCHEDULE_002', 'Click', `菜单id：consumer_schedule_排期id：${data.scheduleId}_排期消费者行id：${data.id}_消费者id：${data.acctId}_报错信息：${e}`);
            }
            if(data.status === 'New' && data.endAuditStatus !== 'AddReviewing' &&  this.pageParam.data.isAddApprove === 'Y' && (['ActionPackageOption', 'ActionNone'].includes(this.customersItem.scheActionType) || ['TargetPackageOption', 'TargetNone'].includes(this.customersItem.scheTargetType))) {
                // 添加消费者
                this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-add-consumer-page.vue', {
                    scheduleCrowd:this.customersItem.scheduleCrowd,
                    data: data,
                    scheInfoId: data.id,
                    scheduleId: data.scheduleId,
                    oauth: this.oauth
                })
            } else {
                // 排期运营执行情况
                this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-detail-implementation-page.vue', {
                    scheduleCrowd:this.customersItem.scheduleCrowd,
                    data: data,
                    scheInfoId: data.id,
                    scheduleId: data.scheduleId,
                    oauth: this.oauth
                })
            }
        },
        /**
         * 监控返回函数
         * <AUTHOR>
         * @date 2024-03-05
         * @param param
         */
        // onBack (param) {
        //     console.log('param', param)
        // }
    }
}
</script>

<style lang="scss">
.consumers-schedule-detail-page {
    .zero-view {
        width: 100%;
        height: 30px;
    }
    .form-column {
        margin: auto;
        border-radius: 16px;
        padding: 40px 28px 4px 28px;
        background: white;
        font-family: PingFangSC-Regular;

        .item-input {
            line-height: 30px;
            margin-bottom: 30px;
            width: 100%;
            display: flex;

            .label {
                align-self: flex-start;
                color: #8c8c8c;
                font-size: 28px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                width: 300px;
            }

            .text {
                color: #262626;
                width: 420px;
                font-size: 28px;
                text-align: right;
            }
        }

        .item-textarea {
            line-height: 30px;
            width: 100%;
            margin-bottom: 30px;

            .label {
                color: #8c8c8c;
                font-size: 28px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                width: 300px;
                margin-bottom: 18px;
            }

            .text {
                color: #262626;
                width: 420px;
                font-size: 28px;
            }
        }
    }
    .schedule-tag-consumer{
        box-sizing: border-box;
        margin: auto;
        border-radius: 16px;
        padding: 40px 28px 10px 28px;
        background: white;
        font-family: PingFangSC-Regular;
        width: 100%;
        .schedule-tag-consumer-title {
            width: 40%;
            color: #262626;
            font-size: 28px;
            margin-bottom: 20px;
            font-weight: bold;
        }
        .schedule-tag-consumer-text-parent{
            width: 100%;
            overflow-x: auto;
        }
        .schedule-tag-consumer-text{
            display: inline-block;
            flex-shrink: 0;
            font-size: 28px;
            color:#8c8c8c;
            margin-bottom: 15px;
            margin-right: 15px;
            white-space:nowrap;
            width: 100%;
        }
    }
    .link-auto-list .link-auto-list-top-bar {
        display: flex;
        margin-bottom: 20px;
        border-bottom: 0px !important;
    }
    /*deep*/
    .link-swipe-option-container .link-swipe-option {
        width: 80px;
        height: 60px !important;
        border-radius: 60px;
        font-size: 24px !important;
    }
    .link-swipe-action {
        position: relative;
        width: 100%;
        overflow-x: hidden;
    }
    .guest-list {
        width: 93%;
        margin: auto auto 130px;
        border-radius: 16px;
        background: white;

        .line-status {
            display: flex;
            flex-wrap: wrap;
            border-bottom: 2px solid #f2f2f2;
            justify-content: flex-start;
            align-items: center;

            .line-status-item {
                border-radius: 10px;
                height: 30px;
                line-height: 30px;
                font-size: 24px;
                padding: 12px;
                display: flex;
                justify-content: center;
                margin: 10px;
                min-width: 18%;
            }

            .unchecked {
                border: 1px solid #E0E4EC;
                color: #8C8C8C;
            }

            .checked {
                background: #EDF3FF;
                color: #2F69F8;
                border: 1px solid #EDF3FF;
            }

            .confirm {
                color: #ffffff;
                background: #2F69F8;
                width: 80px;
                border-radius: 13px;
                display: flex;
                justify-content: center;
            }
        }

        .title {
            border-bottom: 2px solid #f2f2f2;
            height: 92px;
            line-height: 92px;
            display: flex;
            justify-content: space-between;
            letter-spacing: 0;
            font-size: 28px;
            padding: 0 24px;

            .left {
                color: #262626;
            }

            .right {
                color: #2F69F8;
                width: 30%;
                text-align: right;
            }
        }

        .guest-list-item-icon {
            width: 80px;
            text-align: center;

            .iconfont {
                font-size: 40px;
                color: #bfbfbf;
            }
        }

        .tab-filter {
            .active {
                color: $color-primary;
            }

            .lnk-tabs-content {
                display: flex;
                justify-content: space-around;
                flex-wrap: nowrap;
            }

            .lnk-tabs-item {
                height: 92px;
                line-height: 92px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;

                .label-name-line {
                    position: relative;
                    font-size: 28px;
                    margin-left: 10px;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: center;

                    .num-tips {
                        position: absolute;
                        top: 0;
                        right: 0;
                        transform: translateX(90%);
                        background-color: #FF5A5A;
                        color: #ffffff;
                        font-size: 24px;
                        border-radius: 50%;
                        padding-left: 16px;
                        padding-right: 16px;
                        height: 40px;
                        line-height: 40px;
                        text-align: center;
                    }

                    .line {
                        height: 8px;
                        width: 56px;
                        border-radius: 16px 16px 0 0;
                        background-color: $color-primary;
                        box-shadow: 0 3px 8px 0 rgba(47, 105, 248, 0.63);
                        margin-top: -8px;
                    }
                }
            }
        }

        .guest-list-item {
            @include flex;
            @include flex-start-center;
            overflow: hidden;
            .logo-img {
                width: 20%;
                display: flex;
                flex-direction: column;
                justify-content: center;
                margin-right: 20px;
                align-items: center;
            }
            .media-list-logo {
                height: 94px;
                width: 94px;

                image {
                    height: 100%;
                    width: 100%;
                }
            }
            .guest-list-logo {
                border-radius: 50%;
                width: 80px;
                height: 80px;
                overflow: hidden;
            }
            .guest-list-item-content {
                width: 50%;
                padding-right: 16px;
                display: flex;
                flex-direction: column;
                .guest-list-item-content-row1 {
                    width: 100%;
                    display: flex;
                    .consumer-name {
                        display: block;
                        margin-bottom: 4px;
                        white-space: nowrap;
                        line-height: 42px;
                        font-size: 26px;
                        color: #333333;
                        overflow: hidden;
                        text-overflow: ellipsis;
                    }
                    .consumer-flag {
                        background-color: #F6F6F6;
                        width: 170px;
                        height: 42px;
                        display: inline-block;
                        font-size: 24px;
                        line-height: 42px;
                        border-radius: 8px;
                        text-align: center;
                        margin-left: 12px;

                        .consumer-flag-text {
                            color: #9F9F9F;
                        }
                    }
                }

                .guest-list-item-content-row2 {
                    display: block;
                    max-width: 100%;
                    justify-content: space-between;
                    align-items: center;
                    margin-bottom: 4px;
                    font-size: 22px;
                    white-space: nowrap;
                    line-height: 42px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
            }
            .content-status {
                width: 30%;
                display: flex;
                align-items: center;
                justify-content: center;
                position: sticky; /* 使按钮始终可见 */
                right: 0; /* 将按钮固定在右侧 */
                font-size: 24px;
                height: 48px;
                .red {
                    color: red;
                }
                .blue {
                    color: #306cff;
                }
                .green {
                    color: #00B050;
                }
                .endPass {
                    color: #9F9F9F;
                }
            }
        }
    }
    .link-sticky .link-sticky-content {
        width: 100%;
    }
    .link-search-input {
        width: 100%;
    }
    .schedule-detail-desc {
        margin: auto;
        border-radius: 16px;
        background: white;
        .desc-text {
            display: flex;
            justify-content: center;
            align-items: center;
            font-size: 28px;
            color: #8c8c8c;
            overflow: auto;
            padding: 30px;
            max-height: 70vh;
        }
        .desc-button {
            padding-top: 40px;
            display: flex;
            justify-content: center;
            align-items: center;
            .link-button {
                width: 200px;
                height: 60px;
                font-size: 28px;
            }
        }
    }

    .bottom-button {
        background: #f2f2f2;
        padding: 12px;
        box-sizing: border-box;
        display: flex;
        flex-direction: column;
        align-items: center;
        margin-top: auto; /* 确保在页面底部 */
    }

    .text-bottom {
        font-size: 24px;
        color: red;
    }

    .link-button {
        width: 95%;
    }
}
</style>
