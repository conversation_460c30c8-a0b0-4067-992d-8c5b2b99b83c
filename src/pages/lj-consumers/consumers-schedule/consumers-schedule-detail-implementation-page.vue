<!--
消费者-消费者排期运营-消费者排期运营执行情况
<AUTHOR>
@date 2024-07-01
@file consumers-schedule-detail-implementation-page.vue
-->
<template>
    <link-page class="consumers-schedule-detail-implementation-page">
        <lnk-taps :taps="detailOptions" v-model="detailActive" @switchTab="switchTab"></lnk-taps>
        <view class="blank"></view>
        <view class="zero-view"></view>
        <!-- 执行情况/添加消费者-顶部消费者展示 -->
        <target-customers :scheduleItem="scheduleItem"
                          :conStartTime="pageParam.data.conStartTime"
                          :conEndTime="pageParam.data.conEndTime"
                          :headData="headData">
        </target-customers>
        <!-- 业务动作 -->
        <view v-if="detailActive.seq === '0'">
            <!-- 终止原因-->
            <implement-end-reason :scheduleItem="scheduleItem"></implement-end-reason>
            <implement-person-info v-if="scheduleItem.id && scheduleItem.scheduleId"
                                   :scheduleItem="scheduleItem"
                                   :consumerTimeFlag="consumerTimeFlag"
                                   :isPrincipal="isPrincipal"
                                   :oauth="oauth"
                                   :headData="headData"
                                   :data="pageParam.data"></implement-person-info>
            <view class="main-content">
                <!-- 待提交动作排期计划 -->
                <implement-action-submit v-if="headFlag && scheduleItem.id && scheduleItem.scheduleId"
                                         :scheduleItem="scheduleItem"
                                         :oauth="oauth"
                                         :isPrincipal="isPrincipal"
                                         :consumerTimeFlag="consumerTimeFlag"
                                         :conStartTime="pageParam.data.conStartTime"
                                         :conEndTime="pageParam.data.conEndTime"
                                         :headData="headData"
                                         pageFrom="implementation"
                                         @editScheduling="editScheduling"
                                         @updateActionListNum="handleUpdateActionListNum"
                                         @reloadActionList2="reloadActionList2"
                                         @reloadActionInvalid="reloadActionInvalid"
                                         ref="actionListSubmit"
                ></implement-action-submit>
<!--                <view class="zero-view"></view>-->
                <!-- 动作不可选 -->
                <implement-action-all v-if="headData.scheActionType === 'ActionPackageAll' && scheduleItem.id && scheduleItem.scheduleId"
                                      :scheduleItem="scheduleItem"
                                      :oauth="oauth"
                                      :isTequFlag="isTequFlag"
                                      ref="actionList1"
                ></implement-action-all>
                <!-- 动作可选 -->
                <implement-action-option-none v-if="headFlag && (headData.scheActionType === 'ActionPackageOption' || headData.scheActionType === 'ActionNone') && scheduleItem.id && scheduleItem.scheduleId"
                                              :scheduleItem="scheduleItem"
                                              :oauth="oauth"
                                              :headData="headData"
                                              :isPrincipal="isPrincipal"
                                              :isTequFlag="isTequFlag"
                                              :conStartTime="pageParam.data.conStartTime"
                                              :conEndTime="pageParam.data.conEndTime"
                                              pageFrom="implementation"
                                              :consumerTimeFlag="consumerTimeFlag"
                                              :submitActionFlag="submitActionFlag"
                                              @editScheduling="editScheduling"
                                              @reloadActionListSubmit="reloadActionListSubmit"
                                              @reloadActionInvalid="reloadActionInvalid"
                                              ref="actionList2"
                ></implement-action-option-none>
                <view class="zero-view"></view>
                <!-- 已作废动作排期计划 -->
                <implement-action-invalid :scheduleItem="scheduleItem"
                                          :oauth="oauth"
                                          :conStartTime="pageParam.data.conStartTime"
                                          :conEndTime="pageParam.data.conEndTime"
                                          :headData="headData"
                                          :isPrincipal="isPrincipal"
                                          ref="actionListInvalid"
                ></implement-action-invalid>
            </view>
        </view>
        <!-- 业务目标 -->
        <view v-if="detailActive.seq === '1'">
            <!-- 终止原因-->
            <implement-end-reason :scheduleItem="scheduleItem"></implement-end-reason>
            <!-- <view class="zero-view"></view>-->
            <view class="main-content">
                <!-- 待提交目标排期计划 -->
                <implement-target-submit v-if="headFlag && scheduleItem.id && scheduleItem.scheduleId"
                                         :scheduleItem="scheduleItem"
                                         :oauth="oauth"
                                         :headData="headData"
                                         :consumerTimeFlag="consumerTimeFlag"
                                         :isPrincipal="isPrincipal"
                                         pageFrom="implementation"
                                         @editScheduling="editScheduling"
                                         @updateTargetListNum="handleUpdateTargetListNum"
                                         @reloadTargetList2="reloadTargetList2"
                                         @reloadTargetInvalid="reloadTargetInvalid"
                                         ref="targetListSubmit"
                ></implement-target-submit>
                <!-- <view class="zero-view"></view>-->
                <!-- 目标不可选 -->
                <implement-target-all v-if="headData.scheTargetType === 'TargetPackageAll' && scheduleItem.id && scheduleItem.scheduleId"
                                      :scheduleItem="scheduleItem"
                                      :oauth="oauth"
                                      :consumerTimeFlag="consumerTimeFlag"
                                      ref="targetList1"
                ></implement-target-all>
                <!-- 目标可选 -->
                <implement-target-option-none v-if="headFlag && (headData.scheTargetType === 'TargetPackageOption' || headData.scheTargetType === 'TargetNone') && scheduleItem.id && scheduleItem.scheduleId"
                                              :scheduleItem="scheduleItem"
                                              :headData="headData"
                                              :oauth="oauth"
                                              :consumerTimeFlag="consumerTimeFlag"
                                              :conStartTime="pageParam.data.conStartTime"
                                              :conEndTime="pageParam.data.conEndTime"
                                              :isPrincipal="isPrincipal"
                                              pageFrom="implementation"
                                              :submitTargetFlag="submitTargetFlag"
                                              @editScheduling="editScheduling"
                                              @reloadTargetListSubmit="reloadTargetListSubmit"
                                              @reloadTargetInvalid="reloadTargetInvalid"
                                              ref="targetList2"
                ></implement-target-option-none>
                <view class="zero-view"></view>
                <!-- 已作废目标排期计划 -->
                <implement-target-invalid :scheduleItem="scheduleItem"
                                          :oauth="oauth"
                                          ref="targetListInvalid"
                ></implement-target-invalid>
            </view>
        </view>
        <!-- 业务动作执行明细 -->
        <view v-if="detailActive.seq === '2'">
            <view class="zero-view"></view>
            <implement-detail-action v-if="scheduleItem.id && scheduleItem.scheduleId"
                                        :scheduleItem="scheduleItem"
                                        :isTequFlag="isTequFlag"
            ></implement-detail-action>
            <view class="zero-view"></view>
            <implement-detail-target  v-if="scheduleItem.id && scheduleItem.scheduleId"
                                      :isTequFlag="isTequFlag"
                                      :scheduleItem="scheduleItem"></implement-detail-target>
        </view>
        <!-- 排期运营状态不等于已结束、新建、已作废才展示其他按钮逻辑 -->
        <link-sticky class="button-bottom" style="width: 100%">
            <link-button block @tap="v3planApproval" v-if="(detailActive.seq === '0' || detailActive.seq === '1')
                            && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)
                            && (headData.isAddApprove === 'Y' || headData.targetApprove === 'Y' || headData.actionApprove === 'Y')
                            && scheduleItem.endAuditStatus !== 'AddReviewing'
                            && pageParam.data.status !== 'End'
                            && consumerTimeFlag
                            && isPrincipal
                            && ((detailActive.seq === '0' && submitActionFlag) || (detailActive.seq === '1' && submitTargetFlag))">提交审批</link-button>
            <link-button block @tap="endApproval"
                         v-if="headData.scheGuide
                                && ((headData.scheGuide === 'ActionGuide' && pageParam.data.actionFinish === 'Finished')
                                    || (headData.scheGuide === 'TargetGuide' && pageParam.data.targetFinish === 'Finished')
                                    || ((headData.scheGuide === 'ActionGuide,TargetGuide' || headData.scheGuide === 'TargetGuide,ActionGuide') && pageParam.data.actionFinish === 'Finished' && pageParam.data.targetFinish === 'Finished'))
                                && consumerTimeFlag
                                && isPrincipal">
                结束排期</link-button>
        </link-sticky>
        <link-fab-button icon="icon-plus" label="新建排期" @tap="addSchedule"
                         v-if="(detailActive.seq === '0' || detailActive.seq === '1')
                             && addFlag
                             && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)
                             && scheduleItem.endAuditStatus !== 'AddReviewing'
                             && pageParam.data.status !== 'End'
                             && isPrincipal
                             && consumerTimeFlag"/>
    </link-page>
</template>

<script>
import LnkTaps from "../../core/lnk-taps/lnk-taps.vue";
import LineTitle from "../../lzlj/components/line-title.vue";
import ImplementEndReason from "./components/implement-end-reason.vue";
import ImplementActionAll from "./components/implement-action-all.vue";
import ImplementActionOptionNone from "./components/implement-action-option-none.vue";
import ImplementTargetAll from "./components/implement-target-all.vue";
import ImplementTargetOptionNone from "./components/implement-target-option-none.vue";
import ImplementActionSubmit from "./components/implement-action-submit.vue";
import ImplementTargetSubmit from "./components/implement-target-submit.vue";
import ImplementActionInvalid from "./components/implement-action-invalid.vue";
import ImplementTargetInvalid from "./components/implement-target-invalid.vue";
import ImplementDetailAction from "./components/implement-detail-action.vue";
import ImplementDetailTarget from "./components/implement-detail-target.vue";
import TargetCustomers from "./components/target-customers.vue";
import ImplementPersonInfo from "./components/implement-person-info.vue";
import Taro from "@tarojs/taro";

export default {
    name: 'consumers-schedule-detail-implementation-page',
    components: {LineTitle, LnkTaps, ImplementEndReason, ImplementActionAll, ImplementActionOptionNone, ImplementTargetAll, ImplementTargetOptionNone, ImplementActionSubmit, ImplementTargetSubmit, ImplementActionInvalid, ImplementTargetInvalid, ImplementDetailAction, ImplementDetailTarget, TargetCustomers, ImplementPersonInfo},
    data() {
        const detailOptions = [
            {name: '业务动作', seq: '0', val: 'action'},
            {name: '业务目标', seq: '1', val: 'target'},
            {name: '执行明细', seq: '2', val: 'detail'}
        ]
        return {
            detailOptions, // 明细
            detailActive: {},
            scheduleItem: {},  // 消费者信息
            formData: {
                scheduleClassName: '', // 运营大类
                scheduleSmallClassName: '', // 运营小类
                targetNum: 1, // 目标值
                planTimeType: 'ByDay', // 执行时间类型
                beginTime: '', // 预估执行时间
                endTime: '', // 预估执行结束时间
                // reachNum: '' // 运营小类的达成值
            }, // 新增运营排期-表单数据
            scheDetailId: '', // 运营排期明细id
            scheduleId: '', // 运营排期id
            scheInfoId: '', // 消费者行id
            editFlag: false, // 是否可修改，新增时为false，修改时为true
            headData: {
                isAddApprove: '', // 是否走审批
            }, // 运营头数据
            addFlag: false, // 新增标识
            oauth: this.pageParam.oauth,
            consumerTimeFlag: false, // 当前时间处于排期消费者的开始时间和结束时间范围内
            submitActionFlag: false, // 动作提交是否有数据
            submitTargetFlag: false, // 目标提交是否有数据
            headFlag: false, // 头部数据是否加载完成
            isPrincipal: false, // 是否是负责人
            // isButtonVisible: false
            isTequFlag: false, // 是否为特曲公司
        }
    },
    async created() {
        console.log('oauth', this.oauth);
        console.log('消费者数据pageParam.data', this.pageParam.data);
        this.scheduleId = this.pageParam.scheduleId;
        this.scheInfoId = this.pageParam.scheInfoId;
        await this.queryDetail(this.scheduleId);
        await this.queryConsumer();
        // 查询是否是负责人
        await this.queryIsPrincipal();
        this.detailActive = this.detailOptions[0];
        this.timeFlag();
        if (this.detailActive.seq === '0' && (this.headData.scheActionType === 'ActionPackageOption' || this.headData.scheActionType === 'ActionNone')) {
            this.addFlag = true;
            // this.smallClassFlag = false;
        }
        this.userInfo = Taro.getStorageSync('token').result;
        console.log('userInfo', this.userInfo)
        const isTequ = await this.$utils.getCfgProperty('SCHE_TEQU_VAL');
        // 判断是否为特曲公司
        this.isTequFlag = isTequ.split(',').some(item => item === this.userInfo.companyId);
        // this.checkButtonVisibility();
        console.log('scheduleItem', this.scheduleItem);
        console.log('headData', this.headData);
        console.log('scheActionType', this.headData.scheActionType);
        console.log('scheTargetType', this.headData.scheTargetType);
        this.$bus.$on('refreshImpleList', this.handleRefreshList);
    },
    beforeDestroy() {
        this.$bus.$off('refreshImpleList', (data) => {
            this.handleRefreshList(data);
        });
    },
    methods: {
        /**
         * @desc 查询是否是负责人
         * <AUTHOR>
         * @date 2025-01-16
         **/
        async queryIsPrincipal() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/queryMyPostnType',
                    scheInfoId: this.scheduleItem.id, //排期消费者行id
                    businessType: 'chargePerson'
                });
                if(data.success && data.rows[0]) {
                    this.isPrincipal = data.rows[0].businessType === 'chargePerson';
                    console.log('是否是负责人1', this.isPrincipal, data.rows[0].businessType);
                } else {
                    this.isPrincipal = false;
                }
                console.log('是否是负责人2', this.isPrincipal);
            } catch (e) {
                this.$message.error('查询负责人失败', data.message);
                console.log('查询负责人出错', e);
            }
        },
        /**
         * @desc 刷新【执行情况-排期列表】-动作-待提交
         * <AUTHOR>
         * @date 2025-01-16
         **/
        reloadActionListSubmit() {
            this.$refs.actionListSubmit.reloadActionList();
        },
        /**
         * @desc 刷新【执行情况-排期列表】-目标-待提交
         * <AUTHOR>
         * @date 2025-01-16
         **/
        reloadTargetListSubmit() {
            this.$refs.targetListSubmit.reloadTargetList();
        },
        /**
         * @desc 刷新【执行情况-排期列表】-目标-作废
         * <AUTHOR>
         * @date 2025-01-14
         **/
        reloadTargetInvalid() {
            this.$refs.targetListInvalid.reloadTargetList();
        },
        /**
         * @desc 刷新【执行情况-排期列表】-动作-作废
         * @date 2025-01-14
         **/
        reloadActionInvalid() {
            this.$refs.actionListInvalid.reloadActionList();
        },
        /**
         * @desc 刷新【执行情况-排期列表】/添加消费者中列表-目标
         * <AUTHOR>
         * @date 2025-01-14
         **/
        reloadTargetList2() {
            this.$refs.targetList2.reloadTargetList();
        },
        /**
         * @desc 刷新【执行情况-排期列表】/添加消费者中列表-动作
         * @date 2025-01-14
         **/
        reloadActionList2() {
            this.$refs.actionList2.reloadActionList();
        },
        handleUpdateTargetListNum(targetListNum) {
            this.submitTargetFlag = targetListNum > 0;
            if(this.headData.scheTargetType === 'TargetPackageOption' || this.headData.scheTargetType === 'TargetNone') {
                this.reloadTargetList2();
            }
        },
        handleUpdateActionListNum(actionListNum) {
            this.submitActionFlag = actionListNum > 0;
            if(this.headData.scheActionType === 'ActionPackageOption' || this.headData.scheActionType === 'ActionNone') {
                this.reloadActionList2();
            }
        },
        /**
         * @desc 判断当前时间处于排期消费者的开始时间和结束时间范围内
         * <AUTHOR>
         * @date 2025-01-08
         **/
        timeFlag() {
            // 250121 因iso版本问题（15以下）会导致时间格式处理出错，iso15以下版本处理时间只能处理2025/01/21这种格式，当前数据格式为2025-01-21，故作处理
            const formatDate = (dateStr) => dateStr.replace(/-/g, '/');
            const currentTime = new Date();
            const startTime = new Date(formatDate(this.pageParam.data.conStartTime));
            const endTime = new Date(formatDate(this.pageParam.data.conEndTime));
            this.consumerTimeFlag = currentTime >= startTime && currentTime <= endTime;
            console.log('consumerTimeFlag', this.consumerTimeFlag);
        },
        /**
         * @desc 查询消费者信息
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryConsumer() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/queryById',
                    id: this.scheInfoId
                    // scheduleId: this.scheduleId
                });
                if(data.success) {
                    this.scheduleItem = data.result;
                } else {
                    this.$message.warn('查询消费者信息异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询消费者信息异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryDetail() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: this.scheduleId
                });
                if(data.success) {
                    this.headData = data.result;
                    this.headFlag = true;
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 判断是否是v3需要审批
         * <AUTHOR>
         * @date 2025-05-13
         **/
        async isV3(applyType='') {
            return new Promise(async (res, rej) => {
                const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                    companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    applyType
                });
                console.log('result', result)
                if (success && result === 'v3') {
                    res(true);
                } else {
                    res(false);
                }
            });
        },
        // 超高的审批要获取参数给到企微审批流
        async getHighPId(postnId){
           const data = await this.$http.post(this.$env.appURL+'/action/link/identityEmp/queryByPostnId',{postnId,"waitstaffType":"BusinessAgent"});
            if(data.success){
                return data.result&& data.result.headUserId
            } else {
                this.$message.error(data.result);
            }
        },
        /**
         * @desc 提交审批
         * <AUTHOR>
         * @date 2024-05-13
         **/
        async v3planApproval(){

            const applyType = (this.pageParam.scheduleCrowd||this.pageParam.data.scheduleCrowd)=='HighValueAcct'?'HighAcctPlan':'ScheAcctPlan'
            const isV3 = await this.isV3(applyType)
           isV3 && this.v3ApprovalPlan(applyType);
           !isV3&& this.submitApproval();
        },
        async v3ApprovalPlan(approvalType=''){
               let preViewId = null;
               let postnId = null;

                 const data = await this.$http.post(this.$env.appURL + '/action/link/approvalRecord/queryByExamplePage',
                    { filtersRaw: [
                        {"id":"id_0_auto","property":"businessId","value":this.scheduleItem.id},
                        {"id":"appCreateStatus","property":"appCreateStatus", "operator":"is null", "value":""},
                        {"id":"applyType","property":"applyType", "value": approvalType}]
                    })
                if (data.success) {
                    if(!data.rows||!data.rows.length)return this.$message.success('没有待提交的排期计划，无需审批！');
                    preViewId = data.rows[0].id;
                    postnId = data.rows[0].postnId;
                } else {
                   return this.$message.error(data.result);
                }
            // const newId = await this.$newId();
            let flowStartPsnId = undefined;
            if(approvalType=='HighAcctPlan'&&postnId){
               flowStartPsnId = await this.getHighPId(postnId)
            }
            this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                submitData: { id: this.scheduleItem.id,flowStartPsnId
                },
                submitUser: this.userInfo,
                flowObjId: preViewId,
                source: 'planSubmit',
                // 审批类型编码
                approvalType
            });
        },
         /**
         * @desc 监听返回
         * <AUTHOR>
         * @date 2022/4/1 11:19
         **/
        async onBack(param) {
            if (param && param.flag === 'flow') {
                this.$set(this, 'nodeApprovers', param.nodeDtos);
                if(param.source === 'planSubmit') {
                    await this.submitApproval(param.nodeDtos, param.preApprovalId);
                }
            }
            await this.autoList.methods.reload();
        },
        /**
         * @desc 提交审批
         * <AUTHOR>
         * @date 2024-09-09
         **/
        async submitApproval(nodeApprovers,approvalRecordId) {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    ...(nodeApprovers&&{nodeApprovers,approvalRecordId}),
                    dmpUrl: '/link/consumerSchedulePlan/addPlanApprove',
                    id: this.scheduleItem.id
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success('提交审批成功');
                    //     提交审批成功后更新状态
                    await this.queryConsumer();
                    if(this.detailActive.seq === '0') {
                        this.$refs.actionListSubmit.reloadActionList();
                    }
                    if(this.detailActive.seq === '1') {
                        this.$refs.targetListSubmit.reloadTargetList();
                    }
                    //     重新加载上一个页面的guestList
                    this.$bus.$emit('refreshList');
                } else {
                    this.$utils.hideLoading();
                    // 后端报错即可
                    // this.$message.warn('提交审批失败', data.message);
                }
            } catch (e) {
                this.$utils.hideLoading();
                // 后端报错即可
                // this.$message.error('提交审批异常，请联系管理员', e);
            }
        },
        /**
         * @desc 提交审批
         * <AUTHOR>
         * @date 2024-09-09
         **/
        async endApproval() {
            try {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/calculateScheduleInfo',
                    scheInfoId: this.scheInfoId
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success('结束排期成功');
                    this.consumerTimeFlag = false;
                    //     结束排期成功后更新状态
                    await this.queryConsumer();
                    if(this.detailActive.seq === '0') {
                        this.$refs.actionListSubmit.reloadActionList();
                    }
                    if(this.detailActive.seq === '1') {
                        this.$refs.targetListSubmit.reloadTargetList();
                    }
                    //     重新加载上一个页面的guestList
                    this.$bus.$emit('refreshList');
                } else {
                    this.$utils.hideLoading();
                    // 后端报错即可
                    // this.$message.warn('结束排期失败', data.message);
                }
            } catch (e) {
                this.$utils.hideLoading();
                // 后端报错即可
                // this.$message.error('结束排期异常，请联系管理员', e);
            }
        },
        /**
         * @desc 添加排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async addSchedule() {
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-edit-page', {
                editFlag: false,  // 新增
                isAction: this.detailActive.seq === '0',
                scheduleItem: this.scheduleItem,
                headData: this.headData,
                conStartTime: this.pageParam.data.conStartTime,
                conEndTime: this.pageParam.data.conEndTime,
                pageFrom: 'implementation'
            });
        },

        /**
         * @desc 修改排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async editScheduling(data) {
            console.log('data', data);
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-edit-page', {
                editFlag: true,  // 修改
                isAction: this.detailActive.seq === '0',
                editData: data,
                scheduleItem: this.scheduleItem,
                conStartTime: this.pageParam.data.conStartTime,
                conEndTime: this.pageParam.data.conEndTime,
                headData: this.headData,
                pageFrom: 'implementation'
            });
        },
        /**
         * @desc 处理时间
         * <AUTHOR>
         * @date 2024-10-24
         **/
        handleTime() {
            console.log('handleTime')
            // 目标可选时，不显示时间字段，直接赋值，执行时间类型TimeRange
            if (this.headData.scheTargetType === 'TargetPackageOption' && this.detailActive.seq === '1') {
                this.formData.planTimeType = 'TimeRange';
                this.formData.beginTime = this.pageParam.data.conStartTime;
                this.formData.endTime = this.pageParam.data.conEndTime;
            }
            // 动作可选时，显示时间字段，执行时间类型ByDay
            if(this.headData.scheActionType === 'ActionPackageOption' && this.detailActive.seq === '0') {
                this.formData.planTimeType = 'ByDay';
                this.timeTypeChange('ByDay');
            }
            console.log('formData', this.formData)
        },
        /**
         * @desc 当执行时间类型为TimeRange时，对预估执行开始时间和结束时间进行赋值操作
         * <AUTHOR>
         * @date 2024-08-06
         **/
        async timeTypeChange(val) {
            console.log('val',val)
            const timeStamp = await this.$utils.getServerTime();
            const formatDate = (dateStr) => dateStr.replace(/-/g, '/');
            const date = typeof timeStamp === 'string' ? new Date(formatDate(timeStamp)) :  new Date(timeStamp);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            if(val === 'TimeRange') {
                // 开始时间为服务器时间 + ' 00:00:00'
                this.formData.beginTime = formattedDate + ' 00:00:00';
                // 结束时间为排期运营的结束时间
                this.formData.endTime = this.pageParam.data.conEndTime;
            } else if(val === 'ByDay') {
                // 开始时间为服务器时间 + ' 00:00:00'
                this.formData.beginTime = formattedDate + ' 00:00:00';
            } else {
                this.formData.beginTime = '';
                this.formData.endTime = '';
            }
        },
        /**
         * @desc 切换tab页签
         * <AUTHOR>
         * @date 2024-07-12
         **/
        async switchTab(val) {
            this.detailActive = val;
            // 动作不可选（必选）
            if (val.seq === '0' && this.headData.scheActionType === 'ActionPackageAll') {
                this.addFlag = false;
                // await this.getAllActionList();
            }
            // 动作可选
            if (val.seq === '0' && (this.headData.scheActionType === 'ActionPackageOption' || this.headData.scheActionType === 'ActionNone')) {
                this.addFlag = true;
                this.smallClassFlag = false;
                // await this.getActionList();
            }
            // 目标不可选（必选）
            if (val.seq === '1' &&  this.headData.scheTargetType === 'TargetPackageAll') {
                this.addFlag = false;
                // await this.getAllTargetList();
            }
            // 目标可选
            if (val.seq === '1' && (this.headData.scheTargetType === 'TargetPackageOption' || this.headData.scheTargetType === 'TargetNone')) {
                this.addFlag = true;
                this.smallClassFlag = false;
                // await this.getTargetList();
            }
        },
        /**
         * @desc 根据当前tab刷新对应的列表
         * <AUTHOR>
         * @date 2025-01-12
         **/
        handleRefreshList(data) {
            // 根据当前tab刷新对应的列表
            this.queryIsPrincipal();
            if (data === 'ScheAction') {
                // 刷新动作列表
                this.reloadActionListSubmit();
                this.reloadActionInvalid();
            } else {
                // 刷新目标列表
                this.reloadTargetListSubmit();
                this.reloadTargetInvalid();
            }
        }
    }
}
</script>

<style lang="scss">
@import './components/common-styles.scss';
.consumers-schedule-detail-implementation-page {
    .blank {
        width: 100%;
        height: 96px;
        background: #F2F2F2;
    }

    .zero-view {
        width: 100%;
        height: 30px;
    }

    .link-dialog-foot-custom {
        width: auto !important;
    }

    .end-scheduling {
        width: 100vw;
        bottom: 80px;
        margin: 0 auto;
        position: fixed;
        display: flex;
        justify-content: center;
    }
    .link-textarea {
        background-color: white;
        padding: 0px !important;
        -webkit-box-sizing: border-box;
        box-sizing: border-box;
    }
    .fixed-height .link-textarea-content {
        height: 180px !important;
        min-height: 180px !important;
    }
}
</style>
