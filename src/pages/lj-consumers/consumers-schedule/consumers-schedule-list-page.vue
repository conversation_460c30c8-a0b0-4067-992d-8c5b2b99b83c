<!--
消费者-消费者排期运营
<AUTHOR>
@date 2024-07-01
@file consumers-schedule-list-page.vue
-->
<template>
    <link-page class="consumers-schedule-list">
        <lnk-taps :taps="statusOptions" v-model="scheduleStatusActive" @switchTab="switchTab"></lnk-taps>
        <view class="blank"></view>
        <view class="schedule-list-content" v-if="scheduleStatusActive.seq === '0'">
            <link-auto-list :option="autoList" hideCreateButton :key="0"
                            :searchInputBinding="{props:{placeholder:'排期运营名称/创建人'}}">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="perform-case-list-item"
                          style="padding: 4px 4px 20px 12px;" @tap="gotoItem(data)">
                        <view slot="note">
                            <view class="media-list">
                                <view class="media-top">
                                    <status-button>{{ data.scheStatus | lov('SCHE_STATUS') }}</status-button>
                                </view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">排期运营名称</view>
                                <view class="name">{{ data.scheduleName ? data.scheduleName : '' }}</view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">排期人群</view>
                                <view class="name">{{ data.scheduleCrowd | lov('SCHEDULE_CROWD') }}</view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">排期导向</view>
                                <view class="name">{{ data.scheGuide ? convertScheGuide(data.scheGuide) : '' }}</view>
                            </view>
                            <view class="content-middle" v-if="data.term">
                                <view class="lable-name">期限（天）</view>
                                <view class="name">{{ data.term }} 天</view>
                            </view>
                            <view class="content-middle" v-if="data.startTime && data.endTime">
                                <view class="lable-name">排期时间</view>
                                <view class="name">{{ (data.startTime).substr(0,10) }} - {{ (data.endTime).substr(0,10) }}</view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">创建人</view>
                                <view class="name">{{ data.createdName ? data.createdName : ''}}</view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
        <view class="target-customers-content" v-if="scheduleStatusActive.seq === '1'">
            <view class="tab-filter">
                <view class="lnk-tabs-content">
                    <view class="lnk-tabs-item" :class="{'active': tab.seq === customersStatusActive.seq}"
                          v-for="(tab, index) in customersStatusList" :key="index" @tap="switchTabChoose(tab, index)">
                        <view class="label-name-line">
                            <text class="label-name-text">{{tab.name}}</text>
                            <view class="line" v-if="tab.seq === customersStatusActive.seq"></view>
                        </view>
                    </view>
                </view>
            </view>
            <link-auto-list :option="guestList" hideCreateButton :key="1" :searchInputBinding="{props:{placeholder:'消费者姓名/手机号'}}">
                <template slot-scope="{data,index}">
                    <link-swipe-action>
                        <item :key="index" :data="data" style="border-bottom: 0.5px solid #f2f2f2;" @tap="gotoCustomers(data)">
                            <view slot="note" class="guest-list-item">
                                <view class="logo-img">
                                    <image class="media-list-logo" :src="data|headImgAccount(data)"/>
                                    <view style="width: 32px;height: 12px; margin-top: -10px;"><image style="width: 100%;height: 100%;" :src="data.addCertify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image></view>
                                </view>
                                <view class="guest-list-item-content">
                                    <view class="guest-list-item-content-row1">
                                        <view class="consumer-name" :style="{ maxWidth: (data.isAddApprove === 'Y' && data.endAuditStatus) ? 'calc(100% - 90px)' : '100%' }">
                                            {{data.consumerName ? data.consumerName : ''}}
                                        </view>
                                    </view>
<!--                                    <view class="guest-list-item-content-row1">{{data.consumerName ? data.consumerName : ''}}</view>-->
                                    <view class="guest-list-item-content-row2">{{ data.consumerPhone ? data.consumerPhone : '' }}</view>
                                    <view class="guest-list-item-content-row2">
                                        排期运营名称：{{ data.scheduleName ? data.scheduleName : ''}}
                                    </view>
                                    <view class="guest-list-item-content-row2">
                                        排期时间：{{ data.conStartTime ? (data.conStartTime).substr(0,10) : ''}} - {{ data.conEndTime ? (data.conEndTime).substr(0,10) : ''}}
                                    </view>
                                </view>
                                <view class="content-status">
                                    <!--  颜色规则见lzlj-zhyx06-8410  -->
                                    <view v-if="(data.endAuditStatus === 'EndPass' && data.status === 'End') || ($utils.isEmpty(data.endAuditStatus) && data.status === 'New') || ($utils.isEmpty(data.endAuditStatus) && data.status === 'End') || (data.endAuditStatus === 'EndPass' && data.status === 'New') || (data.endAuditStatus === 'AddPass' && data.status === 'End')" :class="data.status === 'End' ? 'endPass' : 'blue'">{{data.status | lov('TARGET_ACCT_STATUS')}}</view>
                                    <view v-else-if="((data.status === 'New' || data.status === 'End') && ['AddReject', 'AddReviewing'].includes(data.endAuditStatus) || (data.status === 'Processing' && data.endAuditStatus === 'EndReviewing'))" class="blue">{{data.endAuditStatus | lov('SCHE_ADUIT_STATUS')}}</view>
                                    <view v-else-if="data.status === 'Processing' && ['AddPass', 'EndRejected'].includes(data.endAuditStatus)" :class="data.targetStatus === 'Finished' ? 'green' : 'red'">{{data.targetStatus | lov('TARGET_STATUS_SCHE')}}</view>
                                    <view v-else-if="data.status === 'Processing' && $utils.isEmpty(data.endAuditStatus)" :class="data.targetStatus === 'Finished' ? 'green' : 'red'">{{data.targetStatus | lov('TARGET_STATUS_SCHE')}}</view>
                                </view>
                            </view>
                        </item>
                        <link-swipe-option label="终止" @tap="endConsumer(data)" v-if="data.status === 'Processing' && userInfo.postnId === data.chargePersonId && !(data.conEndTime < nowTime)" slot="option"/>
                        <link-swipe-option label="删除" @tap="deleteConsumer(data)" v-if="data.status === 'New' && ($utils.isEmpty(data.endAuditStatus) || data.endAuditStatus === 'AddReject') && userInfo.postnId === data.chargePersonId" slot="option"/>
                    </link-swipe-action>
                </template>
            </link-auto-list>
        </view>
    </link-page>
</template>

<script>
import {$logger} from "@/utils/log/$logger"
import StatusButton from "../../lzlj/components/status-button.vue";
import LnkTaps from "../../core/lnk-taps/lnk-taps.vue";
import {isEmpty} from "../../../components/painter/libs/util";

export default {
    name: 'consumers-schedule-list',
    components: {LnkTaps, StatusButton},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        let autoList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            searchFields: ['scheduleName', 'createdName','id'],
            loadOnStart: false,
            exactSearchFields: [
                {
                    field: 'scheduleName',
                    showValue: '排期运营名称',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },{
                    field: 'createdName',
                    showValue: '创建人',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },
            ],
            param: {
                dmpUrl: '/link/consumerSchedule/queryByExamplePage',
                filtersRaw: [
                    {id: 'scheStatus', property: 'scheStatus', value: '[Processing,Closed]', operator: 'in'}
                ]
            },
            scrollToTop: false,
            sortOptions: null,
            slots: {
                searchRight: () => (
                    <view class="filter-type-item" style="max-width: 224rpx; min-width:210rpx; height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 12rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;" onTap={this.chooseOauthData}>{this.pageOauthName}<link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/></view>
                )
            }
        });
        const pageOauthList = this.pageParam.secMenus;
        const statusOptions = [
            {name: '排期运营列表', seq: '0'},
            {name: '排期消费者', seq: '1'},
        ];
        const customersStatusList = [
            {name: '全部', value: 'ALL', seq: '0'},
            {name: '已达成', value: 'Finished', seq: '1'},
            {name: '未达成', value: 'Failed', seq: '2'}
        ];
        const filtersRawTemp = [{id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}];
        const guestList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            exactSearchFields: [
                {
                    field: 'consumerName',
                    showValue: '消费者姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },
                {
                    field: 'consumerPhone',
                    showValue: '手机号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            scrollToTop: false,
            param: {
                dmpUrl: '/link/consumerScheduleTarget/queryTargetConsumerList',
                filtersRaw: [
                    {id: 'scheStatus', property: 'scheStatus', value: 'Processing', operator: '='}
                ]
            },
            slots: {
                searchRight: () => (
                    <view class="filter-type-item" style="max-width: 224rpx; min-width:180rpx; height: 72rpx;display: flex;align-items: center;justify-content: flex-end;padding-left: 16rpx;font-family: PingFangSC-Regular;font-size: 26rpx;color: #333333;line-height: 40rpx;font-weight: 400;">{this.pageOauthName}</view>
                )
            }
        });
        return {
            statusOptions,
            scheduleStatusActive: {},
            userInfo,
            autoList,
            pageOauthList,
            pageOauth: '',           // 列表安全性
            toItemOauth: '',        // 传递给详情页排期消费者的安全性
            pageOauthName: '',
            customersStatusList,
            customersStatusActive: {},
            filtersRawTemp,
            guestList,
            oauthRule: [
                {name: '我的区域的数据', securityMode: 'MY_ORG', pageOauth: ''},
                {name: '我的团队的数据', securityMode: 'MY_POSTN', pageOauth: ''},
                {name: '我的职位的数据', securityMode: 'MY_POSTN_ONLY', pageOauth: ''}
            ],
            endReasonText: '',    // 终止原因
            translatedScheGuide: '', // 排期导向
            guideList: [],
            nowTime: '',
            isGotoPreCreate: 'N',  // 是否跳转审批预览页面
            headUserId: null,
            nodeApprovers: [],
            endScheConsumerIsV3: false,
            endScheConsumerData: {}
        }
    },
    async created() {
        this.scheduleStatusActive = this.statusOptions[0];
        await this.initOauth();
        await this.checkIsInMemberEmp();
        // 获取当前系统时间：当前系统时间大于消费者排期结束时间
        const nowTime = await this.$utils.getServerTime();
        this.nowTime = this.$date.format(new Date(nowTime), 'YYYY-MM-DD HH:mm:ss')
        this.guideList = await this.$lov.getLovByType('SCHE_GUIDE')
    },
    async mounted() {
        // 变更了执行情况，刷新列表
        this.$bus.$on('refreshList', async () => {
            console.log('列表refreshList');
            await this.guestList.methods.reload();
        })
    },
    methods: {
        /**
         * @desc 判断是否是v3需要审批
         * <AUTHOR>
         * @date 2025-03-06
         **/
         async isV3(applyType='AddScheConsumer') {
            return new Promise(async (res, rej) => {
                const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                    companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    applyType
                });
                console.log('result', result)
                if (success && result === 'v3') {
                    res(true);
                } else {
                    res(false);
                }
            });
        },
        /**
         * @desc 校验当前登录人职位是否为【权限人员】/【1v1服务人员】，该逻辑同超高价值会员列表，由前端处理判断
         * <AUTHOR>
         * @date 2024-10-17
         **/
        async checkIsInMemberEmp () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/identityEmp/queryByExamplePage', {
                filtersRaw: [
                    {id: 'waitstaffType', property: 'waitstaffType', value: '[ServiceStaff, BusinessAgent]', operator: 'in'},
                    {id: 'waitstaffStatus', property: 'waitstaffStatus', value: 'Y'},
                    {id: 'waitstaffPostnId', property: 'waitstaffPostnId', value: this.userInfo.postnId},
                ]
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`查询权限人员失败：${response.result}`);
                }
            });
            if (data.success) {
                // 获取参数配置
                const company = await this.$utils.getCfgProperty('SCHE_HIGH_CONSUMER');
                const loginCompanyld = this.userInfo.coreOrganizationTile.l3Id
                let inLoginCompanyldFlag = false
                if (!isEmpty(company)) {
                    const companyArray = company.split(',')
                    inLoginCompanyldFlag = companyArray.includes(loginCompanyld)
                }
                // 职位不是权限业代也不是1v1服务人员且当前登录职位所属公司不在参数配置内时，只查询核心消费者
                if (data.rows.length === 0 && !inLoginCompanyldFlag) {
                    this.autoList.option.param.filtersRaw.push({id: 'scheduleCrowd', property: 'scheduleCrowd', value: 'CoreConsumer', operator: '='});
                }
                this.autoList.methods.reload();
            }
        },
        /**
         * @desc 处理排期导向字段
         * <AUTHOR>
         * @date 2024-07-22
         **/
        convertScheGuide(scheGuide) {
            if (!scheGuide) return '';
            const scheGuideArr = scheGuide.split(',');
            const translatedScheGuide = scheGuideArr
                .map(item => this.guideList.find(guide => guide.val === item))
                .filter(guide => guide)
                .map(guide => guide.name)
                .join('，');
            return translatedScheGuide;
        },
        /**
         * @desc 初始化安全性
         * <AUTHOR>
         * @date 2024-07-22
         **/
        async initOauth() {
            const securityModes = await Promise.all([
                this.$utils.getCfgProperty("schedule_securityMode_org"),
                this.$utils.getCfgProperty("schedule_securityMode_postn"),
                this.$utils.getCfgProperty("schedule_securityMode_postn_only")
            ]);
            const [securityModeOrg, securityModePostn, securityModePostnOnly] = securityModes;
            this.oauthRule.forEach(rule => {
                switch (rule.securityMode) {
                    case 'MY_ORG':
                        rule.pageOauth = securityModeOrg;
                        break;
                    case 'MY_POSTN':
                        rule.pageOauth = securityModePostn;
                        break;
                    case 'MY_POSTN_ONLY':
                        rule.pageOauth = securityModePostnOnly;
                        break;
                }
            });
            this.pageOauthList.forEach(item => {
                const matchingRule = this.oauthRule.find(rule => rule.securityMode === item.securityMode);
                if (matchingRule) {
                    item.pageOauth = matchingRule.pageOauth;
                }
            });
            // 250102修改逻辑：1、当存在我职位MY_POSTN_ONLY的数据，默认展示我职位MY_POSTN_ONLY的数据；2、当不存在我职位MY_POSTN_ONLY的数据，存在我团队MY_POSTN的数据，默认展示我团队MY_POSTN的数据；3、当不存在我职位MY_POSTN_ONLY的数据，也不存在我团队MY_POSTN的数据，默认展示我区域MY_ORG的数据；
            const defaultOauth = this.pageOauthList.find(item => item.securityMode === 'MY_POSTN_ONLY') ||
                this.pageOauthList.find(item => item.securityMode === 'MY_POSTN') ||
                this.pageOauthList.find(item => item.securityMode === 'MY_ORG');
            if (defaultOauth) {
                this.pageOauthName = defaultOauth.name;
                this.pageOauth = defaultOauth.pageOauth;
                this.toItemOauth = defaultOauth.securityMode;
            }
            console.log('this.pageOauthList', this.pageOauthList);
            this.autoList.option.param.oauth = this.pageOauth;
            this.guestList.option.param.oauth = this.toItemOauth;
        },
        /**
         * @desc 删除消费者
         * <AUTHOR>
         * @date 2024-12-17
         **/
        async deleteConsumer(data) {
            this.$dialog({
                title: '提示',
                content: '确定删除消费者吗？',
                confirmText: '确定',
                onConfirm: async () => {
                    await this.deleteConsumerInfo(data);
                },
                cancelButton: true
            })
        },
        async deleteConsumerInfo(data) {
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/consumerScheduleTarget/remove',
                id: data.id
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`删除消费者失败：${response.result}`);
                }
            });
            if (res) {
                this.$message.success('删除消费者成功！');
                await this.guestList.methods.reload();
            }
        },
        /**
         * @desc 终止消费者
         * <AUTHOR>
         * @date 2024-07-31
         **/
        async endConsumer(data) {
            console.log('终止点击-data', data);
            if(data.isEndAcct === 'N') {
                // 不需要走审批
                await this.cancelConsumer(data);
            }
            if(data.isEndAcct === 'Y') {
                // 需要走审批
                this.endReasonText = '';
                this.$dialog({
                    title: '提示',
                    content: (h) => {
                        return (
                            <view>
                                <view>终止后代表消费者不再参与排期运营，是否确认终止？</view>
                                <link-form-item required label="终止原因" vertical>
                                    <link-textarea placeholder="请填写申请说明，100字以内" padding-start padding-end
                                                   v-model={this.endReasonText} nativeProps={{maxlength: 100}}>
                                    </link-textarea>
                                </link-form-item>
                            </view>
                        )
                    },
                    confirmText: '提交终止申请',
                    cancelButton: true,
                    onConfirm: async () => {
                        console.log('data', data);
                        this.endScheConsumerData = data;
                        this.endScheConsumerIsV3 = await this.isV3(data.scheduleCrowd=='HighValueAcct'?'EndScheHighConsumer':'EndScheConsumer');
                        console.log('判断审批类型走V3还是V2', this.endScheConsumerIsV3);
                        if(this.endScheConsumerIsV3) {
                            const newId = data.newId = await this.$newId();
                            data.positionType = this.userInfo.positionType;
                            console.log('this.userInfo.positionType', this.userInfo.positionType);
                            this.headUserId = data.flowStartPsnId = await this.queryByPostinId();
                            this.isGotoPreCreate = await this.gotoPreCreateFlow(data);
                            console.log('走V3', this.isGotoPreCreate);
                            if (this.isGotoPreCreate === 'Y') {
                                await this.confirmEndConsumer(data, this.endScheConsumerIsV3);
                            } else {
                                console.log('submitData', data);
                                this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                                    submitData: data,
                                    submitUser: this.userInfo,
                                    flowObjId: newId,
                                    source: 'EndScheConsumerSubmit',
                                    // 审批类型编码
                                    approvalType: data.scheduleCrowd=='HighValueAcct'?'EndScheHighConsumer':'EndScheConsumer'
                                });
                            }
                        } else {
                            await this.confirmEndConsumer(data, this.endScheConsumerIsV3);
                        }
                    }
                })
            }
        },
        /**
         * @Author: 胡政民
         * @Date: 2025-05-15 17:43:29
         * @method: queryByPostinId
         * @LastEditTime: Do not edit
         * @return {*}
         * @description:
         */
         async queryByPostinId() {
            const {success, result} = await this.$http.post(this.$env.appURL + '/action/link/identityEmp/queryByPostnId', {
                postnId: this.userInfo.postnId,
                waitstaffType:"BusinessAgent"
            });
            if(success) {
                return result.headUserId;
            } else {
                this.$showError('查询审批人失败！' + result);
            }
         },
        /**
         * @desc 判断是否需要唤起预览页
         * <AUTHOR>
         * @date 2025-03-20
         **/
         async gotoPreCreateFlow(data) {
            const {success, result} = await this.$http.post('action/link/flow/v3/preCreateFlow', {
                // 审批对象ID
                objectId: data.id,
                // 审批类型编码
                approvalType: data.scheduleCrowd=='HighValueAcct'?'EndScheHighConsumer':'EndScheConsumer',
                // 业务对象JSON字符串
                flowObjDetail: JSON.stringify(data),
                // flowObjId传订单id
                flowObjId: data.id,
                // 提交用户id
                submitUserId: this.userInfo.id,
                // 提交用户职位id
                submitPostnId: this.userInfo.postnId,
                // 提交用户组织id
                submitOrgId: this.userInfo.orgId,
                // 提交审批人id
                flowStartPsnId: this.headUserId
            });
            if(success) {
                return result.jumpApproval;
            } else {
                this.$showError('预生成审批记录失败！' + result);
            }
        },

        /**
         * @desc 调用-终止消费者接口-不需要走审批
         * <AUTHOR>
         * @date 2024-07-31
         **/
        async cancelConsumer(data) {
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/consumerScheduleTarget/cancel',
                id: data.id
            }, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`终止消费者失败：${response.result}`);
                }
            });
            if (res) {
                this.$message.success('终止消费者成功！');
                await this.guestList.methods.reload();
            }
        },
        /**
         * @desc 调用-终止消费者接口-需要走审批
         * <AUTHOR>
         * @date 2024-07-31
         **/
        async confirmEndConsumer(data, isV3) {
            let param = {};
            if(isV3) {
                param = {
                    id: data.id,
                    dmpUrl: '/link/consumerScheduleTarget/terminate',
                    endReason: this.endReasonText,
                    nodeApprovers: this.nodeApprovers,
                    approvalRecordId: data.newId,
                    jumpApproval: this.isGotoPreCreate
                }
            } else {
                param = {
                    id: data.id,
                    dmpUrl: '/link/consumerScheduleTarget/terminate',
                    endReason: this.endReasonText
                }
            }
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`终止消费者失败：${response.result}`);
                }
            });
            if (res) {
                this.$message.success('终止消费者审批提交成功！');
                await this.guestList.methods.reload();
            }
        },
        /**
         * @desc 跳转详情-消费者排期运营-详情
         * <AUTHOR>
         * @date 2024-07-09
         **/
        gotoItem(data) {
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-detail-page.vue', {
                data: data,
                oauth: this.toItemOauth,
                source: 'list'
            })
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryDetail(scheduleId) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: scheduleId
                });
                if(data.success) {
                    this.customersItem = data.result;
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 跳转详情-消费者排期运营执行情况
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async gotoCustomers(data) {
            // 消费者排期详情埋点：事件编码、排期id、排期消费者id（row_Id）、消费者id、菜单id
            try{
                $logger.info('CONSUMER_SCHEDULE_002', 'Click', `菜单id：consumer_schedule_排期id：${data.scheduleId}_排期消费者行id：${data.id}_消费者id：${data.acctId}`);
            } catch (e) {
                console.log('e', e)
                $logger.error('CONSUMER_SCHEDULE_002', 'Click', `菜单id：consumer_schedule_排期id：${data.scheduleId}_排期消费者行id：${data.id}_消费者id：${data.acctId}_报错信息：${e}`);
            }
            await this.queryDetail(data.scheduleId);
            if(data.status === 'New' && data.endAuditStatus !== 'AddReviewing' && data.isAddApprove === 'Y' && (['ActionPackageOption', 'ActionNone'].includes(this.customersItem.scheActionType) || ['TargetPackageOption', 'TargetNone'].includes(this.customersItem.scheTargetType))) {
                // 添加消费者
                this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-add-consumer-page.vue', {
                    data: data,
                    scheInfoId: data.id,
                    scheduleId: data.scheduleId,
                    oauth: this.oauth
                })
            } else {
                // 排期运营执行情况
                this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-detail-implementation-page.vue', {
                    data: data,
                    scheduleId: data.scheduleId,
                    scheInfoId: data.id,
                    oauth: this.toItemOauth
                })
            }
        },
        /**
         * @desc 监听返回
         * <AUTHOR>
         * @date 2022/4/1 11:19
         **/
        async onBack(param) {
            console.log('详情onBack', param);
            if (param && param.flag === 'flow') {
                this.$set(this, 'nodeApprovers', param.nodeDtos);
                if(param.source === 'EndScheConsumerSubmit') {
                    await this.confirmEndConsumer(this.endScheConsumerData, this.endScheConsumerIsV3);
                }
            }
            await this.autoList.methods.reload();
        },
        /**
         * @desc 选择不同状态/全部/已达成/未达成
         * <AUTHOR>
         * @date 2024-07-18
         **/
        async switchTabChoose (tab) {
            this.customersStatusActive = tab;
            let filtersRaw = this.guestList.option.param.filtersRaw;
            if (tab.seq === '0') {
                filtersRaw = filtersRaw.filter(item => item.property !== 'targetStatus');
            } else {
                filtersRaw = filtersRaw.filter(item => item.property !== 'targetStatus');
                filtersRaw.push({id: 'targetStatus', property: 'targetStatus', value: tab.value, operator: '='});
            }
            this.guestList.option.param.filtersRaw = filtersRaw;
            await this.guestList.methods.reload();
        },
        /**
         * @desc 切换tab页签
         * <AUTHOR>
         * @date 2022/4/13 10:33
         **/
        async switchTab(val) {
            this.scheduleStatusActive = val;
            console.log('switchTab', val);
            if(val.seq === '0'){
                this.autoList.methods.reload();
            }
            if(val.seq === '1'){
                this.customersStatusActive = this.customersStatusList[0];
                // this.guestList.methods.reload();
            }
        },
        /**
         * @createdBy 何春霞
         * @date 2024-04-22
         * @methods: chooseOauthData
         * @description: 选择页面安全性
         **/
        chooseOauthData() {
            this.$actionSheet(() => (
                <link-action-sheet title="请选择数据范围" onCancel={() => {
                }}>
                    {this.pageOauthList.map((item) => {
                        return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>
                    })}
                </link-action-sheet>
            ));
        },
        /**
         * @createdBy 何春霞
         * @date 2024-04-22
         * @methods: pageOauthChange
         * @para: oauth 安全性
         * @description: 页面安全性切换
         **/
        pageOauthChange(oauth) {
            console.log('oauth', oauth);
            this.$utils.showLoading();
            this.autoList.list = [];
            this.pageOauthName = oauth.name;
            this.pageOauth = oauth.pageOauth;
            this.toItemOauth = oauth.securityMode;
            this.autoList.option.param.oauth = oauth.pageOauth;
            this.guestList.option.param.oauth = this.toItemOauth;
            this.autoList.methods.reload();
            this.$utils.hideLoading();
        },
    }
}
</script>

<style lang="scss">
.consumers-schedule-list {
    .blank {
        width: 100%;
        height: 96px;
        background: #F2F2F2;
    }
    /*deep*/
    .link-swipe-option-container .link-swipe-option {
        width: 80px;
        height: 60px !important;
        border-radius: 60px;
        font-size: 24px !important;
    }
    .link-input.link-input-text-align-left {
        text-align: left;
        max-width: 490px;
    }
    .perform-case-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }

    .media-list {
        //line-height: 56px;
        //height: 56px;
        float: right;

        .media-top {
            width: 100%;
            @include flex-start-center;
            @include space-between;

            .num-view {
                width: 75%;
                overflow-x: auto;
                display: flex;
                line-height: 50px;

                .num {
                    flex-shrink: 0;
                    //letter-spacing: 0;
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                    background: #A6B4C7;
                    border-radius: 8px;
                }
            }

            .status-view {
                min-width: 120px;
                height: 36px;
                line-height: 36px;
                text-align: center;
                color: #ffffff;
                background: #2F69F8;
                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                border-radius: 8px;
                font-size: 20px;
                margin-right: 8px;
                -webkit-transform: skewX(-30deg);
                -ms-transform: skewX(-30deg);
                transform: skewX(-30deg);

                &-refuse {
                    background: #FF5A5A !important;
                    box-shadow: 0 3px 4px 0 rgba(255, 90, 90, 0.35) !important;
                }

                .status {
                    transform: skewX(30deg);
                    font-size: 20px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 36px;
                }
            }
        }
    }

    .content-middle {
        width: 95%;
        display: flex;
        align-items: center;
        font-size: 28px;
        letter-spacing: 0;
        font-family: PingFangSC-Regular;
        line-height: 52px;
        overflow-x: auto;

        .lable-name {
            color: #8C8C8C;
            margin-right: 10px;
            min-width: 180px;
            flex-shrink: 0;
        }

        .name {
            color: #262626;
            width: 70%;
             //    超出部分滑动显示
            flex-shrink: 1;
            white-space: nowrap;
            overflow: auto;
        }
    }

    .tab-filter{
        .active {
            color: $color-primary;
        }
        .lnk-tabs-content {
            display: flex;
            justify-content: space-around;
            flex-wrap: nowrap;
        }
        .lnk-tabs-item {
            height: 92px;
            line-height: 92px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            .label-name-line {
                position: relative;
                font-size: 28px;
                margin-left: 10px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                .num-tips {
                    position: absolute;
                    top: 0;
                    right: 0;
                    transform: translateX(90%);
                    background-color: #FF5A5A;
                    color: #ffffff;
                    font-size: 24px;
                    border-radius: 50%;
                    padding-left: 16px;
                    padding-right: 16px;
                    height: 40px;
                    line-height: 40px;
                    text-align: center;
                }
                .line {
                    height: 8px;
                    width: 56px;
                    border-radius: 16px 16px 0 0;
                    background-color: $color-primary;
                    box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                    margin-top: -8px;
                }
            }
        }
    }

    .guest-list-item-icon {
        width: 80px;
        text-align: center;

        .iconfont {
            font-size: 40px;
            color: #bfbfbf;
        }
    }
    .guest-list-item {
        @include flex;
        @include flex-start-center;
        overflow: hidden;
        .logo-img {
            width: 15%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-right: 20px;
            align-items: center;
        }
        .media-list-logo {
            height: 94px;
            width: 94px;

            image {
                height: 100%;
                width: 100%;
            }
        }
        .guest-list-logo {
            border-radius: 50%;
            width: 80px;
            height: 80px;
            overflow: hidden;
        }

        .guest-list-item-content {
            width: 55%;
            padding-right: 16px;
            display: flex;
            flex-direction: column;

            .guest-list-item-content-row1 {
                width: 100%;
                display: flex;
                .consumer-name {
                    display: block;
                    margin-bottom: 4px;
                    white-space: nowrap;
                    line-height: 48px;
                    font-size: 26px;
                    color: #333333;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }
                .consumer-flag {
                    background-color: #F6F6F6;
                    width: 170px;
                    height: 48px;
                    display: inline-block;
                    font-size: 24px;
                    line-height: 48px;
                    border-radius: 8px;
                    text-align: center;
                    margin-left: 16px;

                    .consumer-flag-text {
                        color: #9F9F9F;
                    }
                }
            }
            .guest-list-item-content-row2 {
                display: block;
                max-width: 100%;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
                font-size: 24px;
                line-height: 48px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }
        }
        .content-status {
            width: 30%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: sticky; /* 使按钮始终可见 */
            right: 0; /* 将按钮固定在右侧 */
            font-size: 24px;
            height: 48px;
            .red {
                color: red;
            }
            .blue {
                color: #306cff;
            }
            .green {
                color: #00B050;
            }
            .endPass {
                color: #9F9F9F;
            }
        }

    }

}
</style>
