<!--
执行情况-执行人信息
<AUTHOR>
@date 2025-02-17
@file implement-person-info.vue
-->
<template>
    <view class="implement-person-info">
        <line-title title="执行人信息"></line-title>
<!--        <view class="implement-person" v-if="personData.length > 0">-->
        <view class="implement-person">
            <view class="line">
                <view class="name">{{'执行人'}}</view>
                <view class="add" v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                            && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)
                                            && !['Finished', 'Planing', 'Invalid'].includes(data.planDeStatus)
                                            && scheduleItem.status !== 'End'
                                            && isPrincipal
                                            && consumerTimeFlag"
                      @tap="addPerson">{{'添加'}}</view>
            </view>
            <view class="implement-person-data">
                <link-swipe-action v-for="(item,index) in personData" :key="item.id">
                    <link-swipe-option slot="option"
                                       v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                            && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)
                                            && !['Finished', 'Planing', 'Invalid'].includes(data.planDeStatus)
                                            && scheduleItem.status !== 'End'
                                            && isPrincipal
                                            && consumerTimeFlag"
                                       @tap="deletePersonData(item, index)">失效</link-swipe-option>
                    <view class="implement-person-item">
                        <view class="left">
                            <view class="name">{{ item.firstName }}</view>
                            <view class="postn">{{ item.postnName }}</view>
                        </view>
                        <view class="right">{{ item.deptName }}</view>
                    </view>
                </link-swipe-action>
            </view>
        </view>

    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";
import Taro from "@tarojs/taro";

export default {
    name: 'implement-person-info',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
            }
        },
        consumerTimeFlag: {
            type: Boolean,
            default: false
        },
        oauth: {
            type: String,
            default: ''
        },
        headData: {
            type: Object,
            default: () => {
            }
        },
        data: {
            type: Object,
            default: () => {
            }
        },
        isPrincipal: {
            type: Boolean,
            default: false
        }
    },
    data() {
        const personOption = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            // loadOnStart: false,
            param: {
                dmpUrl: '/link/scheduleRelatedPostn/queryAddExecutor'
            },
            searchFields: ['fstName', 'postnName', 'deptName'],
            exactSearchFields: [
                {
                    field: 'fstName',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },
                {
                    field: 'postnName',
                    showValue: '职位',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },
                {
                    field: 'deptName',
                    showValue: '组织',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                }
            ],
            hooks: {
                beforeLoad(option) {
                    if(this.userInfo.orgId) {
                        option.param.businessOrgId = this.userInfo.orgId;
                    }
                },
                afterLoad: async (data) => {
                }
            },
            renderFunc: (h, {data, index}) => {
                return (<item key={index} title={data.fstName} data={data} note={data.postnName}
                              desc={data.deptName}></item>)
            }
        });
        return {
            personData: [],  // 协同领导数据
            personOption,
            userInfo: {},


        }
    },
    async created() {
        this.userInfo = Taro.getStorageSync('token').result;
        console.log('userInfo', this.userInfo)
        // await this.personOption.methods.reload();
        await this.queryPerson();
    },
    methods: {
        /**
         * @desc 查询执行人
         * <AUTHOR>
         * @date 2025-02-18
         **/
        async queryPerson() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/queryExecutorByInfoId',
                    scheInfoId: this.scheduleItem.id,
                    status: 'Y'
                });
                if(data.success) {
                    this.personData = data.rows;
                } else {
                    this.$message.error('查询执行人失败', data.message);
                }
            } catch (e) {
                console.log('查询执行人出错', e)
            }
        },
        /**
         * @desc 添加执行人
         * <AUTHOR>
         * @date 2025-02-17
         **/
        async addPerson() {
            console.log('addPerson')
            const list = await this.$object(this.personOption, {
                multiple: false,
                pageTitle: "请选择执行人"
            });
            console.log('选择了的数据list', list)
            // 判断personData中是否已经存在，如果存在则不添加，后端处理
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/insert',
                    scheInfoId: this.scheduleItem.id,
                    businessPostnId: list.postnId,
                    businessType: 'scheExecutor'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        setTimeout(() => {
                            this.$utils.hideLoading();
                            setTimeout(() => {
                                // eg.当前执行人已添加，请勿重复添加！[***********-0428153956Q9.99]\n[***********-0428153956Q9.99]
                                // 只取一个linkrequestId
                                this.$message.error(`添加执行人出错：${response.result.split('[')[0]}[${response.result.split('[')[1]}`);
                            }, 1000);
                            return Promise.reject(response.result);
                        }, 1000)
                    }
                });
                if (data.success) {
                    if (data.newRow) {
                        this.personData = this.personData.concat(data.newRow);
                    } else {
                        this.personData = this.personData.concat(list);
                    }
                } else {
                    this.$message.error('选择执行人失败', data.result);
                }
            } catch (e) {
                console.log('选择执行人出错',e, e.result)
            }
        },
        /**
         * @desc 失效执行人
         * <AUTHOR>
         * @date 2025-02-18
         **/
        async deletePersonData(item, index) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/updateStatus',
                    id: item.id,
                    status: 'N'
                });
                if (data.success) {
                    this.personData.splice(index, 1);
                } else {
                    this.$message.error('失效执行人失败', data.message);
                }
            } catch (e) {
                console.log('查询执行人出错', e)
            }
        }
    }
}
</script>

<style lang="scss">
.implement-person-info {
    width: 100%;
    //.link-swipe-option-container .link-swipe-option {
    //    width: 60px;
    //    height: 40% !important;
    //    margin: 10px;
    //}
    .link-swipe-action {
        width: 100% !important;
    }

    /*deep*/
    .link-swipe-option-container .link-swipe-option {
        width: 60px;
        height: 40% !important;
        border-radius: 30px;
        font-size: 24px !important;
        margin: 60px 10px;
    }
    .implement-person {
        width: 94%;
        margin: 16px auto;
        .line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 16rpx;
            padding: 24rpx 28rpx;
            background: white;
            .name {
                font-size: 28px;
                color: #777777;
            }
            .add {
                font-size: 28px;
                color: #2F69F8;
            }
        }
        .implement-person-data {
            display: flex;
            flex-direction: column;
            .implement-person-item {
                display: flex;
                padding: 24px 28px;
                background: white;
                border-radius: 16px;
                margin-top: 16px;
                font-size: 28px;
                color: #262626;
                .left {
                    width: 65%;
                    display: flex;
                    flex-direction: column;
                    //justify-content: center;
                    .name {
                        width: 100%;
                        font-size: 28px;
                        color: #262626;
                        flex-shrink: 0;
                        overflow: auto;
                        white-space: nowrap; /* 防止换行 */
                    }
                    .postn {
                        font-size: 24px;
                        color: #777777;
                    }
                }
                .right {
                    width: 32%;
                    max-width: 32%;
                    font-size: 28px;
                    color: #777777;
                    display: flex;
                    align-items: center;
                    //justify-content: flex-end;
                    margin-left: 12px;
                    flex-shrink: 0;
                    overflow: auto;
                    white-space: nowrap; /* 防止换行 */
                }
            }
        }
    }
}
</style>
