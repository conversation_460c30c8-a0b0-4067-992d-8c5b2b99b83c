<!--
执行情况-动作-待提交动作排期计划
<AUTHOR>
@date 2025-01-08
@file implement-action-submit.vue
-->
<template>
    <view class="implement-action-submit" v-show="submitTabFlag">
        <line-title title="待提交动作排期计划" v-if="showflag"></line-title>
        <view class="zero-view" v-if="showflag"></view>
        <link-auto-list :option="actionList" hideCreateButton :key="5" v-if="showflag">
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <item :key="index" :data="data" :arrow="false" class="overview-item"
                          style="padding: 14px 14px 2px 14px;">
                        <view slot="note" @tap="gotoDetail(data)">
                            <view class="account-label" style="background: #8e97a6">
                                <view class="label" v-if="['New', 'Invalid'].includes(data.planDeStatus)">{{'待提交-'}}{{ data.planDeStatus | lov('SCHE_PLAN_STATUS') }}</view>
                                <view class="label" v-else>{{'待提交-变更'}}</view>
                            </view>
                            <view class="item-input" style="margin-top: 25px">
                                <view class="label">{{ '动作大类' }}</view>
                                <view class="text">{{ data.planScheType | lov('SCHE_TYPE') }}</view>
                            </view>
                            <view class="item-input" v-if="!!data.planScheChild">
                                <view class="label">{{ '动作小类' }}</view>
                                <view class="text">{{ data.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                            </view>
                            <view class="item-input">
                                <view class="label">{{ '预估执行时间' }}</view>
                                <view class="text">{{ data.beginTime ? (data.beginTime).substr(0, 10) : '' }} -
                                    {{ data.endTime ? (data.endTime).substr(0, 10) : '' }}
                                </view>
                            </view>
                            <view class="item-input">
                                <view class="label">{{ '目标值' }}</view>
                                <view class="text">{{ data.planNum }}</view>
                            </view>
                            <view class="item-input" style="flex-direction: column" v-if="!!data.planDesc">
                                <view class="label">动作计划说明</view>
                                <link-textarea disabled placeholder="请输入动作计划说明，最多输入100字" style="padding: 0;margin-top: 10px"
                                               v-model="data.planDesc" :nativeProps="{maxlength:100}"></link-textarea>
                            </view>
                        </view>
                    </item>
                    <!--排期计划审核状态planStatus处于新建审核中、变更审核中、作废审核中的排期计划不展示任何按钮-->
                    <!--除了作废，其他的状态都展示修改按钮-->
                    <link-swipe-option slot="option" color="primary"
                                       v-if="data.planDeStatus !== 'Invalid'
                                       && isPrincipal
                                       && consumerTimeFlag
                                       && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)"
                                       @tap="editScheduling(data)">修改
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                        v-if="data.planStatus === 'InsertReject'
                                        && isPrincipal
                                        && consumerTimeFlag
                                        && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)"
                                       @tap="inValidScheduling(data)">作废
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                       && isPrincipal
                                       && consumerTimeFlag
                                       && (!!data.planDeStatus && !['New', 'Invalid'].includes(data.planDeStatus))"
                                       @tap="recallScheduling(data)">撤回变更
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                       && isPrincipal
                                       && consumerTimeFlag
                                       && (!!data.planDeStatus && data.planDeStatus === 'Invalid')"
                                       @tap="recallScheduling(data)">撤回作废
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="data.planDeStatus === 'New'
                                       && isPrincipal
                                       && consumerTimeFlag
                                       && !['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)"
                                       @tap="deleteScheduling(data)">删除
                    </link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-action-submit',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        },
        pageFrom: {
            type: String,
            default: ''
        },
        headData: {
            type: Object
        },
        consumerTimeFlag: {
            type: Boolean,
            default: false
        },
        isPrincipal: {
            type: Boolean,
            default: false
        },
        conStartTime: {
            type: String,
            default: ''
        },
        conEndTime: {
            type: String,
            default: ''
        }
    },
    data() {
        const actionList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            param: {
                dmpUrl:  '/link/consumerSchedulePlan/queryApprovalPlan',
                consumerScheduleId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                scheRuleType: 'ScheAction',
                // rows: 20,
                // planStatus: 'Y'
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                    this.actionListNum = data.rows.length;
                    this.$emit('updateActionListNum', this.actionListNum);
                    if(data.rows.length === 0) {
                        this.showflag = false;
                    } else {
                        this.showflag = true;
                        this.submitTabFlag = true;
                    }
                }
            },
        });
        return {
            actionList,
            actionListNum: null,
            showflag: true, // 是否显示，默认为显示
            submitTabFlag: false
        }
    },
    created() {

    },
    methods: {
        /**
         * @desc 重新加载
         * <AUTHOR>
         * @date 2025-01-10
         **/
        reloadActionList() {
            this.actionList.methods.reload();
        },
        /**
         * @desc 撤回排期
         * <AUTHOR>
         * @date 2025-01-09
         **/
        async recallScheduling(data) {
            const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                dmpUrl: '/link/consumerSchedulePlan/cancelStorage',
                id: data.id
            });
            if (res.success) {
                this.$message.success('撤回排期成功');
                this.actionList.methods.reload();
                this.$emit('reloadActionList2');
            } else {
                this.$utils.hideLoading();
                this.$message.warn('撤回排期失败', res.message);
            }
        },
        /**
         * @desc 作废排期计划
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async inValidScheduling(data) {
            try {
                const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/cancel',
                    id: data.id
                });
                if (res.success) {
                    this.$message.success('保存成功，若需审批，提交审批并通过后作废排期计划');
                    this.actionList.methods.reload();
                    // 刷新已作废栏目
                    this.$emit('reloadActionInvalid');
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('作废运营排期失败', res.message);
                }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
                // this.$message.error('作废运营排期异常，请联系管理员', e);
            }
        },
        /**
         * @desc 修改排期
         * <AUTHOR>
         * @date 2025-01-09
         **/
       async editScheduling(data) {
           this.$emit('editScheduling', data);
       },
        /**
         * @desc 删除排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async deleteScheduling(data) {
            try {
                const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/delete',
                    id: data.id
                });
                if (res.success) {
                    this.$message.success('删除运营排期成功');
                    this.actionList.methods.reload();
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('删除运营排期失败', res.message);
                }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
            }
        },
        /**
         * @Description: 跳转到详情页面
         * @Author: 何春霞
         * @Date: 2025-02-25
         */
        gotoDetail (data) {
            if(this.pageFrom !== 'implementation') {
                return;
            }
            const query = {
                scheduleItem: this.scheduleItem,
                customersItem: data
            }
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-work-summary-detail-page', {
                data: query,
                isPrincipal: this.isPrincipal,
                pageFrom: 'implementation',
                headData: this.headData,
                oauth: this.oauth,
                conStartTime: this.conStartTime,
                conEndTime: this.conEndTime
            });
        },
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-action-submit {
}
</style>
