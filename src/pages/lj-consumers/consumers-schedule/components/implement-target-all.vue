<!--
执行情况-目标-不可选
<AUTHOR>
@date 2025-01-08
@file implement-target-all.vue
-->
<template>
    <view class="implement-target-all">
        <line-title title="执行总览"></line-title>
        <view style="font-size: 14px; margin: 12px 16px;">{{ targetTitle }}</view>
        <link-auto-list :option="targetList" hideCreateButton :key="3">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="overview-item" style="padding: 14px 14px 2px 14px;">
                    <view slot="note">
                        <view class="item-input">
                            <view class="label">{{ '目标大类' }}</view>
                            <view class="text">{{ data.scheduleClass | lov('SCHE_TYPE') }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '目标小类' }}</view>
                            <view class="text">{{ data.scheduleSmallClass | lov('SHCE_CHILD_TYPE') }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '目标值' }}</view>
                            <view class="text">{{ data.targetNum }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '完成值' }}</view>
                            <view class="text">{{ data.reachNum }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '完成状态' }}</view>
                            <view class="text">{{ data.targetStatus | lov('SCHE_PLAN_STATUS') }}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-target-all',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        }
    },
    data() {
        const targetList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            // loadOnStart: false,
            param: {
                dmpUrl:  '/link/consumerScheduleTarget/queryByConsumer',
                scheInfoId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                rows: 20,
                scheRuleType: 'ScheTarget'
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                    // console.log('data', data);
                    this.targetTitle = '(数据截止时间：' + data.deadline + ')';
                }
            },
        });
        return {
            targetTitle: '',    // 业务目标时间
            targetList
        }
    },
    created() {

    },
    methods: {
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-target-all {
}
</style>
