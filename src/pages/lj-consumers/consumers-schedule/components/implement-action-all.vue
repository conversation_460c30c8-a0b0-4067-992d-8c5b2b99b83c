<!--
执行情况-动作-不可选
<AUTHOR>
@date 2025-01-08
@file implement-action-all.vue
-->
<template>
    <view class="implement-action-all">
        <line-title title="执行总览"></line-title>
        <view style="font-size: 14px; margin: 12px 16px;" v-if="!isTequFlag">{{ actionTitle }}</view>
        <link-auto-list :option="actionList" hideCreateButton :key="1">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="overview-item" style="padding: 14px 14px 2px 14px;">
                    <view slot="note">
                        <view class="item-input">
                            <view class="label">{{ '动作大类' }}</view>
                            <view class="text">{{ data.scheduleClass | lov('SCHE_TYPE') }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '动作小类' }}</view>
                            <view class="text">{{ data.scheduleSmallClass | lov('SHCE_CHILD_TYPE') }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '目标值' }}</view>
                            <view class="text">{{ data.targetNum }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '完成值' }}</view>
                            <view class="text">{{ data.reachNum }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '完成状态' }}</view>
                            <view class="text">{{ data.targetStatus | lov('SCHE_PLAN_STATUS') }}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-action-all',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        },
        isTequFlag: {
            type: Boolean,
            default: false
        }
    },
    data() {
        const actionList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            // loadOnStart: false,
            param: {
                dmpUrl: '/link/consumerScheduleTarget/queryByConsumer',
                scheInfoId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                rows: 20,
                scheRuleType: 'ScheAction'
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                    // console.log('data', data);
                    this.actionTitle = '(数据截止时间：' + data.deadline + ')';
                }
            },
        });

        return {
            actionList,
            actionTitle: '' // 业务动作时间
        }
    },
    created() {

    },
    methods: {
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-action-all {
}
</style>
