<!--
执行情况-动作-已作废动作排期计划
<AUTHOR>
@date 2025-01-08
@file implement-action-invalid.vue.vue
-->
<template>
    <view class="implement-action-invalid" v-show="submitTabFlag">
        <line-title title="已作废动作排期计划" v-if="showflag"></line-title>
        <view class="zero-view" v-if="showflag"></view>
        <link-auto-list :option="actionList" hideCreateButton :key="7" v-if="showflag">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="overview-item" style="padding: 14px 14px 2px 14px; background: #d7d7d7;">
                    <view slot="note" @tap="gotoDetail(data)">
                        <view class="account-label" style="background: #8e97a6">
                            <view class="label">{{'已作废'}}</view>
                        </view>
                        <view class="item-input" style="margin-top: 25px">
                            <view class="label">{{ '动作大类' }}</view>
                            <view class="text">{{ data.planScheType | lov('SCHE_TYPE') }}</view>
                        </view>
                        <view class="item-input" v-if="!!data.planScheChild">
                            <view class="label">{{ '动作小类' }}</view>
                            <view class="text">{{ data.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '预估执行时间' }}</view>
                            <view class="text">{{ data.beginTime ? (data.beginTime).substr(0, 10) : '' }} -
                                {{ data.endTime ? (data.endTime).substr(0, 10) : '' }}
                            </view>
                        </view>
                        <view class="item-input">
                            <view class="label">{{ '目标值' }}</view>
                            <view class="text">{{ data.planNum }}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-action-invalid',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        },
        headData: {
            type: Object
        },
        isPrincipal: {
            type: Boolean
        },
        conStartTime: {
            type: String,
            default: ''
        },
        conEndTime: {
            type: String,
            default: ''
        }
    },
    data() {
        const actionList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            // loadOnStart: false,
            param: {
                dmpUrl: '/link/consumerSchedulePlan/queryByExamplePage',
                scheInfoId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                rows: 20,
                filtersRaw: [{
                    id: 'planDeStatus',
                    property: 'planDeStatus',
                    operator: '=',
                    value: 'Invalid'
                },{
                    id: 'scheRuleType',
                    property: 'scheRuleType',
                    operator: '=',
                    value: 'ScheAction'
                }]
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                    // console.log('data', data);
                    this.actionTitle = '(数据截止时间：' + data.deadline + ')';
                    if(data.rows.length === 0) {
                        this.showflag = false;
                    } else {
                        this.showflag = true;
                        this.submitTabFlag = true;
                    }
                }
            },
        });
        return {
            actionList,
            showflag: true, // 是否显示
            submitTabFlag: false
        }
    },
    created() {

    },
    methods: {
        /**
         * @desc 重新加载
         * <AUTHOR>
         * @date 2025-01-10
         **/
        reloadActionList() {
            this.actionList.methods.reload();
        },
        /**
         * @Description: 跳转到详情页面
         * @Author: 何春霞
         * @Date: 2025-02-25
         */
        gotoDetail (data) {
            const query = {
                scheduleItem: this.scheduleItem,
                customersItem: data
            }
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-work-summary-detail-page', {
                data: query,
                isPrincipal: this.isPrincipal,
                pageFrom: 'implementation',
                headData: this.headData,
                oauth: this.oauth,
                conStartTime: this.conStartTime,
                conEndTime: this.conEndTime
            });
        },
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-action-invalid {
    .text {
        color: #8c8c8c !important;
    }
}
</style>
