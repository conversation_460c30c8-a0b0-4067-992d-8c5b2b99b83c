<!--
排期运营-执行情况/添加消费者-顶部消费者展示
<AUTHOR>
@date 2025-
@file target-customers.vue
-->
<template>
    <view class="target-customers">
        <view class="title">排期消费者</view>
        <view class="guest-list-item">
            <view class="logo-img">
                <image class="media-list-logo" :src="scheduleItem | headImgAccount(scheduleItem)"/>
                <view style="width: 32px;height: 12px; margin-top: -10px;">
                    <image style="width: 100%;height: 100%;"
                           :src="scheduleItem.addCertify === 'certified' ? $imageAssets.storeStatusVerifiedImage : $imageAssets.storeStatusUnverifiedImage"></image>
                </view>
            </view>
            <view class="guest-list-item-content">
                <view class="guest-list-item-content-row1">
                    <view class="consumer-name"
                          :style="{ maxWidth: (headData.isAddApprove === 'Y' && scheduleItem.endAuditStatus) ? 'calc(100% - 90px)' : '100%' }">
                        {{ scheduleItem.consumerName ? scheduleItem.consumerName : '' }}
                    </view>
                </view>
                <view class="guest-list-item-content-row2">
                    {{ scheduleItem.consumerPhone ? scheduleItem.consumerPhone : '' }}
                </view>
                <view class="guest-list-item-content-row3">
                    排期时间：{{ conStartTime ? (conStartTime).substr(0, 10) : '' }} -
                    {{ conEndTime ? (conEndTime).substr(0, 10) : '' }}
                </view>
            </view>
            <view class="content-status">
                <!--  颜色规则见lzlj-zhyx06-8410  -->
                <view
                    v-if="(scheduleItem.endAuditStatus === 'EndPass' && scheduleItem.status === 'End') || ($utils.isEmpty(scheduleItem.endAuditStatus) && scheduleItem.status === 'New') || ($utils.isEmpty(scheduleItem.endAuditStatus) && scheduleItem.status === 'End') || (scheduleItem.endAuditStatus === 'EndPass' && scheduleItem.status === 'New') || (scheduleItem.endAuditStatus === 'AddPass' && scheduleItem.status === 'End')"
                    :class="scheduleItem.status === 'End' ? 'endPass' : 'blue'">
                    {{ scheduleItem.status | lov('TARGET_ACCT_STATUS') }}
                </view>
                <view
                    v-else-if="((scheduleItem.status === 'New' || scheduleItem.status === 'End') && ['AddReject', 'AddReviewing'].includes(scheduleItem.endAuditStatus) || (scheduleItem.status === 'Processing' && scheduleItem.endAuditStatus === 'EndReviewing'))"
                    class="blue">{{ scheduleItem.endAuditStatus | lov('SCHE_ADUIT_STATUS') }}
                </view>
                <view
                    v-else-if="scheduleItem.status === 'Processing' && ['AddPass', 'EndRejected'].includes(scheduleItem.endAuditStatus)"
                    :class="scheduleItem.targetStatus === 'Finished' ? 'green' : 'red'">
                    {{ scheduleItem.targetStatus | lov('TARGET_STATUS_SCHE') }}
                </view>
                <view v-else-if="scheduleItem.status === 'Processing' && $utils.isEmpty(scheduleItem.endAuditStatus)"
                      :class="scheduleItem.targetStatus === 'Finished' ? 'green' : 'red'">
                    {{ scheduleItem.targetStatus | lov('TARGET_STATUS_SCHE') }}
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: 'target-customers',
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        headData: {
            type: Object,
            default: () => {
                return {};
            }
        },
        conStartTime: {
            type: String,
            default: () => {
                return '';
            }
        },
        conEndTime: {
            type: String,
            default: () => {
                return '';
            }
        }
    },
    data() {


        return {}
    },
    created() {

    },
    methods: {}
}
</script>

<style lang="scss">
@import './common-styles.scss';

.target-customers {
    padding: 34px;
    background-color: #fff;
    border-radius: 16px;
    margin: 0px 24px 24px 24px;

    .title {
        font-size: 32px;
        font-weight: bold;
        color: #333333;
        margin-left: 24px;
        margin-bottom: 24px;
        border-bottom: 1px solid #E5E5E5;
        padding-bottom: 24px;
    }

    .guest-list-item {
        @include flex;
        @include flex-start-center;
        overflow: hidden;

        .logo-img {
            width: 15%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-right: 20px;
            align-items: center;
        }

        .media-list-logo {
            height: 94px;
            width: 94px;

            image {
                height: 100%;
                width: 100%;
            }
        }

        .guest-list-logo {
            border-radius: 50%;
            width: 80px;
            height: 80px;
            overflow: hidden;
        }

        .guest-list-item-content {
            width: 55%;
            padding-right: 16px;
            display: flex;
            flex-direction: column;

            .guest-list-item-content-row1 {
                width: 100%;
                display: flex;

                .consumer-name {
                    display: block;
                    margin-bottom: 4px;
                    white-space: nowrap;
                    line-height: 48px;
                    font-size: 26px;
                    color: #333333;
                    overflow: hidden;
                    text-overflow: ellipsis;
                }

                .consumer-flag {
                    background-color: #F6F6F6;
                    width: 170px;
                    height: 48px;
                    display: inline-block;
                    font-size: 24px;
                    line-height: 48px;
                    border-radius: 8px;
                    text-align: center;
                    margin-left: 16px;

                    .consumer-flag-text {
                        color: #9F9F9F;
                    }
                }
            }

            .guest-list-item-content-row2 {
                display: block;
                max-width: 100%;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
                font-size: 22px;
                line-height: 42px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .guest-list-item-content-row3 {
                display: block;
                max-width: 100%;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 4px;
                font-size: 22px;
                line-height: 42px;
                // 超出宽度滑动显示
                white-space: nowrap;
                overflow: auto;
            }
        }

        .content-status {
            width: 30%;
            display: flex;
            align-items: center;
            justify-content: center;
            position: sticky; /* 使按钮始终可见 */
            right: 0; /* 将按钮固定在右侧 */
            font-size: 24px;
            height: 48px;

            .red {
                color: red;
            }

            .blue {
                color: #306cff;
            }

            .green {
                color: #00B050;
            }

            .endPass {
                color: #9F9F9F;
            }
        }
    }
}
</style>
