<!--
执行情况-动作-可选
<AUTHOR>
@date 2025-01-08
@file implement-action-option-none
-->
<template>
    <view class="implement-action-option-none">
        <line-title title="业务动作排期计划"></line-title>
        <view style="font-size: 14px; margin: 12px 16px;" v-if="!isTequFlag">{{ actionTitle }}</view>
        <link-auto-list :option="actionList" hideCreateButton :key="2">
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <item :key="index" :data="data" :arrow="false" class="overview-item"
                          style="padding: 14px 14px 2px 14px;">
                        <view slot="note" @tap="gotoDetail(data)">
                            <!--若排期计划审核状态planStatus=新建审核中、新建审核拒绝、变更审核中、作废审核中均角标均展示审核状态planStatus；-->
                            <!--其他情况展示planDeStatus排期计划完成状态；-->
                            <view class="account-label" :style="getColorByStatus(data.planStatus, data.planDeStatus)">
                                <view class="label" v-if="['InsertReject', 'InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)">{{ data.planStatus | lov('PLAN_ADUIT_STATUS') }}</view>
                                <view class="label" v-else>{{ data.planDeStatus | lov('SCHE_PLAN_STATUS') }}</view>
                            </view>
                            <view class="item-input" style="margin-top: 25px">
                                <view class="label">{{ '动作大类' }}</view>
                                <view class="text">{{ data.planScheType | lov('SCHE_TYPE') }}</view>
                            </view>
                            <view class="item-input" v-if="!!data.planScheChild">
                                <view class="label">{{ '动作小类' }}</view>
                                <view class="text">{{ data.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                            </view>
                            <view class="item-input">
                                <view class="label">{{ '预估执行时间' }}</view>
                                <view class="text">{{ data.beginTime ? (data.beginTime).substr(0, 10) : '' }} -
                                    {{ data.endTime ? (data.endTime).substr(0, 10) : '' }}
                                </view>
                            </view>
                            <view class="item-input">
                                <view class="label">{{ '目标值' }}</view>
                                <view class="text">{{ data.planNum }}</view>
                            </view>
                            <view class="item-input auto-height" style="flex-direction: column" v-if="!!data.planDesc">
                                <view class="label">动作计划说明</view>
                                <link-textarea disabled placeholder="请输入动作计划说明，最多输入100字" style="padding: 0;margin-top: 10px"
                                               v-model="data.planDesc" :nativeProps="{maxlength:100, autoHeight: true}"></link-textarea>
                            </view>
                            <view class="item-input" style="justify-content: space-between" v-if="pageFrom !== 'addConsumer' &&
                            ['Execution', 'Executing', 'ExeDelayed', 'ExeEarly', 'Planing', 'Finished', 'Suspend'].includes(data.planDeStatus) &&
                            !['UpdateRevering', 'InvalidRevering'].includes(data.planStatus) &&
                            !['Closed', 'New', 'InActive'].includes(headData.scheStatus)">
                                <view class="label">工作小结与评论</view>
                                <view style="display: flex; align-items: center" @tap.stop="openAddSummaryDialog(data)">
                                    <view class="icon-box">
                                        <link-icon icon="icon-plus" style="color: #FFFFFF;font-size: 20px;"/>
                                    </view>
                                    <link-button mode="text" :label="isPrincipal ? '添加小结' :'添加评论'"></link-button>
                                </view>
                            </view>
                            <view class="item-input" style="flex-direction: column" v-if= "pageFrom !== 'addConsumer' && data.comments">
                                <view class="cell-box">
                                    <view class="cell-icon">{{ getLastTwoChars(data.comCreatedBy) }}</view>
                                    <view class="cell-title" v-if="data.personnelType">{{ data.personnelType | lov('COMMENT_TYPE')}}</view>
<!--                                    <view class="cell-desc">{{ data.comPositionType | lov('ASSIGN_POSITION') }}</view>-->
                                    <view class="cell-time" :style="{'max-width': $utils.isNotEmpty(data.personnelType) ? '133px':'183px'}">{{ data.comLastUpdated }}</view>
                                    <view class="cell-status" :style="getColorByStatus('', data.comPlanDeStatus)">
                                        <view class="dot"></view>
                                        {{ data.comPlanDeStatus | lov('SCHE_PLAN_STATUS') }}
                                    </view>
                                </view>
                                <view class="cell-label">{{ data.comments }}</view>
                                <link-button mode="stroke">点击查看更多>></link-button>
                            </view>
                        </view>
                    </item>
                    <!--排期计划审核状态planStatus处于新建审核中、变更审核中、作废审核中的排期计划不展示任何按钮-->
                    <!--动作不受参数影响，一直都可以编辑/删除-->
                    <!-- 排期运营状态不等于已结束、新建、已作废才展示其他按钮逻辑 -->
                    <!--已完成/未完成的排期不展示修改按钮-->
                    <link-swipe-option slot="option" color="primary"
                                       v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                             && !['Finished', 'Planing', 'Invalid'].includes(data.planDeStatus)
                                             && consumerTimeFlag
                                             && isPrincipal
                                             && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)"
                                       @tap="editScheduling(data)">修改
                    </link-swipe-option>
                    <!--只要planStatus审批状态为【新建审核拒绝】的时候，展示修改和作废按钮，没有其他限制-->
                    <link-swipe-option v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                            && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)
                                            && isPrincipal
                                            && consumerTimeFlag
                                            && data.planStatus === 'InsertReject'"
                                       slot="option" color="error" @tap="inValidScheduling(data)">作废
                    </link-swipe-option>
                    <link-swipe-option v-if="['Suspend', 'Execution', 'Executing', 'ExeDelayed', 'ExeEarly'].includes(data.planDeStatus)
                                            && !['InsertReject','InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                            && consumerTimeFlag
                                            && isPrincipal
                                            && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)"
                                       slot="option" color="error" @tap="inValidScheduling(data)">作废
                    </link-swipe-option>
                    <link-swipe-option slot="option" color="error"
                                       v-if="!['InsertRevering', 'UpdateRevering', 'InvalidRevering'].includes(data.planStatus)
                                         && data.planDeStatus === 'New'
                                         && consumerTimeFlag
                                         && isPrincipal
                                         && !['Closed', 'New', 'InActive'].includes(headData.scheStatus)
                                         && data.planStatus !== 'InsertReject'"
                                       @tap="deleteScheduling(data)">删除
                    </link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <!--添加小结弹窗-->
        <link-dialog ref="addSummary" v-model="addSummaryFlag" title="添加小结" width="80%">
            <view class="add-summary">
                <link-form ref="addSummaryForm" :value="formData">
                    <link-form-item label="是否暂缓排期" v-if="showSuspendSchedule && consumerTimeFlag">
                        <link-switch v-model="formData.suspendSchedule"/>
                    </link-form-item>
                    <link-form-item label="总结说明" vertical field="comments">
                        <view class="text-area-box">
                            <view class="text-area-len">{{ `${formData.comments? formData.comments.length : 0}/100` }}</view>
                            <link-textarea :height="144" v-model="formData.comments" style="padding-bottom: 20px" placeholder="请填写总结说明" :nativeProps="{maxlength:100}"/>
                        </view>
                    </link-form-item>
                </link-form>
                <view class="button">
                    <link-button class="button-item" mode="stroke" block @tap="cancel">取消</link-button>
                    <link-button class="button-item" block @tap="submit">确认</link-button>
                </view>
            </view>
        </link-dialog>
        <!-- 添加评论弹窗 -->
        <link-dialog ref="addComment" v-model="addCommentFlag" title="添加评论" width="80%">
            <view class="add-summary">
                <link-form ref="addCommentForm" :value="commentData">
                    <link-form-item label="评论说明" vertical field="comments">
                        <view class="text-area-box">
                            <view class="text-area-len">{{ `${commentData.comments? commentData.comments.length : 0}/100` }}</view>
                            <link-textarea :height="144" v-model="commentData.comments"  style="padding: 0px !important;" placeholder="请填写评论说明" :nativeProps="{maxlength:100}"/>
                        </view>
                    </link-form-item>
                </link-form>
                <view class="button">
                    <link-button class="button-item" mode="stroke" block @tap="commentCancel">取消</link-button>
                    <link-button class="button-item" block @tap="commentSubmit">确认</link-button>
                </view>
            </view>
        </link-dialog>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";
import {getColorByStatus} from "./schedule-common";
import Taro from "@tarojs/taro";
import {ComponentUtils} from 'link-taro-component';

export default {
    name: 'implement-action-option-none',
    components: {LineTitle},
    props: {
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        },
        consumerTimeFlag: {
            type: Boolean
        },
        pageFrom: {
            type: String
        },
        submitActionFlag: {
            type: Boolean
        },
        headData: {
            type: Object
        },
        isPrincipal: {
            type: Boolean
        },
        isTequFlag: {
            type: Boolean,
            default: false
        },
        conStartTime: {
            type: String,
            default: ''
        },
        conEndTime: {
            type: String,
            default: ''
        }
    },
    data() {
        const actionList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            loadOnStart: this.pageFrom === 'addConsumer' ? true : false,
            param: {
                scheInfoId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                rows: 20,
                filtersRaw: [{
                    id: 'planDeStatus',
                    property: 'planDeStatus',
                    operator: 'not in',
                    value: '[Invalid, Deleted]'
                },{
                    id: 'scheRuleType',
                    property: 'scheRuleType',
                    operator: '=',
                    value: 'ScheAction'
                }]
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.sort;
                    delete option.param.order;
                    // 有待提交的数据就用新接口/link/consumerSchedulePlan/queryDistinctSchedulePlan，没有待提交数据用/link/consumerSchedulePlan/queryByExamplePage
                    if(this.submitActionFlag){
                        option.param.dmpUrl = '/link/consumerSchedulePlan/queryDistinctSchedulePlan';
                    }else{
                        option.param.dmpUrl = '/link/consumerSchedulePlan/queryByExamplePage';
                    }
                },
                afterLoad: async (data) => {
                    this.actionTitle = '(数据截止时间：' + data.deadline + ')';
                }
            },
        });
        return {
            userInfo: {},
            showSuspendSchedule: false,       // 是否展示暂缓排期
            actionList,
            addSummaryFlag: false,            // 添加小结的弹窗
            addCommentFlag: false,            // 添加评论的弹窗
            debounceFlag: false,
            formData: {
                suspendSchedule: 'N',
                schedulePlanId: '',
                comments: ''
            },                     // 添加小结的表单
            commentData: {
                schedulePlanId: '',
                comments: ''
            },
            actionTitle: '' // 业务动作时间
        }
    },
    computed: {},
    created() {
        this.userInfo = Taro.getStorageSync("token").result;
    },
    methods: {
        getColorByStatus,
        /**
         * @Description: 打开添加小结的弹窗
         * @Author: 胡益阳
         * @Date: 2025/1/10
        */
        openAddSummaryDialog (data) {
            console.log('data', data)
            this.showSuspendSchedule = ['Execution', 'Executing', 'ExeDelayed', 'ExeEarly'].includes(data.planDeStatus)
            //     判断是负责人/执行人，展示添加小结，否则展示添加评论
            if (this.isPrincipal) {
                this.formData.schedulePlanId = data.id;
                this.addSummaryFlag = true;
            } else {
                this.commentData.schedulePlanId = data.id;
                this.addCommentFlag = true;
            }
        },
        /**
         * @Description: 跳转到详情页面
         * @Author: 胡益阳
         * @Date: 2025/1/10
        */
        gotoDetail (data) {
            if(this.pageFrom !== 'implementation') {
                return;
            }
            const query = {
                scheduleItem: this.scheduleItem,
                customersItem: data
            }
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-work-summary-detail-page', {
                data: query,
                isPrincipal: this.isPrincipal,
                pageFrom: this.pageFrom,
                headData: this.headData,
                oauth: this.oauth,
                conStartTime: this.conStartTime,
                conEndTime: this.conEndTime
            });
        },
        /**
         * @Description: 只展示创建人的后两位
         * @Author: 胡益阳
         * @Date: 2025/1/15
        */
        getLastTwoChars(str) {
            try {
                if (typeof str!== 'string') {
                    str = String(str)
                }
                if (str.length === 0) {
                    return '';
                } else if (str.length === 1) {
                    return str;
                } else {
                    return str.slice(-2);
                }
            } catch (error) {
                console.error('获取字符串后两位发生错误: ', error);
                return '';
            }
        },
        /**
         * @desc 重新加载
         * <AUTHOR>
         * @date 2025-01-10
         **/
        async reloadActionList() {
            this.actionList.methods.reload();
        },
        /**
         * @desc 作废排期计划
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async inValidScheduling(data) {
            try {
                const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/cancel',
                    id: data.id
                });
                if (res.success) {
                    this.$message.success('保存成功，若需审批，提交审批并通过后作废排期计划');
                    // 刷新待提交栏目
                    this.$emit('reloadActionListSubmit');
                    if(this.pageFrom === 'addConsumer'){
                        this.actionList.methods.reload();
                    }
                    // 刷新已作废栏目
                    this.$emit('reloadActionInvalid');
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('作废运营排期失败', res.message);
                }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
            }
        },
        /**
         * @desc 确认
         * <AUTHOR>
         * @date 2025-01-09
         **/
        submit: ComponentUtils.debounce(async function() {
            try {
                if(this.$utils.isEmpty(this.formData.comments)){
                    this.$message.warn('请填写总结说明');
                    return
                }
                if (this.debounceFlag) return;
                if (!this.debounceFlag) {
                    this.debounceFlag = true;
                }
                let params = {
                    dmpUrl: '/link/scheduleComment/addComment',
                    commentType: 'Summary'                            // 小排期类型
                }
                Object.assign(params, this.formData)
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', params);
                if (data.success) {
                    this.debounceFlag = false;
                    this.$message.success('添加小结成功！')
                    this.cancel();
                    await this.actionList.methods.reload();
                } else {
                    this.$message.error('添加小结异常，请联系管理员');
                }
            } catch (err) {
                this.$message.error('添加小结异常，请联系管理员', err);
            }
        }, 500),
        /**
         * @desc 取消
         * <AUTHOR>
         * @date 2025-01-09
         **/
        cancel () {
            this.formData = {};
            this.addSummaryFlag = false;
        },
        /**
         * @desc 修改排期
         * <AUTHOR>
         * @date 2025-01-08
         **/
        async editScheduling(data) {
            // 执行父组件的方法
            this.$emit('editScheduling', data, 'ScheAction');
        },
        /**
         * @desc 删除排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async deleteScheduling(data) {
            try {
                const res = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/delete',
                    id: data.id
                });
                if (res.success) {
                    this.$message.success('删除运营排期成功');
                    this.actionList.methods.reload();
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('删除运营排期失败', res.message);
                }
            } catch (e) {
                console.log('e', e);
                this.$utils.hideLoading();
            }
        },
        /**
         * @desc 添加评论-确认
         * <AUTHOR>
         * @date 2025-02-18
         **/
        commentSubmit: ComponentUtils.debounce(async function() {
            // 待联调
            try {
                if(this.$utils.isEmpty(this.commentData.comments)){
                    this.$message.warn('请填写评论说明');
                    return
                }
                if (this.debounceFlag) return;
                if (!this.debounceFlag) {
                    this.debounceFlag = true;
                }
                let params = {
                    dmpUrl: '/link/scheduleComment/addComment',
                    commentType: 'OtherReply'                            // 小排期类型
                }
                Object.assign(params, this.commentData)
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', params);
                if (data.success) {
                    this.debounceFlag = false;
                    this.$message.success('添加评论成功！')
                    this.commentCancel();
                    await this.actionList.methods.reload();
                } else {
                    this.$message.error('添加评论异常，请联系管理员');
                }
            } catch (err) {
                this.$message.error('添加评论异常，请联系管理员', err);
            }
        }, 500),
        /**
         * @desc 添加评论-取消操作
         * @date 2025-02-18
         **/
        commentCancel() {
            this.commentData = {};
            this.addCommentFlag = false;
        }
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';

.implement-action-option-none {
    .link-dialog-content {
        border-radius: 24px !important;
    }
    .auto-height {
        .link-textarea-content {
            height: auto !important;
            min-height: auto !important;
        }
    }
    .add-summary {
        width: 100%;
        .link-switch {
            transform: scale(0.7);
        }
        .text-area-box {
            width: 100%;
            height: 100%;
            position: relative;
            .text-area-len {
                position: absolute;
                right: 10px;
                bottom: 10px;
                font-size: 24px;
                color: #BFC1CA;
            }
            .link-textarea-content {
                padding: 24px 24px 45px;
            }
        }
        .button {
            display: flex;
            justify-content: space-between;
            margin-top: 50px;
            .link-button {
                margin: 0 !important;
                width: 252px;
                height: 72px;
            }
        }
    }
}
</style>
