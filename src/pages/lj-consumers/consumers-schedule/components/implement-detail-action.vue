<!--
执行情况-执行明细-动作
<AUTHOR>
@date 2025-01-09
@file implement-detail-action.vue
-->
<template>
    <view class="implement-detail-action">
        <line-title :title="title"></line-title>
        <view class="zero-view"></view>
        <view style="font-size: 14px; margin: 12px 16px;" v-if="!isTequFlag">{{ detailTitle1 }}</view>
        <link-auto-list :option="detailActionList" hideCreateButton :key="9">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="detail-list-item">
                    <view slot="note" style="display: flex; padding: 0px">
                        <view class="detail-item">
                            <view class="detail-item-info">
                                <view class="detail-item-info-item">
                                    <view class="label">业务名称</view>
                                    <view class="detail-item-info-text">
                                        {{ data.businessName }}
                                    </view>
                                </view>
                                <view class="detail-item-info-item">
                                    <view class="label">执行时间</view>
                                    <view class="detail-item-info-text">{{ data.behaviorTime }}</view>
                                </view>
                                <view class="detail-item-info-item">
                                    <view class="label">创建人</view>
                                    <view class="detail-item-info-text">{{ data.creatorName }}</view>
                                </view>
                            </view>
                        </view>
                        <view class="tag-button">
                            <view class="tag-item" v-if="!!data.scheduleClass">{{ data.scheduleClass | lov('SCHE_TYPE') }}
                            </view>
                            <view class="tag-item" v-if="!!data.scheduleSmallClass && (data.scheduleSmallClass !== data.scheduleClass)">
                                {{ data.scheduleSmallClass | lov('SHCE_CHILD_TYPE') }}
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import LineTitle from "../../../lzlj/components/line-title.vue";

export default {
    name: 'implement-detail-action',
    components: {LineTitle},
    props: {
        title: {
            type: String,
            default: '业务动作执行明细'
        },
        scheduleItem: {
            type: Object,
            default: () => {
                return {};
            }
        },
        oauth: {
            type: String,
            default: ''
        },
        isTequFlag: {
            type: Boolean,
            default: false
        }
    },
    data() {
        const detailActionList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            param: {
                dmpUrl: '/link/consumerScheduleTarget/queryDetails',
                scheInfoId: this.scheduleItem.id,
                scheduleId: this.scheduleItem.scheduleId,
                rows: 20,
                scheRuleType: 'ScheAction'
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                    delete option.param.sort;
                    delete option.param.order;
                },
                afterLoad: async (data) => {
                    // console.log('data', data);
                    this.detailTitle1 = '(数据截止时间：' + data.deadline + ')';
                }
            },
        });

        return {
            detailActionList,
            detailTitle1: '' // 业务动作时间
        }
    },
    created() {

    },
    methods: {
    }
}
</script>

<style lang="scss">
@import './common-styles.scss';
.implement-detail-action {
}
</style>
