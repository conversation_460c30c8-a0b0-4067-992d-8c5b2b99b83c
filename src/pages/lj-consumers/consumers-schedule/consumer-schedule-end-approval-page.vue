<!--
审批-消费者排期运营-终止排期消费者审批
<AUTHOR>
@date 2024-07-01
@file consumer-schedule-end-approval-page.vue
-->
<template>
    <link-page class="consumer-schedule-end-approval-page">
        <approval-history-point-v3 ref="apprHisPointV3" :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'"></approval-history-point-v3>
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && ($utils.isEmpty(apVersion) || apVersion !== 'v3')"></approval-history-point>
        <link-form>
            <view class="item main-item">
                <view class="account-info-container">
                    <image class="head-back" :src="$imageAssets.homeMenuBgImage"></image>
                    <view class="top-bg">
                        <view class="top-bg-1"></view>
                        <view class="top-bg-2">
                            <image :src="consumerItem.headUrl|headImgAccount(consumerItem)"
                                   class="top-bg-2-img"></image>
                        </view>
                        <view class="top-bg-3">
                            <view style="text-align: center;margin-top: 20px">
                                <view class="top-bg-3-text">{{consumerItem.name}}
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view>
                    <view class="account-detail-container">
                        <link-form-item label="所属排期运营名称">
                            {{consumerItem.scheduleName}}
                        </link-form-item>
                        <link-form-item label="排期开始时间">
                            {{consumerItem.conStartTime}}
                        </link-form-item>
                        <link-form-item label="排期结束时间">
                            {{consumerItem.conEndTime}}
                        </link-form-item>
                        <view class="list-bottom-width-line"></view>
                        <link-form-item label="消费者姓名">
                            {{consumerItem.consumerName}}
                        </link-form-item>
                        <link-form-item label="消费者跟进人">
                            {{consumerItem.addFstName}}
                        </link-form-item>
                        <view class="list-bottom-width-line"></view>
                        <link-form-item label="申请人">
                            {{applyName}}
                        </link-form-item>
                        <link-form-item label="申请时间">
                            {{applyTime}}
                        </link-form-item>
                        <view class="list-bottom-width-line"></view>
                        <link-form-item required label="申请说明" vertical>
                            <view class="comments">
                                <link-textarea placeholder="请填写申请说明，100字以内" padding-start padding-end
                                               v-model="consumerItem.endReason" :nativeProps="{maxlength:100}" :readonly="!!approvalId"></link-textarea>
                            </view>
                        </link-form-item>
                    </view>
                </view>
            </view>
        </link-form>
        <link-sticky>
            <approval-operator-v3 :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'" @approvalInfoResult="approvalInfoResult" :fixed="true"></approval-operator-v3>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && ($utils.isEmpty(apVersion) || apVersion !== 'v3')" @approvalInfoResult="approvalInfoResult"  :fixed="true"></approval-operator>
        </link-sticky>
        <view class="blank" :style="apVersion === 'v3'?'height: 270rpx;':''" v-if="!$utils.isEmpty(approvalId)"></view>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
import Taro from "@tarojs/taro";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
import ApprovalHistoryPointV3 from "../../lzlj/approval-v3/components/approval-history-point.vue";
import ApprovalOperatorV3 from "../../lzlj/approval-v3/components/approval-operator.vue";
import waterMark from "../../lzlj/components/water-mark";
export default {
    name: "consumer-schedule-end-approval-page",
    data() {
        return {
            pageFrom: this.pageParam.pageFrom,
            userInfo: {},
            applyName: '' ,
            applyTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm'),
            consumerItem: {},
            id: null,   // 审批传过来的业务对象ID
            scheduleId: null,
            approvalId: null,
            apVersion: null, // v2/v3审批
            flowObjDetail: {},  // 审批传过来的审批数据
            approvalFlag: false, //从审批菜单进入的
            businessId: null
        }
    },
    components: {
        ApprovalOperatorV3,
        ApprovalHistoryPointV3,
        ApprovalHistoryPoint,
        ApprovalOperator,
        waterMark
    },
    async created() {
        this.userInfo = Taro.getStorageSync('token').result
        let code = this.pageParam.source;//页面来源
        let sceneObj = await this.$scene.ready(); //消息场景对象
        const approval_from = sceneObj.query['approval_from'];
        console.log('sceneObj', sceneObj)
        if (code === 'approval') {
            console.log('this.pageParam.data', this.pageParam.data)
            this.approvalFlag = true;
            this.approvalId = this.pageParam.data.id;//审批传过来的审批数据ID
            this.id = this.pageParam.data.flowObjId;//审批传过来的业务对象ID
            this.flowObjDetail = JSON.parse(this.pageParam.data.flowObjDetail);
            this.scheduleId = this.flowObjDetail.scheduleId;
            this.apVersion = this.pageParam.data.apVersion;
            if (this.$utils.isNotEmpty(this.scheduleId)) {
                await this.queryApprovalFlow();
            } else {
                this.$utils.showAlert('未获取到终止排期消费者信息，请联系管理员！', {icon: 'none'});
                return
            }
        } else {
            if (approval_from === 'qw') { //从小程序审批消息而来
                this.approvalId = sceneObj.query['approval_id'];
                this.id = sceneObj.query['flowObjId'];
                this.apVersion = sceneObj.query['apVersion'];
                if (this.$utils.isNotEmpty(this.id)) {
                    await this.queryApprovalFlow();
                } else {
                    this.$utils.showAlert('未获取到终止排期消费者信息，请联系管理员！', {icon: 'none'});
                    return
                }
            }
        }
        //用户操作（同意、拒绝、撤回、转交申请）后更新
        this.$bus.$on('refreshApprovalDetail', async()=> {
            console.log('refreshApprovalDetail')
            await this.$refs.apprHisPointV3.initFlowRecord()
            await this.$refs.apprHisPointV3.$refs.apprProcess.initFlowRecord()
        });
    },
    methods: {
        /**
         * @desc 获取ScheduleId
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async queryApprovalFlow() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/approvalRecord/queryById', {
                    id: this.id
                });
                if (data.success && data.result) {
                    this.businessId = data.result.businessId;
                    this.businessData = JSON.parse(data.result.businessData);
                    if (!this.approvalFlag) {
                        this.scheduleId = this.businessData.scheduleId;
                    }
                    await this.queryItemById();
                } else {
                    this.$message.warn('未查询到对应审批流，请联系管理员' + data.message);
                }
            } catch (e) {
                console.log('e', e)
            }
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/8
         * @methods approvalInfoResult
         * @para
         * @description 赋值，提交人，提交时间，以及申请备注
         */
        approvalInfoResult(info) {
            console.log('info >>>', info);
            this.applyName = info.flowStartPsnName;
            this.applyTime = this.$date.format(info.created, 'YYYY-MM-DD HH:mm')
        },
        /**
         * @desc 获取审批数据
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async queryItemById() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/queryByExamplePage',
                    scheduleId: this.scheduleId,
                    filtersRaw:[
                        {id: 'id', property: 'id', value: this.businessId, operator: '='},
                    ]
                });
                if(data.success){
                    this.consumerItem = data.rows[0];
                    // 从审批列表
                    if(this.approvalFlag){
                        this.consumerItem.endReason = this.flowObjDetail.endReason;
                    } else {
                        // 从企微消息
                        this.consumerItem.endReason = this.businessData.endReason;
                    }
                }else{
                    this.$message.warn('未获取到终止排期消费者信息，请联系管理员！' + data.message);
                }
            } catch (e) {

            }
        }

    }
}
</script>

<style lang="scss">
.consumer-schedule-end-approval-page {
    .blank {
        height: 376px;
        width: 100%;
    }
    .main-item {
        width: 100%;
        .head-back{
            width: 100%;
        }

        .top-bg {
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 100%;
            // height: 500px;
            position: absolute;
            z-index: 1;
            top: 68px;
            .top-bg-1 {
                width: 100%;
                height: 12%;
            }

            .top-bg-2 {
                width: 100%;
                height: 35%;
                text-align: center;

                .top-bg-2-img {
                    width: 164px;
                    height: 164px;
                    border: 6px solid #569bf5;
                    border-radius: 50%;
                }
            }

            .top-bg-3 {
                width: 100%;
                height: 35%;

                .top-bg-3-text {
                    text-align: center;
                    font-size: 34px;
                    color: white;
                    text-overflow: ellipsis;
                    overflow: hidden;
                    white-space: nowrap;
                }
            }

            .top-bg-4 {
                width: 100%;
                height: 25%;

                .top-bg-4-1 {
                    height: 88px;
                    width: 100%;
                    display: flex;
                    background-color: rgba(255, 255, 255, 0.2);
                    position: absolute;
                    bottom: 0;

                    .top-bg-4-1-btn {
                        width: 50%;
                        height: 88px;
                        color: white;
                        line-height: 88px;
                        text-align: center;
                    }
                }
            }
        }
    }
    .account-info-container {
        background-repeat: no-repeat;
        background-size: 100% 100%;
        width: 100%;
        height: 400px;
        position: relative;
        .message-button-box {
            height: 44px;
            width: 100%;
            display: flex;
            background-color: rgba(255, 255, 255, 0.2);
            position: absolute;
            bottom: 0;

            .message-button {
                width: 50%;
                height: 44px;
                color: white;
                line-height: 44px;
                text-align: center;
            }
        }
    }

    .account-label-container {
        .list {
            .list-header {
                font-size: 16px;
                color: #41484D;
                min-height: 54px;
            }

            @include label-item-container();

            .label-item-selected {
                background: rgba(0, 118, 255, 0.75) !important;
                border: none !important;
                padding: 2px 24px !important;
            }

            .personal-label {
                font-size: 16px;
            }
        }
    }

    .account-detail-container {
        margin-top: 10px;
        background-color: white;

        .list-bottom-width-line {
            width: 100%;
            height: 20px;
            background: #F2F2F2;
        }
        .comments {
            width: 90%;
            margin: 0 5%;

            link-textarea {
                height: 120px;
                font-size: 14px;
                padding: 3px;
            }

            .count {
                text-align: right;
                padding: 5px 10px;
                color: #999;
            }

            link-textarea {
                background: #EEF3F5;
            }
        }
    }
}
</style>
