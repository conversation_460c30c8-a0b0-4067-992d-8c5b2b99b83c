<!--
消费者-消费者排期运营-消费者排期运营执行情况-编辑排期
<AUTHOR>
@date 2025-02-12
@file consumers-schedule-edit-page.vue
-->
<template>
    <link-page class="consumers-schedule-edit-page">
        <view class="zero-view"></view>
        <line-title title="基础信息"></line-title>
        <view class="zero-view"></view>
        <link-form ref="addForm">
            <link-form-item label="运营大类" required :disabled="editFlag">
                <link-select v-model="formData.scheduleClass" @change="querySmallList">
                    <link-select-option v-for="(data,index) in scheduleClassList" :label="data.scheduleClassName"
                                        :val="data.scheduleClass" :key="index"></link-select-option>
                </link-select>
            </link-form-item>
            <link-form-item label="运营小类" v-if="smallClassFlag" :disabled="editFlag">
                <link-select v-model="formData.scheduleSmallClass" @change="queryScheDetailId">
                    <link-select-option v-for="(data,index) in scheduleSmallList"
                                        :label="data.scheduleSmallClassName" :val="data.scheduleSmallClass"
                                        :key="index"/>
                </link-select>
            </link-form-item>
            <link-form-item label="目标值" required :disabled="editFlag || (isAction && headData.scheActionType === 'ActionNone')">
                <link-number v-model="formData.planNum" :min="1" :max="tempPlanNum"/>
            </link-form-item>
            <!-- 弹窗的时间字段，动作可选时，展示时间字段；目标可选时， 不展示时间字段，默认赋值 -->
            <link-form-item label="执行时间类型" v-if="(headData.scheActionType === 'ActionPackageOption' || headData.scheActionType === 'ActionNone') && isAction">
                <link-lov type="PLAN_TIME_TYPE" v-model="formData.planTimeType" @change="timeTypeChange(formData.planTimeType)"></link-lov>
            </link-form-item>
            <link-form-item label="预估执行时间" v-if="formData.planTimeType === 'ByDay' && (headData.scheActionType === 'ActionPackageOption' || headData.scheActionType === 'ActionNone') && isAction" required>
                <link-date v-model="formData.beginTime" view="YMD" display-format="YYYY-MM-DD"
                           value-format="YYYY-MM-DD"></link-date>
            </link-form-item>
            <link-form-item label="预估执行开始时间" v-if="formData.planTimeType === 'TimeRange' && (headData.scheActionType === 'ActionPackageOption' || headData.scheActionType === 'ActionNone') && isAction" required>
                <link-date v-model="formData.beginTime" view="YMD" display-format="YYYY-MM-DD"
                           value-format="YYYY-MM-DD" :max="formData.endTime"></link-date>
            </link-form-item>
            <link-form-item label="预估执行结束时间" v-if="formData.planTimeType === 'TimeRange' && (headData.scheActionType === 'ActionPackageOption' || headData.scheActionType === 'ActionNone') && isAction" required>
                <link-date v-model="formData.endTime" view="YMD" display-format="YYYY-MM-DD"
                           value-format="YYYY-MM-DD" :min="formData.beginTime"></link-date>
            </link-form-item>
            <link-form-item label="是否需要领导协同" v-if="isAction">
                <link-switch v-model="formData.isLeadership"  @change="chooseLeader"/>
            </link-form-item>
            <view class="planDesc" v-if="(headData.scheActionType === 'ActionPackageOption' || headData.scheActionType === 'ActionNone') && isAction">
                <view class="planDesc-title">动作计划说明</view>
                <view class="comments">
                    <link-textarea v-model="formData.planDesc" :nativeProps="{maxlength:100}" placeholder="请输入动作计划说明，最多输入100字" class="fixed-height"></link-textarea>
                </view>
            </view>
            <view class="planDesc" v-if="(headData.scheTargetType === 'TargetPackageOption' || headData.scheTargetType === 'TargetNone') && !isAction">
                <view class="planDesc-title">目标计划说明</view>
                <view class="comments">
                    <link-textarea v-model="formData.planDesc" :nativeProps="{maxlength:100}" placeholder="请输入目标计划说明，最多输入100字" class="fixed-height"></link-textarea>
                </view>
            </view>
        </link-form>
        <view class="zero-view"></view>
        <view class="leader-ship" v-if="formData.isLeadership === 'Y'">
            <view class="line">
                <view class="name">{{'协同领导'}}</view>
                <view class="add" @tap="addLeader">{{'添加'}}</view>
            </view>
            <view class="leader-ship-data">
                <link-swipe-action v-for="(item,index) in leaderData" :key="item.id">
                    <link-swipe-option slot="option" @tap="deleteLeaderData(item, index)">失效</link-swipe-option>
                    <view class="leader-ship-item">
                        <text class="num">{{ item.firstName }}</text>
                        <text class="name">{{ item.userName ? item.userName : item.username }}</text>
                    </view>
                </link-swipe-action>
            </view>
        </view>

        <link-sticky v-if="pageFrom === 'implementation' || pageFrom === 'addConsumer'">
            <link-button block @tap="confirmAdd">确定</link-button>
        </link-sticky>

        <link-sticky v-if="pageFrom === 'calendar'">
            <link-button v-if="editFlag" mode="stroke" block @tap="inValid">作废</link-button>
            <link-button v-if="editFlag" block @tap="confirmAdd">保存</link-button>
            <link-button v-if="!editFlag" block @tap="confirmAdd">确定</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import LineTitle from "../../lzlj/components/line-title.vue";
import Taro from "@tarojs/taro";

export default {
    name: 'consumers-schedule-edit-page',
    components: {LineTitle},
    data() {
        const leaderOption =new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
            },
            searchFields: ['firstName', 'username'],
            exactSearchFields: [
                {
                    field: 'firstName',
                    showValue: '姓名',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: false
                },
                {
                    field: 'username',
                    showValue: '工号',
                    searchOnChange: true,
                    clearOnChange: true,
                    exactSearch: true
                }
            ],
            param: {
                dmpUrl: '/link/scheduleRelatedPostn/queryAddLeaders'
            },
            renderFunc: (h, {data, index}) => {
                return (<item key={index} title={data.firstName} data={data}
                              desc={data.username}></item>)
            },
            hooks: {
                beforeLoad(option) {
                    if(this.userInfo.postnId) {
                        option.param.postnId = this.userInfo.postnId;
                    }
                },
                afterLoad: async (data) => {
                }
            },
        });
        return {
            formData: {
                scheduleClassName: '', // 运营大类
                scheduleSmallClassName: '', // 运营小类
                targetNum: 1, // 目标值
                planTimeType: 'ByDay', // 执行时间类型
                beginTime: '', // 预估执行时间
                endTime: '', // 预估执行结束时间
                isLeadership: 'N', // 是否需要领导协同
                // reachNum: '' // 运营小类的达成值
            }, // 新增运营排期-表单数据
            isAction: this.pageParam.isAction, // 是否是动作
            editFlag: this.pageParam.editFlag, // 是否可修改
            scheduleClassList: [],      // 运营大类列表
            scheduleSmallList: [],      // 运营小类列表
            smallClassFlag: false,      // 是否显示运营小类
            tempPlanNum: 1,            // 目标值上限
            scheDetailId: '',          // 运营排期明细id
            scheRuleType: '',          // 规则类型
            headData: this.pageParam.headData ? this.pageParam.headData : {}, // 大排期数据
            scheduleItem: this.pageParam.scheduleItem ? this.pageParam.scheduleItem : {}, // a
            conStartTime: this.pageParam.conStartTime ? this.pageParam.conStartTime : null,
            conEndTime: this.pageParam.conEndTime ? this.pageParam.conEndTime : null,
            pageFrom: this.pageParam.pageFrom,
            leaderOption,
            leaderData: [],  // 协同领导数据
            editData: this.pageParam.editData ? this.pageParam.editData : '', // 编辑数据
        }
    },
    async created() {
        this.userInfo = Taro.getStorageSync('token').result;
        this.scheRuleType = this.isAction ? 'ScheAction' : 'ScheTarget';
        // 判断是否是特曲公司使用参数配置
        // 250218新增逻辑，如果是特曲公司，单独用一套查询运营大类的值列表
        const isTequ = await this.$utils.getCfgProperty('SCHE_TEQU_VAL');
        console.log('isTequ', isTequ)

        // 执行情况-新增
        if (!this.editFlag) {
            await this.initNewData();
        } else {
        // 执行情况-修改
            await this.initEditData();
            await this.queryPerson();
        }
        // 查询协同领导数据
        console.log('新增/编辑-scheduleItem', this.scheduleItem);
        console.log('新增/编辑-headData', this.headData);
        console.log('新增/编辑-pageFrom', this.pageFrom);
        console.log('新增/编辑-editData', this.editData);
        console.log('新增/编辑-scheActionType', this.headData.scheActionType);
        console.log('新增/编辑-scheTargetType', this.headData.scheTargetType);
    },
    methods: {
        /**
         * @desc 初始化表单-新增数据
         * <AUTHOR>
         * @date 2025-02-12
         **/
        async initNewData() {
            // 初始化表单
            this.formData = {
                scheduleClassName: '', // 运营大类
                scheduleSmallClassName: '', // 运营小类
                planNum: 1, // 目标值
                planTimeType: 'ByDay', // 执行时间类型
                beginTime: '', // 预估执行时间
                endTime: '', // 预估执行结束时间
                planDesc: '', // 计划说明
                isLeadership: 'N', // 是否需要领导协同
            };
            await this.queryScheduleClassList();
            await this.handleTime();
        },
        /**
         * @desc 初始化表单-编辑数据
         * <AUTHOR>
         * @date 2025-02-12
         **/
        async initEditData() {
            await this.queryScheduleClassList();
            this.formData = {
                scheduleClassName: '', // 运营大类
                scheduleSmallClassName: '', // 运营小类
                planNum: this.editData.planNum, // 目标值
                planTimeType: this.editData.planTimeType, // 执行时间类型
                beginTime: this.editData.beginTime, // 预估执行时间
                endTime: this.editData.endTime, // 预估执行结束时间
                planDesc: this.editData.planDesc, // 计划说明
                isLeadership: this.editData.isLeadership, // 是否需要领导协同
            };
            this.$set(this.formData, 'scheduleClass', this.editData.planScheType);
            if(this.formData.scheduleClass) {
                await this.querySmallList(this.formData.scheduleClass);
            }
            this.$set(this.formData, 'scheduleSmallClass', this.editData.planScheChild);
            if(this.formData.scheduleSmallClass) {
                await this.queryScheDetailId(this.formData.scheduleSmallClass);
            }
            if(!!this.editData.planNum) {
                this.formData.planNum = this.editData.planNum;
            }
        },
        /**
         * @desc 查询运营大类列表
         * <AUTHOR>
         * @date 2024-08-06
         **/
        async queryScheduleClassList() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/querySchePlanLine',
                    scheduleId: this.scheduleItem.scheduleId,
                    scheRuleType: this.scheRuleType
                });
                if (data.success) {
                    this.scheduleClassList = data.rows;
                }
            } catch (e) {
                this.$message.error('查询运营大类数据异常');
            }
        },
        /**
         * @desc 获取运营大类，查询运营小类列表
         * <AUTHOR>
         * @date 2024-08-06
         **/
        async querySmallList(data) {
            this.formData.scheduleClass = data;
            this.scheduleSmallList = this.scheduleClassList.find(item => item.scheduleClass === data).scheduleSmallClass;
            if(this.scheduleSmallList.length === 0) {
                this.smallClassFlag = false;
            } else if (this.scheduleSmallList.length === 1 && this.$utils.isEmpty(this.scheduleSmallList[0].scheduleSmallClass)){
                this.scheDetailId = this.scheduleSmallList[0].detailId;
                this.formData.planNum = this.scheduleSmallList[0].reachNum;
                this.tempPlanNum = this.scheduleSmallList[0].reachNum;
                this.smallClassFlag = false;
            } else {
                this.smallClassFlag = true;
            }
            // 动作无规划有小类，不管选不选小类，最大值为1
            if (this.isAction && this.headData.scheActionType === 'ActionNone' && !(this.scheduleSmallList.length === 1 && this.$utils.isEmpty(this.scheduleSmallList[0].scheduleSmallClass))) {
              this.tempPlanNum = 1;
            }
            // 目标无规划不选小类，最大值为不设限制
            if(!this.isAction && this.headData.scheTargetType === 'TargetNone' && !(this.scheduleSmallList.length === 1 && this.$utils.isEmpty(this.scheduleSmallList[0].scheduleSmallClass))) {
              this.tempPlanNum = 999999;
            }
        },
        /**
         * @desc 获取运营小类id
         * <AUTHOR>
         * @date 2024-08-06
         */
        async queryScheDetailId(data) {
            this.formData.scheduleSmallClass = data;
            this.scheDetailId = this.scheduleSmallList.find(item => item.scheduleSmallClass === data).detailId;
            this.formData.planNum = this.scheduleSmallList.find(item => item.scheduleSmallClass === data).reachNum;
            this.tempPlanNum = this.scheduleSmallList.find(item => item.scheduleSmallClass === data).reachNum;
            if((this.headData.scheActionType === 'ActionNone' || this.headData.scheTargetType === 'TargetNone') && !this.formData.planNum) {
                this.formData.planNum = 1;
                this.tempPlanNum = 999999;
            }
        },
        /**
         * @desc 处理时间
         * <AUTHOR>
         * @date 2024-10-24
         **/
        handleTime() {
            console.log('handleTime')
            // 目标可选时，不显示时间字段，直接赋值，执行时间类型TimeRange
            if ((this.headData.scheTargetType === 'TargetPackageOption' || this.headData.scheTargetType === 'TargetNone')  && !this.isAction) {
                this.formData.planTimeType = 'TimeRange';
                this.formData.beginTime = this.conStartTime;
                this.formData.endTime = this.conEndTime;
            }
            // 动作可选时，显示时间字段，执行时间类型ByDay
            // 无规划，动作时间类型默认为按天，时间类型字段页面不展示
            if((this.headData.scheActionType === 'ActionPackageOption' || this.headData.scheActionType === 'ActionNone') && this.isAction) {
                this.formData.planTimeType = 'ByDay';
                this.timeTypeChange('ByDay');
            }
            console.log('formData', this.formData)
        },
        /**
         * @desc 当执行时间类型为TimeRange时，对预估执行开始时间和结束时间进行赋值操作
         * <AUTHOR>
         * @date 2024-08-06
         **/
        async timeTypeChange(val) {
            console.log('val',val)
            const timeStamp = await this.$utils.getServerTime();
            const formatDate = (dateStr) => dateStr.replace(/-/g, '/');
            const date = typeof timeStamp === 'string' ? new Date(formatDate(timeStamp)) :  new Date(timeStamp);
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0'); // 月份从0开始，需要加1
            const day = String(date.getDate()).padStart(2, '0');
            const formattedDate = `${year}-${month}-${day}`;
            if(val === 'TimeRange') {
                // 开始时间为服务器时间 + ' 00:00:00'
                this.formData.beginTime = formattedDate + ' 00:00:00';
                // 结束时间为排期运营的结束时间
                this.formData.endTime = this.conEndTime;
            } else if(val === 'ByDay') {
                // 开始时间为服务器时间 + ' 00:00:00'
                this.formData.beginTime = formattedDate + ' 00:00:00';
            } else {
                this.formData.beginTime = '';
                this.formData.endTime = '';
            }
        },
        chooseLeader(val) {
            console.log('val', val)
            this.formData.isLeadership = val;
        },
        /**
         * @desc 查询协同领导数据
         * <AUTHOR>
         * @date 2025-02-18
         **/
        async queryPerson() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/queryByExamplePage',
                    scheInfoId: this.scheduleItem.id,
                    schePlanId: this.editData.id,
                    businessType: 'leadership',
                    status: 'Y'
                });
                if(data.success) {
                    this.leaderData = data.rows;
                } else {
                    this.$message.error('查询执行人失败', data.message);
                }
            } catch (e) {
                console.log('查询执行人出错', e)
            }
        },
        /**
         * @desc 失效协同领导数据
         * <AUTHOR>
         * @date 2025-02-13
         **/
        async deleteLeaderData(item, index) {
            try {
                // 新增的协同领导，直接删除，有id的协同领导，调用失效接口
                if(!item.id) {
                    this.leaderData.splice(index, 1);
                    return;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/scheduleRelatedPostn/updateStatus',
                    id: item.id
                });
                if (data.success) {
                    this.leaderData.splice(index, 1);
                } else {
                    this.$message.error('失效协同领导失败', data.message);
                }
            } catch (e) {
                console.log('失效协同领导出错', e)
            }
        },
        /**
         * @desc 添加协同领导
         * <AUTHOR>
         * @date 2025-02-12
         **/
        async addLeader() {
            console.log('addLeader')
            const list = await this.$object(this.leaderOption, {
                multiple: false,
                pageTitle: "请选择协同领导"
            });
            const leaderItem = {
                businessPostnId: list.postnId,
                scheInfoId: this.scheduleItem.id,
                schePlanId: this.editData.id,
                businessType: 'leadership',
                firstName: list.firstName,
                username: list.username
            }
            this.leaderData = this.leaderData.concat(leaderItem);
            // leaderData有postnId相同的数据，去重，仅保留一条
            this.leaderData = Object.values(this.leaderData.reduce((acc, item) => {
                acc[item.businessPostnId] = item;
                return acc;
            }, {}));
            console.log('leaderData', this.leaderData)
        },
        /**
         * @desc 执行完保存接口后，保存协同领导数据
         * <AUTHOR>
         * @date 2025-02-19
         **/
        async confirmAddLeader(id) {
            try {
                // 保存前去掉this.leaderData的firstName和username
                const leaderData = this.leaderData.map(item => {
                    return {
                        businessPostnId: item.businessPostnId,
                        scheInfoId: item.scheInfoId,
                        schePlanId: id,
                        businessType: item.businessType
                    }
                });
                const data = await this.$http.post(this.$env.dmpURL + '/action/link/scheduleRelatedPostn/batchInsert', leaderData);
                if (data.success) {
                } else {
                    this.$message.error('保存协同领导失败', data.message);
                }
            } catch (e) {
                console.log('保存协同领导出错', e)
            }
        },
        /**
         * @desc 添加排期
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async confirmAdd() {
            try {
                this.$utils.showLoading();
                // 校验
                if (!this.formData.scheduleClass) {
                    this.$message.warn('请选择运营大类');
                    this.$utils.hideLoading();
                    return;
                }
                if(this.smallClassFlag && !this.formData.scheduleSmallClass) {
                    // 无规划时不需要校验【运营小类】
                    if ((this.isAction && this.headData.scheActionType === 'ActionPackageOption') || (!this.isAction && this.headData.scheTargetType === 'TargetPackageOption')) {
                        this.$message.warn('请选择运营小类');
                        this.$utils.hideLoading();
                        return;
                    }
                }
                if (!this.formData.planNum) {
                    this.$message.warn('请输入目标值');
                    this.$utils.hideLoading();
                    return;
                }
                if (this.formData.planTimeType === 'ByDay' && (!this.formData.beginTime) && this.headData.scheActionType === 'ActionPackageOption' && this.isAction) {
                    this.$message.warn('请选择预估执行时间');
                    this.$utils.hideLoading();
                    return;
                }
                if (this.formData.planTimeType === 'TimeRange' && (!this.formData.beginTime || !this.formData.endTime) && this.headData.scheActionType === 'ActionPackageOption' && this.isAction) {
                    this.$message.warn('请选择预估执行开始时间和结束时间');
                    this.$utils.hideLoading();
                    return;
                }
                if (this.formData.isLeadership === 'Y' && this.leaderData.length === 0) {
                    this.$message.warn('请选择协同领导');
                    this.$utils.hideLoading();
                    return;
                }
                const time = {}
                if (this.formData.planTimeType === 'ByDay' && this.formData.beginTime) {
                    if (this.formData.beginTime) {
                        time.beginTime = this.formData.beginTime.substring(0, 10) + ' 00:00:00';
                        time.endTime = this.formData.beginTime.substring(0, 10) + ' 23:59:59';
                    } else {
                        time.beginTime = '';
                        time.endTime = '';
                    }
                }
                if (this.formData.planTimeType === 'TimeRange' && this.formData.beginTime && this.formData.endTime) {
                    if(!this.isAction) {
                        await this.timeTypeChange('TimeRange');
                    }
                    if (this.formData.beginTime) {
                        time.beginTime = this.formData.beginTime.substring(0, 10) + ' 00:00:00';
                        time.endTime = this.formData.endTime.substring(0, 10) + ' 23:59:59';
                    } else {
                        time.beginTime = '';
                        time.endTime = '';
                    }
                }
                // 目标可选时，不显示时间字段，直接赋值消费者开始结束时间，类型为TimeRange
                if (!this.isAction) {
                    time.beginTime = this.conStartTime;
                    time.endTime = this.conEndTime;
                    this.formData.planTimeType = 'TimeRange';
                }
                const text = this.editFlag ? '修改' : '新增';
                const dmpUrl = this.editFlag ? '/link/consumerSchedulePlan/update' : '/link/consumerSchedulePlan/insert';
                const param = this.editFlag ? {
                    id: this.editData.id,
                    planDeStatus: this.editData.planDeStatus,
                    planStatus: this.editData.planStatus
                } : {};
                // const noneParam = (this.headData.scheActionType === 'ActionNone' || this.headData.scheTargetType === 'TargetNone') ? {
                //     planScheType: this.formData.scheduleClass,
                //     planScheChild: this.formData.scheduleSmallClass,
                //     scheRuleType: this.scheRuleType,
                // } : {};
                // 无规划为none的时候不传scheDetailId: this.scheDetailId,
                const param2 = !this.scheDetailId ? {} : {
                    scheDetailId: this.scheDetailId
                };
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: dmpUrl,
                    scheInfoId: this.scheduleItem.id,
                    scheduleId: this.scheduleItem.scheduleId,
                    planNum: this.formData.planNum,
                    planTimeType: this.formData.planTimeType,
                    planDesc: this.formData.planDesc,
                    planScheType: this.formData.scheduleClass,
                    planScheChild: this.formData.scheduleSmallClass,
                    scheRuleType: this.scheRuleType,
                    isLeadership: this.formData.isLeadership,
                    // ...noneParam,
                    ...param2,
                    ...param,
                    ...time
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success(text + '业务排期计划成功！');
                    if(data.newRow) {
                        await this.confirmAddLeader(data.newRow.id);
                    }
                    console.log('this.scheduleItem.scheduleId', this.scheduleItem.scheduleId,'this.editData.id', this.editData.id, 'this.scheduleItem.acctId', this.scheduleItem.acctId)
                    // 通过事件总线通知父页面刷新列表
                    if(this.pageFrom === 'implementation' ) {
                        this.$bus.$emit('refreshImpleList', this.scheRuleType);
                        this.$bus.$emit('refreshWorkSummary', this.scheduleItem.scheduleId, this.editData.id, this.scheduleItem.acctId, this.scheduleItem.id);
                    }
                    if(this.pageFrom === 'addConsumer') {
                        this.$bus.$emit('refreshAddList', this.scheRuleType);
                        this.$bus.$emit('refreshWorkSummary', this.scheduleItem.scheduleId, this.editData.id, this.scheduleItem.acctId);
                    }
                    if(this.pageFrom === 'calendar') {
                        this.$bus.$emit('refreshCalendarList', this.scheRuleType);
                        this.$bus.$emit('refreshWorkSummary', this.scheduleItem.scheduleId, this.editData.id, this.scheduleItem.acctId);
                    }
                    // 返回上一页
                    this.$nav.back();
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn(text + '运营排期失败', data.message);
                }
            } catch (e) {
                this.$utils.hideLoading();
                console.log('e', e);
            }
        },
        /**
         * @desc 作废排期计划
         * @Author: 何春霞
         * @date 2025-02-18
         **/
        inValid() {
            this.$dialog({
                title: '提示',
                content: '确定作废此排期？',
                confirmText: '确定',
                onConfirm: async () => {
                    await this.confirminValid();
                },
                cancelButton: true
            })
        },
        /**
         * @desc 确认-作废排期计划
         * @Author: 何春霞
         * @date 2025-02-18
         **/
        async confirminValid() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/cancel',
                    id: this.editData.id
                });
                if (data.success) {
                    this.$message.success('作废排期成功');
                } else {
                    this.$message.error('作废排期失败', data.message);
                }
            } catch (e) {
                console.log('作废排期失败', e);
            }
        }
    }
}
</script>

<style lang="scss">
@import './components/common-styles.scss';
.consumers-schedule-edit-page {
    //background-color: #fff;
    .link-swipe-option-container .link-swipe-option {
        width: 60px;
        height: 50% !important;
        margin: 10px;
    }
    .leader-ship {

        width: 94%;
        margin: 16px auto;

        .line {
            display: flex;
            justify-content: space-between;
            align-items: center;
            border-radius: 16rpx;
            padding: 24rpx 28rpx;
            background: white;
            .name{
                display: flex;
                font-size: 28px;
            }
            .add{
                width: 30%;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: right;
            }
        }

        .leader-ship-data {
            display: flex;
            flex-direction: column;
            .leader-ship-item {
                display: flex;
                padding: 24px 28px;
                background: white;
                border-radius: 16px;
                margin-top: 16px;
                font-size: 28px;
                color: #262626;
                .num {
                    width: 30%;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
                .name {
                    width: 65%;
                    margin-left: 12px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                }
            }
        }
    }
}
</style>

