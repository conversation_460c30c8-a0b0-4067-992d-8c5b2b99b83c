
<!--
描述 添加消费者排期申请
<AUTHOR>
@date 2024-09-18
@file consumer-schedule-add-approval-page.vue
-->
<template>
    <link-page class="consumer-schedule-add-approval-page">
        <approval-history-point-v3 ref="apprHisPointV3" :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'"></approval-history-point-v3>
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)&& apVersion !== 'v3'"></approval-history-point>
        <line-title title="排期运营信息"></line-title>
        <view style="width: 94%;margin-left: 3%">
            <view class="form-column">
                <view v-for="item in scheduleFields" :key="item.label" class="item-input">
                    <view class="label">{{ item.label }}</view>
                    <view class="text">{{ item.text }}</view>
                </view>
            </view>
        </view>
        <line-title title="业务动作排期计划" v-if="actionList.length > 0"></line-title>
        <view class="main-content" v-if="actionList.length > 0">
            <view v-for="(data, index) in actionList" :key="index">
                <view class="overview-item" style="padding: 14px 14px 2px 14px;">
                    <view class="item-input">
                        <view class="label">动作大类</view>
                        <view class="text">{{ data.planScheType | lov('SCHE_TYPE') }}</view>
                    </view>
                    <view class="item-input" v-if="!!data.planScheChild">
                        <view class="label">动作小类</view>
                        <view class="text">{{ data.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">预估执行开始时间</view>
                        <view class="text">{{ $date.format(data.beginTime, 'YYYY-MM-DD') }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">预估执行结束时间</view>
                        <view class="text">{{ $date.format(data.endTime, 'YYYY-MM-DD') }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">目标值</view>
                        <view class="text">{{ data.planNum }}</view>
                    </view>
                    <view class="item-input" v-if="data.isLeadership === 'Y'">
                        <view class="label">协同领导</view>
                        <view class="text">{{ data.leadershipName ? data.leadershipName : ''}}</view>
                    </view>
                    <view class="item-input" style="flex-direction: column" v-if="!!data.planDesc">
                        <view class="label">动作计划说明</view>
                        <link-textarea disabled placeholder="请输入动作计划说明，最多输入100字" style="padding: 0;margin-top: 10px"
                                       v-model="data.planDesc" :nativeProps="{maxlength:100}" readonly></link-textarea>
                    </view>
                </view>
            </view>
        </view>
        <line-title title="业务目标排期计划" v-if="schedulingList.length > 0"></line-title>
        <view class="main-content" v-if="schedulingList.length > 0">
            <view v-for="(data, index) in schedulingList" :key="index">
                <view class="overview-item" style="padding: 14px 14px 2px 14px;">
                    <view class="item-input">
                        <view class="label">{{ '目标大类' }}</view>
                        <view class="text">{{ data.planScheType | lov('SCHE_TYPE') }}</view>
                    </view>
                    <view class="item-input" v-if="!!data.planScheChild">
                        <view class="label">{{ '目标小类' }}</view>
                        <view class="text">{{ data.planScheChild | lov('SHCE_CHILD_TYPE') }}</view>
                    </view>
                    <view class="item-input">
                        <view class="label">{{ '目标值' }}</view>
                        <view class="text">{{ data.planNum }}</view>
                    </view>
                    <view class="item-input" style="flex-direction: column" v-if="!!data.planDesc">
                        <view class="label">目标计划说明</view>
                        <link-textarea disabled placeholder="请输入目标计划说明，最多输入100字" style="padding: 0;margin-top: 10px"
                                       v-model="data.planDesc" :nativeProps="{maxlength:100}" readonly></link-textarea>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="!$utils.isEmpty(approvalId)" class="account-info">
            <line-title title="消费者信息"></line-title>
            <link-form>
                <view>
                    <view class="account-detail-container">
                        <link-form-item label="消费者姓名">
                            {{ consumerItem.consumerName ? consumerItem.consumerName : '-' }}
                        </link-form-item>
                        <view v-if="!cfgArray.includes(userInfo.coreOrganizationTile['l3Id'])">
                            <link-form-item label="影响力K序列等级">
                                {{ consumerItem.addType | lov('ACCT_SUB_TYPE')}}
                            </link-form-item>
                            <link-form-item label="购买力V序列等级">
                                {{ consumerItem.addLoyaltyLevel | lov('ACCT_MEMBER_LEVEL_XFZ')}}
                            </link-form-item>
                        </view>
                        <view v-if="cfgArray.includes(userInfo.coreOrganizationTile['l3Id'])">
                            <link-form-item label="影响力K序列等级">
                                {{ consumerItem.addType | lov('ACCT_SUB_TYPE')}}
                            </link-form-item>
                            <link-form-item label="购买力V序列等级">
                                {{ consumerItem.addLoyaltyLevel | lov('ACCT_MEMBER_LEVEL_XFZ')}}
                            </link-form-item>
                        </view>
                        <link-form-item label="是否认证">
                            {{ consumerItem.addCertify | lov('CERTIFY') }}
                        </link-form-item>
                        <link-form-item label="所属客户">
                            {{ consumerItem.belongToStore ? consumerItem.belongToStore : '-' }}
                        </link-form-item>
                        <view class="check-more">
                            <link-button mode="stroke" @tap="gotoAccountItem">点击查看更多>></link-button>
                        </view>
                        <view class="list-bottom-width-line"></view>
                        <link-form-item label="申请人">
                            {{ applyName }}
                        </link-form-item>
                        <link-form-item label="申请时间">
                            {{ applyTime }}
                        </link-form-item>
                    </view>
                </view>
            </link-form>
        </view>
        <link-sticky>
            <approval-operator-v3 :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId) && apVersion === 'v3'" :fixed="true"  @approvalInfoResult="approvalInfoResult"></approval-operator-v3>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)&& apVersion !== 'v3'" @approvalInfoResult="approvalInfoResult"></approval-operator>
        </link-sticky>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
    </link-page>
</template>

<script>
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point.vue";
import LineTitle from "../../lzlj/components/line-title.vue";
import Taro from "@tarojs/taro";
import waterMark from "../../lzlj/components/water-mark.vue";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator.vue";
import ApprovalHistoryPointV3 from "../../lzlj/approval-v3/components/approval-history-point.vue";
import ApprovalOperatorV3 from "../../lzlj/approval-v3/components/approval-operator.vue";
export default {
    name: 'consumer-schedule-add-approval-page',
    components: {ApprovalOperator, waterMark, LineTitle, ApprovalHistoryPoint,ApprovalHistoryPointV3,ApprovalOperatorV3},
    data() {
        const userInfo = Taro.getStorageSync('token').result

        return {
            pageFrom: this.pageParam.pageFrom,
            userInfo,
            applyName: '' ,
            scheInfoId: '', // 消费者行id
            applyTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm'),
            detailItem: {},
            consumerItem: {
                acctName: '',
                type: '',
                loyaltyLevel: '',
                identityLevel: '',
                addCertify: '',
                belongToStore: '',
                fromType: ''
            },
            id: null,   // 审批传过来的业务对象ID
            scheduleId: null,
            apVersion: null, // v2/v3审批
            scheduleFields: [], // 排期运营信息
            actionList: [], // 业务动作列表
            schedulingList: [], // 业务目标排期计划
            approvalId: null,
            flowObjDetail: {},  // 审批传过来的审批数据
            approvalFlag: false, //从审批菜单进入的
            businessId: null,
            businessData: {},
            cfgArray: [], // 企业参数配置信息-新K涉及公司范围
        }
    },
    async created() {
         // 查询企业参数配置-参数键New_Type_Company
         const obj = await this.$utils.getCfgProperty('New_Type_Company');
        this.cfgArray = obj.split(',')
        console.log('超高添加排期消费者审批-cfgArray', this.cfgArray, this.userInfo.coreOrganizationTile['l3Id']);
        let code = this.pageParam.source; // 页面来源
        let sceneObj = await this.$scene.ready(); // 消息场景对象
        const approval_from = sceneObj.query['approval_from'];
        console.log('sceneObj', sceneObj)
        if (code === 'approval') {
            this.apVersion = this.pageParam.data.apVersion;
            this.approvalFlag = true;
            this.approvalId = this.pageParam.data.id; // 审批传过来的审批数据ID
            this.flowObjDetail = JSON.parse(this.pageParam.data.flowObjDetail);
            console.log('this.flowObjDetail', this.flowObjDetail)
            this.scheduleId = this.flowObjDetail.scheduleId;
            this.scheInfoId = this.flowObjDetail.scheInfoId;
        } else if (approval_from === 'qw') { // 从小程序审批消息而来
            this.apVersion = sceneObj.query['apVersion'];
            this.approvalId = sceneObj.query['approval_id'];
            this.id = sceneObj.query['flowObjId'];
            if(this.$utils.isNotEmpty(this.id)){
                await this.queryApprovalFlow();
            }else{
                this.$utils.showAlert('未获取到审批业务对象id，请联系管理员！', {icon: 'none'});
                return
            }
        } else {
            return;
        }
        if (this.$utils.isNotEmpty(this.scheduleId)) {
            await this.queryDetail();
            await this.initData();
            await this.getScheduleList();
            await this.getAllActionList();
            await this.queryConsumer();
        } else {
            this.$utils.showAlert('未获取到排期运营id，请联系管理员！', {icon: 'none'});
        }
    },
    methods: {
        /**
         * @desc 企微消息卡片-获取ScheduleId
         * <AUTHOR>
         * @date 2024-08-05
         **/
        async queryApprovalFlow() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/approvalRecord/queryById', {
                    id: this.id
                });
                if(data.success && data.result){
                    this.businessId = data.result.businessId;
                    this.businessData = JSON.parse(data.result.businessData);
                    if(!this.approvalFlag) {
                        this.scheduleId = this.businessData.scheduleId;
                        this.scheInfoId = this.businessData.id;
                    }
                }else{
                    this.$message.warn('未查询到对应审批流，请联系管理员' + data.message);
                }
            } catch (e) {
                console.log('e', e)
            }
        },
        /**
         * @desc 获取动作列表
         * <AUTHOR>
         * @date 2024-09-05
         * @desc /link/consumerScheduleTarget/queryByConsumer
         **/
        async getAllActionList() {
            try {
                this.$utils.showLoading();
                const params = {
                    dmpUrl: '/link/consumerSchedulePlan/queryByExamplePage',
                    scheduleId: this.scheduleId,
                    scheInfoId: this.scheInfoId,
                }
                params.filtersRaw = [{
                    id: 'planDeStatus',
                    property: 'planDeStatus',
                    operator: 'not in',
                    value: '[Invalid, Deleted]'
                },{
                    id: 'scheRuleType',
                    property: 'scheRuleType',
                    operator: '=',
                    value: 'ScheAction'
                }]
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', params);
                if (data.success) {
                    this.actionList = data.rows;
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$message.warn('查询业务动作数据异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$utils.hideLoading();
                this.$message.error('查询业务动作数据异常，请联系管理员', e);
            }
        },
        /**
         * @desc 获取排期计划
         * <AUTHOR>
         * @date 2024-09-29
         **/
        async getScheduleList() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedulePlan/queryByExamplePage',
                    scheduleId: this.scheduleId,
                    scheInfoId: this.scheInfoId,
                    filtersRaw: [{
                        id: 'planDeStatus',
                        property: 'planDeStatus',
                        operator: 'not in',
                        value: '[Invalid, Deleted]'
                    },{
                        id: 'scheRuleType',
                        property: 'scheRuleType',
                        operator: '=',
                        value: 'ScheTarget'
                    }]
                });
                if (data.success) {
                    this.schedulingList = data.rows;
                } else {
                    this.$message.warn('查询业务动作排期计划数据异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询业务动作排期计划数据异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询运营详情
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryDetail() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: this.scheduleId
                });
                if(data.success) {
                    this.detailItem = data.result;
                    console.log("this.detailItem1:",this.detailItem)
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询消费者信息
         * <AUTHOR>
         * @date 2024-09-27
         **/
        async queryConsumer() {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/queryBaseConsumerInfos',
                    scheduleId: this.scheduleId,
                    scheInfoId: this.scheInfoId
                });
                if(data.success) {
                    this.consumerItem = data.rows[0];
                } else {
                    this.$message.warn('查询消费者信息异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询消费者信息异常，请联系管理员', e);
            }
        },
        /**
         * @desc 初始化数据
         * <AUTHOR>
         * @date 2024-09-18
         **/
        async initData() {
            this.$utils.showLoading();
            // 详情头初始化
            this.scheduleFields = [
                {label: '排期运营名称', text: this.detailItem.scheduleName ? this.detailItem.scheduleName : '-'},
                {label: '排期人群', text: this.detailItem.scheduleCrowd ? await this.$lov.getNameByTypeAndVal('SCHEDULE_CROWD', this.detailItem.scheduleCrowd) : '-'},
                {label: '排期导向', text: this.detailItem.scheGuide ? (await Promise.all(this.detailItem.scheGuide.split(',').map(async guideType => await this.$lov.getNameByTypeAndVal('SCHE_GUIDE', guideType)))).join(', ') : '-'},
                {label: '排期开始时间', text: this.detailItem.startTime ? this.detailItem.startTime : '-'},// 这里反复改改回去startTime,git容易bug,合并取改回去的startTime
                {label: '排期结束时间', text: this.detailItem.endTime ? this.detailItem.endTime : '-'},
                {label: '期限（天）', text: this.detailItem.term ? this.detailItem.term + '天' : '-'},
                {label: '创建人', text: this.detailItem.createdName ? this.detailItem.createdName : '-'},
                {label: '运营动作', text: this.detailItem.scheActionType ?  await this.$lov.getNameByTypeAndVal('SCHEDULE_ACTION_TYPE', this.detailItem.scheActionType) : '-'},
                {label: '运营目标', text: this.detailItem.scheTargetType ?  await this.$lov.getNameByTypeAndVal('SCHEDULE_TARGET_TYPE', this.detailItem.scheTargetType) : '-'},
            ]
            if(this.detailItem.term) {
                this.scheduleFields.splice(3, 2)
            } else {
                this.scheduleFields.splice(5, 1)
            }
            this.$utils.hideLoading();
        },

        // 需要联调，跳转到消费者详情需要的参数
        /**
         * @desc 跳转消费者详情
         * <AUTHOR>
         * @date 2024-09-18
         **/
        gotoAccountItem () {
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: this.consumerItem,
                pageFrom: 'consumerSchedule'
            });
        },
        /**
         * @createdBy  张丽娟
         * @date  2021/4/8
         * @methods approvalInfoResult
         * @para
         * @description 赋值，提交人，提交时间，以及申请备注
         */
        approvalInfoResult(info){
            this.applyName = info.flowStartPsnName;
            this.applyTime = this.$date.format(info.created, 'YYYY-MM-DD HH:mm')
        },
    }
}
</script>

<style lang="scss">
.consumer-schedule-add-approval-page {
    .blank {
        height: 376px;
        width: 100%;
    }
    .form-column {
        margin: 24px auto;
        border-radius: 16px;
        background: white;
        font-family: PingFangSC-Regular;
        padding: 24px 24px 2px 24px;

        .item-input {
            line-height: 30px;
            margin-bottom: 30px;
            width: 100%;
            display: flex;

            .label {
                align-self: flex-start;
                color: #8c8c8c;
                font-size: 28px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                width: 300px;
            }

            .text {
                color: #262626;
                width: 420px;
                font-size: 28px;
                text-align: right;
            }
        }

        .item-textarea {
            line-height: 30px;
            width: 100%;
            margin-bottom: 30px;

            .label {
                color: #8c8c8c;
                font-size: 28px;
                display: flex;
                align-items: center;
                box-sizing: border-box;
                width: 300px;
                margin-bottom: 18px;
            }

            .text {
                color: #262626;
                width: 420px;
                font-size: 28px;
            }
        }
    }
    .main-content {
        display: flex;
        flex-direction: column;
        margin: 16px;
        max-width: calc(100% - 32px);
        .overview-item {
            margin: 12px;
            border-radius: 16px;
            background: white;
            font-family: PingFangSC-Regular;

            .item-input {
                line-height: 30px;
                margin-bottom: 30px;
                width: 100%;
                display: flex;

                .label {
                    align-self: flex-start;
                    color: #8c8c8c;
                    font-size: 28px;
                    display: flex;
                    align-items: center;
                    box-sizing: border-box;
                    width: 300px;
                }

                .text {
                    color: #262626;
                    width: 420px;
                    font-size: 28px;
                    text-align: right;
                }
            }
        }
    }


    .account-info{
        .account-detail-container {
            margin-top: 28px;
            background-color: white;

            .list-bottom-width-line {
                width: 100%;
                height: 20px;
                background: #F2F2F2;
            }
            .comments {
                width: 90%;
                margin: 0 5%;

                link-textarea {
                    height: 120px;
                    font-size: 14px;
                    padding: 3px;
                }

                .count {
                    text-align: right;
                    padding: 5px 10px;
                    color: #999;
                }

                link-textarea {
                    background: #EEF3F5;
                }
            }
        }

        .check-more{
            width: 100%;
            background: white;
            padding: 10px 0;
            display: flex;
            justify-content: center;
        }
        .no-data{
            width: 100%;
            color: #8f8f94;
            text-align: center;
            font-size: 28px;
            line-height: 60px;
        }
    }

}
</style>

