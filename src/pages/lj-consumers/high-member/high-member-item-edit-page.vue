<template>
    <link-page class="high-member-item-edit-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <link-form :value="formData" ref="highMemberForm">
            <line-title :title="memberTitle" v-if="!$utils.isEmpty(approvalId) && !!memberTitle"/>
            <link-form-item label="手机号" required field="mobilePhone" :readonly="pageFrom === 'AccountItem'">
                <link-object :option="memberOption" pageTitle="选择申请身份"
                             :row="formData"
                             :disabled="formOverallReadonly"
                             :value="formData.mobilePhone"
                             :map="{businessId:'id', acctName: 'acctName', mobilePhone:'mobilePhone', presentIdentityLevel: 'identityLevel', endTime: 'endTime', fromType: 'fromType', empName:'empName'}"
                >
                    <template v-slot="{data}">
                        <item :arrow="false" :key="data.id" :data="data" style="margin: 12px;border-radius: 8px; position: relative;overflow: hidden;padding: 20px 14px 14px 14px;">
                            <view style="width: 100%;">
                                <view style="width: 100%;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;margin-bottom: 8px;">{{data.acctName}}</view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #317DF7;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: 66px;">联系方式</view>
                                    <view>{{data.mobilePhone}}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: 66px;">身份等级</view>
                                    <view>{{data.identityLevel | lov('ACCT_SUB_TYPE')}}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: auto; margin-right: 5px;white-space: nowrap;">身份有效结束时间</view>
                                    <view>{{data.endTime | date('YYYY-MM-DD HH:mm:ss')}}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: auto; margin-right: 5px;white-space: nowrap;">身份建设来源</view>
                                    <view>{{data.fromType | lov('FROM_TYPE')}}</view>
                                </view>
                                <view style="width: 100%;font-size: 14px;display: flex;color: #333333;margin-bottom: 4px;line-height: 22px;">
                                    <view style="color: #999999;width: auto; margin-right: 5px;white-space: nowrap;">邀约人员</view>
                                    <view>{{data.empName}}</view>
                                </view>
                            </view>
                        </item>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="消费者姓名" readonly>
                <link-input type="text" v-model="formData.acctName" :disabled="formOverallReadonly"/>
            </link-form-item>
            <link-form-item  label="原始身份等级" readonly v-if="approvalType !== 'BusinessAgentClaim' && applyType !== 'salesmanApply' && applyType !== 'salesmanChange' && updateType !== 'followChange'">
                <link-lov type="ACCT_SUB_TYPE" v-model="formData.presentIdentityLevel" :disabled="formOverallReadonly"/>
            </link-form-item>
            <link-form-item v-if="applyType !== 'salesmanChange' && applyType !== 'salesmanApply' && approvalType !== 'BusinessAgentClaim'  && updateType !== 'businessExpire'" required field="identityLevel" label="申请身份等级">
                <item slot="custom" title="申请身份等级" @tap="chooseIdentity" required>
                    <view v-if="formData.identityLevel" style="color: #3b4144">
                        {{formData.identityLevel|lov('ACCT_SUB_TYPE')}}
                    </view>
                    <view style="color: #e0e0e0;" v-else>
                        请选择申请身份等级
                    </view>
                </item>
            </link-form-item>
            <view v-if="pageFrom!=='ApprovalList' && pageFrom!=='HighMemberItem' && pageFrom !== 'HighMemberList' && approvalType !== 'ServiceStaffClaim' && updateType !== 'directorLifting' && updateType !== 'businessExpire' && updateType !== 'followChange' && approvalType !== 'IdentityChange'&& updateType !== 'businessUpdate' && approval_from !== 'wq'">
                <link-form-item v-for="(item, index) in kvALLFields" :key="'c'+index" :required="item.required === 'Y'" :field="item.field" v-if="item.showFlag" :label="item.label" :readonly="item['readonly']" :note="item.note" :disabled="formOverallReadonly">
                    <item slot="custom" :title="item.label" @tap="selectData(item.field, item.key, item.type)">
                        <view v-if="formData[item.field]" style="color: #3b4144">
                            {{formData[item.field]| lov(item.type)}}
                        </view>
                        <view style="color: #e0e0e0;" v-else>
                            请选择{{item.label}}
                        </view>

                    </item>
                </link-form-item>
                <link-form-item label="申请身份等级" :required="true" field="type" :readonly="true">
                    <link-lov type="ACCT_SUB_TYPE"
                              :disabled="formOverallReadonly"
                              :excludeLovs="['Unassigned']"
                              v-model="formData.type"/>
                </link-form-item>
            </view>
            <link-form-item v-if="followShow||!formOverallReadonly" label="跟进业代" :required="applyType !== 'salesmanChange' && applyType !== 'salesmanApply'" field="fstName" :readonly="applyType === 'salesmanChange' || applyType === 'salesmanApply'">
                <link-object :option="empOption" pageTitle="选择跟进业代"
                             :row="formData"
                             :disabled="formOverallReadonly"
                             :value="formData.fstName"
                             :map="{transmitPostnId:'id',fstName:'fstName'}">
                    <template v-slot="{data}">
                        <item :key="data.id" :title="data.fstName" :data="data" :note="data.postnName" a="1"
                              :desc="data.orgName">
                        </item>
                    </template>
                </link-object>
            </link-form-item>
            <link-form-item label="身份有效结束时间" readonly>
                <link-date v-model="formData.endTime" view="YMDHm" display-format="YYYY-MM-DD HH:mm:ss"
                           value-format="YYYY-MM-DD HH:mm:ss"></link-date>
            </link-form-item>
            <link-form-item v-if="approvalType ==='MemberTransmit' || approvalType === 'ServiceStaffClaim' || pageFrom === 'HighMemberItem' || pageFrom === 'HighMemberList'" label="身份建设来源" readonly>
                <link-input type="text" v-model="fromType" :disabled="formOverallReadonly"/>
            </link-form-item>
            <link-form-item v-if="approvalType ==='MemberTransmit' ||approvalType === 'ServiceStaffClaim' || pageFrom === 'HighMemberItem' || pageFrom === 'HighMemberList'" label="邀约人员" readonly>
                <link-input type="text" v-model="formData.empName" :disabled="formOverallReadonly"/>
            </link-form-item>
            <view v-if="configStr !== 'Y'">
                <link-form-item required label="申请说明" vertical field="reason" v-if="updateType !== 'followChange'">
                    <link-textarea placeholder="请填写申请说明，300字以内" padding-start padding-end
                                   :disabled="formOverallReadonly && applyType !== 'change'"
                                   v-model="formData.reason" :nativeProps="{maxlength:300}">

                    </link-textarea>
                </link-form-item>
            </view>
            <!--            <link-form-item vertical  label="照片" v-if="($utils.isEmpty(approvalId) && formData.id && approvalType !== 'IdentityChange' && updateType !== 'businessExpire') || !$utils.isEmpty(approvalId)">-->
            <link-form-item vertical  label="照片" v-if="(!$utils.isEmpty(approvalId) && formData.id  && updateType !== 'businessExpire' && updateType !== 'followChange') || $utils.isEmpty(approvalId)">
                <lnk-img-lzlj :parentId="$utils.isEmpty(approvalId) ? formData.id : flowObjId" :moduleType="'highMember'" :delFlag="!(formOverallReadonly && applyType !== 'change') && $utils.isEmpty(approvalId)"
                              @call="getImgList"
                              :newFlag="!(formOverallReadonly && applyType !== 'change') && $utils.isEmpty(approvalId) && attachmentList.length < 9"/>
                <list-title style="background-color: #fff">说明：如有领导签批文件，请上传</list-title>
            </link-form-item>

            <link-sticky v-if="$utils.isEmpty(approvalId)">
                <link-button block mode="stroke" @tap="cancelApply">取消</link-button>
                <link-button block @tap="submit">提交</link-button>
            </link-sticky>
            <view v-if="!$utils.isEmpty(approvalId)" class="account-info">
                <line-title title="消费者信息"/>
                <view class="account-info-data" v-if="!$utils.isEmpty(accountItem)">
                    <view v-for="(item, index) in kvALLFields" :key="index">
                        <link-form-item v-if="accountItem[item.field]" :label="item.label">
                            {{accountItem[item.field] | lov(item.type)}}
                        </link-form-item>
                    </view>
                    <link-form-item label="影响力（K序列）等级" field="type" v-if="accountItem['type']">
                        {{accountItem.type|lov('ACCT_SUB_TYPE')}}
                    </link-form-item>
                    <link-form-item label="购买力（V序列）等级" v-if="accountItem['loyaltyLevel']">
                        {{accountItem.loyaltyLevel|lov('ACCT_MEMBER_LEVEL')}}
                    </link-form-item>
                    <link-form-item label="单位名称">{{accountItem.companyName}}</link-form-item>
                    <view class="check-more">
                        <link-button mode="stroke" @tap="gotoAccountItem">点击查看更多>></link-button>
                    </view>
                </view>
                <view v-else class="no-data">暂无数据</view>
            </view>
        </link-form>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-operator>
        </link-sticky>
        <water-mark></water-mark>
    </link-page>
</template>
<script>
import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";
import {LovService} from "link-taro-component";
import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
import LineTitle from "../../lzlj/components/line-title";
import waterMark from "../../lzlj/components/water-mark";
export default {
    name: 'high-member-item-edit-page',
    components: {LineTitle, ApprovalOperator, ApprovalHistoryPoint, LnkImgLzlj, waterMark},
    data () {
        const applyType = this.pageParam.applyType || '';
        const userInfo = this.$taro.getStorageSync('token').result;
        const pageFrom = this.pageParam.pageFrom;
        const accountItem = this.pageParam.accountItem || {};
        const formData = {
            ...this.pageParam.data
        }
        console.log(pageFrom)
        console.log(formData)
        return {
            configStr: 'N',
            approval_from:'',                          //是否点击卡片消息
            updateType: '',                             // 申请类型
            approvalType: '',                           // 审批类型
            companyId: '',                              // 公司ID
            applyType,
            userInfo,
            accountItem,                            // 消费者部分字段
            flowObjId: '',                              // 业务对象ID
            approvalId: null,                           // 审批ID
            kvALLFields: [
                {field: 'socialStatus', key: 1, showFlag: false, type: 'ACCT_SOCIAL_STATUS', label: '社会地位', socialStatusList: []},
                {field: 'acctRank', key: 2, showFlag: false, type: 'ACCT_RANK', label: '职级', acctRankList: []},
                {field: 'socialCircle', key: 3, showFlag: false, type: 'SOCIAL_CIRCLE', label: '所属系统', socialCircleList: []},
                {field: 'positionGrade', key: 4, showFlag: false, type: 'POSITION_GRADE', label: '职位级别', positionGradeList: []},
                {field: 'enterpriseLevel', key: 5, showFlag: false, type: 'ENTERPRISE_LEVEL', label: '单位级别', enterpriseLevelList: []},
                {field: 'enterpriseSize', key: 6, showFlag: false, type: 'ENTERPRISE_SIZE', label: '企业规模', enterpriseSizeList: []},
                {field: 'personnelType', key: 7, showFlag: false, type: 'PERSONNEL_TYPE', label: '人员类型', personnelTypeList: []},
            ],
            attachmentList: [],
            includesLovs: [],         // 可选身份等级数据
            pageFrom,
            formData,
            formOverallReadonly: false,
            memberOption: new this.AutoList(this, {                              // 超高价值会员数据
                module: this.$env.appURL + '/action/link/highIdentity',
                param: {
                    oauth: 'ALL',
                    filtersRaw: [
                        {id: 'fromType', property: 'fromType', value: 'SeedInvite'},
                        {id: 'identityStatus', property: 'identityStatus', value: 'Effective'},
                        {id: 'servicePersonal', property: 'servicePersonal', value: userInfo.postnId,operator: '<>'},
                    ]
                },
                searchFields: ['mobilePhone'],
                exactSearchFields: [
                    {
                        field: 'acctName',
                        showValue: '姓名',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }, {
                        field: 'mobilePhone',
                        showValue: '手机号',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }
                ],
                sortOptions: null,
                hooks: {
                    beforeLoad(options) {
                        for (let i = 0; i < options.param.filtersRaw.length; i++) {
                            if (options.param.filtersRaw[i].property === 'acctName') {
                                options.param.filtersRaw[i].operator = 'like';
                            }
                        }
                        options.param.sort = 'created';
                    }
                }
            }),
            empOption: new this.AutoList(this, {
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/position/queryByIdentityEmp'
                },
                param: {
                    sort: '',
                    order: 'desc',
                    oauth: 'ALL',
                    pageFlag: true,
                    onlyCountFlag: false,
                    rows: 50,
                    page: 1,
                    attr1: 'attr1',
                    attr2: '',
                    filtersRaw: []
                },
                searchFields: ['fstName', 'postnName', 'orgName'],
                exactSearchFields: [
                    {
                        field: 'fstName',
                        showValue: '姓名',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }, {
                        field: 'empTel',
                        showValue: '手机号',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    }
                ],
                sortOptions: null,
                hooks: {
                    async beforeLoad(options) {
                        for (let i = 0; i < options.param.filtersRaw.length; i++) {
                            if (options.param.filtersRaw[i].property === 'fstName') {
                                options.param.filtersRaw[i].operator = 'like';
                            }
                        }
                        options.param.sort = 'created';
                        options.param.attr2 = this.userInfo.coreOrganizationTile.orgId;
                        const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
                        let str = '';
                        lovData.forEach((item) => {
                            str = item.val + ',' + str
                        });
                        str = str.substring(0, str.length - 1);
                        options.param.filtersRaw = [
                            ...options.param.filtersRaw,
                            {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'},
                            {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}
                        ];
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} title={data.fstName} data={data} note={data.postnName}
                              desc={data.orgName}>
                        </item>
                    )
                }
            })
        }
    },
    async created () {
        // 查询企业参数配置-参数键consumer_display_control
        this.configStr = await this.$utils.getCfgProperty('consumer_display_control');
        console.log('configStr', this.configStr);
        await this.queryIdentityLevelRange();
        let code = this.pageParam.source; //页面来源
        let sceneObj = await this.$scene.ready(); //消息场景对象
        const approval_from = sceneObj.query['approval_from'];
        this.approval_from = approval_from
        if(code === 'approval'){
            this.formOverallReadonly = true;
            this.approvalId = this.pageParam.data.id;//审批传过来的审批数据ID
            this.approvalType = this.pageParam.data.approvalType;
            this.flowObjId = this.pageParam.data.flowObjId;//审批传过来的业务对象ID
            if (this.$utils.isNotEmpty( this.flowObjId)){
                this.queryContentInfoById();
            } else {
                this.$utils.showAlert('请联系管理员，未获取到超高价值会员申请信息！', {icon: 'none'});
            }
        } else {
            if(approval_from === 'qw') { //从小程序审批消息而来
                this.formOverallReadonly = true;
                this.approvalId = sceneObj.query['approval_id'];
                this.flowObjId = sceneObj.query['flowObjId'];
                this.approvalType = sceneObj.query['approvalType'];
                if(this.$utils.isNotEmpty( this.flowObjId)){
                    this.queryContentInfoById();
                }else{

                    this.$utils.showAlert('请联系管理员，未获取到超高价值会员申请信息！', {icon: 'none'});
                }
            } else {
                this.formData = this.pageParam.data;
            }
        }
        if (this.applyType === 'change') { // 申请变更
            const id = await this.$newId();
            this.formOverallReadonly = true;
            this.formData.businessId = this.pageParam.data.id;
            this.formData.id = id;
            this.formData.reason = '';
        }
        if (this.pageFrom === 'AccountItem') {
            await this.queryCfg();
            await this.initLevelData();
            this.accountItem = this.pageParam.accountItem;
            this.$set(this.formData, 'mobilePhone', this.accountItem.phoneNumber);
            this.$set(this.formData, 'acctName', this.accountItem.name);
            this.$set(this.formData, 'consumerId', this.accountItem.id);
            this.$set(this.formData, 'fstName', this.accountItem.fstName);
            this.$set(this.formData, 'businessId', this.pageParam.data.id);
            const id = await this.$newId();
            this.$set(this.formData, 'id', id);
            this.formData.reason = '';
        }
    },
    computed: {
        fromType () {
            if(this.formData.fromType === 'SeedInvite'){
                return '种子邀约'
            }else if (this.formData.fromType === 'AuthorityAgent') {
                return '权限业代'
            }else if (this.formData.fromType === 'MembershipAgent'){
                return '会员服务部业代'
            }
        },
        /**
         * @desc 是否显示跟进业代字段
         * <AUTHOR>
         * @date 2023/7/27 16:30
         **/
        followShow () {
            if (this.updateType === 'directorClaim') {
                return true;
            } else if (this.updateType === 'followChange') {
                return false;
            } else if (this.updateType === 'businessInsert') {
                return false;
            } else if (this.updateType === 'businessExpire') {
                return false;
            } else if (this.updateType === 'businessUpdate') {
                return false;
            } else if (this.updateType === 'directorLifting') {
                return false;
            }else {
                return false;
            }
        },
        /**
         * @desc 标题名称
         * <AUTHOR>
         * @date 2023/6/15 21:43
         **/
        memberTitle () {
            console.log('approvalType', this.approvalType,'updateType',this.updateType)
            if (this.approvalType === 'IdentityChange' && this.updateType === 'businessExpire') {
                return '作废超高价值会员身份申请';
            } else if (this.approvalType === 'IdentityChange' && this.updateType === 'directorLifting') {
                return '超高价值会员身份等级变更申请';
            } else if (this.approvalType === 'IdentityChange' && this.updateType === 'businessUpdate') {
                return '超高价值会员身份等级变更申请';
            } else if (this.approvalType === 'BusinessAgentClaim' && this.updateType === 'businessInsert') {
                return '超高价值会员身份申请';
            } else if (this.approvalType === 'ServiceStaffClaim' && this.updateType === 'directorClaim') {
                return '超高价值会员身份申请';
            } else {
                return null;
            }
        },
        /**
         * @desc kv序列值
         * <AUTHOR>
         * @date 2023/6/15 21:43
         **/
        kvAllData () {
            let kvData = {};
            this.kvALLFields.forEach((item)=> {
                kvData[item.field + 'List'] = item[item.field + 'List']
            })
            return kvData;
        },
    },
    methods: {
        /**
         * @desc 查询审批数据详情
         * <AUTHOR>
         * @date 2023/6/15 16:31
         **/
        async queryContentInfoById () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/identityAppRecord/queryById', {id: this.flowObjId});
            if (data.success) {
                this.updateType = data.result.updateType;
                const businessData = JSON.parse(data.result.businessData);
                console.log(businessData)
                this.formData = businessData.newData;
                this.formData.reason = data.result.reason;
                this.formData.presentIdentityLevel = businessData.oldData.identityLevel;
                this.formData.socialStatus = data.result['showSocialStatus'] || null;
                this.formData.acctRank = data.result['showAcctRank'] || null;
                this.formData.typeId = data.result['showTypeId'] || null;
                this.formData.type =businessData.newData.identityLevel || null;
                this.formData.socialCircle = data.result['showSocialCircle'] || null;
                this.formData.positionGrade = data.result['showPositionGrade'] || null;
                this.formData.enterpriseLevel = data.result['showEnterpriseLevel'] || null;
                this.formData.enterpriseSize = data.result['showEnterpriseSize'] || null;
                this.formData.personnelType = data.result['showPersonnelType'] || null;
                if(this.approvalType === 'ServiceStaffClaim' && this.updateType === 'directorClaim'){
                    this.formData.fstName = businessData['fstName'] || null;
                }else{
                    this.formData.fstName = businessData.newData['fstName'] || null;
                }

                if (data.result['showTypeId']) {
                    this.kvALLFields.forEach((item)=> {
                        item.showFlag = !!this.formData[item.field];
                    })
                }
                this.formData.id = data.result.id;
                await this.queryConsumerInfo();
            }
        },
        /**
         * @desc 查询消费者数据
         * <AUTHOR>
         * @date 2023/6/15 17:26
         **/
        async queryConsumerInfo () {
            const data = await this.$http.post(this.$env.dmpURL + '/action/link/cdcPubConsumer/queryMpById', {id: this.formData.consumerId}, {
                autoHandleError: false,
                handleFailed: (response) => {
                }
            });
            if (data.success) {
                this.accountItem = data.result;
            }
        },
        /**
         * @desc 查询参数配置
         * <AUTHOR>
         * @date 2022/6/7 17:21
         **/
        async queryCfg() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'member_service_dept_id'});
            if (data.success) {
                this.companyId = data.value;
            }
        },
        /**
         * @desc 跳转查询消费者详情数据
         * <AUTHOR>
         * @date 2023/6/7 15:50
         **/
        gotoAccountItem () {
            this.$nav.push('/pages/lj-consumers/account/account-item-page', {
                data: this.accountItem,
                pageFrom: 'HighMemberItemEdit'
            });
        },
        /**
         * @desc 选择数据
         * <AUTHOR>
         * @date 2023/5/8 22:36
         * @param  field 选择的字段
         * @param key 对应的下一层级key
         * @param type 值列表类型
         * @param readonly 只读不可点击
         **/
        async selectData (field, key, type, readonly) {
            if (readonly) return;
            if (this.formOverallReadonly) return;
            const data = await this.$select(this.kvAllData[field + 'List'], {
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data}>
                            <view slot="title">{LovService.filter(data[field], type)}</view>
                        </item>
                    )
                }
            })
            if (data) {
                let tempAllField = this.kvALLFields;
                tempAllField.forEach((item, index)=> {
                    if (index >= key) {
                        item.showFlag = false;
                        item[item.field + 'List'] = [];
                        this.formData[item.field] = null;
                    }
                });
                this.formData['type'] = null;
                this.formData['typeId'] = null;
            }
            this.$set(this.formData, field, data[field]);
            if (data.itemList && data.itemList.length === 1 && data.itemList[0]['type']) {
                this.formData.type = data.itemList[0]['type'];
                this.formData.typeId = data.itemList[0].id;
                return;
            }
            this.chooseData(data, key);
        },
        /**
         * @desc 选择数据
         * <AUTHOR>
         * @date 2023/5/8 16:43
         * @param data 选择对应数据
         * @param key 对应的下一层级key
         * @param type 操作类型
         **/
        chooseData (data, key, type) {
            let tempData = data;
            let tempKeyData = this.kvALLFields.slice(key, this.kvALLFields.length);
            for (let i = 0; i < tempKeyData.length; i++) {
                let tempKey = Number(i) + Number(key);
                if (tempKey < this.kvALLFields.length) {
                    tempData = this.dealData(tempData, tempKey, type, key);
                }
            }
            let filterData = tempKeyData.filter((item)=> !!item.showFlag);
            if (filterData.length < 1) {
                if (tempData && tempData.itemList && tempData.itemList.length === 1 && tempData.itemList[0]['type']) {
                    this.formData.type = tempData.itemList[0]['type'];
                    this.formData.typeId = tempData.itemList[0].id;
                }
            }
        },
        /**
         * @desc 处理数据
         * <AUTHOR>
         * @date 2023/5/8 17:48
         * @param data 选择对应数据e
         * @param key 对应的下一层级key
         * @param type 操作类型
         **/
        dealData (data, key, type) {
            let tempAllField = this.kvALLFields;
            if (data && data.itemList && data.itemList.length > 0) {
                let tempItemList = data.itemList;
                if ((tempItemList.length === 1 && tempItemList[0][tempAllField[key].field] === '空值') || (type === 'edit' && tempItemList[0][tempAllField[key].field] === this.formData[tempAllField[key].field])) {
                    tempAllField[key].showFlag = type === 'edit' && tempItemList[0][tempAllField[key].field] === this.formData[tempAllField[key].field];
                    this.kvALLFields = tempAllField;
                    if (tempItemList[0]['itemList'] && tempItemList[0]['itemList'].length > 0) {
                        return tempItemList[0];
                    }
                } else if (tempItemList.length > 1) {
                    tempAllField[key].showFlag = true;
                    tempAllField[key][tempAllField[key].field + 'List'] = tempItemList;
                    this.kvALLFields = tempAllField;
                } else if (tempItemList.length === 1 && tempItemList[0][tempAllField[key].field] !== '空值') {
                    tempAllField[key].showFlag = true;
                    tempAllField[key][tempAllField[key].field + 'List'] = tempItemList;
                    this.kvALLFields = tempAllField;
                    if (tempItemList[0]['itemList'] && tempItemList[0]['itemList'].length > 0) {
                        return tempItemList[0];
                    }
                }
            }
        },
        /**
         * @desc 查询分类映射关系
         * <AUTHOR>
         * @date 2023/5/8 09:56
         **/
        async initLevelData () {
            const data = await this.$http.post(this.$env.appURL + '/link/mapConType/queryCache', {
                companyId: this.companyId
            });
            if (data.success) {
                if (data.rows.length > 1) {
                    this.kvALLFields[0]['showFlag'] = true;
                    this.kvALLFields[0][this.kvALLFields[0].field + 'List'] = data.rows;
                } else if (data.rows.length === 1){
                    if (data.rows[0]['socialStatus'] === '空值') {
                        this.kvALLFields[0]['showFlag'] = false;
                    } else if (data.rows[0].itemList && data.rows[0].itemList > 1) {
                        this.kvALLFields[0][this.kvALLFields[0].field + 'List'] = data.rows[0].itemList;
                    }
                    if (data.rows[0].itemList.length > 0) {
                        data.rows[0].itemList.forEach((item)=> {
                            if (item.acctRank === '空值') {
                                this.kvALLFields[1]['showFlag'] = false;
                            }
                            this.kvALLFields[2]['showFlag'] = true;
                            this.kvALLFields[2][this.kvALLFields[2].field + 'List'] = item.itemList;
                        })
                    }
                }
            }
        },
        /**
         * @desc 查询图片
         * <AUTHOR>
         * @date 2023/6/6 21:09
         **/
        getImgList (data) {
            this.attachmentList = data;
            console.log(data, '测试数据')
        },
        /**
         * @desc 选择身份等级
         * <AUTHOR>
         * @date 2023/6/6 15:59
         **/
        async chooseIdentity() {
            if (this.approvalId) return;
            //业代申请等级变更排除自己当前身份等级
            //营销主任认领：因为都是大使身份，所以不排除当前身份等级
            let screeningLevel =  ( (presentIdentityLevel)=>{
                if(this.pageFrom === 'HighMemberItem'){
                    return this.includesLovs.filter(function(item){
                        return item !== presentIdentityLevel
                    })
                }else{
                    return this.includesLovs
                }
            })
            let includesLovs = screeningLevel(this.formData.presentIdentityLevel)
            const data = await this.$select(includesLovs, {
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data}>
                            <view slot="title">{LovService.filter(data, 'ACCT_SUB_TYPE')}</view>
                        </item>
                    )
                }
            });
            this.$set(this.formData, 'identityLevel', data);
        },
        /**
         * @desc 查询身份等级可选范围
         * <AUTHOR>
         * @date 2023/6/6 15:34
         **/
        async queryIdentityLevelRange () {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'Identity_level_range'});
            if (data.success) {
                this.includesLovs = data.value.split('/');
            }
        },
        /**
         * @desc 取消申请
         * <AUTHOR>
         * @date 2023/5/31 15:36
         **/
        cancelApply () {
            this.$nav.back();
        },
        /**
         * @desc 提交申请
         * <AUTHOR>
         * @date 2023/5/31 15:37
         **/
        async submit () {
            await this.$refs.highMemberForm.validate();
            if(this.formData.identityStatus === 'InApproval'){
                this.$showError('此超高价值会员身份变更中，无法发起变更身份等级!');
                return;
            }
            this.$utils.showLoading();
            const tempData = {transmitPostnId: this.formData.transmitPostnId, fstName: this.formData.fstName};
            // const tempData = {transmitPostnId: this.formData.transmitPostnId, reason: this.formData.reason, fstName: this.formData.fstName};
            const businessData = JSON.stringify(tempData);
            let url = '/link/identityAppRecord/directorClaim';
            let param = {
                id: this.formData.id,
                businessId: this.formData.businessId,
                identityLevel: this.formData.identityLevel,
                businessData: businessData,
                reason: this.formData.reason
            };
            if (this.applyType === 'change') {
                url = '/link/identityAppRecord/directorLifting';
                param = {
                    id: this.formData.id,
                    businessId: this.formData.businessId,
                    identityLevel: this.formData.identityLevel,
                    reason: this.formData.reason
                };
            } else if (this.applyType === 'salesmanApply') { // 业代申请超高身份
                url = '/link/highIdentity/businessInsert';
                this.formData['identityLevel'] = this.formData.type;
                param = this.formData;
            } else if (this.applyType === 'salesmanChange') { // 业代申请变更超高身份
                url = '/link/identityAppRecord/businessUpdate';
                this.formData['identityLevel'] = this.formData.type;
                param = {
                    id: this.formData.id,
                    businessId: this.formData.businessId,
                    identityLevel: this.formData.identityLevel,
                    showTypeId: this.formData.typeId,
                    businessData: businessData,
                    reason: this.formData.reason
                }
            }
            const data = await this.$http.post(this.$env.appURL + url, param, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`提交申请失败：${response.result}`);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.$message.success(`提交申请成功`);
                this.$nav.back();
            }
        }
    }
}
</script>
<style lang="scss">
.high-member-item-edit-page{
    .blank {
        width: 100%;
        height: 384px;
        background: #F2F2F2;
    }
    /*deep*/ .line-title {
                 margin-bottom: 24px;
             }
    .account-info{
        .check-more{
            width: 100%;
            background: white;
            padding: 10px 0;
            display: flex;
            justify-content: center;
        }
        .no-data{
            width: 100%;
            color: #8f8f94;
            text-align: center;
            font-size: 28px;
            line-height: 60px;
        }
    }
    .link-form-item{
        .link-textarea{
            padding: 14rpx 24rpx 34rpx 24rpx ;
        }
    }
}
</style>
