import {LovService} from "link-taro-component";
import TagInfo from './account/components/tag-info/tag-info';
export default function consumerCommon () {
    return {
        components: {TagInfo},
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
            const filtersRawTemp = [{id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
                {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}];
            const newAccountItem = {
                id: '',
                phoneNumber: '',
                row_status: 'NEW',
                consumerDataType: 'ChannelConsumer',
                dataSource: 'MarketingPlatform',
                dataType: 'Consumer',
                accntSourceFrom: 'SalesAssistant',
                orgId: userInfo.orgId,
                fstName: userInfo.firstName,
                postnId: userInfo.postnId,
                belongToCompanyId: userInfo.coreOrganizationTile['l3Id'] || '',
                type: "ToBeFollowed",
                birthType: 'Yang',
                brandPreference: "",
                hobby: "",
                terminalFlag : 'N',
                impFlag: 'N',
                schedulFlag: 'N'
            };
            return {
                customerOptionOauth: '',
                isStoreChosen: false,
                allConTypeList: {},         // 所有kv映射备选对象值
                kvALLFields: [
                    {field: 'socialStatus', showFlag: false, type: 'ACCT_SOCIAL_STATUS', label: '社会地位', socialStatusList: []},
                    {field: 'acctRank', showFlag: false, type: 'ACCT_RANK', label: '职级', acctRankList: []},
                    {field: 'socialCircle', showFlag: false, type: 'SOCIAL_CIRCLE', label: '所属系统', socialCircleList: []},
                    {field: 'positionGrade', showFlag: false, type: 'POSITION_GRADE', label: '职位级别', positionGradeList: []},
                    {field: 'enterpriseLevel', showFlag: false, type: 'ENTERPRISE_LEVEL', label: '单位级别', enterpriseLevelList: []},
                    {field: 'enterpriseSize', showFlag: false, type: 'ENTERPRISE_SIZE', label: '企业规模', enterpriseSizeList: []},
                    {field: 'personnelType', showFlag: false, type: 'PERSONNEL_TYPE', label: '人员类型', personnelTypeList: []},
                ],
                newKFields: [
                    {field: 'memberType', showFlag: false, type: 'MEMBER_TYPE', label: '会员类型', memberTypeList: []},
                    {field: 'affiliatedUnitType', showFlag: false, type: 'AFFI_UNIT_TYPE', label: '所属单位类型', affiliatedUnitTypeList: []},
                    {field: 'unit', showFlag: false, type: 'UNIT', label: '单位', unitList: []},
                    {field: 'office', showFlag: false, type: 'OFFICE', label: '职务层级', officeList: []},
                ],
                filtersRawTemp,
                tagIdList: [],
                tagDialogFlag: false,                     // 标签筛选弹窗
                tagGroupList: [],
                isGuoJiao: false,     // 判断是否查询国窖系公司的分类分级值列表
                classifyList: [
                    {val: 'ALL', name: '全部', seq: '1', field: ''},
                    {val: 'IMP_FLAG', name: '重点客户', seq: '2', field: 'impFlag'},
                    {val: 'subAcctType', name: 'K序列', seq: '3', field: 'subAcctType'},
                    {val: 'ACCT_MEMBER_LEVEL', name: 'V序列', seq: '4', field: 'loyaltyLevel'}
                ],
                classifyListActive: {val: 'ALL', name: '全部', seq: '1', field: ''},
                classifyItemListActive: {},
                classifyItemList: [],
                copyCustomFieldsData: {},                  // 备份自定义字段
                copyFormData: {},                          // 备份消费者自定义字段
                insertApproval: 'N',                       // 是否新建审批
                fieldFlag: 'Y', // 根据参数配置跳转
                editPath: '/pages/lj-consumers/account/account-item-edit-page', // 跳转路径
                newAccountItem,
                userInfo,
                accountOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/action/link/consumer',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/consumerListSend'
                    },
                    param: {
                        rows: 25,
                        filtersRaw: [
                            {id: 'companyId', property: 'companyId', value: userInfo.coreOrganizationTile['l3Id'], operator: '='},
                            {id: 'consumerType', property: 'consumerType', value: 'ChannelConsumer', operator: '='},
                            {id: 'accntChannel', property: 'accntChannel', value: 'MarketingPlatform', operator: '='},
                            {id: 'empFlag', property: 'empFlag', value: 'N', operator: '='},
                            {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                        ],
                        oauth: 'MY_POSTN_ONLY'
                    },
                    exactSearchFields: [
                        {
                            field: 'acctName',
                            showValue: '姓名',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }, {
                            field: 'mobilePhone1',
                            showValue: '手机号',
                            searchOnChange: true,
                            clearOnChange: true,
                            exactSearch: true
                        }
                    ],
                    filterOption: [
                        {label: '客户性别', field: 'gender', type: 'lov', lov: 'GENDER'},
                        {label: '消费者生日', field: 'birthdayMd', type: 'date', view: 'MD', format: 'MM-DD'}
                    ],
                    hooks: {
                        beforeLoad (option) {
                            for (let i = 0; i < option.param.filtersRaw.length; i++) {
                                if (option.param.filtersRaw[i].property === 'acctName') {
                                    option.param.filtersRaw[i].operator = 'like';
                                }
                            }
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} arrow="false" style="overflow: hidden; margin: 12px;">
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                                <view style="position: relative;-webkit-box-sizing: border-box;box-sizing: border-box;display: flex;width: 100%; padding: 16px 12px 12px 12px;-webkit-flex-direction: column;-ms-flex-direction: column;flex-direction: column; overflow-x: auto;">
                                    <view style="height: 24px;font-family: PingFangSC-Semibold;font-size: 16px;color: #212223;line-height: 24px;font-weight: 600;">{ data.acctName }</view>
                                    <view style="height: 18px;margin: 8px 0;display: flex;">
                                        <view style="min-width: 40px;padding: 0 8px;margin-right: 8px;background: #F0F5FF;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 18px;font-weight: 400;flex-shrink: 0;white-space: nowrap;">{ LovService.filter(data.subAcctType, 'ACCT_SUB_TYPE') }</view>
                                        <view style="min-width: 40px;padding: 0 8px;margin-right: 8px;background: #F0F5FF;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 18px;font-weight: 400;flex-shrink: 0;white-space: nowrap;">{ LovService.filter(data.loyaltyLevel, 'ACCT_MEMBER_LEVEL') }</view>
                                        {data.impFlag === 'Y' ?  <view style="width: 56px;margin-right: 8px;background: #FFF1EB;border-radius: 2px;font-family: PingFangSC-Regular;font-size: 11px;color: #FF461E;line-height: 18px;font-weight: 400;text-align: center;flex-shrink: 0;white-space: nowrap;">重点客户</view> : '' }
                                        {data.identityLevel ?  <view style="width: auto;padding: 0 2px;background: #262626;border-radius: 2px; font-size: 11px;color: #F0BE94;line-height: 18px;font-weight: 400;text-align: center;flex-shrink: 0;white-space: nowrap;">{LovService.filter(data.identityLevel, 'ACCT_SUB_TYPE')}</view> : '' }
                                    </view>
                                    <view style="display: flex;flex-direction: column;">
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="min-width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">联系方式</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #317DF7;line-height: 22px;font-weight: 400;flex-shrink: 0;white-space: nowrap;">{data.mobilePhone1}</view>
                                        </view>
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="min-width: 32px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">单位</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;flex-shrink: 0;white-space: nowrap;">{data.company}</view>
                                        </view>
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="min-width: 32px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;">职务</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;flex-shrink: 0;white-space: nowrap;">{data.position}</view>
                                        </view>
                                        <view style="height: 22px;display: flex;align-items: center;margin-bottom: 4px;">
                                            <view style="min-width: 56px;margin-right: 12px;font-family: PingFangSC-Regular;font-size: 14px;color: #999999;line-height: 22px;font-weight: 400;white-space: nowrap;">所属客户</view>
                                            <view style="font-family: PingFangSC-Regular;font-size: 14px;color: #333333;line-height: 22px;font-weight: 400;flex-shrink: 0;white-space: nowrap;">{data.belongToStore}</view>
                                        </view>
                                    </view>
                                </view>
                                <view style="position: absolute; right: -5px;top:0;">
                                        <view style="background: #2F69F8;min-width: 72px;color: white;letter-spacing: 0;text-align: center;text-decoration: none;height: 20px;transform: skew(30deg, 0);display: flex;justify-content: center;align-items: center;border-radius: 3px;padding: 0 8px 0 16px;">
                                            <view style="font-size: 12px; transform: skew(-30deg, 0);color: #FFFFFF;">{ data.fstName }跟进</view>
                                        </view>
                                </view>
                            </item>
                        )
                    },
                    customClass: 'choose-consumer-list-page',
                    slots: {
                        other: () => (
                            <view>
                                {this.addFlag && <link-fab-button onTap={this.addConsumer}></link-fab-button>}
                                <tag-info show={this.tagDialogFlag} {...{on: {'update:show': val => this.tagDialogFlag = val}}} tagGroupList={this.tagGroupList} {...{on: {'choose': val => {this.chooseTagGroups(val)}}}}/>
                            </view>),
                        top: () => (<view style="position: relative;">
                            <view style="width: 100%;position: absolute;top: -88px;display: flex;justify-content: space-around;flex-wrap: nowrap;">
                                {this.classifyList.map(tab => {
                                    return (
                                        <view onTap={()=>this.switchTab(tab)} style={tab.seq === this.classifyListActive.seq
                                            ? 'width: 25%;justify-content: center;align-items: center;flex-direction: column;color:#2f69f8; height: 46px;line-height: 46px; display: flex;'
                                            : 'width: 25%;justify-content: center;align-items: center;flex-direction: column; height: 46px;line-height: 46px; display: flex;'}>
                                            <view style="font-size: 14px;margin-left: 5px;display: flex;flex-direction: column;align-items: center;justify-content: center;">
                                                <text>{tab.name}</text>
                                                {tab.seq === this.classifyListActive.seq
                                                    ? <view style="height: 4px;width: 14px;border-radius: 8px 8px 0 0;background-color: #2f69f8;box-shadow: 0 1.5px 4px 0 rgba(47,105,248,0.63);margin-top: -4px;"></view>
                                                    : ''
                                                }
                                        </view>
                                    </view>
                                )})}
                            </view>
                                {
                                    this.classifyItemList.length > 0?
                                        <view>
                                            <scroll-view scroll-x="true">
                                                <view style="height: 36px; display: flex;align-items: center;">
                                                    {
                                                        this.classifyItemList.map(tabItem => {
                                                            return (
                                                                <view  onTap={() =>this.switchTabItem(tabItem)} style={tabItem.seq === this.classifyItemListActive.seq
                                                                    ? 'min-width: 85px;height: 28px;box-sizing: border-box;margin: 0 0 8px 12px;font-size:12px;color: #3F66EF;letter-spacing: 0;text-align: center;line-height: 28px; font-weight: 400;border: 0.5px solid rgba(63,102,239,1);border-radius: 14px;'
                                                                    : 'min-width: 85px;height: 28px;box-sizing: border-box;margin: 0 0 8px 12px;font-size:12px;color: #333333;letter-spacing: 0;text-align: center;line-height: 28px; font-weight: 400;border: 0.5px solid rgba(221,221,221,1);border-radius: 14px;'}>
                                                                    { tabItem.name }
                                                                </view>
                                                            )
                                                        })
                                                    }
                                                </view>
                                            </scroll-view>
                                        </view> : ''
                                }
                        </view>
                        ),
                        filterGroup: () => (
                            <view style="flex: 1;overflow-x: hidden;">
                                <scroll-view style="width: 100%;">
                                    <view style="display: flex;white-space: nowrap;font-size: 24px;padding: 4px 12px;align-items: center;flex-wrap: nowrap;">
                                        <view onTap={() => this.chooseStoreData()} style={this.isStoreChosen
                                            ? 'width:82px; height: 20px;margin: 6px 2px;background: #EDF3FF;background: #EDF3FF;color: #2F69F8;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'
                                            : 'margin: 7.5px 0;height: 25px; line-height: 25px;border-radius: 3px;padding: 0 10px;max-width: 62px;color: #333333;height: 20px;margin: 6px 2px;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'}>
                                            所属客户<link-icon icon="mp-desc" style="width: 8px;height: 6px;color: #CCCCCC;margin-left: 4px;"/>
                                        </view>
                                        {/*<view onTap={() => {this.tagDialogFlag=true}} style={this.tagIdList.length>0*/}
                                        {/*    ? 'height: 20px;margin: 6px 2px;background: #EDF3FF;background: #EDF3FF;color: #2F69F8;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'*/}
                                        {/*    : 'margin: 7.5px 0;height: 25px; line-height: 25px;border-radius: 3px;padding: 0 10px;max-width: 62px;color: #333333;height: 20px;margin: 6px 2px;font-size: 13px;line-height: 20px;font-weight: 400;text-align: center;'}>*/}
                                        {/*    标签<link-icon icon="mp-desc" style="width: 8px;height: 6px;color: #CCCCCC;margin-left: 4px;"/>*/}
                                        {/*</view>*/}
                                        <view style="width: 1px;height: 16px;position: absolute;right: 2px;top: 12px;background-color: #CCCCCC;border: 1px solid rgba(204,204,204,1);"></view>
                                    </view>
                                </scroll-view>
                            </view>

                        )
                    }
                }),
                templateId: '', // 模板ID
                componentsMap: {             // 组件映射,将后台配置的值列表转换成小程序组件
                    'SinglelineText': {component: {ctrlCode: 'link-input', values: {}}}, // 单行文本
                    'Checkbox': {component: {ctrlCode: 'link-select', values: {multiple: true, complexSelectData: []}}}, // 多选
                    'Radio': {component: {ctrlCode: 'link-select', values: {multiple: false, complexSelectData: []}}}, // 单选
                    'Money': {component: {ctrlCode: 'link-input', values: {}}}, // 金额
                    'Number': {component: {ctrlCode: 'link-input', values: {}}}, // 数字
                    'DateAndTime': {component: {ctrlCode: 'link-date', values: {view: 'YMDHms', displayFormat: 'YYYY-MM-DD HH:mm:ss', valueFormat: 'YYYY-MM-DD HH:mm:ss'}}}, // 年月日时分秒
                    'Date': {component: {ctrlCode: 'link-date', values: {view: 'YMD', displayFormat: 'YYYY-MM-DD', valueFormat: 'YYYY-MM-DD'}}}, // 年月日
                    'MultilineText': {component: {ctrlCode: 'link-textarea', values: {}}}, // 多行文本
                    'ProvincialCity': {component: {ctrlCode: 'view-line', values: {}}}, // 地址
                    'Photo': {component: {ctrlCode: 'lnk-img-watermark', values: {}}}, // 图片
                    // 'ProvincialCity': {component: {ctrlCode: 'link-address', values: {}}}, // 地址
                },
                changeFieldList: [], // 变更记录
                customFieldsData: {}, // 自定义字段对象
                labelField: [],   // 标签字段
                customFields: [],
                standarFields: [], // 标准字段
                addFlag: true,     // 是否可以新建消费者
                customerOption: new this.AutoList(this, { // 所属客户
                    module: this.$env.appURL + '/action/link/accnt',
                    url: {
                        queryByExamplePage: this.$env.appURL + '/link/interCustTerminal/queryAccntPage'
                    },
                    param: () => {
                        return {
                            postnId: '',
                            oauth: this.customerOptionOauth || 'MY_POSTN_ONLY'
                        }
                    },
                    searchFields: ['acctName'],
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} className="select-box" arrow="false">
                                <link-checkbox val={data.accntId} toggleOnClickItem slot="thumb"></link-checkbox>
                                <view slot="title" style="display: flex;">{data.acctName} <view style="margin-left: 1em;background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;font-size:12px;padding: 3px;border-radius: 3px;">{LovService.filter(data.acctType, 'ACCT_TYPE')}</view></view>
                                <view slot="note">{data.province}{data.city}{data.district}{data.address}</view>
                            </item>)
                    },
                    hooks: {
                        beforeLoad (option) {
                            delete option.param.order;
                            delete option.param.sort;
                            option.param.postnId = this.userInfo.postnId;
                            option.param.oauth = this.customerOptionOauth || 'MY_POSTN_ONLY';
                        }
                    }
                }),
            }
        },
        async created () {
            this.isStoreChosen = false;
            await this.queryCfg();
            if(!this.tagGroupNotShow){
                this.tagIdList = [];
                await this.queryTagGroups();
            }
            // 不展示消费者添加按钮：不在值列表【ASSIGN_POSITION】中，且值列表状态为有效的，其余的职位类型进入【消费者列表】【市场活动添加消费者，新建按钮】【礼赠添加消费者新建】【动销添加消费者新建】【拜访添加消费者新建】【名单提报添加消费者新建】
            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
            const assignList = lovData.map(item => item.val);
            if(!assignList.includes(this.userInfo.positionType)) {
                this.addFlag = false;
            }
        },
        methods: {
            /** 根据标签组选择对应的标签值
             * @desc
             * <AUTHOR>
             * @date 2022/8/22 11:14
             **/
            async chooseTagGroups(item) {
                const data = this.$utils.deepcopy(item);
                if (data.length === 0) {
                    this.tagIdList = [];
                } else {
                    this.tagIdList = data.map(i => ({
                        tagGroupId: i.headId,
                        tagId: i.id
                    }));
                }
                if (this.tagIdList.length > 0) {
                    this.accountOption.option.param['tagIdList'] = this.tagIdList.map((item) => {
                        return item.tagId
                    });
                    this.accountOption.methods.reload();
                } else {
                    delete this.accountOption.option.param['tagIdList'];
                    this.accountOption.methods.reload();
                }
            },
            /**
             * @desc 查询分类映射关系
             * <AUTHOR>
             * @date 2023/5/8 09:56
             **/
            async initLevelData () {
                const cfgStr = await this.$utils.getCfgProperty('New_Type_Company');
                const cfgArray = cfgStr.split(',');
                // 查询初始分类映射关系
                const orgStr = await this.$utils.getCfgProperty('ORG_LIMIT');
                const orgArray = orgStr.split(',');
                const data = await this.$http.post(this.$env.appURL + '/link/mapConType/queryCache', {
                    companyId: this.userInfo.coreOrganizationTile['l3Id']
                });

                if (data.success) {
                    this.allConTypeList = data.rows[0];
                    if (data.rows.length > 1) {
                        this.kvALLFields[0]['showFlag'] = true;
                        this.kvALLFields[0][this.kvALLFields[0].field + 'List'] = data.rows;
                    } else if (data.rows.length === 1){
                        if (data.rows[0]['socialStatus'] === '空值') {
                            this.kvALLFields[0]['showFlag'] = false;
                        } else if (data.rows[0].itemList && data.rows[0].itemList > 1) {
                            this.kvALLFields[0][this.kvALLFields[0].field + 'List'] = data.rows[0].itemList;
                        }
                        if (data.rows[0].itemList.length > 0) {
                            data.rows[0].itemList.forEach((item)=> {
                                if (item.acctRank === '空值') {
                                    this.kvALLFields[1]['showFlag'] = false;
                                }
                                this.kvALLFields[2]['showFlag'] = true;

                                if(orgArray.includes(this.userInfo.orgId)) {
                                    this.kvALLFields[2][this.kvALLFields[2].field + 'List'] = item.itemList.filter(item => item.socialCircle !== 'PartyGovernment' && item.socialCircle!== 'Military');
                                } else {
                                    this.kvALLFields[2][this.kvALLFields[2].field + 'List'] = item.itemList;
                                }
                            })
                        }
                    }
                    this.$store.commit('consumerLabel/setKvALLFields', this.kvALLFields);
                }

                if (cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                    const newKData = await this.$http.post(this.$env.appURL + '/link/mapConType/queryNewTypeCache', {
                        companyId: this.userInfo.coreOrganizationTile['l3Id']
                    });
                    if (newKData.success) {
                        this.allConTypeList = newKData.rows[0];
                        // 新K映射表如果取不到数据，会员类型就不会展示，需要取新K映射表配置
                        if (newKData.rows.length > 0) {
                            this.newKFields[0]['showFlag'] = true;
                            // 将 rows 中 field 不为空值的对象赋值给 list
                            this.newKFields[0][this.newKFields[0].field + 'List'] = newKData.rows.filter(item => item[this.newKFields[0].field] !== '空值');
                        }
                        this.$store.commit('consumerLabel/setNewKFields', this.newKFields);
                    }
                }
            },
            /**
             * @desc 筛选数据
             * <AUTHOR>
             * @date 2022/6/1 10:19
             **/
            switchTabItem(item) {
                this.classifyItemListActive = item;
                let filtersRaw = this.accountOption.option.param.filtersRaw;
                if (this.classifyListActive.field === 'subAcctType') {
                    if (filtersRaw.length === 0) {
                        filtersRaw.push({id: 'subAcctType', property: 'subAcctType', value: item.type, operator: '='})
                    } else {
                        if (!filtersRaw.some((item1) => {
                            return item1.property === 'subAcctType'
                        })) {
                            filtersRaw.push({id: 'subAcctType', property: 'subAcctType', value: item.type, operator: '='})
                        }
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'subAcctType') {
                                filtersRaw[i].value = item.type;
                                filtersRaw[i].operator = '=';
                            }
                            if (filtersRaw[i].property === 'loyaltyLevel') {
                                filtersRaw.splice(i, 1);
                            }
                        }
                    }
                } else if (this.classifyListActive.field === 'loyaltyLevel') {
                    if (!filtersRaw.some((item1) => {
                        return item1.property === 'loyaltyLevel'
                    })) {
                        filtersRaw.push({id: 'loyaltyLevel', property: 'loyaltyLevel', value: item.val, operator: '='})
                    } else {
                        for (let i = 0; i < filtersRaw.length; i++) {
                            if (filtersRaw[i].property === 'loyaltyLevel') {
                                filtersRaw[i].value = item.val;
                                filtersRaw[i].operator = '=';
                                break;
                            }
                        }
                    }
                }
                this.accountOption.list = [];
                this.accountOption.option.param.filtersRaw = filtersRaw;
                this.accountOption.methods.reload();
            },
            /**
             * @desc 查询类型数据
             * <AUTHOR>
             * @date 2022/6/1 15:27
             **/
            async queryTypeList() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                    oauth: 'ALL',
                    pageFlag: true,
                    rows: 500,
                    page: 1,
                    distinctFields: 'type',
                    filtersRaw: [
                        {
                            id: 'companyId',
                            property: 'companyId',
                            value: this.userInfo.coreOrganizationTile['l3Id'],
                            operator: '='
                        },
                        {id: 'status', property: 'status', value: 'Active', operator: '='}]
                });
                if (data.success) {
                    for (const item of data.rows) {
                        let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                        this.$set(item, 'name', name);
                    }
                    data.rows.forEach((item, index) => {
                        this.$set(item, 'seq', index + 1)
                    });
                    this.classifyItemList = data.rows;
                }
            },
            /**
             * @desc 级联分类分级
             * <AUTHOR>
             * @date 2022/6/1 10:14
             **/
            async switchTab(item) {
                this.classifyListActive = item;
                if (this.option.formData.preType === 'HighValuePre' && !this.filtersRawTemp.some(item => item.property === 'identityId')) {
                    this.filtersRawTemp = this.filtersRawTemp.concat([{id: 'identityId', property: 'identityId', operator: 'not null', value: ''}]);
                }
                if (item.val === 'ACCT_MEMBER_LEVEL') {
                    const lovData = await this.$lov.getLovByType(item.val);
                    if (this.isGuoJiao) {
                        this.classifyItemList = await this.$lov.getLovByParentTypeAndValue({
                            type: item.val,
                            parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                            parentVal: this.userInfo.coreOrganizationTile['l3Id']
                        });
                    } else {
                        this.classifyItemList = lovData.filter(item => !item.parentId);
                    }
                    this.classifyItemListActive = {};
                    this.accountOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                    this.accountOption.methods.reload();
                } else if (item.val === 'subAcctType') {
                    await this.queryTypeList();
                    this.accountOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                    this.accountOption.methods.reload();
                } else if (item.val === 'IMP_FLAG') {
                    this.classifyItemList = [];
                    this.classifyItemListActive = {};
                    this.accountOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp).concat([{
                        id: 'impFlag',
                        property: 'impFlag',
                        value: 'Y',
                        operator: '='
                    }]);
                    this.accountOption.methods.reload();
                } else {
                    this.classifyItemList = [];
                    this.classifyItemListActive = {};
                    this.accountOption.option.param.filtersRaw = this.$utils.deepcopy(this.filtersRawTemp);
                    this.accountOption.methods.reload();
                }
            },
            /**
             * @desc 查询参数配置
             * <AUTHOR>
             * @date 2022/6/7 17:21
             **/
            async queryCfg() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'GUOJIAO_ACCT_MEMBER_LEVEL'});
                if (data.success) {
                    const companyIds = data.value.split(',');
                    this.isGuoJiao = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1
                }
            },
            /**
             * @desc 查询标签组信息
             * <AUTHOR>
             * @date 2022/8/22 10:51
             **/
            async queryTagGroups() {
                const data = await this.$http.post(this.$env.dmpURL + '/link/portalAccntTagGroup/queryTagGroups', {
                    validFlag: 'Y',
                    queryItemFlag: 'Y',         // 查询标签值
                    labelType: 'CustomLabel',
                    labelRange: 'GroupAndBrandLabel',
                    companyId: this.userInfo.coreOrganizationTile['l3Id']
                });
                if (data.rows.length > 0) {
                    data.rows.forEach((item) => {
                        item.checked = true;
                        item.showMore = false;
                        item.editFlag = false;
                        item.selectAllFlag = false;
                        if (!this.$utils.isUndefined(this.tagLabels)) {
                            item.editFlag = true;
                            for (const tagPart of this.labelField) {
                                if(tagPart.id === 'tag' + item.id) item.required = tagPart.required;
                            }
                            if (item.tagItemList.length > 0) {
                                item.tagItemList.forEach((tag) => {
                                    tag.checked = false;
                                    delete tag.id;
                                });
                            }
                            this.tagLabels.forEach((group) => {
                                group.itemTags && group.itemTags.forEach((tag) => {
                                    const flag = item.tagItemList.find((label) => label.tagId === tag.tagId);
                                    if(flag) {
                                        flag.checked = true;
                                        flag.id = tag.id;
                                        flag.ifActive = tag.ifActive;
                                    }
                                })
                            });
                        } else {
                            if (item.tagItemList.length > 0) {
                                item.tagItemList.forEach((tag) => {
                                    tag.checked = false;
                                });
                            }
                        }
                    });
                    this.tagGroupList = data.rows;
                    this.$store.commit('consumerLabel/setTagGroupList', data.rows);
                }
            },
            /**
             * @desc 查询变更记录
             * <AUTHOR>
             * @date 2022/11/18 10:43
             **/
            async queryChangeFieldList(accountItemId) {
                if (this.$utils.isEmpty(accountItemId)) {
                    return;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send',
                    {
                        dmpUrl: '/link/fieldTemApp/queryByExamplePage',
                        pageFlag: true,
                        rows: 50,
                        page: 1,
                        order: 'desc',
                        sort: 'created',
                        filtersRaw: [
                            {id: ' businessId', operator: '=', property: ' businessId', value: accountItemId},
                            {id: ' appStatus', operator: '=', property: ' appStatus', value: 'Reviewing'},
                            {id: 'applyType', property: 'applyType', value: 'DigitalCertify', operator: '<>'}
                        ]
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`查询变更记录失败：${response.result}`);
                        }
                    });
                let changeField = []
                data.rows.forEach(item=> {
                    if(item.businessData) {
                        changeField = changeField.concat(JSON.parse(item.businessData))
                    }
                })
                this.changeFieldList = changeField;
            },
            /**
             * @desc 选择所属门店信息
             * <AUTHOR>
             * @date 2022/7/5 09:33
             **/
            async chooseStoreData () {
                const data = await this.chooseStoreList();
                if (data) {
                    this.isStoreChosen = true;
                    this.accountOption.option.param['belongToStoreIdList'] = data;
                    this.accountOption.methods.reload();
                } else {
                    this.isStoreChosen = false;
                    delete this.accountOption.option.param['belongToStoreIdList'];
                    this.accountOption.methods.reload();
                }
            },
            /**
             * @desc 跳转变更详情
             * <AUTHOR>
             * @date 2022/11/9 16:08
             **/
            gotoChangeApplyItem(item) {
                this.$nav.push('/pages/lj-consumers/account/account-change-apply-page', {
                    data: item,
                    pageFrom: 'AccountItem'
                })
            },
            /**
             *  前往拜访
             *  <AUTHOR>
             *  @date        2020-06-23
             */
            gotoVisitRegisterItem(item) {
                item.row_status = 'UPDATE';
                this.$nav.push('/pages/lj-consumers/visit-register/visit-register-item-page', {
                    data: item
                })
            },
            /**
             * @createdBy 曾宇
             * @date 2022/7/12
             * @methods: gotoPresentItem
             * @description: 礼赠详情
             **/
            gotoPresentItem(item) {
                this.$nav.push('/pages/lj-consumers/visit-present/visit-present-item-page', {module: this.$env.appURL + "/action/link/present", data: item, operator: 'READ'})
            },
            /**
             *  进入动销详情
             *  <AUTHOR>
             *  @date        2020-06-23
             */
            gotoOrderItem(item) {
                let type = item.orderChildType === 'InvoiceBased' ? 'bill' : '';
                this.$nav.push('/pages/lj-consumers/booked-order/booked-order-item-page', {
                    data: item,
                    type: type
                })
            },
            /**
             * @desc 获取模板数据，解析字段配置信息
             * <AUTHOR>
             * @date 2022/11/9 09:50
             **/
            async checkFieldsInfo (type = '', orgId) {
                let jsonData = {};
                if (this.$taro.getStorageSync('template')) {
                    jsonData = this.$taro.getStorageSync('template');
                } else {
                    const data = await this.$http.post(this.$env.appURL + '/link/fieldTemplate/queryJsonFileUrl', {
                        applicationType: 'CsmType'
                    });
                    jsonData = await this.$http.get(data.result);
                    this.$taro.setStorageSync('template', jsonData);
                }
                this.dealTemplateData(type, jsonData);
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/06/27
             * @methods: dealTemplateData
             * @para:
             * @description: 解析模板数据
             **/
            async dealTemplateData (type, jsonData) {
                this.tempList = [];
                if (jsonData.success) {
                    this.templateId = jsonData.result.id;
                    if (jsonData.result.insertApproval) {
                        this.insertApproval = jsonData.result.insertApproval;
                    }
                    const fieldData = JSON.parse(jsonData.result.canvasCfg);
                    const standarFields = fieldData.filter((item) => item.fieldType === 'StandarField');
                    let tempStandDataList = [];
                    let tempCustDataList = [];
                    if (standarFields.length > 0) {
                        if (type === 'edit') {
                            fieldData.filter((item) => item.fieldType === 'StandarField')[0]['widgets'].forEach((fieldItem) => {
                                if (this.changeFieldList.find((changeItem) => fieldItem['field'] === changeItem.field)) {
                                    fieldItem.readonly = true
                                    fieldItem.note = '该字段审批中，不可编辑';
                                }
                            })
                        }
                        this.standarFields = fieldData.filter((item) => item.fieldType === 'StandarField')[0]['widgets'];
                        console.log(type,'type consumer_display_control    ',this.consumer_display_control);
                        if(this.consumer_display_control == "Y"&&(!type||type=='view')){ //是否控制 'companyName','position','detailedAddress'展示与否
                           const exArr= ['companyName','position','detailedAddress']
                           this.standarFields = this.standarFields.filter(e=>!exArr.includes(e.field));
                        }else{}
                         // 查询企业参数配置-参数键New_Type_Company
                        const obj = await this.$utils.getCfgProperty('New_Type_Company');
                        const cfgArray = obj.split(',')
                        this.standarFields.forEach((item, index)=> {
                            if (type === 'view' && item.field === 'certify') {
                                item.hide = 'Y';

                            }
                            if (item.field === 'identityLevel') {
                                item.hide = 'Y';
                            }
                            if (item.field === 'fromType') {
                                item.hide = 'Y';
                            }
                            this.copyFormData[item.field] = null;
                            if(cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                                this.kvALLFields.forEach(itm => {
                                    // 新增的情况将 kvALLFields 中所有字段设置为隐藏
                                    if(this.pageFrom ==='Account') {
                                        this.$nextTick(() => {
                                            itm.showFlag = false;
                                        })
                                    }

                                    if(this.pageFrom ==='IneffectiveAccount') {
                                        if (!this.formData[itm.field]) {
                                            this.$nextTick(() => {
                                                itm.showFlag = false;
                                            })
                                        }
                                    }

                                    if(this.pageFrom ==='accountItem' && type === 'edit') {
                                        if (item.field === itm.field && !this.formData[itm.field]) {
                                            this.$nextTick(() => {
                                                itm.showFlag = false;
                                            })
                                        }
                                    }

                                    if(type === 'view') {
                                       // 判断 accountItem 中是否有 itm.field 字段，没有则将 itm.showFlag 设置为 false
                                        if(!this.accountItem[itm.field]) {
                                            this.$nextTick(() => {
                                                itm.showFlag = false;
                                            })
                                        }
                                    }
   // 名单提报添加也只读                // 名单提报也置灰
                                    // 编辑的情况将 kvALLFields 中填写的字段设置为只读
                                    if (type === 'edit'||this.pageFrom =='ListReport') {
                                        // console.log('edit类型', this.formData);
                                        if(this.formData[itm.field]) {
                                            this.$nextTick(() => {
                                                itm.showFlag = true;
                                            })
                                        }
                                        if (item.field === itm.field) {
                                            item.readonly = true;
                                        }
                                    }
                                });

                                this.newKFields.forEach(itm => {
                                    switch (this.pageFrom) {
                                        case 'accountItem':
                                            if (type === 'edit') {
                                                itm.showFlag = false;
                                                if(this.formData[itm.field]) {
                                                    itm.showFlag = true;
                                                }
                                                if(!this.formData.memberType) {
                                                    itm.showFlag = true;
                                                }
                                            }
                                            break;
                                        case 'IneffectiveAccount':
                                            // console.log('ineffectiveAccount', this.formData);
                                            // console.log('ineffectiveAccount--进来了000', itm.field);
                                            // if(!this.formData[itm.field]) {
                                                itm.showFlag = true;
                                            // }
                                            break;
                                    }
                                })

                                if(this.pageFrom ==='Account' && type !== 'edit') {
                                    if (item.field === 'type') {
                                        item.hide = 'Y';
                                    }
                                }

                                for (let j = 0; j < this.newKFields.length; j++) {
                                    if (item.field === this.newKFields[j].field) {
                                        item.showFlag = this.newKFields[j].showFlag;
                                        item.type = this.newKFields[j].type;
                                        item['key'] = j + 1;
                                    }
                                }
                            }
                            for (let i = 0; i < this.kvALLFields.length; i++) {
                                if (item.field === this.kvALLFields[i].field) {
                                    item.showFlag = this.kvALLFields[i].showFlag;
                                    item.type = this.kvALLFields[i].type;
                                    item['key'] = i + 1;
                                }
                            }

                            tempStandDataList.push({
                                hide: item.hide,
                                field: item.field,
                                oldValue:"空值",
                                label: item.label,
                                type: 'StandarField',
                                value: null
                            })
                        });
                        this.copyFormData = Object.assign({}, this.copyFormData, this.newAccountItem);
                    }
                    const labelField = fieldData.filter((item) => item.fieldType === 'LabelField');
                    let tempLabelFieldData = {}
                    if (labelField.length > 0) {
                        this.labelField = fieldData.filter((item) => item.fieldType === 'LabelField')[0]['widgets'];
                        tempLabelFieldData = {
                            field: 'consumerTagsList',
                            oldValue:"空值",
                            label: '消费者标签',
                            type: 'LabelField',
                            value: null
                        }
                    }
                    const customListData = fieldData.filter((item) => item.fieldType === 'CustomField');
                    if (customListData.length > 0) {
                        customListData.forEach((custItem)=> {
                            const configListData = custItem['widgets'];
                            let componentList = []
                            configListData.forEach((item,index) => {
                                let component = this.$utils.deepcopy(this.componentsMap[item.type].component);
                                component.values['placeholder'] = item.placeholder;
                                if (item.type === 'Photo') {
                                    component.values['maxUploadNum'] = item.maxUploadNum;
                                }
                                // 是否必输
                                component.values['required'] = item.required === 'Y';
                                component.values['fieldName'] = item.label;
                                if (item.regularCheck) {
                                    component.values['regCheck'] = item.regularCheck;
                                }
                                component.type = item.type;
                                component.hide = item.hide;
                                // Radio
                                if (item.type === 'Radio') {
                                    component.values['complexSelectData'] = item.choices;
                                }
                                if (item.type === 'Checkbox') {
                                    component.values['complexSelectData'] = item.choices;
                                }
                                if (type === 'view') { // 详情
                                    component.values['readonly'] = true;
                                }
                                // 对应字段
                                component.values['field'] = item.field;
                                if (type === 'edit') {
                                    if (this.changeFieldList.find((changeItem) => item.field === changeItem.field)) {
                                        component.values['readonly'] = true;
                                        component.values['note'] = '该字段审批中，不可编辑';
                                    }
                                }
                                if (component.type === 'ProvincialCity') {
                                    this.$set(this.customFieldsData, item.field, {province: '', city: '', district: '', street: ''});
                                } else {
                                    this.$set(this.customFieldsData, item.field, null);
                                }
                                // @ts-ignore
                                componentList.push(component);
                                tempCustDataList.push({
                                    hide: item.hide,
                                    field: item.field,
                                    oldValue: '空值',
                                    label: item.label,
                                    type: 'CustomField',
                                    value: null
                                })
                            });
                            this.$set(custItem, 'componentList',componentList);
                        })
                    }
                    this.copyCustomFieldsData = this.$utils.deepcopy(this.customFieldsData);
                    this.tempList = tempStandDataList.concat(tempCustDataList);
                    this.tempList.push(tempLabelFieldData);
                    this.customFields = customListData;
                }
            },
            /**
             * @desc 选择所属客户数据
             * <AUTHOR>
             * @date 2022/5/27 11:21
             **/
            async chooseStoreList (pageOauth) {
                // console.log('pageOauth', pageOauth)
                this.customerOptionOauth = pageOauth;
                this.isStoreChosen = !this.isStoreChosen
                if (this.isStoreChosen) {
                    const list = await this.$object(this.customerOption, {
                        pageTitle: '请选择所属客户',
                        showInDialog: true,
                        multiple: false
                    });
                    return [list.id];
                } else {
                    return false
                }
            },
             /**
              * @createdBy 黄鹏
              * @date 2024/06/27
              * @methods: getTemplateData
              * @para:
              * @description: 进入消费者列表页面请求消费者模板数据存入缓存
              **/
             async getTemplateData () {
                 this.$taro.setStorageSync('template', null);  // 清空缓存的模板数据
                 const data = await this.$http.post(this.$env.appURL + '/link/fieldTemplate/queryJsonFileUrl', {
                     applicationType: 'CsmType'
                 });
                 const jsonData = await this.$http.get(data.result);
                 this.$taro.setStorageSync('template', jsonData);
             }
        }
    }
}

