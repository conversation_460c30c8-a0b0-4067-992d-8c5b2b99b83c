<!--
消费者排期日历
@date 2025-02-12
@file consumer-schedule-calendar-page.vue
-->
<template>
    <link-page class="consumer-schedule-calendar-page">
        <link-auto-list :option="autoList" :searchInputBinding="{props:{placeholder:'搜索'}}">
            <!--搜索框-->
            <view slot="searchRight" class="oauth-selector"
                  @tap="chooseOauthData">
                {{ pageOauthName }}
                <link-icon icon="mp-desc" style="color: #CCCCCC; margin: 4rpx 0 0 8rpx;"/>
            </view>
            <!--状态筛选-->
            <view slot="filterGroup" class="filter-group">
                <scroll-view scroll-x="true" enable-flex="true" class="top-filter-content">
                    <view :class="['filter-item', { active: keyPersonIsCheck}]" v-if="['MY_ORG', 'MY_POSTN'].includes(pageOauth)" @tap="scheduleFilter('keyPerson')">执行人/负责人<link-icon icon="mp-desc"/></view>
                    <view :class="['filter-item', { active: planDeStatusIsCheck}]" @tap="scheduleFilter('planDeStatus')">排期计划状态<link-icon icon="mp-desc"/></view>
                    <view :class="['filter-item', { active: orgIsCheck}]" v-if="pageOauth === 'MY_ORG'" @tap="scheduleFilter('org')">所属组织<link-icon icon="mp-desc"/></view>
                </scroll-view>
            </view>
            <view slot="top" class="auto-list-top">
                <Calendar :events="events" :startDate="startDate" :endDate="endDate" @dateRangeSelect="dateRangeSelect" @changeYearMonth="changeYearMonth"></Calendar>
                <view class="schedule-total">{{ `排期总计：${scheduleTotal}` }}</view>
            </view>
            <template slot-scope="{data, index}">
                <link-swipe-action>
                    <view class="account-list-item">
                        <view class="tag" v-if="data.chargePersonName">
                            <view class="tag-item">{{ data.chargePersonName }}</view>
                        </view>
                        <view class="title">
                            <scroll-view class="consumer-name-container" enable-flex scroll-x="true">
                                <view class="consumer-name">{{ data.acctName }}</view>
                            </scroll-view>
                            <view class="consumer-status">
                                <image v-if="data.addCertify === 'certified'" :src="$imageAssets.storeStatusVerifiedImage"></image>
                                <image v-else :src="$imageAssets.storeStatusUnverifiedImage"></image>
                            </view>
                            <view class="consumer-tag">{{ data.addType | lov('ACCT_SUB_TYPE') }}</view>
                            <view class="consumer-tag">{{ data.addLoyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                        </view>
                        <view class="belong-to-store" v-if="data.belongToStore">
                            <view class="store-title">所属客户</view>
                            <view class="store-name">{{data.belongToStore}}</view>
                        </view>
                        <view class="consumer-schedule-plan-box" v-for="(item, index) in displayConsumerSchedulePlan(data)" :key="index">
                            <view class="consumer-schedule-plan-time">{{ $date.format(item.beginTime, 'MM-DD') }}</view>
                            <view :class="['consumer-schedule-plan-cell', {'slanted-stripes': item.isStored === 'Y'}]" @tap="gotoDetail(item)">
                                <view class="cell-title">{{ item.planName }}</view>
                                <view class="cell-value" v-if="item.isStored === 'N' || $utils.isEmpty(item.isStored)"
                                      :style="getColorByStatus(item.planStatus, item.planDeStatus, 'ALL')">
                                    {{ item.planDeStatus | lov('SCHE_PLAN_STATUS') }}</view>
                                <view class="cell-value" v-else>
                                    <link-icon v-if="item.planDeStatus === 'New'" icon="icon-xinjian"/>
                                    <link-icon v-else-if="item.planDeStatus === 'Invalid'" icon="icon-liuchengzuofei"/>
                                    <link-icon v-else icon="icon-bianji1"/>
                                    {{ handleSpecialApprovalStatus(item.planDeStatus) }}
                                </view>
                                <link-icon class="icon-xiangyou" icon="icon-xiangyou"/>
                            </view>
                        </view>
                        <view class="all" v-if="data.plansData.length > 3 && !data.displayAll" @tap="showAll(data)">全部排期<link-icon icon="icon-pullDown"/></view>
                    </view>
                    <link-swipe-option color="primary" slot="option" @tap="addSchedule(data.plansData[0])"
                                       v-if="pageOauth === 'MY_POSTN_ONLY'
                                       && !['End', 'New'].includes(data.scheInfoStatus)
                                       && (nowTime >= data.conStartTime && nowTime <= data.conEndTime)">
                        新建计划</link-swipe-option>
                    <!--提交审批-仅我的数据展示-->
                    <link-swipe-option color="success" v-if="pageOauth === 'MY_POSTN_ONLY'
                                                            && !['End', 'New'].includes(data.scheInfoStatus)
                                                            && (nowTime >= data.conStartTime && nowTime <= data.conEndTime)"
                                       slot="option" @tap="submitApproval(data.plansData[0])">提交审批
                    </link-swipe-option>
                </link-swipe-action>
            </template>
        </link-auto-list>
        <org-select :user-info="userInfo" :show.sync="dialogFlag" @choose="changeOrg"></org-select>
    </link-page>
</template>

<script>

import {$logger} from "@/utils/log/$logger"
import {ComponentUtils} from "link-taro-component";
import {getColorByStatus} from '../consumers-schedule/components/schedule-common'
import Calendar from "./components/calendar.vue";
import {$utils} from "../../../utils/$utils";
import OrgSelect from "../../echart/lzlj/components/org-select.vue";
export default {
    name: 'consumer-schedule-calendar-page',
    components: {OrgSelect, Calendar},
    data() {
        return {
            dialogFlag: false, // 是否展示组织筛选弹窗
            events: [],// 需要展示小蓝点的数组
            userInfo: this.$taro.getStorageSync('token').result,
            selectedVals: [],// 排期计划状态筛选选中的值
            displayAll: false,// 是否展示全部排期，默认展示3条
            scheduleTotal: 0,// 排期统计
            startDate: '',// 日历的开始时间
            endDate: '',// 日历的结束时间
            pageOauthList: this.pageParam.secMenus,
            pageOauth: '', // 列表安全性
            pageOauthName: '', // 页面安全性名称
            // 执行人/负责人
            postnOption: new this.AutoList(this, {
                module: this.$env.appURL + '/link/position',
                sortOptions: null,
                param: {
                    oauth: '',
                    filtersRaw: [
                        {"id": "isEffective", "property": "isEffective", "value": "Y"}
                    ]
                },
                searchFields: ['fstName'],
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data}>
                            <view
                                style="justify-content: space-between;display: -webkit-box;display: -ms-flexbox;display: flex;padding: 12px 8px 12px 12px;width:100%">
                                <view
                                    style="flex: 1;font-family: PingFangSC-Regular;font-size: 14px;color: #262626;letter-spacing: 0;line-height: 14px;">
                                    {data.fstName}
                                </view>
                                <view
                                    style="font-family: PingFangSC-Regular;font-size: 11px;">
                                    {data.postnName}
                                </view>
                            </view>
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                        </item>
                    )
                }
            }),
            // 查询排期计划
            autoList: new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/consumer',
                url: {
                    queryByExamplePage: this.$env.appURL + '/action/link/sendDmp/send'
                },
                param: {
                    dmpUrl: '/link/consumerScheduleCalendar/queryConsumerSchePlanByDate',
                    oauth: 'MY_POSTN_ONLY',
                    beginTime: this.getFirstAndLastDayOfMonth().firstDay,
                    endTime: this.getFirstAndLastDayOfMonth().lastDay,
                    isStored: 'Y',
                    planstatus: [],
                    filtersRaw: []
                },
                pageSize: 9999,
                enableSortField: false,
                filterOption: [
                    {label: '排期时K序列', field: 'addType', type: 'select', data: []},
                    {label: '排期时V序列', field: 'addLoyaltyLevel', type: 'select', data: []},
                    {label: '认证状态', field: 'addCertify', type: 'lov', lov: 'CERTIFY'},
                    {label: '动作大类', field: 'planScheType', type: 'lov', lov: 'SCHE_TYPE',
                        lovOption: {parentType: 'SCHE_RULE_TYPE', parentVal: 'ScheAction'}},
                    {label: '排期消费者状态', field: 'scheInfoStatus', type: 'lov', lov: 'TARGET_ACCT_STATUS', multiple: true}
                ],
                loadOnStart: false,
                disabled:{
                    // 不展示新建
                    creatable: (() => {
                        return false;
                    })
                },
                exactSearchFields: [
                    {
                        field: 'acctName',
                        showValue: '消费者姓名',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: true
                    },
                    {
                        field: 'belongToStore',
                        showValue: '主要所属客户名称',
                        searchOnChange: true,
                        clearOnChange: true,
                        exactSearch: false
                    }
                ],
                sortOptions: null,
                hooks: {
                    async beforeCreateItem(param) {},
                    async beforeLoad(option) {},
                    async afterLoad(data) {
                        data.rows.forEach((item) => {
                            if (item.plansData.length > 3) {
                                item.displayAll = false
                            }
                        })
                        this.scheduleTotal = data.timeRangeToatal;
                    }
                },
                slots: {}
            }),
            caheData: {},   // 审批排期数据
            headData: {},   // 大排期数据
            nowTime: '' // 当前时间
        }
    },
    computed: {
        // 执行人/负责人是否属于选中状态
        keyPersonIsCheck () {
            const schePostnIdValue = this.autoList.option.param.filtersRaw.find(item => item.property === 'schePostnId');
            return schePostnIdValue && !$utils.isEmpty(schePostnIdValue.value)
        },
        // 排期计划状态是否在选中状态
        planDeStatusIsCheck () {
            return !$utils.isEmpty(this.selectedVals)
        },
        // 组织是否在选中状态
        orgIsCheck () {
            const orgValue = this.autoList.option.param.filtersRaw.find(item => item.property === 'orgId');
            return orgValue && !$utils.isEmpty(orgValue.value)
        }
    },
    async created() {
        this.pageOauth = this.pageOauthList[0].securityMode;
        this.pageOauthName = this.pageOauthList[0].name;
        this.autoList.option.param.oauth = this.pageOauth;
        await this.filterKVSequencesByCompanyId();
        await this.queryPlanStatus();
        await this.autoList.methods.reload();
        this.startDate = this.getFirstAndLastDayOfMonth().firstDay;
        this.endDate = this.getFirstAndLastDayOfMonth().lastDay;
        await this.queryScheduleDate();
        const nowTime = await this.$utils.getServerTime();
        this.nowTime = this.$date.format(new Date(nowTime), 'YYYY-MM-DD HH:mm:ss')
        console.log('当前时间', this.nowTime)
        this.$bus.$on('refreshCalendarList', async (data) => {
            await this.autoList.methods.reload();
            await this.queryScheduleDate();
        });
    },
    methods: {
        /**
         * @Description: 选择组织
         * @Author: 胡益阳
         * @Date: 2025/2/27
        */
        async changeOrg (item) {
            this.autoList.option.param.filtersRaw = this.checkAddOrUpdate(this.autoList.option.param.filtersRaw, 'orgId', item.orgId);
            await this.autoList.methods.reload();
        },
        /**
         * @Description: 查询出所有的排期计划状态-排除新建
         * @Author: 胡益阳
         * @Date: 2025/2/26
        */
        async queryPlanStatus() {
            const schePlanStatus = await this.$lov.getLovByType('SCHE_PLAN_STATUS');
            const data = schePlanStatus.map(item => item.val).filter(item => item !== 'New');
            this.autoList.option.param.planstatus = data;
        },
        /**
         * @Description: 审批状态特殊处理
         * @Author: 胡益阳
         * @Date: 2025/2/25
        */
        handleSpecialApprovalStatus(status) {
            switch (status) {
                case 'New':
                    return '新建'
                case 'Invalid':
                    return '作废'
                default:
                    return '修改'
            }
        },
        /**
         * @Description: 筛选K序列和V序列
         * @Author: 胡益阳
         * @Date: 2025/2/24
        */
        async filterKVSequencesByCompanyId() {
            // V序列
            const acctMemberLevel = await this.$lov.getLovByParentTypeAndValue({
                type: 'ACCT_MEMBER_LEVEL',
                parentType: 'ACCT_MEMBER_LEVEL_COMPANY',
                parentVal: this.userInfo.coreOrganizationTile['l3Id']
            })
            // K序列
            const data = await this.$http.post(this.$env.appURL + '/action/link/mapConType/queryByExamplePage', {
                oauth: 'ALL',
                pageFlag: true,
                rows: 500,
                page: 1,
                distinctFields: 'type',
                filtersRaw: [
                    {
                        id: 'companyId',
                        property: 'companyId',
                        value: this.userInfo.coreOrganizationTile['l3Id'],
                        operator: '='
                    },
                    {id: 'status', property: 'status', value: 'Active', operator: '='}]
            });
            if (data.success) {
                for (const item of data.rows) {
                    let name = await this.$lov.getNameByTypeAndVal('ACCT_SUB_TYPE', item.type)
                    this.$set(item, 'name', name);
                }
                data.rows = data.rows.map((item) => {
                    return {
                        name: item.name,
                        val: item.type
                    }
                })
            }
            this.autoList.option.filterOption.forEach((item) => {
                if (item.field === 'addLoyaltyLevel') {
                    item.data = acctMemberLevel;
                } else if (item.field === 'addType' && data.success) {
                    item.data = data.rows;
                }
            })
        },
        /**
         * @Description: 选择页面安全性
         * @Author: 胡益阳
         * @Date: 2025/2/12
        */
        chooseOauthData() {
            this.$actionSheet(() => (
                <link-action-sheet title="请选择数据范围" onCancel={() => {}}>
                    {this.pageOauthList.map((item) => {return <link-action-sheet-item label={item.name} onTap={() => this.pageOauthChange(item)}/>})}
                </link-action-sheet>
            ));
        },
        /**
         * @Description: 切换页面安全性
         * @Author: 胡益阳
         * @Date: 2025/2/14
        */
        pageOauthChange(oauth) {
            this.$utils.showLoading();
            this.pageOauthName = oauth.name;
            this.pageOauth = oauth.securityMode;
            this.autoList.option.param.oauth = oauth.securityMode;
            this.autoList.methods.reload();
            this.$utils.hideLoading();
        },
        /**
         * @Description: 跳转到查看页面
         * @Author: 何春霞
         * @Date: 2025-02-18
         */
        async gotoDetail(data) {
            await this.querySchedule(data.scheduleId);
            await this.queryConsumer(data.scheInfoId);
            // 消费者排期日历详情埋点：事件编码、排期id、排期消费者id（row_Id）、消费者id、动作计划id、菜单id
            try {
                $logger.info('CONSUMER_SCHEDULE_CALENDAR_002', 'Click', `菜单id：consumer_schedule_calendar_排期id：${data.scheduleId}_排期消费者行id：${data.scheInfoId}_消费者id：${this.scheduleItem.acctId}_动作计划id：${data.id}`);
            } catch (e) {
                console.log('e', e)
                $logger.error('CONSUMER_SCHEDULE_CALENDAR_002', 'Click', `菜单id：consumer_schedule_calendar_排期id：${data.scheduleId}_排期消费者行id：${data.scheInfoId}_消费者id：${this.scheduleItem.acctId}_动作计划id：${data.id}_报错信息：${e}`);
            }
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-work-summary-detail-page', {
                oauth: this.pageOauth,
                isAction: true,
                headData: this.headData,
                scheduleItem: this.scheduleItem,
                acctId: data.acctId,    //可以忽略
                scheduleId: data.scheduleId,
                conStartTime: data.conStartTime,
                conEndTime: data.conEndTime,
                rowId: data.id, // 排期计划的id
                pageFrom: 'calendar'
            })
        },
        /**
         * @Description: 新建排期
         * @Author: 何春霞
         * @Date: 2025-02-18
         */
        async addSchedule(data) {
            await this.querySchedule(data.scheduleId);
            await this.queryConsumer(data.scheInfoId);
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-edit-page', {
                editFlag: false,  // 新增
                oauth: this.pageOauth,
                isAction: true,
                scheduleItem: this.scheduleItem,
                headData: this.headData,
                acctId: data.acctId,
                scheduleId: data.scheduleId,
                conStartTime: data.conStartTime,
                conEndTime: data.conEndTime,
                pageFrom: 'calendar'
            })
        },
        /**
         * @desc 查询消费者信息
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async queryConsumer(id) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleTarget/queryById',
                    id: id
                });
                if(data.success) {
                    this.scheduleItem = data.result;
                    console.log('查询消费者信息scheduleItem', this.scheduleItem)
                } else {
                    this.$message.warn('查询消费者信息异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询消费者信息异常，请联系管理员', e);
            }
        },
        /**
         * @desc 查询运营详情-大排期
         * <AUTHOR>
         * @date 2024-07-09
         **/
        async querySchedule(scheduleId) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerSchedule/queryById',
                    id: scheduleId
                });
                if(data.success) {
                    this.headData = data.result;
                    console.log('查询大排期this.headData', this.headData)
                } else {
                    this.$message.warn('查询运营详情异常，请联系管理员', data.message);
                }
            } catch (e) {
                this.$message.error('查询运营详情异常，请联系管理员', e);
            }
        },
        /**
         * @Description: 提交审批排期
         * @Author: 何春霞
         * @Date: 2025-02-18
         */
        async submitApproval(data) {
            this.$dialog({
                title: '提示',
                content: '确定将待提交的排期计划提交审批？',
                confirmText: '确定',
                onConfirm: async () => {
                    this.caheData = data;
                    // await this.confirmSubmit(data);
                    await this.v3confirmSubmit();
                },
                cancelButton: true
            })
        },
        async v3confirmSubmit(){
            console.log(this.caheData,'caheData');
            await this.querySchedule(this.caheData.scheduleId);
            const applyType = (this.headData.scheduleCrowd)=='HighValueAcct'?'HighAcctPlan':'ScheAcctPlan'
            const isV3 = await this.isV3(applyType)
           isV3 && this.v3ApprovalPlan(applyType);
           !isV3&& this.confirmSubmit();
        },
          async v3ApprovalPlan(approvalType=''){
               let preViewId = null;
               let postnId = null;
                await this.queryConsumer(this.caheData.scheInfoId);
                 const data = await this.$http.post(this.$env.appURL + '/action/link/approvalRecord/queryByExamplePage',
                    { filtersRaw: [
                        {"id":"id_0_auto","property":"businessId","value":this.scheduleItem.id},
                        {"id":"appCreateStatus","property":"appCreateStatus", "operator":"is null", "value":""},
                        {"id":"applyType","property":"applyType", "value": approvalType}]
                    })
                if (data.success) {
                    if(!data.rows||!data.rows.length)return this.$message.success('没有待提交的排期计划，无需审批！');
                    preViewId = data.rows[0].id;
                    postnId = data.rows[0].postnId;
                } else {
                   return this.$message.error(data.result);
                }
            // const newId = await this.$newId();
            let flowStartPsnId = undefined;
            if(approvalType=='HighAcctPlan'&&postnId){
               flowStartPsnId = await this.getHighPId(postnId)
            }
            this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                submitData: { id: this.scheduleItem.id,flowStartPsnId
                },
                submitUser: this.userInfo,
                flowObjId: preViewId,
                source: 'planSubmit',
                // 审批类型编码
                approvalType
            });
        },
         // 超高的审批要获取参数给到企微审批流
        async getHighPId(postnId){
           const data = await this.$http.post(this.$env.appURL+'/action/link/identityEmp/queryByPostnId',{postnId,"waitstaffType":"BusinessAgent"});
            if(data.success){
                return data.result&& data.result.headUserId
            } else {
                this.$message.error(data.result);
            }
        },
        /**
         * @desc 监听返回
         * <AUTHOR>
         * @date 2022/4/1 11:19
         **/
        async onBack(param) {
            if (param && param.flag === 'flow') {
                // this.$set(this, 'nodeApprovers', param.nodeDtos);
                if(param.source === 'planSubmit') {
                    await this.confirmSubmit(param.nodeDtos, param.preApprovalId);
                }
            }
            await this.autoList.methods.reload();
        },
        /**
         * @desc 判断是否是v3需要审批
         * <AUTHOR>
         * @date 2025-05-13
         **/
        async isV3(applyType='') {
            return new Promise(async (res, rej) => {
                const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                    companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                    applyType
                });
                console.log('result', result)
                if (success && result === 'v3') {
                    res(true);
                } else {
                    res(false);
                }
            });
        },
        /**
         * @Description: 确认-提交审批排期
         * @Author: 何春霞
         * @Date: 2025-02-18
         */
        async confirmSubmit(nodeApprovers,approvalRecordId) {
            try {
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    ...(nodeApprovers&&{nodeApprovers,approvalRecordId}),
                    dmpUrl: '/link/consumerSchedulePlan/addPlanApprove',
                    id: this.caheData.scheInfoId
                });
                if (data.success) {
                    this.$message.success('提交审批成功');
                    await this.autoList.methods.reload();
                    await this.queryScheduleDate();
                } else {
                    this.$message.error('提交审批失败', data.message);
                }
            } catch (e) {
                console.log('提交审批失败', e);
            }
        },
        /**
         * @Description: 查询排期日历
         * @Author: 胡益阳
         * @Date: 2025/2/19
        */
        async queryScheduleDate () {
            try {
                const result = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                    dmpUrl: '/link/consumerScheduleCalendar/queryPlanDateByDate',
                    beginTime: this.getFirstAndLastDayOfMonth(this.autoList.option.param.beginTime).firstDay,
                    endTime: this.getFirstAndLastDayOfMonth(this.autoList.option.param.beginTime).lastDay,
                    oauth: this.pageOauth,
                });
                if (result.success) {
                    this.events = result.data;
                } else {
                    this.$message.error('查询排期日历失败', result.message);
                }
            } catch (err) {
                console.log('查询排期日历失败', err);
            }
        },
        /**
         * @Description: 获取当月第一天和最后一天
         * @Author: 胡益阳
         * @Date: 2025/2/19
        */
        getFirstAndLastDayOfMonth (value) {
            // 获取当前年份和月份
            const today = new Date();
            if (value) {
                const [year, month] = value.split('-');
                const firstDay = new Date(year, parseInt(month) - 1, 1);
                const lastDay = new Date(year, parseInt(month), 0);
                return {
                    firstDay: this.$date.format(firstDay, 'YYYY-MM-DD'),
                    lastDay: this.$date.format(lastDay, 'YYYY-MM-DD')
                }
            }
            const year = today.getFullYear();
            const month = today.getMonth();
            const firstDay = new Date(year, month, 1);
            const lastDay = new Date(year, month + 1, 0)
            return {
                firstDay: this.$date.format(firstDay, 'YYYY-MM-DD'),
                lastDay: this.$date.format(lastDay, 'YYYY-MM-DD')
            }
        },
        /**
         * @Description: 展示全部排期
         * @Author: 胡益阳
         * @Date: 2025/2/19
        */
        showAll (data) {
            data.displayAll = true;
        },
        /**
         * @Description: 控制排期的展示
         * @Author: 胡益阳
         * @Date: 2025/2/19
        */
        displayConsumerSchedulePlan (data) {
            return data.displayAll ? data.plansData : data.plansData.slice(0, 3);
        },
        /**
         * @Description: 筛选排期
         * @Author: 胡益阳
         * @Date: 2025/2/19
        */
        scheduleFilter: ComponentUtils.debounce( async function(e) {
            switch (e) {
                // 筛选执行人/负责人
                case 'keyPerson':
                    if (this.keyPersonIsCheck) {
                        this.autoList.option.param.filtersRaw = this.autoList.option.param.filtersRaw.filter(item => item.property !== 'schePostnId')
                        await this.autoList.methods.reload();
                    } else {
                        const selectedSchePostnId = this.autoList.option.param.filtersRaw.find(item => item.property === 'schePostnId');
                        this.postnOption.option.param.oauth = this.pageOauth;
                        const item = await this.$object(this.postnOption,{
                            showInDialog: true,
                            selected: selectedSchePostnId ? selectedSchePostnId.value : ''
                        });
                        this.autoList.option.param.filtersRaw = this.checkAddOrUpdate(this.autoList.option.param.filtersRaw, 'schePostnId', item.postnId)
                        await this.autoList.methods.reload();
                    }
                    break
                // 筛选排期计划状态
                case 'planDeStatus':
                    if (this.planDeStatusIsCheck) {
                        await this.queryPlanStatus();
                        this.selectedVals = [];
                        this.autoList.option.param.isStored = 'Y';
                        await this.autoList.methods.reload();
                    } else {
                        await this.filterScheduleStatus()
                    }
                    break
                // 筛选所属组织
                case 'org':
                    if (this.orgIsCheck) {
                        this.autoList.option.param.filtersRaw = this.autoList.option.param.filtersRaw.filter(item => item.property !== 'orgId');
                        await this.autoList.methods.reload();
                    } else {
                        this.dialogFlag = !this.dialogFlag;
                    }
                    break
            }
        }, 500),
        /**
         * @Description: 选择时间
         * @Author: 胡益阳
         * @Date: 2025/2/19
         */
        async dateRangeSelect (val) {
            if (val.startDate && val.endDate === '') {
                // 只有开始时间
                this.autoList.option.param.beginTime = val.startDate;
                this.autoList.option.param.endTime = val.startDate;
            } else if (val.startDate === '' && val.endDate) {
                // 只有结束时间
                this.autoList.option.param.beginTime = val.endDate;
                this.autoList.option.param.endTime = val.endDate;
            } else {
                // 开始 + 结束
                this.autoList.option.param.beginTime = val.startDate;
                this.autoList.option.param.endTime = val.endDate;
            }
            await this.autoList.methods.reload();
        },
        async changeYearMonth (val) {
            this.startDate = this.getFirstAndLastDayOfMonth(val).firstDay;
            this.endDate = this.getFirstAndLastDayOfMonth(val).lastDay;
            this.autoList.option.param.beginTime = this.startDate;
            this.autoList.option.param.endTime = this.endDate;
            await this.autoList.methods.reload();
            await this.queryScheduleDate();
        },
        /**
         * @Description: 筛选排期计划状态
         * @Author: 胡益阳
         * @Date: 2025/2/21
        */
        async filterScheduleStatus () {
            const selectData = await this.$lov.getLovByType('SCHE_PLAN_STATUS')
            let list = await this.$select(selectData, {
                multiple: true,
                selected: this.selectedVals,
                renderFunc: (h, {data, index}) => (
                    <item key={index} arrow={false}>
                        <link-checkbox toggleOnClickItem val={data.val} slot="thumb"/>
                        <view slot="title">{data.val === 'New' ? '待审批': data.name}</view>
                    </item>
                )
            })
            this.selectedVals = list;
            if (list.includes('New')) {
                list = list.filter(item => item !== 'New');
                this.autoList.option.param.isStored = 'Y';
            } else {
                this.autoList.option.param.isStored = 'N';
            }
            this.autoList.option.param.planstatus = list;
            this.autoList.methods.reload();
        },
        /**
         * @Description: 判断新增还是修改参数
         * @Author: 胡益阳
         * @Date: 2025/2/21
         * @param filtersRaw 传入的数组
         * @param targetId 传入的字段
         * @param newValue 需要添加或者修改的字段
        */
        checkAddOrUpdate (filtersRaw, targetId, newValue) {
            let found = false;
            // 遍历 filtersRaw 数组
            for (let i = 0; i < filtersRaw.length; i++) {
                const filter = filtersRaw[i];
                // 检查当前元素的 id 是否等于目标 id
                if (filter.id === targetId) {
                    // 如果找到匹配的 id，修改其 value 属性
                    filter.value = newValue;
                    found = true;
                    break;
                }
            }
            // 如果没有找到匹配的 id，新增一个筛选参数
            if (!found) {
                filtersRaw.push({
                    id: targetId,
                    property: targetId,
                    value: newValue
                });
            }
            return filtersRaw;
        },
        getColorByStatus
    }
}
</script>

<style lang="scss">
.consumer-schedule-calendar-page {
    /*deep*/.model-title {
    height: 72rpx !important;
    align-items: center !important;
}
    .oauth-selector {
        max-width: 224rpx;
        min-width: 135rpx;
        height: 72rpx;
        display: flex;
        align-items: center;
        justify-content: flex-end;
        padding-left: 12rpx;
        font-family: PingFangSC-Regular;
        font-size: 26rpx;
        color: #333333;
        line-height: 40rpx;
        font-weight: 400;
    }
    .filter-group {
        margin-top: 24px;
        flex: 1;
        overflow-x: hidden;
        .top-filter-content {
            height: 80px;
            padding: 0 24px;
            width: 100%;
            display: flex;
            white-space: nowrap;
            .active {
                color: #2f69f8 !important;
                .link-icon {
                    color: #2f69f8 !important;
                }
            }
            .filter-item {
                height: 40px;
                margin: 12px 20px;
                font-size: 26px;
                color: #333333;
                line-height: 40px;
                font-weight: 400;
                text-align: center;
                .link-icon {
                    width: 16px;
                    height: 12px;
                    color: #CCCCCC;
                    margin-left: 8px;
                }
            }
        }
    }
    .auto-list-top {
        background-color: #eff2f6;
        .schedule-total {
            padding: 20px 0 16px 24px;
            font-weight: 400;
            font-size: 24px;
            color: #6A6D75;
        }
    }
    .account-list-item {
        position: relative;
        margin: 0 auto 24px;
        overflow: hidden;
        padding: 24px;
        box-sizing: border-box;
        width: 702px;
        background-color: #ffffff;
        border-radius: 24px;
        .tag {
            position: absolute;
            right: 0;
            top: 0;
            width: 168px;
            height: 40px;
            background-color: #2d6cf4;
            border-radius: 6px;
            transform: skew(30deg, 0deg);
            .tag-item {
                height: 100%;
                width: 100%;
                line-height: 40px;
                text-align: center;
                color: #FFFFFF;
                font-size: 24px;
                font-weight: 400;
                transform: skew(-30deg, 0);
            }
        }
        .title {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            margin-top: 2px;
            .consumer-name-container {
                width: 134px;
                display: flex;
                align-items: center;
                .consumer-name {
                    font-size: 32px;
                    font-weight: bold;
                    color: #212223;
                    white-space: nowrap;
                }
            }
            .consumer-status {
                margin-left: 8px;
                width: 90px;
                height: 40px;
                image {
                    width: 100%;
                    height: 100%;
                }
            }
            .consumer-tag {
                min-width: 52px;
                box-sizing: border-box;
                padding: 0 12px;
                height: 36px;
                position: relative;
                margin-left: 16px;
                color: #764412;
                font-size: 22px;
                font-weight: 400;
                line-height: 36px;
                text-align: center;
            }
            .consumer-tag::after {
                content: '';
                position: absolute;
                left: 50%;
                top: 50%;
                width: 100%;
                height: 100%;
                transform: translate(-50%,-50%);
                border: 2px solid rgba(118, 68, 18, 0.8);
                border-radius: 4px;
            }
        }
        .belong-to-store {
            margin: 24px 0px;
            width: 100%;
            display: flex;
            .store-title {
                font-size: 28px;
                color: #999999;
                width: 20%;
            }
            .store-name {
              font-size: 28px;
              color: #333333;
              width: 80%;
              //  超出宽度展示滚动条
              overflow: auto;
              white-space: nowrap;
              flex-shrink: 0;
            }
        }
        .slanted-stripes {
            background-image: linear-gradient(-60deg,#f9f9f9 25%,#f1f1f2 25%,#f1f1f2 50%,#f9f9f9 50%,#f9f9f9 88%,#f1f1f2 75%,#f1f1f2);
            background-size: 100px 100px;
            background-color: #e8e9ea !important;
        }
        .consumer-schedule-plan-box {
            display: flex;
            align-items: center;
            justify-content: space-between;
            .consumer-schedule-plan-time {
                font-size: 23px;
                color: #212223;
                font-weight: 400;
            }
            .consumer-schedule-plan-cell {
                display: flex;
                align-items: center;
                justify-content: space-between;
                position: relative;
                width: 572px;
                height: 68px;
                padding: 0 64px 0 24px;
                box-sizing: border-box;
                border-radius: 34px;
                background-color: #F3F6F8;
                display: flex;
                align-items: center;
                margin-top: 12px;
                .cell-title {
                    width: 346px;
                    color: #333333;
                    font-size: 28px;
                    font-weight: 400;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
                .cell-value {
                    height: 44px;
                    padding: 2px 10px;
                    line-height: 44px;
                    text-align: center;
                    font-weight: 400;
                    font-size: 24px;
                    color: #6A6D75;
                    border-radius: 6px;
                    background-color: #D6D7DA;
                    .link-icon {
                        font-size: 30px;
                        margin: 0;
                    }
                }
                .icon-xiangyou {
                    position: absolute;
                    right: 28px;
                    top: 50%;
                    transform: translateY(-50%);
                    font-size: 20px;
                    color: #BFC1CA;
                }
            }
        }
        .all {
            font-size: 28px;
            color: #999999;
            text-align: center;
            margin-top: 24px;
        }
    }
}
</style>
