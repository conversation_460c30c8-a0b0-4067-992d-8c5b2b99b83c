<template>
    <link-page class="visit-register-item-new">
        <link-form :value="visitRegisterItem" hideEditButton hideSaveButton>
            <link-form-item label="拜访类型" required readonly>
                <link-lov type="VISIT_TYPE" v-model="visitRegisterItem.visitType"></link-lov>
            </link-form-item>
            <link-form-item label="拜访方式" required arrow field="visitWay">
                <link-lov type="CSM_VISIT_WAY" v-model="visitRegisterItem.visitWay" @change="visitWayChange"></link-lov>
            </link-form-item>
            <link-form-item label="拜访人">
                <link-input v-model="visitRegisterItem.visitorName" readonly></link-input>
            </link-form-item>
            <link-form-item label="拜访时间" :required="type !== 'VisitPlan'" v-if="type !== 'VisitPlan'">
                <link-date v-model="visitRegisterItem.visitTime" view="YMDHm" display-format="YYYY-MM-DD HH:mm"
                           value-format="YYYY-MM-DD HH:mm"></link-date>
            </link-form-item>
            <link-form-item label="详细地址" :required="type !== 'VisitPlan' && visitRegisterItem.visitWay !== 'OnlineVisit'" v-if="type !== 'VisitPlan' && visitRegisterItem.visitWay !== 'OnlineVisit'">
                <view class="address-info" @tap="getLocation">
                    <view :class="addressData === '获取定位' ? 'address-tips' : 'address-data'">{{addressData ? addressData : ''}}</view>
                    <link-icon style="color: #2F69F8" icon="icon-location" class="link-location"/>
                </view>
            </link-form-item>
            <link-form-item label="拜访单位" v-if="visitRegisterItem['visitType'] !== 'PublicRelationVisit'">
                <link-input v-model="visitRegisterItem.visitCompany"></link-input>
            </link-form-item>
            <link-form-item label="拜访单位" required
                            v-if="visitRegisterItem['visitType'] === 'PublicRelationVisit'">
                <link-input v-model="visitRegisterItem.visitCompany" readonly placeholder="请选择拜访单位" @tap="gotoSelectCompany"></link-input>
            </link-form-item>
            <link-form-item label="是否申请领导协访" v-if="visitRegisterItem['visitType'] !== 'PublicRelationVisit'">
                <link-switch v-model="visitRegisterItem.isApplyFlag"></link-switch>
            </link-form-item>
            <view class="visit-register-add">
                <!-- 拜访客户列表 -->
                <user-info @initCustInfoListInfo="initCustInfoListInfo"
                           :edit-flag="true"
                           :parentId="visitRegisterItem.id"
                           :userInfoList="visitCustInfoList"></user-info>
            </view>
            <link-form-item label="计划拜访时间" :required="type === 'VisitPlan'" v-if="type === 'VisitPlan'">
                <link-date v-model="visitRegisterItem.oldVisitTime" view="YMDHm" display-format="YYYY-MM-DD HH:mm"
                           value-format="YYYY-MM-DD HH:mm"></link-date>
            </link-form-item>
            <link-form-item label="拜访情况总结" vertical v-if="type !== 'VisitPlan'">
                <link-textarea v-model="visitRegisterItem.visitSummary"
                               :nativeProps="{maxlength:200}"></link-textarea>
            </link-form-item>
            <view v-if="!!visitRegisterItem.id">
                <view class="lnk-form-header-img" v-if="type !== 'VisitPlan'">
                    <view class="title"><text style="color: red;font-size: 18px">*</text>照片</view>
                    <lnk-img-watermark :parentId="visitRegisterItem.id"
                                       moduleType="visitRegister"
                                       :delFlag="true"
                                       :isZlFlag="true"
                                       filePathKey="/zhls/VisitRegister/"
                                       :newFlag="true">
                    </lnk-img-watermark>
                </view>
            </view>
        </link-form>
        <link-sticky>
            <link-button block @tap="save">保存</link-button>
        </link-sticky>
        <link-dialog ref="saveConfirm">
            <view slot="head">
                提示
            </view>
            <view>
                保存之后拜访单位无法修改，是否继续保存?
            </view>
            <link-button slot="foot" @tap="$refs.saveConfirm.hide()">取消</link-button>
            <link-button slot="foot" @tap="saveVisitRegister">保存</link-button>
        </link-dialog>
        <link-dialog ref="locationFailSelectAddress" class="location-select-address" position="poster">
            <view class="address-t">
                <view style="width: 100%;height: 30px;line-height: 30px">
                    <view style="width: 100%;text-align: center;">当前5G网络定位失败请手动选择就近地址</view>
                </view>
                <view class="address-v">
                    <view>
                        <item title="所在地区">
                            <link-address placeholder="请选择所在地区"
                                          :province.sync="selectAddressObj.province"
                                          :city.sync="selectAddressObj.city"
                                          :district.sync="selectAddressObj.district"/>
                        </item>
                    </view>
                    <view class="t-left">
                        <view class="title">详细地址</view>
                        <view class="val">
                            <link-input type="text" v-model="selectAddressObj.addr" placeholder="请填写或选择单位名称"/>
                        </view>
                        <view class="ent-wrap" @tap="searchAddress()">
                            <view class="iconfont icon-sousuo"
                                  style="float: left;line-height: 30px;font-size: 12px;width: 10%"></view>
                            <view style="float: right;font-size: 12px;width: 80%;margin-left: 5px;">智能联想地址</view>
                        </view>
                    </view>
                </view>
                <scroll-view scroll-y="true" class="list-container">
                    <view v-for="(item1,index) in suggestionAddressData" :key="index">
                        <view class="list-item" @tap="selectAddress(item1)">
                            <view class="left-content">
                                <view class="row-1">
                                    <view class="name">{{item1.title}}</view>
                                    <view class="address">{{item1.address}}</view>
                                </view>
                            </view>
                            <view class="right-content">
                                <view v-if="item1._checked">
                                    <link-icon size="1.8em" style="color:#2F69F8;font-size: 16px" icon="icon-check"/>
                                </view>
                            </view>
                        </view>
                    </view>
                </scroll-view>
                <link-button slot="foot" @tap="confirmAddress" block>确定</link-button>
            </view>
        </link-dialog>
    </link-page>
</template>

<script>
    import {PageCacheManager} from "../../../utils/PageCacheManager";
    import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";
    import TitleLine from "../../lzlj-II/fighting-fakes/components/title-line";
    import ConsumerCommon from "../consumer-common";
    import UserInfo from "./components/user-info";

    export default {
        name: "visit-register-item-new-page",
        components: {LnkImgWatermark, TitleLine, UserInfo},
        mixins: [ConsumerCommon()],
        data() {
            let type = '';// 区分是拜访计划还是拜访登记
            if (!this.$utils.isEmpty(this.pageParam.type)) {
                type = this.pageParam.type;
                if (type === 'VisitPlan') {
                    this.$taro.setNavigationBarTitle({title: '新建拜访计划'});
                } else {
                    this.$taro.setNavigationBarTitle({title: '新建拜访登记'});
                }
            }
            const visitRegisterItem = this.pageParam.data;
            // 5G时定位出现问题后，选择省市区县智能联想地址选择获取经纬度
            const selectAddressObj = {
                province:'',
                city:'',
                district:'',
                street: '',
                addr: '',
            };
            const initialData = {
                visitRegisterItem,
                type,
                companyType: '',
                addressData: '获取定位',
                selectAddressObj,
                suggestionAddressData: [],//根据输入智能联想地址列表
                openSettingNum: 1,//授权次数 默认为1 如果没授权的情况下 5G网络 定位问题 第一次先授权只要授过权次数加1 当次数>1 还是拿不到定位就给默认信息吧....
            };
            const cacheData = PageCacheManager.getInitialData({
                ctx: this,
                path: 'lj-consumers/visit-register/visit-register-item-new-page.vue',
                title: '消费者-拜访登记-新建信息',
                initialData
            })
            return {
                ...cacheData,
                coordinate: {},
                visitCustInfoList: []
            };
        },
        methods: {
            /**
             * @desc 拜访方式切换，清空地址字段
             * <AUTHOR>
             * @date 2022/02/01
             **/
            visitWayChange() {
                this.$set(this.visitRegisterItem, 'visitProvince', null);
                this.$set(this.visitRegisterItem, 'visitCity', null);
                this.$set(this.visitRegisterItem, 'visitCounty', null);
                this.$set(this.visitRegisterItem, 'street', null);
                this.$set(this.visitRegisterItem, 'visitLatitude', null);
                this.$set(this.visitRegisterItem, 'visitLongitude', null);
                this.$set(this.visitRegisterItem, 'visitAddress', null);
                this.addressData = '获取定位';
            },
            /**
             *  请求客户信息
             *
             *  <AUTHOR>
             *  @date        2020-07-03
             */
            async initCustInfoListInfo() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/queryByExamplePage',
                    {
                        visitId: this.visitRegisterItem.id,
                        queryProdsFlag: 'Y',
                        order: 'desc',
                        sort: 'created',
                        rows: 25,
                        page: 1
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`查询客户信息失败：${response.result}`);
                        }
                    });

                this.visitCustInfoList = data.rows || [];
            },
            /**
             *  @description: 选择拜访企业或拜访单位
             *  @author: 马晓娟
             *  @date: 2020/11/17 18:17
             */
            async gotoSelectCompany() {
                const that = this;
                await this.$nav.push('/pages/lj-consumers/visit-register/visit-company-select-page', {
                    item: this.visitRegisterItem,
                    callback: async (data)=> {
                        let visitCompany = data['visitCompany'];
                        that.companyType = data['type'];
                        if (visitCompany) {
                            that.visitRegisterItem['visitEntId'] = visitCompany['id'];                  // 企业id
                            if (visitCompany['visitEntId']) {
                                that.visitRegisterItem['visitEntId'] = visitCompany['visitEntId'];
                            }
                            that.visitRegisterItem['visitCompany'] = visitCompany['enterpriseName'] || visitCompany['visitCompany'];   // 企业名称
                            that.visitRegisterItem['website'] = visitCompany['website'];             // 企业网址
                            that.visitRegisterItem['province'] = visitCompany['province'];           // 企业所在省份
                            that.visitRegisterItem['city'] = visitCompany['city'];                   // 企业所在市
                            that.visitRegisterItem['creditNo'] = visitCompany['creditNo'];           // 企业统一信用代码
                            that.visitRegisterItem['visitSubType'] = visitCompany['visitSubType'] || visitCompany['dataType'];        // 拜访小类
                            that.visitRegisterItem['oldVisitTime'] = visitCompany['oldVisitTime'];      // 计划拜访时间
                            if (that.companyType === 'visitCase') {
                                that.visitRegisterItem['attr2'] = visitCompany['id'];                     // 拜访方案id
                                that.visitRegisterItem['visitGift'] = visitCompany['visitGift'];          // 拜访礼赠
                                that.visitRegisterItem['visitLevel'] = visitCompany['visitLevel'];        // 拜访匹配层级
                            }
                            if (that.visitRegisterItem['visitEntId']) {
                                await that.queryDealerInfo(that.visitRegisterItem['visitEntId']);               // 查询经销商信息
                            }
                        }
                    }
                })
            },
            /**
             *  @description: 根据企业ID查询经销商信息
             *  @author: 马晓娟
             *  @date: 2020/8/13 09:18
             *  @param entId 企业ID
             */
            async queryDealerInfo (entId) {
                const data = await this.$http.post(this.$env.appURL + '/action/link/entServices/getEnterpriseServicesByEntId', {
                    id: entId
                });
                if (data.success) {
                    this.visitRegisterItem['dealerName'] = data.result['name'];          // 对接经销商
                    this.visitRegisterItem['phoneNumber'] = data.result['phoneNumber'];        // 经销商联系方式
                } else {
                    await this.$showError(`获取经销商数据失败：${data.result}`);
                }
             },
            /**
             *  @description: 获取定位地址
             *  @author: songyanrong
             *  @date: 2020-10-28
             */
            async getLocation() {
                const that = this;
                that.coordinate = await that.$locations.getCurrentCoordinate();
                // 匹配5G 某些情况下 定位获取不到的问题
                // 手机系统没开定位getLocation:fail auth deny getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF
                // 手机系统定位开了 但是企业微信没有权限 getLocation:fail system permission denied  getLocation:fail:system permission denied
                if(this.coordinate.errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'
                    || this.coordinate.errMsg === 'getLocation:fail system permission denied'
                    || this.coordinate.errMsg === 'getLocation:fail:system permission denied'){
                    let net = "";
                    await Taro.getNetworkType({
                        success (res) {
                            net = res.networkType
                        }
                    });
                    if(net === '5g' && this.openSettingNum > 1){
                        that.$refs.locationFailSelectAddress.show();
                    } else {
                        if (this.$utils.isEmpty(this.coordinate.latitude) && this.$utils.isEmpty(this.coordinate.longitude)) {
                            this.$dialog({
                                title: '提示',
                                content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                                cancelButton: false,
                                confirmText: '去开启',
                                onConfirm: async () => {
                                    let userLocation = await this.$locations.openSetting();
                                    if (userLocation['scope.userLocation']) {
                                        that.coordinate = await that.$locations.getCurrentCoordinate();
                                    }
                                }
                            });
                            this.openSettingNum ++;
                            return
                        }
                    }
                }
                if (!that.$utils.isEmpty(that.coordinate)) {
                    let address = await that.$locations.reverseTMapGeocoder(that.coordinate.latitude, that.coordinate.longitude, '拜访');
                    const addrCode = address['originalData'].result.addressComponent.adcode;
                    const data = await this.$http.post(this.$env.appURL +'/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode: addrCode})
                    if(data.success) {
                        if(data.adcodeFlag){
                            that.visitRegisterItem.visitProvince = data.province;
                            that.visitRegisterItem.visitCity = data.city;
                            that.visitRegisterItem.visitCounty = data.district;
                            that.visitRegisterItem.street = address['originalData'].result.addressComponent.street ? address['originalData'].result.addressComponent.street : '';
                            that.visitRegisterItem.visitLatitude = that.coordinate.latitude;
                            that.visitRegisterItem.visitLongitude = that.coordinate.longitude;
                            that.$set(that.visitRegisterItem, 'visitAddress', address.originalData.result['sematic_description']);
                            that.addressData = that.visitRegisterItem.visitProvince + that.visitRegisterItem.visitCity + that.visitRegisterItem.visitCounty + that.visitRegisterItem.street + that.visitRegisterItem.visitAddress;
                        }else {
                            that.visitRegisterItem.visitProvince = address['originalData'].result.addressComponent.province;
                            that.visitRegisterItem.visitCity = address['originalData'].result.addressComponent.city;
                            that.visitRegisterItem.visitCounty = address['originalData'].result.addressComponent.district;
                            that.visitRegisterItem.street = address['originalData'].result.addressComponent.street ? address['originalData'].result.addressComponent.street : ''
                            that.visitRegisterItem.visitLatitude = that.coordinate.latitude;
                            that.visitRegisterItem.visitLongitude = that.coordinate.longitude;
                            that.$set(that.visitRegisterItem, 'visitAddress', address.originalData.result['sematic_description']);
                            that.addressData = that.visitRegisterItem.visitProvince + that.visitRegisterItem.visitCity + that.visitRegisterItem.visitCounty + that.visitRegisterItem.street + that.visitRegisterItem.visitAddress;
                            this.$utils.hideLoading();
                        }
                    }
                } else {
                    let userLocation = await that.$locations.openSetting();
                    if (userLocation['scope.userLocation']) {
                        that.coordinate = await that.$locations.getCurrentCoordinate();
                        that.$store.commit('coordinate/setCoordinate', that.coordinate);
                    }
                }
            },
            /**
             *  校验数据
             *  <AUTHOR>
             *  @date   2019-04-01 20:41
             */
            checkData() {
                if (this.$utils.isEmpty(this.visitRegisterItem.visitType)) {
                    this.$message.primary('请选择拜访类型');
                    return false;
                }
                if (this.$utils.isEmpty(this.visitRegisterItem.visitorId)) {
                    this.$message.primary('请选择拜访人');
                    return false;
                }
                if (this.$utils.isEmpty(this.visitRegisterItem.oldVisitTime) && this.type === 'VisitPlan') {
                    this.$message.primary('请选择计划拜访时间');
                    return false;
                }
                if (this.$utils.isEmpty(this.visitRegisterItem.visitTime) && this.type !== 'VisitPlan') {
                    this.$message.primary('请选择拜访时间');
                    return false;
                }
                if (this.type !== 'VisitPlan' && this.visitRegisterItem.visitWay !== 'OnlineVisit' && this.$utils.isEmpty(this.visitRegisterItem.visitAddress)) {
                    this.$message.primary('请输入详细地址');
                    return false;
                }
                if (this.type !== 'VisitPlan' && this.visitRegisterItem.visitWay !== 'OnlineVisit' && this.$utils.isEmpty(this.visitRegisterItem.visitProvince)) {
                    this.$message.primary('请选择拜访地点信息');
                    return false;
                }
                if (this.visitRegisterItem['visitType'] === 'PublicRelationVisit' && this.$utils.isEmpty(this.visitRegisterItem['visitEntId'])) {
                    this.$message.primary('请选择拜访单位');
                    return false;
                }
                if (this.visitRegisterItem.visitCompany && this.visitRegisterItem.visitCompany.length > 100) {
                    this.$message.primary('拜访单位名称字数超出长度100字，请检查');
                    return false;
                }
                return true;
            },
            /**
             * 保存
             * <AUTHOR>
             * @date 2020-09-29
             * */
            async save() {
                if (!this.checkData()) {
                    return;
                }
                if (this.visitRegisterItem['visitType'] === 'PublicRelationVisit') {
                    this.$refs.saveConfirm.show();
                } else {
                    this.saveVisitRegister();
                }
            },
            /**
             *  新建保存客户拜访
             *
             *  <AUTHOR>
             *  @date        2020-10-29
             */
            async saveVisitRegister() {
                if (!this.checkData()) {
                    return;
                }
                if (this.$utils.isEmpty(this.visitRegisterItem.title) && this.type !== 'VisitPlan') {
                    this.visitRegisterItem.title = await this.$lov.getNameByTypeAndVal('VISIT_TYPE', this.visitRegisterItem.visitType) + this.visitRegisterItem.visitTime;
                } else if (this.$utils.isEmpty(this.visitRegisterItem.title) && this.type === 'VisitPlan') {
                    this.visitRegisterItem.title = await this.$lov.getNameByTypeAndVal('VISIT_TYPE', this.visitRegisterItem.visitType) + this.visitRegisterItem.oldVisitTime;
                }
                // 类型为拜访计划时，新建计划，状态为计划中
                if (this.type === 'VisitPlan') {
                    this.visitRegisterItem['visitStatus'] = 'Planning';
                    delete this.visitRegisterItem['visitTime'];
                }
                if (this.visitRegisterItem['visitType'] === 'PublicRelationVisit') {
                    delete this.visitRegisterItem['isApplyFlag'];
                } else {
                    this.visitRegisterItem['visitLevel'] = '';
                    this.visitRegisterItem['visitGift'] = '';
                }
                if (!this.$utils.isEmpty(this.visitRegisterItem['attr2']) && this.companyType === 'visitCase') {
                    this.visitRegisterItem['attr1'] = 'VisitCase';
                }
                // this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/action/link/visit/upsertVisit', this.visitRegisterItem);
                if (data.success) {
                    this.$utils.hideLoading();
                    let item = data.newRow;
                    item.editFlag = true;
                    item.visitAccompanyList = [];
                    item.visitCustInfoList = [];
                    this.$nav.redirect('/pages/lj-consumers/visit-register/visit-register-item-page', {
                        data: item,
                        type: this.type,
                        source: 'New'
                    });
                } else {
                    this.$utils.hideLoading();
                    this.$showError('保存失败' + data.result);
                }

            },
            /**
             *  @description: 根据选择的省市区和输入的详细地址联想
             *  @author: syr
             *  @date: 2021/09/17
             */
            async searchAddress(){
                if(this.$utils.isEmpty(this.selectAddressObj.addr)){
                    this.$showError("请输入详细地址");
                    return false;
                }
                const address = this.selectAddressObj.province + this.selectAddressObj.city + this.selectAddressObj.district + this.selectAddressObj.street + this.selectAddressObj.addr;
                // const data = await this.$locations.getBMapSuggestion(address);
                const data = await this.$locations.getTMapSuggestion(address);
                this.suggestionAddressData = [...data.data];
            },
            /**
             *  @description: 选择某一个联想地址
             *  @author: syr
             *  @date: 2021/09/18
             */
            selectAddress(item){
                const that = this;
                that.$set(item, '_checked', true);
                that.suggestionAddressData.filter(function (val) {
                    if (val.address !== item.address) {
                        that.$set(val, '_checked', false);
                    }
                });
            },
            /**
             *  @description: 确认某一个联想地址
             *  @author: 宋燕荣
             *  @date: 2021/09/18
             */
            async confirmAddress(){
                const that = this;
                const address = this.suggestionAddressData.filter((item1) => item1._checked === true);
                if(this.$utils.isEmpty(address)){
                    this.$message.info("请选择一个地址");
                    return false;
                }
                that.visitRegisterItem.visitProvince = address[0].province;
                that.visitRegisterItem.visitCity = address[0].city;
                that.visitRegisterItem.visitCounty = address[0].district;
                that.visitRegisterItem.street = address[0].street;
                that.visitRegisterItem.visitLatitude = address[0]['location']['lat'];
                that.visitRegisterItem.visitLongitude = address[0]['location']['lng'];
                that.$set(that.visitRegisterItem, 'visitAddress', address[0].title);
                that.addressData = address[0].province + address[0].city + address[0].district + address[0].street + address[0].title;
                that.$refs.locationFailSelectAddress.hide();
            }
        }
    }
</script>

<style lang="scss">
    .visit-register-item-new {
        overflow-x: hidden;
        .visit-register-add {
            margin-top: 24px;
        }
        .lnk-form-header-img {
            width: 100%;
            padding-left: 32px;
            background-color: white;
            font-size: 28px;
            line-height: 88px;
        }
        .company-info {
            margin: 24px 0;
            background: white;
            .company-content{
                padding: 0 24px;
                .company-info-item{
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    font-size: 28px;
                    height: 88px;
                    border-bottom: 1px solid #F2F2F2;
                    line-height: 28px;
                    padding: 5px 12px;
                }
                .company-info-item:last-child{
                    border-bottom: none;
                }
            }
        }
        .address-info {
            display: flex;
            align-items: center;
            .icon-location {
                font-size: 32px;
            }
            .address-data{
                width: 300px;
                word-break: normal;
                color: #3b4144;
                text-align: right;
            }
            .address-tips{
                width: 300px;
                color: #2F69F8;
                text-align: right;
                display: block;
            }
        }
        /*.link-dialog .link-dialog-content {*/
        /*    .link-dialog-body {*/
        /*        padding: 0px !important;*/
        /*        -webkit-flex: 1;*/
        /*        -ms-flex: 1;*/
        /*        flex: 1;*/
        /*        font-size: 28px;*/
        /*        overflow: hidden;*/
        /*        word-break: break-all;*/

        /*    }*/
        /*}*/
        .location-select-address {
            width: 654px !important;
            .link-dialog-content .link-dialog-body {
                padding: 0 !important;
                -webkit-flex: 1;
                -ms-flex: 1;
                flex: 1;
                font-size: 28px;
                overflow: hidden;
                word-break: break-all;
            }
        }
        .address-t {
            width: 654px;
            height: 900px;
            border-radius: 16px;
            background-color: white;
            .list-container {
                height: 560px;
                .list-item {
                    display: flex;
                    border-bottom: 1px solid #F2F2F2;

                    .left-content {
                        display: inline-block;
                        width: 75%;

                        .row-1 {

                            .name{
                                font-family: PingFangSC-Semibold,serif;
                                font-size: 28px;
                                color: #262626;
                                letter-spacing: 0;
                                line-height: 32px;
                                margin-left: 24px;
                                margin-top: 20px;
                            }
                            .address{
                                margin-left: 24px;
                                margin-top: 10px;
                                font-family: PingFangSC-Regular,serif;
                                font-size: 24px;
                                color: #8C8C8C;
                                letter-spacing: 0;
                                line-height: 32px;
                                margin-bottom: 10px;
                            }
                        }

                        .row-2 {
                            width: 100%;
                            margin-top: 1vh;

                            .date {
                                color: gray;
                                font-size: 12px;
                            }
                        }
                    }

                    .right-content {
                        padding-left: 20px;
                        display: inline-block;
                        width: 15%;
                        line-height: 92px;
                        text-align: right;
                    }
                }
            }

            .address-v {
                width: 100%;

                .t-left {
                    padding: 20px;
                    height: 60px;

                    .title {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #333333;
                        letter-spacing: 0;
                        line-height: 44px;
                        width: 25%;
                        float: left;

                    }
                    .val {
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 44px;
                        width: 50%;
                        float: left;
                    }
                    .ent-wrap{
                        color: $main-color;
                        white-space: nowrap;
                        display: inline-block;
                        height: 60px;
                        line-height: 60px;
                        width: 20%;
                        float: left;
                        text-align: center;
                    }
                }
            }
        }
    }
</style>
