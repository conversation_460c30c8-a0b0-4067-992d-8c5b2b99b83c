<template>
    <link-page class="visit-register-item-page">
        <link-form ref="visitRegisterForm" :rules="formRules" hideEditButton hideSaveButton :value="formData"
                   :readonly="!editFlag">
            <link-form-item label="拜访类型" required arrow field="visitType" readonly>
                <link-lov type="VISIT_TYPE" v-model="formData.visitType"></link-lov>
            </link-form-item>
            <link-form-item label="拜访方式" required arrow field="visitWay">
                <link-lov type="CSM_VISIT_WAY" v-model="formData.visitWay" @change="visitWayChange"></link-lov>
            </link-form-item>
            <link-form-item label="拜访人">
                <link-input v-model="formData.visitorName" readonly></link-input>
            </link-form-item>
            <link-form-item label="拜访时间" :required="formData.visitStatus !== 'Planning'" field="visitTime">
                <link-date v-model="formData.visitTime" view="YMDHm" display-format="YYYY-MM-DD HH:mm"
                           value-format="YYYY-MM-DD HH:mm"></link-date>
            </link-form-item>
            <link-form-item label="详细地址" required v-if="formData.visitWay !== 'OnlineVisit'"
                            :readonly="formData.visitStatus !== 'Planning' && formData.visitStatus !== 'New'">
                <view class="address-info" @tap="getLocation">
                    <view :class="addressData === '获取定位' ? 'address-tips' : 'address-data'">{{ addressData ? addressData : ''}}</view>
                    <link-icon style="color: #2F69F8" icon="icon-location" class="link-location"/>
                </view>
            </link-form-item>
            <link-form-item label="拜访单位" field="visitCompany">
                <link-input v-model="formData.visitCompany"
                            :readonly="!editFlag || formData['visitType'] === 'PublicRelationVisit'"></link-input>
            </link-form-item>
            <link-form-item label="是否申请领导协访">
                <link-switch v-model="formData.isApplyFlag"></link-switch>
            </link-form-item>
            <link-form-item label="拜访地点" required :field="['visitProvince', 'visitCity','visitCounty']"
                            readonly v-if="formData.visitWay !== 'OnlineVisit'">
                <view class="address-color">
                    <text v-if="!$utils.isEmpty(formData.visitProvince)">{{ formData.visitProvince }}</text>
                    <text v-if="!$utils.isEmpty(formData.visitCity)">/{{ formData.visitCity }}</text>
                    <text v-if="!$utils.isEmpty(formData.visitCounty)">/{{ formData.visitCounty }}</text>
                    <text v-if="!$utils.isEmpty(formData.street)">/{{ formData.street }}</text>
                </view>
            </link-form-item>
            <!-- 拜访人员列表 -->
            <view v-if="!editFlag" class="visit-register-add">
                <user-info @initCustInfoListInfo="initCustInfoListInfo"
                           :edit-flag="editFlag"
                           :parentId="formData.id"
                           :userInfoList="formData.visitCustInfoList"></user-info>
                <!-- 协访领导 -->
                <title-line label-name="协访领导" @tap="addVisitAccompanyInfo('LeaderAsistVisit')"
                            :buttonName="editFlag? '添加' : ''"/>
                <list class="visit-register-add-list" style="margin-bottom: 15px">
                    <!-- 列表 -->
                    <link-swipe-action v-for="(item,index) in formData.visitLeaderList.slice(0,5)" :key="item.id"
                                       style="padding: 15px 15px 0; box-sizing: border-box;">
                        <link-swipe-option v-if="editFlag" slot="option"
                                           @tap="handleAccompanyDelete(item,'LeaderAsistVisit',index)">删除
                        </link-swipe-option>
                        <item style="width: 100%" :arrow="false">
                            <view class="visit-account-item">
                                <view class="visit-account-info">
                                    <view><text>{{item.accompanyName}}</text></view>
                                    <view>手机号<text>{{item.accompanyTel}}</text></view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                    <!-- 查看更多 -->
                    <view @tap="gotoVisitAccompanyList('LeaderAsistVisit')" v-if="formData.visitLeaderList.length>5" class="more-box">
                        <view class="more">
                            查看全部({{formData.visitLeaderList.length}})>>
                        </view>
                    </view>
                </list>
                <!-- 陪同人员 -->
                <title-line label-name="陪同人员" @tap="addVisitAccompanyInfo('ColleagueAsistVisit')"
                            :buttonName="editFlag? '添加' : ''"/>
                <list class="visit-register-add-list">
                    <!-- 列表 -->
                    <link-swipe-action v-for="(item,index) in formData.visitAccompanyList.slice(0,5)" :key="item.id"
                                       style="padding: 15px 15px 0; box-sizing: border-box;">
                        <link-swipe-option v-if="editFlag" slot="option"
                                           @tap="handleAccompanyDelete(item,'ColleagueAsistVisit',index)">
                            删除
                        </link-swipe-option>
                        <item style="width: 100%" :arrow="false">
                            <view class="visit-account-item">
                                <view class="visit-account-info">
                                    <view><text>{{item.accompanyName}}</text></view>
                                    <view>手机号<text>{{item.accompanyTel}}</text></view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                    <!-- 查看更多 -->
                    <view @tap="gotoVisitAccompanyList('ColleagueAsistVisit')" v-if="formData.visitAccompanyList.length>5" class="more-box">
                        <view class="more">
                            查看全部({{formData.visitAccompanyList.length}})>>
                        </view>
                    </view>
                </list>
            </view>
            <link-form-item label="拜访情况总结" vertical v-if="formData.visitStatus !== 'Planning'" field="visitSummary"
                            :required="formData.visitStatus !== 'Planning' && formData.visitStatus !== 'Registered'">
                <link-textarea v-model="formData.visitSummary"
                               :nativeProps="{maxlength:200}"></link-textarea>
            </link-form-item>
            <link-form-item label="计划拜访时间" :required="formData.visitStatus === 'Planning'" field="oldVisitTime"
                            v-if="formData.visitStatus === 'Planning'">
                <link-date v-model="formData.oldVisitTime" view="YMDHm" display-format="YYYY-MM-DD HH:mm"
                           value-format="YYYY-MM-DD HH:mm"></link-date>
            </link-form-item>
            <view class="lnk-form-header-img" v-if="formData.visitStatus !== 'Planning' && !!formData.id">
                <view class="title"><text style="color: red;font-size: 18px">*</text>照片</view>
                <view v-if="editFlag">
                    <lnk-img-watermark :parentId="formData.id"
                                       moduleType="visitRegister"
                                       :delFlag="editFlag"
                                       :isZlFlag="true"
                                       filePathKey="/zhls/VisitRegister/"
                                       :album="editFlag"
                                       @imgDeleteSuccess="imgDeleteSuccess"
                                       @initSuccess="initSuccess"
                                       @imgUploadSuccess="imageArrLength"
                                       :newFlag="editFlag">
                    </lnk-img-watermark>
                </view>
                <view v-else>
                    <lnk-img-watermark :parentId="formData.id"
                                       moduleType="visitRegister"
                                       :delFlag="false"
                                       :isZlFlag="true"
                                       filePathKey="/zhls/VisitRegister/"
                                       :album="false"
                                       @imgDeleteSuccess="imgDeleteSuccess"
                                       @initSuccess="initSuccess"
                                       @imgUploadSuccess="imageArrLength"
                                       :newFlag="false">
                    </lnk-img-watermark>
                    <lnk-img-watermark :parentId="formData.id"
                                       moduleType="visitRegister"
                                       :delFlag="false"
                                       filePathKey="/zhls/VisitRegister/"
                                       :album="false"
                                       @imgDeleteSuccess="imgDeleteSuccess"
                                       @initSuccess="getImgData"
                                       @imgUploadSuccess="imageArrLength"
                                       :newFlag="false">
                    </lnk-img-watermark>
                </view>
            </view>
            <view v-if="editFlag" class="visit-register-add">
                <!-- 拜访客户列表 -->
                <user-info @initCustInfoListInfo="initCustInfoListInfo"
                           :edit-flag="editFlag"
                           :parentId="formData.id"
                           :userInfoList="formData.visitCustInfoList"></user-info>
                <!-- 协访领导 -->
                <title-line label-name="协访领导" @tap="addVisitAccompanyInfo('LeaderAsistVisit')"
                            :buttonName="editFlag? '添加' : ''"/>
                <list class="visit-register-add-list" style="margin-bottom: 15px">
                    <!-- 列表 -->
                    <link-swipe-action v-for="(item,index) in formData.visitLeaderList.slice(0,5)" :key="item.id"
                                       style="padding: 15px 15px 0; box-sizing: border-box;">
                        <link-swipe-option v-if="editFlag" slot="option"
                                           @tap="handleAccompanyDelete(item,'LeaderAsistVisit',index)">删除
                        </link-swipe-option>
                        <item style="width: 100%" :arrow="false">
                            <view class="visit-account-item">
                                <view class="visit-account-info">
                                    <view><text>{{item.accompanyName}}</text></view>
                                    <view>手机号<text>{{item.accompanyTel}}</text></view>
                                </view>
                            </view>
                        </item>
                    </link-swipe-action>
                    <!-- 查看更多 -->
                    <view @tap="gotoVisitAccompanyList('LeaderAsistVisit')" v-if="formData.visitLeaderList.length>5" class="more-box">
                        <view class="more">
                            查看全部({{formData.visitLeaderList.length}})>>
                        </view>
                    </view>
                </list>
                <!-- 陪同人员 -->
                <title-line label-name="陪同人员" @tap="addVisitAccompanyInfo('ColleagueAsistVisit')"
                            :buttonName="editFlag? '添加' : ''"/>
                <list class="visit-register-add-list">
                    <!-- 列表 -->
                    <link-swipe-action v-for="(item,index) in formData.visitAccompanyList.slice(0,5)" :key="item.id"
                                       style="padding: 15px 15px 0; box-sizing: border-box;">
                        <link-swipe-option v-if="editFlag" slot="option"
                                           @tap="handleAccompanyDelete(item,'ColleagueAsistVisit',index)">
                            删除
                        </link-swipe-option>
                        <item style="width: 100%" :arrow="false">
                            <view class="visit-account-item">
                                    <view class="visit-account-info">
                                        <view><text>{{item.accompanyName}}</text></view>
                                        <view>手机号<text>{{item.accompanyTel}}</text></view>
                                    </view>
                            </view>
                        </item>
                    </link-swipe-action>
                    <!-- 查看更多 -->
                    <view @tap="gotoVisitAccompanyList('ColleagueAsistVisit')" v-if="formData.visitAccompanyList.length>5" class="more-box">
                        <view class="more">
                            查看全部({{formData.visitAccompanyList.length}})>>
                        </view>
                    </view>
                </list>
            </view>
        </link-form>
        <link-sticky v-if="formData.visitStatus !== 'Inactive'">
            <link-button block @tap="handleVisitItemStatus('Inactive')">作废</link-button>
            <link-button block
                         v-if="registerShow"
                         @tap="handleVisitItemStatus('Registered')">登记
            </link-button>
            <link-button block v-if="formData.visitStatus === 'Planning'" @tap="handleVisitItemStatus('ToDo')">
                保存
            </link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import {ROW_STATUS} from "../../../utils/constant";
import TitleLine from "../../lzlj-II/fighting-fakes/components/title-line";
import {PageCacheManager} from "../../../utils/PageCacheManager";
import ConsumerCommon from "../consumer-common";
import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";
import LnkImgLzlj from "../../core/lnk-img-lzlj/lnk-img-lzlj";
import UserInfo from "./components/user-info";
import TagInfo from "../account/components/tag-info/tag-info";
import {reverseTMapGeocoder} from "../../../utils/locations-tencent";

export default {
    name: "visit-register-item-page",
    components: {TagInfo, LnkImgLzlj, LnkImgWatermark, TitleLine, UserInfo},
    mixins: [ConsumerCommon()],
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;         // 获取用户信息
        const formData = {
            ...this.pageParam.data,
            visitCustInfoList: [],
            visitAccompanyList: [],
            visitLeaderList: [],
        };
        let editFlag = false;// 情景只读标志
        editFlag = formData.visitStatus === 'New'
            || formData['visitStatus'] === 'ToDo'
            || formData['visitStatus'] === 'Planning';
            const cacheData = PageCacheManager.getInitialData({
                ctx: this,
                path: 'lj-consumers/visit-register/visit-register-item-page.vue',
                title: "消费者-拜访登记",
                initialData: {
                    formData
                },
            });
            if (cacheData.formData !== formData) {
                setTimeout(() => {
                    this.formData = cacheData.formData
                }, 1000)
        }
        return {
            oldImgList: [],
            imgList: [],
            userInfo,
            addressData: '获取定位',
            formData,
            editFlag,
            formRules: {},
            coordinate: {},
            leaderOption: new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/user',
                searchFields: ['firstName'],
                param: {
                    rows: 25,
                    filtersRaw: [
                        {
                            id: 'positionType',
                            property: 'positionType',
                            value: '[SalesGeneralManager,GeneralManager,HQLeader]',
                            operator: 'IN'
                        }
                    ]
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} title={data.firstName} data={data} arrow={false}>
                            {data.contactPhone}
                        </item>
                    )
                }
            }),
            accompanyOption: new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/user',
                searchFields: ['firstName'],
                param: {
                    rows: 25,
                    filtersRaw: [
                        {
                            id: 'positionType',
                            property: 'positionType',
                            value: '[SalesGeneralManager,GeneralManager,HQLeader]',
                            operator: 'NOT IN'
                        }
                    ]
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} title={data.firstName} data={data}>
                            {data.contactPhone}
                        </item>
                    )
                },
                hooks: {
                    beforeLoad(option) {
                        option.param.filtersRaw = [
                            ...option.param.filtersRaw,
                            {id: 'username', property: 'username', value: this.userInfo.username, operator: '<>'},
                        ];
                    }
                }
            }),
            companyType: '',
            endFlag: false
        }
    },
    async onShow(){
        if(this.pageParam.source === 'New' && this.$utils.isNotEmpty(this.formData.visitProvince)) {
            console.log('不重新处理地址')
            return
        }
        const location = this.$locations.QQGetLocation();
        if(location){
            let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '拜访');
            try {
                this.$utils.showLoading()
                const addrCode = addressInfo['originalData'].result.addressComponent.adcode;
                const data = await this.$http.post(this.$env.appURL +'/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode: addrCode})
                if(data.success) {
                    if(data.adcodeFlag){
                        this.$set(this.formData, 'visitProvince', data.province);
                        this.$set(this.formData, 'visitCity', data.city);
                        this.$set(this.formData, 'visitCounty', data.district);
                        this.$set(this.formData, 'street', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                        this.$set(this.formData, 'attr01', location.longitude);
                        this.$set(this.formData, 'attr02', location.latitude);
                        this.$utils.hideLoading();
                    }else {
                        this.$set(this.formData, 'visitProvince', addressInfo['originalData'].result.addressComponent['province']);
                        this.$set(this.formData, 'visitCity', addressInfo['originalData'].result.addressComponent['city']);
                        this.$set(this.formData, 'visitCounty', addressInfo['originalData'].result.addressComponent['district']);
                        this.$set(this.formData, 'street', addressInfo['originalData'].result.addressComponent['street'] ?  addressInfo['originalData'].result.addressComponent['street'] : '');
                        // this.$set(this.formData, 'visitAddress', addressInfo['result'].formatted_addresses.standard_address);
                        this.$set(this.formData, 'attr01', location.longitude);
                        this.$set(this.formData, 'attr02', location.latitude);
                        this.$utils.hideLoading();
                    }
                }
            } catch (e) {
                this.$utils.hideLoading();
            }
        }
    },
    computed: {
        registerShow(){
            const visitStatusFlag = this.formData.visitStatus !== 'Planning' && this.formData.visitStatus !== 'Registered';
            if(visitStatusFlag && this.formData.visitTime){
                const now = new Date();
                const visitTime = this.formData.visitTime.length > 18 ? this.formData.visitTime : this.formData.visitTime + ':00'
                const end = this.$date.parse(visitTime, 'YYYY-MM-DD HH:mm:ss');
                const time = now.getTime() - end.getTime();
                if(time > 0){
                    this.endFlag = true;
                }else{
                    this.endFlag = false;
                    setTimeout(() => {
                        this.endFlag = true;
                    }, -time)
                }
            }
            return visitStatusFlag && this.endFlag;
        }
    },
    async created() {
        // 拜访客户信息
        await this.initCustInfoListInfo();
        // 初始化陪同人员
        await this.initAccompanyListInfo('ColleagueAsistVisit');
        // 初始化协访领导
        await this.initAccompanyListInfo('LeaderAsistVisit');
        const normalizedProvince = this.formData.visitProvince ? this.formData.visitProvince : '';
        const normalizedCity = this.formData.visitCity ? this.formData.visitCity : '';
        const normalizedCounty = this.formData.visitCounty ? this.formData.visitCounty : '';
        const normalizedStreet = this.formData.street ? this.formData.street : '';
        const normalizedVisitAddress = this.formData.visitAddress ? this.formData.visitAddress : '';
        this.addressData = normalizedProvince + normalizedCity + normalizedCounty + normalizedStreet + normalizedVisitAddress;
        if(!this.addressData) {
            this.addressData = '获取定位';
        }
        this.$bus.$on('initCustInfoListInfo', async () => {
            await this.initCustInfoListInfo();
        })
        await this.initImgList();
    },
    methods: {
        /**
         * @desc 拜访方式切换，清空地址字段
         * <AUTHOR>
         * @date 2022/02/01
         **/
        visitWayChange() {
            this.$set(this.formData, 'visitProvince', null);
            this.$set(this.formData, 'visitCity', null);
            this.$set(this.formData, 'visitCounty', null);
            this.$set(this.formData, 'street', null);
            this.$set(this.formData, 'visitLatitude', null);
            this.$set(this.formData, 'visitLongitude', null);
            this.$set(this.formData, 'visitAddress', null);
            this.addressData = '获取定位';
        },
        imgDeleteSuccess (data) {
            this.imgList = data;
        },
        /**
         * @desc 初始化图片
         * <AUTHOR>
         * @date 2022/12/1 14:40
         **/
        initSuccess (data) {
            this.imgList = data;
        },
        /**
         *  图片信息
         *  <AUTHOR>
         *  @date   2020-07-03
         */
        async initImgList() {
            console.log("查询图片");
            if (!this.$utils.isEmpty(this.formData.id)) {
                const data = await this.$http.post(this.$env.appURL + '/action/link/image/queryAll',
                    {
                        parentid: this.formData.id,
                        module: 'visitRegister',
                    });
                this.oldImgList = data.rows || [];
            }
        },
        /**
         *  @description: 获取定位地址
         *  @author: songyanrong
         *  @date: 2020-10-28
         */
        async getLocation() {
            if (this.formData.visitStatus !== 'Planning' && this.formData.visitStatus !== 'New' && this.formData.visitStatus !== 'ToDo') {
                const addressInfo = await this.$locations.getAddress();
                return;
                await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
            }
            const that = this;
            that.coordinate = await that.$locations.getCurrentCoordinate();
            if (!that.$utils.isEmpty(that.coordinate)) {
                let addressInfo = await that.$locations.reverseTMapGeocoder(that.coordinate.latitude, that.coordinate.longitude, '拜访');
                that.formData.visitProvince = addressInfo.originalData.result.addressComponent['province'];
                that.formData.visitCity = addressInfo.originalData.result.addressComponent['city'];
                that.formData.visitCounty = addressInfo.originalData.result.addressComponent['district'];
                that.formData.street = addressInfo.originalData.result.addressComponent['street'] ? addressInfo.originalData.result.addressComponent['street'] : '';
                that.$set(that.formData, 'visitAddress', addressInfo.originalData.result['sematic_description']);
                that.formData.visitLatitude = that.coordinate.latitude;
                that.formData.visitLongitude = that.coordinate.longitude;
                that.addressData = that.formData.visitProvince + that.formData.visitCity + that.formData.visitCounty + that.formData.street + that.formData.visitAddress;
            } else {
                let userLocation = await that.$locations.openSetting();
                if (userLocation['scope.userLocation']) {
                    that.coordinate = await that.$locations.getCurrentCoordinate();
                    that.$store.commit('coordinate/setCoordinate', that.coordinate);
                }
            }
        },
        /**
        * @desc 拜访地点开启地图选点
        * <AUTHOR>
        * @date 2024/3/20
        **/
        async getLocationMap() {
            const addressInfo = await this.$locations.getAddress();
            await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
        },
        /**
         *  @description: 查询图片信息
         *  @author: 马晓娟
         *  @date: 2020/11/17 20:42
         */
        getImgData(data) {
            console.log(data);
            this.oldImgList = data;
        },
        imageArrLength (param) {
            this.imgList = this.imgList.concat(param);
        },
        /**
         *  请求客户信息
         *
         *  <AUTHOR>
         *  @date        2020-07-03
         */
        async initCustInfoListInfo() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitCustInfo/queryByExamplePage',
                {
                    visitId: this.pageParam.data.id,
                    queryProdsFlag: 'Y',
                    order: 'desc',
                    sort: 'created',
                    rows: 25,
                    page: 1
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询客户信息失败：${response.result}`);
                    }
                });
            this.formData.visitCustInfoList = data.rows || [];
        },
        /**
         *  查询陪同人员信息
         *
         *  <AUTHOR>
         *  @date        2020-07-03
         */
        async initAccompanyListInfo(type) {
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitAccompany/queryByExamplePage',
                {
                    visitId: this.pageParam.data.id,
                    queryProdsFlag: 'Y',
                    order: 'desc',
                    sort: 'created',
                    filtersRaw: [{id: 'accompanyType', property: 'accompanyType', value: type, operator: '='}],
                    rows: 25,
                    page: 1
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询陪同人员数据失败：${response.result}`);
                    }
                });
            if (data.success) {
                if (type === 'ColleagueAsistVisit') {
                    this.formData.visitAccompanyList = [...data.rows];
                }
                if (type === 'LeaderAsistVisit') {
                    this.formData.visitLeaderList = [...data.rows];
                }
            }
        },
        /**
         *  @description: 更新客户信息
         *  @author: 马晓娟
         *  @date: 2020/5/29 14:20
         */
        updateCust(item) {
            item.row_status = ROW_STATUS.UPDATE;
            this.$nav.push('/pages/lj-consumers/visit-register/visit-register-item-cust-info-edit-page', {
                item: this.formData,
                visitRegisterItemCustInfoItem: item,
                callback: () => {
                    this.initCustInfoListInfo();
                }
            });
        },
        /**
         *  新建陪同人员，协访领导信息
         *  <AUTHOR>
         *  @date   2019-03-20 19:58
         */
        addVisitAccompanyInfo(type) {
            // 协访领导
            if (type === 'LeaderAsistVisit') {
                this.selectLeader();
            }
            // 陪同人员
            if (type === 'ColleagueAsistVisit') {
                this.selectAccompany();
                this.$set(this.visitRegisterItem, 'street', null);
            }
        },
        async selectLeader() {
            const item = await this.$object(this.leaderOption);
            let flag = true;
            this.formData.visitAccompanyList.forEach(i => {
                if (i.accompanyId === item.id) {
                    flag = false;
                }
            });
            if (flag) {
                this.$http.post(this.$env.appURL + '/action/link/visitAccompany/upsert', {
                    row_status: 'NEW',
                    visitId: this.formData.id,
                    accompanyId: item.id,
                    accompanyType: 'LeaderAsistVisit'
                }).then(async result => {
                    if (result.success) {
                        this.initAccompanyListInfo('LeaderAsistVisit');
                    }
                });
            }
        },
        async selectAccompany() {
            const item = await this.$object(this.accompanyOption);
            let flag = true;
            this.formData.visitAccompanyList.forEach(item => {
                if (item.accompanyId === item.id) {
                    flag = false;
                }
            });
            if (flag) {
                this.$http.post(this.$env.appURL + '/action/link/visitAccompany/upsert', {
                    row_status: 'NEW',
                    visitId: this.formData.id,
                    accompanyId: item.id,
                    accompanyType: 'ColleagueAsistVisit'
                }).then(async result => {
                    if (result.success) {
                        this.initAccompanyListInfo('ColleagueAsistVisit');
                    }
                });
            }
        },
        /**
         *  @description: 删除陪同人员或者协访领导
         *  @author: songyanrong
         *  @date: 2020-07-17
         */
        async handleAccompanyDelete(item, type, index) {
            const data = await this.$http.post(this.$env.appURL + '/action/link/visitAccompany/deleteById', item, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$showError(`删除失败：${response.result}`);
                }
            });
            if (data.success) {
                this.formData.visitAccompanyList.slice(index, 1);
                this.initAccompanyListInfo(type);
            }
        },
        /**
         *  作废登记
         *  <AUTHOR>
         *  @date  2020-07-08
         */
        async handleVisitItemStatus(status) {
            let params = {};
            if (status === 'Inactive') {
                params = {
                    id: this.formData.id,
                    visitStatus: 'Inactive'
                };
            } else if (status === 'Registered') {
                const imgList = this.imgList.concat(this.oldImgList);
                if (this.$utils.isEmpty(imgList) || imgList.length < 1) {
                    this.$message.primary('请添加图片');
                    return;
                }
                if (this.$utils.isEmpty(this.formData.visitSummary)) {
                    this.$message.primary('请输入拜访情况总结');
                    return;
                }
                if (this.formData.visitCustInfoList.length <= 0) {
                    this.$message.primary('请添加拜访客户');
                    return;
                }
                params = {
                    id: this.formData.id,
                    visitStatus: 'Registered'
                };
            } else if (status === 'ToDo') {
                params = {
                    id: this.formData.id,
                    visitStatus: 'ToDo'
                };
            }
            await this.saveVisitRegister('Registered');
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/action/link/visit/visitStatusUpdate', params, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`更新状态失败：${response.result}`);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.$nav.back();
            }
        },
        /**
         *  新建保存客户拜访
         *  <AUTHOR>
         *  @date   2019-03-20 15:44
         */
        async saveVisitRegister(type) {
            await this.$refs.visitRegisterForm.validate();
            if (this.formData['visitType'] === 'PublicRelationVisit') {
                delete this.formData['isApplyFlag'];
            } else {
                this.formData['visitLevel'] = '';
                this.formData['visitGift'] = '';
            }
            this.formData.row_status = 'UPDATE';
            // this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/action/link/visit/upsert', this.formData, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`保存失败：${response.result}`);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                if (type !== 'Registered') {
                    await this.$nav.back();
                }
            }
        },

        /**
         * 保存
         * <AUTHOR>
         * @date 2020-09-29
         * */
        async save() {
            this.$refs.visitRegisterForm.Validator();
            if (this.formData['visitType'] === 'PublicRelationVisit') {
                delete this.formData['isApplyFlag'];
            } else {
                this.formData['visitLevel'] = '';
                this.formData['visitGift'] = '';
            }
            this.formData.row_status = 'UPDATE';
            this.$utils.showLoading();
            const data = await this.$http.post(this.$env.appURL + '/action/link/visit/upsert', this.formData, {
                autoHandleError: false,
                handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`保存失败：${response.result}`);
                }
            });
            if (data.success) {
                this.$utils.hideLoading();
                this.$nav.back();
            }
        },

        /**
         * 跳转到更多列表页面
         * <AUTHOR>
         * @date 2023-02-01
         * */
        gotoVisitAccompanyList(type) {
            this.$nav.push('/pages/lj-consumers/visit-register/visit-accompany-list-page', {
                visitId: this.formData.id,     // 头id
                editFlag: this.editFlag,    // 是否可编辑
                type,                       // 查询的类型 协防领导LeaderAsistVisit/陪同人员ColleagueAsistVisit
                callback: (type) => {
                    this.initAccompanyListInfo(type);
                }
            })
        }
    }
}
</script>

<style lang="scss">
.visit-register-item-page {
    background-color: #F2F2F2;

    .lnk-form-header-img {
        width: 100%;
        background-color: white;
        font-size: 28px;
        line-height: 88px;
        .title{
            padding-left: 12px;
            display:flex;
            align-items: center;
        }
        .lnk-img-con{
            padding: 0 12px;
        }
    }

    .visit-register-add {
        margin-top: 24px;
        // 列表样式
        .visit-register-add-list {
            margin-bottom: 24px;
            // 每个单元添加圆角
            /*deep*/.link-item {
                        border-radius: 16px;
                    }
            /*deep*/
            .link-swipe-option-container .link-swipe-option {
                width: 100px;
                height: 70px !important;
                border-radius: 80px;
                font-size: 28px !important;
                transform: translateY(10px);
            }

            .visit-account-item {
                display: flex;
                justify-content: space-between;
                position: relative;
                width: 100%;
                .visit-account-info {
                    width: 75%;
                    view {
                        margin-bottom: 20px;
                        &:not(:first-child) text {
                            margin-left: 20px;
                        }
                        text {
                            font-weight: 500;
                            color: #262626;
                        }
                    }
                }
            }
        }
        // 查看更多样式
        .more-box {
            width: 100%;
            height: 50px;
            display: flex;
            justify-content: center;
            align-items: center;
            margin-top: 10px;
            .more {
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 76px;
                background-color: #f2f2f2;
            }
        }
    }

    .company-info {
        margin: 24px 0;
        background: white;

        .company-content {
            padding: 0 24px;

            .company-info-item {
                display: flex;
                justify-content: space-between;
                align-items: center;
                font-size: 28px;
                height: 88px;
                border-bottom: 1px solid #eff1f3;
                line-height: 28px;
                padding: 5px 12px;
            }

            .company-info-item:last-child {
                border-bottom: none;
            }
        }
    }

    .address-info {
        display: flex;
        align-items: center;

        .icon-location {
            font-size: 32px;
        }

        .address-data {
            width: 300px;
            word-break: normal;
            color: #3b4144;
            text-align: right;
        }

        .address-tips {
            width: 300px;
            color: #2F69F8;
            text-align: right;
            display: block;
        }
    }
}
</style>
