<!--
@createdBy 黄鹏
@date 2024/02/27
@description: ---  消费者详情页面（新版）
-->
<template>
    <link-page class="account-item-page" v-show="isCompleted">
        <lnk-taps :taps="subObjOptions"
                  v-model="subObjActive"
                  :showMore="true"
                  pageFrom="consumerList"
                  @switchTab="switchTab"></lnk-taps>
        <view class="blank" id="blank"></view>
        <basic-info v-if="isCompleted && subObjActive.val === 'basicInfo'"
                    :account-item="accountItem"
                    :consumer_display_control="consumer_display_control"
                    :show-button="showButton"
                    :high-member-item="highMemberItem"
                    :custom-data="customFieldsData"
                    @openRedistribution="openRedistribution"
                    :page-from="pageFrom"
                    @fresh="init"></basic-info>
        <touch-record v-else-if="subObjActive.val === 'kanban1'"
                      :account-item="accountItem"
                      :isAllData="true"
                      @show-rule="showRule" id="k-all" key="k-all"></touch-record>
        <touch-record v-else-if="subObjActive.val === 'touchRecord'"
                      :account-item="accountItem"
                      @show-rule="showRule" id="sig" key="sig"></touch-record>
        <!--裂变模型-->
        <net-relation v-else-if="subObjActive.val === 'xuni'" :account-item="accountItem" :pageSecurity="pageOauth"></net-relation>
        <conversion-record v-else-if="subObjActive.val === 'conversionRecord'"
                           :account-item="accountItem"
                           @show-rule="showRule"></conversion-record>
        <award-record v-else-if="subObjActive.val === 'awardRecord'"
                      :account-item="accountItem"
                      @show-rule="showRule"></award-record>
        <consumer-rights v-else-if="subObjActive.val === 'rights'"
                         :account-item="accountItem"
                         @show-rule="showRule"></consumer-rights>
                         <!-- 标签 -->
        <standardLabelsComsumer v-else-if="subObjActive.val === 'biaoqianliebiao'"
                         :account-item="accountItem"
                         :type="'label,statistic'"
                        :tagGroups="tagLabels"
                        :consumerId="accountItem.id"
                        :phoneNumber="accountItem.phoneNumber"
                        :companyId="accountItem.belongToCompanyId"
                         @show-rule="showRule"></standardLabelsComsumer>
        <apply-record v-else-if="subObjActive.val === 'applyRecord'" :account-item="accountItem"></apply-record>
        <filter-model v-else-if="subObjActive.val === 'shaixuan1'" :account-item="accountItem" :pageAuth="pageOauth"></filter-model>
        <!--生命旅程-->
        <life-journey v-else-if="subObjActive.val === 'zongbuhuodongliebiao'" :account-item="accountItem"></life-journey>
<!--        <life-journey v-else-if="subObjActive.val === 'lifeJourney'" :account-item="accountItem"></life-journey>-->
        <six-model v-else-if="subObjActive.val === 'sixModel'" :account-item="accountItem"></six-model>
        <value-assessment v-else-if="subObjActive.val === 'kucunpandian1'" :account-item="accountItem"></value-assessment>
        <account-schedule v-else-if="subObjActive.val === 'renzheng'" :account-item="accountItem"></account-schedule>
        <orderManage v-else-if="subObjActive.seq === '14'" :account-item="accountItem"></orderManage>
        <view v-if="showButton">
            <link-fab-button icon="icon-retweet" :bottom="50" @tap="()=>redistribution()"
                             v-if="typeof isBusinessAgent === 'boolean' && !isBusinessAgent && userInfo.coreOrganizationTile.brandCompanyCode !== '1210'"/>
            <!-- <link-fab-button icon="icon-biaoqianliebiao" :bottom="170" @tap="()=>checkLabels()"
                             v-if="typeof isBusinessAgent === 'boolean' && !isBusinessAgent"/> -->
            <link-fab-group v-if="isBusinessAgent">
                <!-- <link-fab-item icon="icon-biaoqianliebiao" label="标签" @tap-icon="()=>checkLabels()"/> -->
                <link-fab-item icon="icon-retweet" v-if="userInfo.coreOrganizationTile.brandCompanyCode !== '1210'" label="重新分配" @tap-icon="()=>redistribution()"/>
                <link-fab-item label="身份等级变更" icon="icon-change" v-if="memberOperateType !== 'apply'"
                                @tap-icon="()=>changeLevel()"/>
                <link-fab-item :label="memberOperateType === 'apply'?'超高会员申请': '超高会员作废'"
                                :icon="memberOperateType === 'apply'?'icon-ceshishenqing' : 'icon-zuofei'"
                                @tap-icon="()=>applyOrInvalid()"/>
            </link-fab-group>
        </view>
        <!-- <link-fab-button icon="icon-biaoqianliebiao" :bottom="170" @tap="()=>checkLabels()"/> -->
        <link-dialog v-model="ruleDlg" :title="ruleTitle" height="80vh">
            <scroll-view scroll-y="true" :style="{'height': 'calc(80vh - 100px)'}">
                <rich-text ref="contentHtml" :nodes="nodes"></rich-text>
            </scroll-view>
            <link-button slot="foot" @tap="ruleDlg = false">关闭</link-button>
        </link-dialog>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
    import LnkTaps from "../../core/lnk-taps/lnk-taps";
    import Taro from "@tarojs/taro";
    import basicInfo from './components/account-sub-item/basic-info';
    import touchRecord from './components/account-sub-item/touch-record';
    import consumerRights from './components/account-sub-item/consumer-rights';
    import applyRecord from './components/account-sub-item/apply-record';
    import conversionRecord from './components/account-sub-item/conversion-record';
    import awardRecord from './components/account-sub-item/award-record';
    import waterMark from '../../lzlj/components/water-mark';
    import lifeJourney from './components/account-sub-item/life-journey';
    import netRelation from "./components/account-sub-item/net-relation.vue";
    import filterModel from './components/account-sub-item/filter-model.vue';
    import sixModel from './components/account-sub-item/six-model.vue';
    import standardLabelsComsumer from "src/pages/lj-consumers/people-screen/components/standard-labels-view-comsumer.vue";
    import valueAssessment from './components/account-sub-item/value-assessment.vue';
    import accountSchedule from './components/account-sub-item/account-schedule.vue';
    import orderManage from './components/account-sub-item/order-manage.vue';

    export default {
        name: "account-item-page",
        components: {
            LnkTaps,
            basicInfo,
            touchRecord,
            consumerRights,
            applyRecord,
            filterModel,
            conversionRecord,
            awardRecord,
            waterMark,
            lifeJourney,
            netRelation,
            standardLabelsComsumer,
            sixModel,
            valueAssessment,
            orderManage,
            accountSchedule
        },
        data() {
            let subObjOptions = [
                {name: "基础信息", seq: "1", val: "basicInfo"},
                // {name: "触达记录(汇总)", seq: "15", val: "kanban1"}, //配置公司内要展示
                {name: "触达记录", seq: "2", val: "touchRecord"},
                {name: "转化记录", seq: "3", val: "conversionRecord"},
                {name: "中奖记录", seq: "4", val: "awardRecord"},
                // 消费者详情顶部taps需要一个icon，暂时用总部活动icon代替
                {name: "生命旅程", seq: "7", val: "zongbuhuodongliebiao"},
                {name: "裂变模型", seq: "8", val: "xuni"},
                {name: "筛选模型", seq: "9", val: "shaixuan1"},
                {name: "六到模型", seq: "10", val: "sixModel"},
                {name: "审批记录", seq: "6", val: "applyRecord"},
                {name: "投放权益", seq: "5", val: "rights"},
                {name: "标签", seq: "11", val: "biaoqianliebiao", displayControl: true},
                {name: "价值评估模型", seq: "12", val: "kucunpandian1"},
                {name: "消费者排期", seq: "13", val: "renzheng"},
                {name: "订单管理", seq: "14", val: "file-text"}, // 14不能变,icon用的老icon
            ]
            const pageFrom = this.pageParam.pageFrom || '';
            const accountItem = {
                ...this.pageParam.data
            };
            const pageOauth = this.pageParam.pageOauth
            const switchObj = this.pageParam.switchObj
            return {
                pageOauth,                              // 安全性
                subObjOptions,                          // tab配置信息
                pageFrom,                               // 页面来源
                switchObj,                              // 页面来源指定tab信息
                accountItem,                            // 页面来源消费者信息
                userInfo: {},                           // 登陆人信息
                subObjActive: {},                       // 当前选中的tab信息
                customFieldsData: {},                   // 消费者自定义字段数据
                highMemberItem: {},                     // 超高身份数据
                isBusinessAgent: null,                  // 是否权限业代
                transferType: '',                       // 转交操作
                businessId: '',                         // 超高身份ID
                invalidReason: '',                      // 作废原因
                memberOperateType: '',                  // 超高价值会员申请或作废
                isCompleted: false,                     // 拿到消费者数据并处理完成
                tagLabels: [],                          // 消费者标签数据
                showButton: false,                      // 是否显示编辑、分配、转交按钮
                companyName: '',                        // 消费者所属公司类型
                empOption: new this.AutoList(this, {
                    module: this.$env.appURL + '/link/position',
                    param: {
                        rows: 25,
                        attr1: 'queryPostnUnderOrg',
                        attr2: '',
                        attr3: 'SalesRegion',
                        filtersRaw: []
                    },
                    searchFields: ['fstName', 'postnName', 'orgName'],
                    sortOptions: null,
                    hooks: {
                        async beforeLoad(option) {
                            option.param.attr2 = this.userInfo.coreOrganizationTile['l3Id'];
                            const orgTypes = ['SalesRegion', 'SalesCity', 'SalesZone', 'SalesArea', 'Dealer', 'SalesCompany', 'SalesTeam'];
                            if (orgTypes.includes(this.userInfo.orgType)) {
                                option.param.attr2 = this.userInfo.coreOrganizationTile['l5Id'] || this.userInfo.coreOrganizationTile['l3Id'];
                            }
                            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
                            let str = '';
                            lovData.forEach((item) => {
                                str = item.val + ',' + str
                            });
                            str = str.substring(0, str.length - 1);
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'},
                                {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='}
                            ];
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.fstName} data={data} note={data.postnName}
                                  desc={data.orgName}>
                                <link-checkbox val={data.id} toggleOnClickItem slot="thumb"/>
                            </item>
                        )
                    }
                }),
                ruleDlg: false,
                ruleTitle: '',
                consumer_display_control: 'N',
                nodes: '',
                configStr: 'N'
            }
        },
        async created() {
            this.$utils.showLoading();
            this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
            this.subObjActive = this.subObjOptions[0];
            this.setConsumer_display_control();
            // 查询企业参数配置-参数键consumer_display_control
            this.configStr = await this.$utils.getCfgProperty('consumer_display_control');
            console.log('configStr', this.configStr);
            await this.init();
            await this.querySalesman();
            // 查询是否配置展示触达记录汇总
            const WECHAT_TOUCH_SUMMARY = await this.$utils.getCfgProperty('WECHAT_TOUCH_SUMMARY');
            console.log(WECHAT_TOUCH_SUMMARY,'WECHAT_TOUCH_SUMMARY',this.userInfo.coreOrganizationTile['l3Id'])
            if(WECHAT_TOUCH_SUMMARY.includes(this.userInfo.coreOrganizationTile['l3Id'])){
            this.subObjOptions.splice(1,0,
                {name: "触达记录(汇总)", seq: "999", val: "kanban1"},
            );
            }
        },
        methods: {
            /**
             *  初始化数据,通过参数配置设置字段(单位名称/职务/详细地址)和tab是否不展示
             *  <AUTHOR>
             *  @date        2025-05-7
             */
            async setConsumer_display_control(){
                const consumer_display_control = await this.$utils.getCfgProperty('consumer_display_control');
                console.log(consumer_display_control, 'consumer_display_control');
                this.consumer_display_control = consumer_display_control;
                if(consumer_display_control=='Y'){
                    this.subObjOptions = this.subObjOptions.filter(e=>!e.displayControl)
                }
            },
            /**
             *  初始化数据
             *
             *  <AUTHOR>
             *  @date        2020-07-27
             */
            async init() {
                this.queryConsumerTags();
                await this.initAccountInfo(); // 这里顺手把客户也从数据库更新下来了
                this.initSwitch();
            },
            /**
             * @desc 查询消费者标签
             * <AUTHOR>
             * @date 2022/8/8 16:49
             **/
            async queryConsumerTags() {
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    this.$showError('请检查消费者数据是否正确');
                    return;
                }
                const data = await this.$http.post(this.$env.dmpURL + '/link/consumerTags/queryTagsByConsumerIdNew', {
                    acctId: this.accountItem.id,
                    ifActive: 'Y',
                    pageFlag: false
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询标签数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.tagLabels = data.rows;
                }
            },
            /**
             * 查询客户详情信息
             * <AUTHOR>
             * @date 2020-10-13
             */
            async initAccountInfo() {
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    this.$utils.hideLoading();
                    return
                }
                if(this.pageParam.pageFrom === 'OpenBottle' || this.pageParam.pageFrom === 'OpenFollowed') {
                    this.accountItem.id = this.pageParam.data.consumerId;
                }
                // 新增消费者排期运营-审批
                if(this.pageParam.pageFrom === 'consumerSchedule') {
                    this.accountItem.id = this.pageParam.data.acctId;
                }
                const data = await this.$http.post(this.$env.dmpURL + '/action/link/cdcPubConsumer/queryOwnEditById', {
                    id: this.accountItem.id,
                    inTerminal: 'Y'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询标签数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    for (let key in data.result) {
                        if (this.$utils.isEmpty(data.result[key])) {
                            data.result[key] = null;
                        }
                    }
                    this.accountItem = data.result;
                    // 250318取消该逻辑——如果this.accountItem.identityId为空，则隐藏价值评估模型tab页签（subObjOptions中val为kucunpandian1的tab）
                    // if (this.$utils.isEmpty(this.accountItem.identityId)) {
                    //     this.subObjOptions = this.subObjOptions.filter(item => item.val !== 'kucunpandian1');
                    // }
                    // console.log('identityId', this.accountItem.identityId, !!this.accountItem.identityId);
                    if (this.accountItem.belongToBrand && typeof this.accountItem.belongToBrand === 'string') {
                        this.accountItem.belongToBrand = this.accountItem.belongToBrand.split('/')
                    }
                    this.accountItem.identityLevel = this.pageParam.data.identityLevel;
                    this.accountItem.endTime = this.pageParam.data.endTime;
                    if (this.$utils.isNotEmpty(data.result.extendField)) {
                        const customDataTemp = JSON.parse(data.result.extendField);
                        if (!this.$utils.isEmpty(customDataTemp)) {
                            this.$set(this, 'customFieldsData', customDataTemp);
                        }
                    }
                    if (!this.$utils.isEmpty(data.memberData)) {
                        this.accountItem.loyaltyLevel = data.memberData['consumerGrade']
                    }
                    this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', this.accountItem.belongToCompanyId);
                    if (this.userInfo.postnId === this.accountItem.postnId.replace(/\s+/g, '')) {
                        this.showButton = true
                    }
                    if (this.pageFrom === 'AuthenticationApply' || this.pageFrom === 'ConsumerOverviewItem' || this.pageFrom === 'AccountChangeApply' || this.pageFrom === 'HighMemberItemEdit' || this.pageFrom === 'OpenBottle' || this.pageFrom === 'OpenFollowed') {
                        this.showButton = false;
                    }
                    this.isCompleted = true;
                    this.$utils.hideLoading();
                }
            },
            /**
             *  @description: 切换tab页签页面滚动
             *  @author: 马晓娟
             *  @date: 2020/11/16 15:23
             */
            switchTab(val, key, flag) {
                wx.pageScrollTo({
                    selector: `#blank`,
                    duration: 50
                })
                this.subObjActive = val;
            },
            /**
             * @desc 特殊页面跳转过来切换到指定tab
             * <AUTHOR>
             * @date 2022/4/14 11:00
             **/
            initSwitch() {
                setTimeout(() => {
                    if (this.switchObj) this.switchTab(this.switchObj, Number(this.switchObj.seq) - 1)
                }, 500)
            },
            /**
             * @desc 查询权限业代数据
             * <AUTHOR>
             * @date 2023/6/14 15:52
             **/
            async querySalesman() {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/link/identityEmp/queryByExamplePage', {
                    oauth: 'ALL',
                    filtersRaw: [
                        {id: 'waitstaffType', property: 'waitstaffType', value: 'BusinessAgent', operator: '='},
                        {id: 'waitstaffStatus', property: 'waitstaffStatus', value: 'Y', operator: '='},
                        {
                            id: 'waitstaffPostnId',
                            property: 'waitstaffPostnId',
                            value: this.userInfo.postnId,
                            operator: '='
                        }
                    ],
                    page: 1,
                    rows: 10
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询权限人员数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.isBusinessAgent = data.rows.length > 0;
                    if (this.isBusinessAgent) {
                        await this.queryMemberAppRecord();
                    }
                }
            },
            /**
             * @desc
             * <AUTHOR>
             * @date 2023/6/20 11:19
             **/
            async queryMemberAppRecord() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/highIdentity/queryByExamplePage', {
                    filtersRaw: [
                        {
                            id: 'identityStatus',
                            property: 'identityStatus',
                            value: '[Effective,InApproval]',
                            operator: 'IN'
                        },
                        {id: 'consumerId', property: 'consumerId', value: this.accountItem.id, operator: '='}
                    ]
                });
                if (data.success) {
                    this.memberOperateType = data.rows.length <= 0 ? 'apply' : 'invalid';
                    this.highMemberItem = data.rows[0] || {};
                    this.businessId = this.highMemberItem.id || '';
                }
            },
            /**
             * @desc 重新分配
             * <AUTHOR>
             * @date 2021/7/23 14:45
             **/
            async redistribution() {
                const item = await this.$object(this.empOption, {
                    showInDialog: true,
                    beforeConfirm: async (row) => {
                        //判断消费者是否在排期中
                        const res = await this.$http.post(this.$env.dmpURL + '/link/consumerScheduleTarget/checkConsumerSchedule', {
                            acctId: this.accountItem.id
                        });
                        if (!!res.result) {
                            this.$dialog({
                                title: '提示',
                                content: '当前消费者正排期运营中，是否确认重新分配消费者跟进人？',
                                confirmText: '确定',
                                onConfirm: async () => {
                                    // 确定后继续执行后续逻辑
                                    await this.reDistribute(row.postnId);
                                },
                                cancelButton: true
                            });
                        } else {
                            // res.result返回false执行之前的逻辑
                            await this.reDistribute(row.postnId);
                        }
                    }
                });
            },
            /**
             * @desc 重新分配
             * <AUTHOR>
             * @date 2025-04-11
             **/
            async reDistribute(postnId) {
                try {
                    const data = await this.$http.post(this.$env.dmpURL + '/action/link/consumer/distribution', {
                        id: this.accountItem.id,
                        postnId: postnId,
                        interfaceSource: 'Artificial'
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            setTimeout(() => {
                                this.$showError(`重新分配失败：${response.result}`);
                            }, 500);
                        }
                    });
                    if (data.success) {
                        setTimeout(() => {
                            this.$message.success('重新分配成功！');
                        }, 1000);
                        this.$nav.back();
                    }
                } catch (error) {
                    console.error('接口调用失败:', error);
                }
            },
            async openRedistribution() {
                this.$bus.$emit('openRedistribution', this.pageParam.data, 'AccountItem');
            },
            /**
             * @desc 查看标签
             * <AUTHOR>
             * @date 2023/9/12 10:07
             **/
            checkLabels() {
                this.$nav.push('/pages/lj-consumers/people-screen/people-screen-item-page', {
                    tagList: this.tagLabels,
                    consumerId: this.accountItem.id,
                    phoneNumber: this.accountItem.phoneNumber,
                    companyId: this.accountItem.belongToCompanyId,
                    type: 'label,statistic',
                    title: '消费者'
                })
            },
            /**
             * @desc 会员级别变更
             * <AUTHOR>
             * @date 2023/6/5 16:35
             **/
            async changeLevel() {
                const data = Object.assign({}, this.highMemberItem, {
                        socialStatus: null,
                        acctRank: null,
                        socialCircle: null,
                        positionGrade: null,
                        enterpriseLevel: null,
                        enterpriseSize: null,
                        personnelType: null
                    }
                );
                this.$nav.push('/pages/lj-consumers/high-member/high-member-item-edit-page', {
                    accountItem: this.accountItem,
                    data: data,
                    pageFrom: 'AccountItem',
                    applyType: 'salesmanChange'
                });
            },
            /**
             * @desc 超高价值会员申请或作废
             * <AUTHOR>
             * @date 2023/6/5 16:36
             **/
            async applyOrInvalid() {
                if (this.memberOperateType === 'apply') {
                    let currentDate = new Date();
                    let year = currentDate.getFullYear();
                    let date = new Date(year + '/' + '12/01 00:00:00');
                    let endTime = this.$date.filter(new Date(year + '/' + '12/31 23:59:59'), 'YYYY-MM-DD HH:mm:ss');
                    if (date <= currentDate) {
                        year = Number(year) + 1;
                        endTime = this.$date.filter(new Date(year + '/' + '12/31 23:59:59'), 'YYYY-MM-DD HH:mm:ss');
                    }
                    const id = await this.$newId();
                    const data = {
                        id: id,
                        mobilePhone: this.accountItem.phoneNumber,
                        acctName: this.accountItem.name,
                        consumerId: this.accountItem.id,
                        fstName: this.accountItem.fstName,
                        socialStatus: null,
                        acctRank: null,
                        socialCircle: null,
                        positionGrade: null,
                        enterpriseLevel: null,
                        enterpriseSize: null,
                        personnelType: null,
                        endTime: endTime
                    };
                    this.$nav.push('/pages/lj-consumers/high-member/high-member-item-edit-page', {
                        accountItem: this.accountItem,
                        pageFrom: 'AccountItem',
                        data: data,
                        applyType: 'salesmanApply'
                    });
                } else {
                    if(this.configStr === 'Y') {
                        this.$dialog({
                            title: '提示',
                            content: (h) => {
                                return (
                                    <view>
                                        <view>作废后消费者不再享有超高价值权益，确认失效？</view>
                                    </view>
                                )
                            },
                            // confirmText: '提交作废申请',
                            cancelButton: true,
                            onConfirm: async () => {
                                // 调用作废接口作废当前消费者超高价值身份
                                await this.invalidHighMember();
                            }
                        })
                    } else {
                        this.$dialog({
                            title: '提示',
                            content: (h) => {
                                return (
                                    <view>
                                        <view>作废后消费者不再享有超高价值权益，确认失效？</view>
                                        <link-form-item required label="作废原因" vertical>
                                            <link-textarea placeholder="请填写申请说明，100字以内" padding-start padding-end
                                                           v-model={this.invalidReason} nativeProps={{maxlength: 300}}>
                                            </link-textarea>
                                        </link-form-item>
                                    </view>
                                )
                            },
                            confirmText: '提交作废申请',
                            cancelButton: true,
                            onConfirm: async () => {
                                // 调用作废接口作废当前消费者超高价值身份
                                await this.invalidHighMember();
                            }
                        })
                    }
                }
            },
            /**
             * @desc 业代作废超高价值会员身份
             * <AUTHOR>
             * @date 2023/6/14 10:31
             **/
            async invalidHighMember() {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.appURL + '/link/identityAppRecord/businessExpire', {
                    businessId: this.businessId,
                    reason: this.invalidReason
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`作废失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.$utils.hideLoading();
                    this.$message.success('提交作废申请成功！');
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/03/11
             * @methods: showRule
             * @para:
             * @description:
             **/
            showRule(nodes, title) {
                this.nodes = nodes;
                this.ruleTitle = title;
                this.ruleDlg = true;
            }
        }
    }
</script>

<style lang="scss">
    .account-item-page {
        min-height: 100vh;
        background-color: #F2F2F2;
        font-size: 28px;

        .blank {
            width: 100%;
            height: 96px;
            background: #F2F2F2;
        }

        .main-content-con {
            /*height: calc(100vh - 120px);*/
            /*margin-top: 92px;*/
        }
    }
</style>
