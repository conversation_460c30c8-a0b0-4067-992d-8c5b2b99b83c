<template>
    <link-page class="account-item-edit-page">
        <!--审批历史记录-->
        <approval-history-point-v3 :approvalId="pageParam.approvalId" v-if="!$utils.isEmpty(pageParam.approvalId) && !isFanwei && pageParam.apVersion === 'v3'"></approval-history-point-v3>
        <approval-history-point :flowObjId="businessDataId" v-if="!$utils.isEmpty(businessDataId) && !isFanwei && ($utils.isEmpty(pageParam.apVersion) || pageParam.apVersion !== 'v3')"></approval-history-point>
        <!--消费者基础字段-->
        <essential-info ref="essentialInfo"
                        :appStatus="appStatus"
                        v-show="isCompleted"
                        :isEditFlag="isEditFlag"
                        :isFromAccountList.sync="isFromAccountList"
                        :cacheData="cacheData"
                        :fromCache="pageParam.__fromCache"
                        :standarFields="standarFields"
                        :formRules.sync="formRules"
                        :essentialData="formData"
                        :pageFrom="pageFrom"
                        :allConTypeList="allConTypeList"
                        :kvALLFields.sync="kvALLFields"
                        :newKFields.sync="newKFields"
                        :readonly-flag="readonlyFlag"
                        :form-overall-readonly.sync="formOverallReadonly"
                        :basicAddressFlag="basicAddressFlag"
                        :invaliLocFlag="invaliLocFlag"
                        @initData="initData"
                        @showOldCustomPoster="showOldCustomPoster"
                        @submitApproval="submitApproval"
                        @updateBasicAddrFlag="updateBasicAddrFlag"
                        @saveAccount="saveAccount"/>
        <!--标签信息-->
        <view v-show="isCompleted">
            <line-title title="标签信息" :buttonName="buttonName" @tap="btnTap"/>
            <view :style="{'height':$device.isIphoneX ? '100px':'80px', 'width': '100%'}" v-if="customFields.length < 1"></view>
            <!--            <view class="label-info" v-for="(labelItem, labelIndex) in tagLabels" :key="labelIndex + 'label'">
                            <view class="group-label">
                                <view class="label-data">{{labelItem.tagGroupName}}</view>
                            </view>
                            <view class="label-data-info" v-if="labelItem['itemTags'] && labelItem['itemTags'].length > 0">
                                <view v-for="(itemTag, itemIndex) in labelItem['itemTags']" :key="itemIndex">
                                    <view v-if="itemTag['ifActive'] !== 'N'" class="label-item" >
                                        {{itemTag.tagName}}<view class="iconfont-operate" v-if="!readonlyFlag&&!formOverallReadonly" @tap="deleteItemTags(labelItem, itemTag, itemIndex)"><link-icon icon="mp-close"></link-icon></view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view :style="{'height':$device.isIphoneX ? '100px':'80px', 'width': '100%'}" v-if="customFields.length<=0"></view>-->
        </view>
        <!--消费者自定义字段-->
        <link-form :value="customFieldsData" ref="customFieldsData" v-show="isCompleted">
            <view v-for="(custItem, custIndex) in customFields" :key="custIndex + 'head'">
                <line-title :title="custItem.label"/>
                <view v-for="(item,index) in custItem.componentList" :key="index + 'cust'">
                    <link-form-item :label="item.values.fieldName"
                                    :field="item.values.field"
                                    :note="item.values.note"
                                    :readonly="item.values['readonly'] || formOverallReadonly"
                                    :rules="item.values.regCheck ? Validator.regexp({reg: new RegExp(item.values.regCheck), msg: `请输入正确的${item.values.fieldName}`}) : []"
                                    v-if="item.hide !== 'Y'"
                                    :vertical="['link-textarea', 'lnk-img-watermark'].includes(item.ctrlCode)"
                                    :required="item.values.required">
                        <!--自定义地址字段-->
                        <view class="address-color" v-if="item.ctrlCode === 'view-line'" @tap="customGetLocation(item)">
                            <view class="address-placeholder" style="color: #e0e0e0; width: 200%"
                            v-if="$utils.isEmpty(customFieldsData[item.values.field].province)">
                                请选择所在地区
                                <link-icon icon="icon-location" class="link-location"/>
                            </view>
                            <view class="address-color" v-if="!$utils.isEmpty(customFieldsData[item.values.field].province)"
                                  @tap="customGetLocation(item)">
                                <text v-if="!$utils.isEmpty(customFieldsData[item.values.field].province)">
                                    {{ customFieldsData[item.values.field].province }}
                                </text>
                                <text v-if="!$utils.isEmpty(customFieldsData[item.values.field].city)">
                                    /{{ customFieldsData[item.values.field].city }}
                                </text>
                                <text v-if="!$utils.isEmpty(customFieldsData[item.values.field].district)">
                                    /{{ customFieldsData[item.values.field].district }}
                                </text>
                                <text v-if="!$utils.isEmpty(customFieldsData[item.values.field].street)">
                                    /{{ customFieldsData[item.values.field].street }}
                                </text>
                            </view>
                        </view>
                        <view v-else-if="item.ctrlCode === 'lnk-img-watermark'">
                            <lnk-img-watermark :parentId="formData.id"
                                               moduleType="consumerattachment"
                                               :delFlag="true"
                                               :album="true"
                                               isZlFlag
                                               isCount
                                               :cos-delete="false"
                                               :pathKeyArray="imgList"
                                               :addLength="item.values.maxUploadNum"
                                               filePathKey="/consumerattachment/"
                                               @uploadImgPromise="uploadImgPromise"
                                               @imgDeleteSuccess="imgDeleteSuccess"
                                               ref="fakeTerminalPhoto"
                                               :isCompressByImgInfo="true"
                                               :newFlag="!disabledAddImgFlag(item)">
                            </lnk-img-watermark>
                            <view class="img-watermark-tips" v-if="disabledAddImgFlag(item)"><link-icon icon="icon-warning-circle"></link-icon>附件可维护数量已达上限，请联系管理员调整数量上限</view>
                        </view>
                        <account-detail-jsx-component v-else :option="item" :formData="customFieldsData"/>
                    </link-form-item>
                </view>
            </view>
            <view :style="{'height':$device.isIphoneX ? '100px':'80px', 'width': '100%'}" v-if="customFields.length > 0"></view>
        </link-form>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
        <!--新建消费者时使用的老客户弹窗-->
        <link-dialog ref="oldCustomerPoster" class="account-item-dialog" position="poster" @click-close-poster-icon="cancel">
            <view class="old-cus-dialog-view">
                <view class="old-cus-o">
                    <view class="o-left" v-if="oldCustomerItem.phoneNumber">
                        <image :src="oldCustomerItem|headImgAccount(oldCustomerItem)"/>
                    </view>
                    <view v-else class="o-left">
                        <image :src="ineffectiveAccount|headImgAccount(ineffectiveAccount)"/>
                    </view>
                    <view class="o-right">
                        <view class="name">{{oldCustomerItem.name || ineffectiveAccount.name}}</view>
                        <view class="tel">{{oldCustomerItem.phoneNumber || ineffectiveAccount.mobilePhone}}</view>
                    </view>
                </view>
                <view class="old-cus-t" v-if="oldCustomerItem.phoneNumber">
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-leixinghedengji"/>
                        </view>
                        <view class="title">影响力（K序列）等级</view>
                        <view class="val">
                            {{ oldCustomerItem.type | lov('ACCT_SUB_TYPE')}}
                        </view>
                    </view>
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-leixinghedengji"/>
                        </view>
                        <view class="title">购买力（V序列）等级</view>
                        <view class="val">
                            {{ oldCustomerItem.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}
                        </view>
                    </view>
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-jiaose"/>
                        </view>
                        <view class="title">业代</view>
                        <view class="val">{{oldCustomerItem.fstName}}</view>
                    </view>
                    <view class="t-left">
                        <view class="icon-v">
                            <link-icon icon="icon-hexinzhongduan"/>
                        </view>
                        <view class="title">所属终端</view>
                        <view class="val">{{oldCustomerItem.belongToStore}}</view>
                    </view>
                </view>
                <view class="old-cus-t" v-else style="height: 158rpx;">
                    <view class="t-left" style="text-align:center;" v-if="ineffectiveAccount.postnId=== userInfo.postnId">当前消费者新建审批中，不能重复新建</view>
                    <view class="t-left" style="text-align:center;"  v-else>当前消费者已由【{{ineffectiveAccount.fstName}}】业代跟进暂未生效，不能重复创建</view>
                </view>
                <view class="old-cus-s" v-if="isFromAccountList && oldCustomerItem.phoneNumber">
                    <view @tap="applicationTransfer('distribution')" class="pike-v">
                        <view class="title">申请跟进此客户</view>
                    </view>
                </view>
                <view v-else>
                    <view class="old-cus-s" v-if="isEditFlag  && oldCustomerItem.phoneNumber">
                        <view @tap="applicationTransfer('distributionAndSelect')" class="pike-v">
                            <view class="title">申请跟进并选择此客户</view>
                        </view>
                    </view>
                    <view class="old-cus-s" style="margin-top: 10px">
                        <view @tap="pick" class="cancel-v" v-if="oldCustomerItem.phoneNumber">
                            <view class="title">选择此客户</view>
                        </view>
                        <view @tap="gotoAccountInfo" class="cancel-v" v-else-if="ineffectiveAccount.postnId=== userInfo.postnId">
                            <view class="title">查看详情</view>
                        </view>
                    </view>
                </view>
            </view>
        </link-dialog>
        <link-dialog v-model="tagDialogFlag" position="bottom" height="85vh" noTitle class="group-dialog" noPadding>
            <view class="model-title">
                <view class="iconfont icon-left" @tap="tagDialogFlag = false"></view>
                <view class="title" style="padding-left:0;">{{tagDialogType?'已选择标签':'请选择标签'}}</view>
                <view class="iconfont icon-close" @tap="tagDialogFlag = false"></view>
            </view>
            <scroll-view scroll-y="true" :style="{'height': '100%'}">
                <standard-labels v-if="!tagDialogType"
                                 :allData.sync="formData"
                                 :showGenerate="false"
                                 @save="chooseTagGroup"
                                 :tagList="tagList"></standard-labels>
                <standard-labels-view v-else :tagList="tagList" :tag-groups="tagGroups"/>
                <view style="width: 100%; height: 50px;"></view>
            </scroll-view>
        </link-dialog>
<!--        <tag-info :user-info="userInfo" :show.sync="tagDialogFlag" :tag-group-list="tagGroupList" :tag-labels="tagLabels" @choose="chooseTagGroup"/>-->
    </link-page>
</template>

<script>
    import {ROW_STATUS} from "../../../utils/constant";
    import TitleLine from "../../lzlj-II/fighting-fakes/components/title-line";
    import {PageCacheManager} from "../../../utils/PageCacheManager";
    import {reverseTMapGeocoder} from "../../../utils/locations-tencent";
    import LineTitle from "../../lzlj/components/line-title";
    import AccountDetailJsxComponent from './account-detail-jsx-component';
    import ConsumerCommon from "../consumer-common";
    import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
    import EssentialInfo from "./components/essential-info/essential-info";
    import TagInfo from "./components/tag-info/tag-info";
    import StandardLabels from '../people-screen/components/standard-labels-new';
    import StandardLabelsView from '../people-screen/components/standard-labels-view-new';
    import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark";
    import ApprovalHistoryPointV3 from "../../lzlj/approval-v3/components/approval-history-point.vue";

    export default {
        name: "account-item-edit-page",
        components: {
            ApprovalHistoryPointV3,
            LnkImgWatermark,
            EssentialInfo,TagInfo, StandardLabels, StandardLabelsView,
            ApprovalHistoryPoint, LineTitle, TitleLine, AccountDetailJsxComponent},
        mixins: [ConsumerCommon()],
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            const pageFrom = this.pageParam.pageFrom;//活动：Activity， 消费者：Account， 未生效消费者：IneffectiveAccount， 宴席主家：masterActivity，名单提报：ListApply，拜访登记：visitRegisterItem，动销登记：BookedOrder，拜访申请：visitApplyItem，活动名单提报：ListReport,礼赠：visitPresent, 单客运营scheduleDetail
            const marketActivityId = this.pageParam.marketActivityId;//活动ID
            const activityType =  this.pageParam.activityType; // 活动类型
            const consumer_display_control =  this.pageParam.consumer_display_control; // //是否控制 'companyName','position','detailedAddress'展示与否
            const isFanwei =  this.pageParam.isFanwei; // 活动类型
            let readonlyFlag = false;
            let formOverallReadonly = false;//表单整体只读，鼎昊职位老客校验出来数据，前端页面做单独处理，禁用所有字段的编辑。
            let formData = {
                socialStatus: '',
                acctRank: '',
                followFlag: ''
            };
            const cacheData = PageCacheManager.getInitialData({
                ctx: this,
                path: 'lj-consumers/account/account-item-edit-page',
                title: '消费者-信息',
                initialData: {
                    formData,
                    recommendAccountList:[],
                    terminalList:[]
                },
            })
            if (!cacheData.formData) {
                cacheData.formData = formData;
            }
            if (cacheData.formData !== formData) {
                setTimeout(() => {
                    formData = cacheData.formData
                    if (!formData.socialStatus) {
                        this.$set(formData, 'socialStatus', '');
                    }
                }, 500)
            }
            return {
                type: '',
                consumer_display_control,
                // isApprovalV3: false, // 审批是否走V3逻辑
                tagGroups: [], // 审批标签展示数据
                imgList: [],          // 图片数组
                uploadNum: '',        // 总的图片已上传的数量
                tagDialogFlag: false, // 标签弹窗展示
                tagDialogType: '', // 查看标签 or 添加标签
                isCompleted: false,                         // 数据是否加载完成
                ineffectiveAccount: {},                     // 未生效消费者
                businessData: {},                           // 未生效消费者业务对象
                appStatus: '',                              // 未生效消费者审批状态
                businessDataId: '',                         // 未生效业务对象ID
                approvalId: null,                           // 审批ID
                tagLabels: [],                          // 自定义标签父
                chooseTagLabels: [], //当前选择标签
                copyContent: '',                        // 页面顶部复制内容
                timer: null,   // 计时器
                isFromAccountList: true,                // 是否来自消费者列表
                isEditFlag: false,                      // 判断是否在当前登录用户职位是否在ASSIGN_POSITION范围内
                isHadValue: true,
                cacheData,                               // 缓存信息
                formOverallReadonly,                     // 表单整体只读
                readonlyFlag,                            // 根据跟进人ID与业代ID进行比对，判断是否只读
                marketActivityId,                        // 市场活动ID
                activityType,                            // 活动类型
                isFanwei,                                // 是否是泛微流程,不展示审批历史,审批记录在泛微
                pageFrom,                                // 页面来源
                oldCustomerItem: {},                     // 新建消费者使用-老客对象信息
                userInfo,                                // 登录用户信息
                recommendAccountList: cacheData.recommendAccountList,      // 内部推荐人列表
                terminalList: cacheData.terminalList,     // 所属门店
                formRules: {                              // 表单校验规则
                },
                formData,                                  // 消费者对象
                basicAddressFlag: false,                   // 是否为基础字段的地址
                customAddressFlag: false,                  // 是否为自定义字段的地址
                tempCustomField: '',                       // 临时存储自定义字段的地址
                tagGroupNotShow :true,
                invaliLocFlag: false,                   // 不需要校验地址
                invalidateLocString: '', // 区域限制字符串，如香港特别行政区/澳门特别行政区...
                isGotoPreCreate: 'N'  // 是否跳转审批预览页面
            }
        },
        mounted() {
            this.$bus.$on('accountApplyPage', data => {
                if (data.BackpageFrom === "accountApplyPage") {
                    this.$refs.oldCustomerPoster.show();
                }
            })
        },
        destroyed () {
            this.$bus.$off('accountApplyPage');
            if(this.$refs.oldCustomerPoster){
                this.$refs.oldCustomerPoster.hide();
            }
            // 清除计时器
            clearInterval(this.timer);
        },
        async onShow(){
            const location = this.$locations.QQGetLocation();
            if(location){
                let addressInfo =  await reverseTMapGeocoder(location.latitude, location.longitude, '消费者编辑');
                this.$set(this.formData, 'attr01', location.longitude);
                this.$set(this.formData, 'attr02', location.latitude);
                try {
                    this.$utils.showLoading()
                    const addrCode = addressInfo['originalData'].result.addressComponent.adcode;
                    const data = await this.$http.post(this.$env.appURL +'/action/link/alladdress/queryEffectiveByDistrictCode',{addrCode: addrCode})
                    if(data.success) {
                        if(this.basicAddressFlag) {
                            if(data.adcodeFlag){
                                this.$set(this.formData, 'province', data.province);
                                this.$set(this.formData, 'city', data.city);
                                this.$set(this.formData, 'county', data.district);
                                this.$set(this.formData, 'street', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                                this.$utils.hideLoading();
                            }else {
                                this.$set(this.formData, 'province', addressInfo['originalData'].result.addressComponent.province);
                                this.$set(this.formData, 'city', addressInfo['originalData'].result.addressComponent.city);
                                this.$set(this.formData, 'county', addressInfo['originalData'].result.addressComponent.district);
                                this.$set(this.formData, 'street', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                                this.$utils.hideLoading();
                            }
                            this.basicAddressFlag = false;
                            if(this.invalidateLocString.indexOf(this.formData.province) !== -1){
                                this.invaliLocFlag = true;
                            } else {
                                this.invaliLocFlag = false;
                            }
                        }
                        if(this.customAddressFlag) {
                            if(data.adcodeFlag){
                                this.$set(this.customFieldsData[this.tempCustomField], 'province', data.province);
                                this.$set(this.customFieldsData[this.tempCustomField], 'city', data.city);
                                this.$set(this.customFieldsData[this.tempCustomField], 'district', data.district);
                                this.$set(this.customFieldsData[this.tempCustomField], 'street', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                                this.$utils.hideLoading();
                            }else {
                                this.$set(this.customFieldsData[this.tempCustomField], 'province', addressInfo['originalData'].result.addressComponent.province);
                                this.$set(this.customFieldsData[this.tempCustomField], 'city', addressInfo['originalData'].result.addressComponent.city);
                                this.$set(this.customFieldsData[this.tempCustomField], 'district', addressInfo['originalData'].result.addressComponent.district);
                                this.$set(this.customFieldsData[this.tempCustomField], 'street', addressInfo['originalData'].result.addressComponent.street ? addressInfo['originalData'].result.addressComponent.street : '');
                                this.$utils.hideLoading();
                            }
                            this.tempCustomField = null;
                            this.customAddressFlag = false;
                        }
                    }
                    this.$utils.hideLoading();
                } catch (e) {
                    this.$utils.hideLoading();
                }
            }
        },
        async created() {
            await this.setConsumer_display_control();
            this.$locations.QQClearLocation();
            this.$utils.showLoading()
            // 清空标签选择数据
            this.$store.commit('consumerLabel/setTagList', []);
            this.$store.commit('consumerLabel/setSelectItem', {});
            this.$store.commit('consumerLabel/setSecondSelect', {});
            console.log('this.pageFrom',this.pageFrom);
            // 新建进来不调用,在列表调用
            if (this.pageFrom ==='Account'){
                this.kvALLFields = this.$store.getters['consumerLabel/getKvALLFields'];
                this.newKFields = this.$store.getters['consumerLabel/getNewKFields'];
            }
            console.log(this.newKFields,'newKFields');
            this.initLevelData();

            // 结合消费者界面对象缓存需要，等待一秒钟初始化完成之后，对比缓存的数据和当前界面的数据是否一致，created等待1.5秒后执行
            this.timer = setTimeout(async() => {
                if (this.pageFrom === 'IneffectiveAccount') {
                    // 未生效消费者列表
                    this.businessDataId = this.pageParam.businessDataId;
                    this.appStatus = this.pageParam.appStatus;
                    if (this.appStatus !== 'Reject' && this.appStatus !== 'New') {
                        this.formOverallReadonly = true;
                    } else {
                        this.formData.row_status = ROW_STATUS.NEW;
                    }
                    await this.checkFieldsInfo('', this.pageParam.orgId);
                    await this.queryFieldValueById();
                    await this.queryTagGroups();
                } else {
                    // 变更审批拒绝后重新编辑和消费者编辑和其他情况
                    this.formData = this.$utils.deepcopy(this.pageParam.data);
                    if (this.formData.row_status !== ROW_STATUS.NEW && !this.pageParam.__fromCache) {
                        if (this.$utils.isEmpty(this.formData.sourceFrom)) this.isHadValue = false
                        // 查询在变更审批中的字段，不可编辑
                        await this.queryChangeFieldList(this.formData.id);
                    }
                    const postnTypeArr = await this.$lov.getLovByType('ASSIGN_POSITION');
                    const postnData = postnTypeArr.filter((item) => item.val === this.userInfo.positionType);
                    if (postnData.length > 0 && this.userInfo.coreOrganizationTile.brandCompanyCode !== '1210') {
                        this.isEditFlag = true
                    }
                    if (!this.$utils.isEmpty(this.formData.postnId) && this.formData.postnId.replace(/\s+/g, '') !== this.userInfo.postnId) {
                        this.readonlyFlag = true;
                    }
                    await this.queryConsumerTags();
                    if (this.formData.row_status === ROW_STATUS.UPDATE) {
                        if (this.formData['inTerminal'] === 'Y') {
                            this.formData['terminalFlag'] = 'Y'
                        }
                        await this.checkFieldsInfo('edit');
                    } else {
                        await this.checkFieldsInfo();
                    }
                    // 在列表界面提前调用
                    if (this.pageFrom ==='Account'){
                        this.tagGroupList = this.$store.getters['consumerLabel/getTagGroupList']
                    }else {
                        await this.queryTagGroups();
                    }
                    if (this.$utils.isNotEmpty(this.formData.extendField)) {
                        const customDataTemp = JSON.parse(this.formData.extendField);
                        if (!this.$utils.isEmpty(customDataTemp)) {
                            this.customFieldsData = customDataTemp;
                            this.dealCustomFieldsData();
                        }
                    }
                    this.isCompleted = true;
                }
                const tempData = Object.assign({},this.copyFormData, this.formData);
                this.formData = this.$utils.deepcopy(tempData);
                this.queryCusAttachment()
                this.queryImgTotal()
                // 如果this.formData包含brand字段，brand可能有值也可能为null，删除brand字段
                if (this.formData.hasOwnProperty('brand')) {
                    delete this.formData.brand;
                }
                // 编辑
                if(!this.formData.corporateId && !!this.formData.companyName) {
                    this.$refs.essentialInfo.disableCompanyFlag = false;
                }
                this.invalidateLocString = await this.$utils.getCfgProperty('REGIONAL_RESTRICTION');
                await this.isInvalidLoc(this.formData);
                this.$utils.hideLoading()
            }, 1000)

        },
        computed: {
          buttonName() {
              if(this.readonlyFlag || this.formOverallReadonly){
                  return [{name: '查看', val: 'view'}];
              } else {
                  if(this.tagLabels?.length > 0){
                      return [{name: '查看', val: 'view'}, {name: '添加'}]
                  }else{
                      return '添加';
                  }
              }
          },
            tagList() {
               return this.chooseTagLabels.map((item) =>item.tagId);
            }
        },
        methods: {
             /**
             *  初始化数据,通过参数配置设置字段(单位名称/职务/详细地址)和tab是否不展示
             *  <AUTHOR>
             *  @date        2025-05-7
             */
             async setConsumer_display_control(){
                const consumer_display_control = await this.$utils.getCfgProperty('consumer_display_control');
                console.log(consumer_display_control, 'consumer_display_control');
                this.consumer_display_control = consumer_display_control;
                if(consumer_display_control=='Y'){
                    // this.subObjOptions = this.subObjOptions.filter(e=>!e.displayControl)
                }
            },
            /**
             * @author:  何春霞
             * @desc 判断是否为不需要校验地址
             * @param: 当前表单数据
             * @date:  2024-11-08
             */
            async isInvalidLoc(data) {
               // 如果this.invalidateLocString字符串为空，重新获取参数配置
                if(this.invalidateLocString === ''){
                    this.invalidateLocString = await this.$utils.getCfgProperty('REGIONAL_RESTRICTION');
                }
                if(this.invalidateLocString.indexOf(data.province) !== -1){
                    this.invaliLocFlag = true;
                } else {
                    this.invaliLocFlag = false;
                }
            },
            updateBasicAddrFlag(newValue) {
                this.basicAddressFlag = newValue;
            },
            async customGetLocation(item) {
                if(item.type === 'ProvincialCity') {
                    this.tempCustomField = item.values.field;
                }
                const addressInfo = await this.$locations.getAddress();
                this.customAddressFlag = true;
                await this.$locations.chooseLocation(addressInfo.wxMarkerData[0].latitude, addressInfo.wxMarkerData[0].longitude);
            },
            btnTap(type){
                this.tagDialogFlag=true;
                this.tagDialogType = type;
            },
            /**
             * 查询已上传的图片总数
             * @author:  胡益阳
             * @date:  2024/7/29
             */
            queryImgTotal () {
                try {
                    this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`,
                        {
                            pageFlag: false,
                            rows: 50,
                            page: 1,
                            dmpUrl: '/link/cusAttachment/queryByExamplePage',
                            order: 'desc',
                            sort: 'created',
                            filtersRaw: [{id: 'sourceId', operator: '=', property: 'sourceId', value: this.formData.id}]
                        }
                    ).then((res) => {
                        if (res.success) {
                            this.uploadNum = res.total;
                        }
                    })
                } catch (e) {}
            },
            /**
             * 是否禁用图片上传
             * @author:  胡益阳
             * @date:  2024/7/29
             */
            disabledAddImgFlag (e) {
                return this.uploadNum >= e.values.maxUploadNum;
            },
            /**
             * 消费者附件查询
             * @author:  胡益阳
             * @date:  2024/7/25
             */
            queryCusAttachment () {
                try {
                    this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`,
                        {
                            pageFlag: false,
                            rows: 50,
                            page: 1,
                            dmpUrl: '/link/cusAttachment/queryByExamplePage',
                            order: 'desc',
                            sort: 'created',
                            filtersRaw: [
                                {id: 'sourceId', operator: '=', property: 'sourceId', value: this.formData.id},
                                {id: 'uploadStatus', operator: 'in', property: 'uploadStatus', value: '[Effective, NewlyBuild]'}
                            ]
                        }
                    ).then((res) => {
                        if (res.success) {
                            this.imgList = res.rows;
                        }
                    })
                } catch (e) {}
            },
            /**
             * 删除图片的回调
             * @author:  胡益阳
             * @date:  2024/7/29
             */
            imgDeleteSuccess (e, img) {
                this.imgList = e
                this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`, {dmpUrl: '/link/cusAttachment/deletePicture', id: img.id}).then(() => {
                    this.queryImgTotal()
                })
            },
            /**
             * 图片上传cos成功钩子
             * @author:  胡益阳
             * @date:  2024/7/25
             */
            uploadImgPromise (e, k) {
                const param = e.newRow.map((item) => ({
                    sourceId: this.formData.id,
                    attachmentPath: item.attachmentPath,
                    templateId: this.templateId,
                    bigurl: item.bigurl || item.smallurl,
                    smallurl: item.smallurl
                }));
                 this.$http.post(`${this.$env.appURL}/action/link/cusAttachment/batchUpload`, param).then((res) => {
                     if (res.success) {
                         this.queryImgTotal()
                         this.imgList.forEach((item) => {
                             res.rows.forEach((k) => {
                                 if (item.attachmentPath === k.attachmentPath) {
                                     item.id = k.id
                                 }
                             })
                         })
                     }
                 })
            },
            /**
             * @desc 初始化查询
             * <AUTHOR>
             * @date 2023/3/16 16:44
             * @param data 自定义字段
             * @param formData 查询的消费者对象
             **/
            initData (data, formData) {
               if (!this.$utils.isEmpty(data)) {
                   this.customFieldsData = data;
                   this.dealCustomFieldsData();
               }
               if (formData) {
                   this.formData = formData;
                   this.isInvalidLoc(this.formData);
                   this.queryConsumerTags();
                   this.queryCusAttachment()
               }
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/11/30
             * @methods: dealCustomFieldsData
             * @para:
             * @description: 处理自定义字段的数据
             **/
            dealCustomFieldsData () {
                const customFields = this.customFields.map((item) => item.componentList).flat();
                const oldKeys = Object.keys(this.customFieldsData);
                const newKeys = customFields.map((item) => item.values.field);
                // 已经没有的自定义字段，已经存值的数据删除
                oldKeys.forEach((key) => {
                    if (!newKeys.includes(key)) {
                        delete this.customFieldsData[key];
                    }
                })
                // 如果有自定义地址字段，没有值的时候需要赋值{}
                customFields.forEach((field) => {
                    if (field.ctrlCode === 'link-address' && !this.customFieldsData[field.values.field]){
                        this.$set(this.customFieldsData, field.values.field, {});
                    }
                })
                customFields.forEach((field) => {
                    if (field.ctrlCode === 'view-line' && !this.customFieldsData[field.values.field]){
                        this.$set(this.customFieldsData, field.values.field, {
                            province: '',
                            city: '',
                            district: '',
                            street: ''
                        });
                    }
                })
            },
            /**
             * @desc 显示老客弹窗或未生效消费者详情
             * <AUTHOR>
             * @date 2023/3/16 16:39
             * @param  data 老客数据
             * @param type 用来区分未生效消费者和老客信息
             **/
            showOldCustomPoster (data, type) {
                if (type === 'inValid') {
                    this.ineffectiveAccount = data
                } else {
                    this.oldCustomerItem = data;
                    // this.isInvalidLoc(this.oldCustomerItem);
                }
                this.$refs.oldCustomerPoster.show();
            },
            /**
             * @desc 查看未生效消费者详情
             * <AUTHOR>
             * @date 2023/3/2 20:30
             **/
            gotoAccountInfo () {
                this.$nav.redirect('/pages/lj-consumers/account/account-item-edit-page', {
                    orgId: this.ineffectiveAccount.orgId,
                    businessDataId: this.ineffectiveAccount.id,
                    appStatus: this.ineffectiveAccount.appStatus,
                    pageFrom: 'IneffectiveAccount'
                });
            },
            /**
             * @desc 查询未生效消费者详情数据
             * <AUTHOR>
             * @date 2023/2/21 15:44
             **/
            async queryFieldValueById () {
                if (this.$utils.isEmpty(this.businessDataId)) return
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {dmpUrl: '/link/fieldTemApp/queryById',id: this.businessDataId}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询失败' + response.result);
                    }
                });
                if (data.success) {
                    if(data.result.businessData) {
                        this.businessData = data.result.businessData;
                        this.formData.id = data.result.businessId;
                        await this.dealBusinessData('readonly');
                    }
                }
            },
            /**
             * @desc 处理未生效详情字段
             * <AUTHOR>
             * @date 2023/2/22 11:01
             **/
            async dealBusinessData (type) {
                let changeField = []
                changeField = JSON.parse(this.businessData);
                if (type === 'readonly') {
                    changeField.filter(item => (item.type === 'StandarField')).forEach((i)=> {
                        this.$set(this.formData, i.field, i.value === '空值' ? '' : i.value);
                    });
                    changeField.filter(item=> (item.type === 'CustomField')).forEach((i) => {
                        this.$set(this.customFieldsData, i.field, i.value === '空值' ? '' : i.value);
                    });
                    let labelInfo = {}
                    changeField.filter(item=> (item.type === 'LabelField')).forEach((i)=> {
                        labelInfo[i.field] = i.value
                    });
                    let map = {};
                    if (labelInfo['consumerTagsList'] && labelInfo['consumerTagsList'].length > 0) {
                        for (let i = 0; i < labelInfo['consumerTagsList'].length; i++) {
                            let ai = labelInfo['consumerTagsList'][i]
                            if (!map[ai.tagGroupId]) {
                                map[ai.tagGroupId] = [ai]
                            } else {
                                map[ai.tagGroupId].push(ai)
                            }
                        }
                        let res = []
                        Object.keys(map).forEach(key => {
                            res.push({
                                tagGroupId: key,
                                tagUniqVerify: map[key][0]['tagUniqVerify'],
                                tagGroupName: map[key][0]['tagGroupName'],
                                itemTags: map[key]
                            })
                        })
                        this.tagLabels = res;
                        this.chooseTagLabels = labelInfo['consumerTagsList'];
                    }
                    this.tagGroups = labelInfo['consumerTagsList'];
                } else {
                    changeField.filter(item => (item.type === 'StandarField')).forEach((i)=> {
                        const labelData = this.standarFields.filter((m)=> m.field === i.field);
                        if (labelData.length > 0) {
                            i.label = labelData[0].label;
                        }
                        i.value = this.formData[i.field];
                        for (let a = 0; a < this.kvALLFields.length; a++) {
                            if (i.field === this.kvALLFields[a].field && i.value && !this.kvALLFields[a].showFlag) {
                                i.value = '';
                            }
                        }
                    });
                    changeField.filter(item=> (item.type === 'CustomField')).forEach((i) => {
                        i.value = this.customFieldsData[i.field]
                    });
                    if (this.formData['consumerTagsList']) {
                        changeField.filter(item => item.type === 'LabelField').forEach((i) => {
                            i.value = this.formData['consumerTagsList'];
                        });
                    }
                    let tempKVData = this.kvALLFields.filter((item)=>item.showFlag);
                    const standardField = changeField.filter((item)=> item.type === 'StandarField');
                    for (let i = 0; i < tempKVData.length; i++) {
                        let tempFieldData = standardField.filter(item=> item.field === tempKVData[i].field);
                        if (tempFieldData.length < 1 && this.formData[tempKVData[i].field]) {
                            changeField.push({field: tempKVData[i].field,
                                'oldValue': '空值',
                                'label': tempKVData[i].label,
                                'type': 'StandarField',
                                'value': this.formData[tempKVData[i].field]})
                        }
                    }
                    const typeIdData = standardField.filter(item => item.field === 'typeId');
                    if (typeIdData.length < 1 && this.formData['typeId']) {
                        changeField.push({field: 'typeId',
                            'oldValue': '空值',
                            'label': '',
                            'type': 'StandarField',
                            'value': this.formData['typeId']
                        })
                    }
                    const tempAllData = Object.assign({}, this.formData, this.customFieldsData);
                    this.tempList.forEach((item)=> {
                        item.value = tempAllData[item.field]
                        changeField.forEach((item1) => {
                            if (item1.field ===  item.field) {
                                item.oldValue = item1.oldValue
                            }
                        })
                    });
                    this.tempList = this.tempList.filter((item)=> !!item.value && item.field !== 'id');
                    // belongToBrand做格式处理，转换为"Tequ/Boda/Other"格式
                    this.tempList.forEach((item) => {
                        if (item.field === 'belongToBrand' && item.value) {
                            item.value = item.value.join('/');
                        }
                    })
                    this.businessData = JSON.stringify(this.tempList);
                }
                this.isCompleted = true;
            },
            /**
             * @desc 提交审批
             * <AUTHOR>
             * @date 2023/2/20 17:50
             **/
            async submitApproval (data) {
                this.formData = data;
                if (this.insertApproval === 'Y' && this.formData.row_status === ROW_STATUS.NEW) {
                    await this.saveTags();
                }
                await this.dealBusinessData('edit');
                if (!await this.checkData()) {
                    return;
                }
                this.formData.extendField = JSON.stringify(this.customFieldsData);
                this.formData.templateId = this.templateId;
                this.formData.yxFlag = 'Y';
                this.formData.interfaceSource = 'Artificial';
                // 250327审批拒绝的场景不调/action/link/consumer/toApproval接口
                // const needApproval = await this.needApproval();
                // 250310逻辑变更，判断是否为v3审批不走参数配置，调接口判断
                const approvalV3Flag = await this.isApprovalV3();
                if (approvalV3Flag) {
                    this.isGotoPreCreate = await this.gotoPreCreateFlow();
                    if (this.isGotoPreCreate === 'Y') {
                        await this.confirmSubmit();
                    } else {
                        this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                            submitData: this.formData,
                            submitUser: this.userInfo,
                            flowObjId: this.businessDataId,
                            source: 'submit',
                            // 审批类型编码
                            approvalType: this.formData.row_status === ROW_STATUS.NEW ? 'ConsumerInsert' :'ConsumerUpdate'
                        });
                    }
                } else {
                    await this.confirmSubmit();
                }
            },
            async confirmSubmit(nodeDtos) {
                const fetchData = async (nodeDtos) => {
                    try {
                        const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                            dmpUrl: '/link/fieldTemApp/submitUpdate',
                            id: this.businessDataId,
                            businessData: this.businessData,
                            name: this.formData.name,
                            ...(nodeDtos && { nodeApprovers: nodeDtos }),
                            jumpApproval: this.isGotoPreCreate
                        });
                        if (data.success) {
                            setTimeout(() => {
                                this.$message.success('提交审批成功！');
                            }, 1000);
                            this.$nav.back();
                        }
                    } finally {
                        this.$utils.hideLoading();
                    }
                };
                await fetchData(nodeDtos);
            },
            /**
             * @createdBy 曾宇
             * @date 2023/4/12
             * @methods: handleTagGroupList
             * @description: 处理标签弹窗数据
             **/
            handleTagGroupList() {
                this.tagGroupList.forEach((item) => {
                    this.tagLabels.forEach((group) => {
                        group.itemTags.forEach((tag) => {
                            const flag = item.tagItemList.find((label) => label.tagId === tag.tagId);
                            if(flag) {
                                flag.checked = true;
                                flag.id = tag.id;
                                flag.ifActive = tag.ifActive;
                            }
                        })
                    })
                });
            },
            /**
             * @desc 查询消费者标签
             * <AUTHOR>
             * @date 2022/8/8 16:49
             **/
            async queryConsumerTags (tagGroupId) {
                let filtersRaw = [{id: 'ifActive', property: 'ifActive', value: 'Y'}];
                if (tagGroupId) {
                    filtersRaw = filtersRaw.concat([{id: 'tagGroupId', property: 'tagGroupId', value: tagGroupId}])
                }
                const data = await this.$http.post(this.$env.dmpURL + '/link/consumerTags/queryTagsByConsumerIdNew', {acctId: this.formData.id, filtersRaw: filtersRaw, pageFlag: false}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询标签数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.chooseTagLabels = this.$utils.deepcopy(data.rows);
                    let map = {};
                    for (let i = 0; i < data.rows.length; i++) {
                        let ai = data.rows[i]
                        if (!map[ai.tagGroupId]) {
                            map[ai.tagGroupId] = [ai]
                        } else {
                            map[ai.tagGroupId].push(ai)
                        }
                    }
                    let res = []
                    Object.keys(map).forEach(key => {
                        res.push({
                            tagGroupId: key,
                            tagUniqVerify: map[key][0]['tagUniqVerify'],
                            tagGroupName: map[key][0]['tagGroupName'],
                            itemTags: map[key]
                        })
                    })
                    if (tagGroupId) {
                        this.tagLabels.forEach((item, index)=> {
                            if (item.tagGroupId === tagGroupId) {
                                this.$set(this.tagLabels, index,  res[0]);
                            }
                        })
                    } else {
                        this.tagLabels = res;
                    }
                }
            },
            /**
             * @desc 删除子标签数据
             * <AUTHOR>
             * @date 2022/8/8 11:42
             **/
            async deleteItemTags (item, itemTag, itemIndex) {
                if(this.formOverallReadonly){
                    return
                }
                if (this.formData.row_status !== ROW_STATUS.NEW) {
                    this.$utils.showLoading();
                    const data = await this.$http.post(this.$env.dmpURL + '/link/consumerTags/deleteConsumerTags', {id: itemTag.id, ifActive: 'N'}, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$utils.hideLoading();
                            this.$showError(`删除标签数据失败：${response.result}`);
                        }
                    });
                    if (data.success) {
                        this.$utils.hideLoading();
                        const groups = this.tagGroupList.find((group) => group.id === item.tagGroupId);
                        if (groups) {
                            const tags = groups.tagItemList.find((tag) => tag.id === itemTag.id);
                            if(tags) tags.checked = false;
                        }
                        item['itemTags'].splice(itemIndex, 1);
                        this.$message.success('删除标签数据成功！')
                    }
                } else {
                    const groups = this.tagGroupList.find((group) => group.id === item.tagGroupId);
                    if (groups) {
                        const tags = groups.tagItemList.find((tag) => tag.tagId === itemTag.tagId);
                        if(tags) tags.checked = false;
                    }
                    item['itemTags'].splice(itemIndex, 1);
                }
            },
            /**
             * @desc 保存标签数据
             * <AUTHOR>
             * @date 2022/8/8 11:43
             **/
            async saveTags (tagGroupId) {
                if(this.formOverallReadonly){
                    return
                }
                if (this.tagLabels.length < 1) {
                    if (this.insertApproval === 'Y' && this.formData.row_status === ROW_STATUS.NEW) {
                        this.formData['consumerTagsList'] = [];
                    }
                    return;
                }
                const approvalFlag = this.insertApproval === 'Y' && this.formData.row_status === ROW_STATUS.NEW;
                let tagsData = [];
                let lists = this.$utils.deepcopy(this.tagLabels);
                for(let i = 0; i < lists.length; i++) {
                    if (lists[i]['itemTags']) {
                        let itemTags = []
                        for (let j = 0; j < lists[i]['itemTags'].length; j++) {
                            // 新增的数据和取消的数据才去更新
                            if (this.$utils.isUndefined(lists[i]['itemTags'][j].id) || this.tagLabels[i]['itemTags'][j].ifActive === 'N') {
                                if (this.$utils.isUndefined(lists[i]['itemTags'][j].id)) {
                                    lists[i]['itemTags'][j]['ifActive'] = 'Y';
                                }
                                if(approvalFlag){
                                    lists[i]['itemTags'][j]['l1TagId'] = lists[i]['l1TagId'];
                                    lists[i]['itemTags'][j]['l1TagName'] = lists[i]['l1TagName'];
                                    lists[i]['itemTags'][j]['l2TagId'] = lists[i]['l2TagId'];
                                    lists[i]['itemTags'][j]['l2TagName'] = lists[i]['l2TagName'];
                                }
                                itemTags = itemTags.concat([lists[i]['itemTags'][j]]);
                            }
                        }
                        tagsData = tagsData.concat(itemTags)
                    }
                }
                if (approvalFlag) {
                    this.formData['consumerTagsList'] = tagsData;
                } else {
                    if (tagsData.length > 0) {
                        this.$utils.showLoading();
                        const data = await this.$http.post(this.$env.dmpURL + '/link/consumerTags/batchSaveAndUpdateConsumerTags', tagsData, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$utils.hideLoading();
                                this.$showError(`保存标签数据失败：${response.result}`);
                            }
                        });
                        if (data.success) {
                            this.$utils.hideLoading();
                            this.$message.success('保存标签数据成功！');
                            if (this.formData.row_status === ROW_STATUS.UPDATE) {
                                await this.queryConsumerTags(tagGroupId);
                            }
                        }
                    }
                }
            },
            /**
             * @createdBy 曾宇
             * @date 2023/4/12
             * @methods: chooseTagGroup
             * @description: 选择标签
             **/
            async chooseTagGroup(data) {
                const copyTagLabels = this.$utils.deepcopy(this.tagLabels);
                const updateFlag =  this.formData.row_status === ROW_STATUS.UPDATE;
                if(updateFlag){
                    copyTagLabels.forEach((group, index) => {
                        const findList = data.find((dt) => dt.tagGroupId === group.tagGroupId); //判断当前数据是否在之前的数据里。有则更新数据。
                        //处理被删除的三级标签
                        if(!findList){
                            this.tagLabels[index]['itemTags'].forEach(it => it.ifActive = 'N');
                        }else{
                            // 处理被删除的四级标签
                            group.itemTags.forEach((list, listIndex) => {
                                const findTagList = findList.valueList.find((tag) => list.tagId === tag.tagId);
                                if(!findTagList){
                                    this.tagLabels[index]['itemTags'][listIndex].ifActive = 'N';
                                }
                            })
                            findList.valueList.forEach((fd) => {
                                const findTagList = group.itemTags.find((tag) => fd.tagId === tag.tagId);
                                if(!findTagList){
                                    this.tagLabels[index]['itemTags'].push(
                                        {
                                            acctId: this.formData.id,
                                            tagGroupId: findList.tagGroupId,
                                            tagId: fd.tagId,
                                            tagName: fd.tagName,
                                            tagGroupName: findList.tagGroupName,
                                            tagUniqVerify: findList.isMultipleValue ? 'TagItem' : 'TagGroup'
                                        }
                                    );
                                }
                            })
                        }
                    });
                }else{
                    this.chooseTagLabels = [];
                    this.tagGroups = [];
                }
                //再添加数据
                data.forEach((dt) =>{
                    dt['itemTags'] = dt['valueList'].map(
                        i => ({
                            acctId: this.formData.id,
                            tagGroupId: dt.tagGroupId,
                            tagId: i.tagId,
                            tagName: i.tagName,
                            tagGroupName: dt.tagGroupName,
                            tagUniqVerify: dt.isMultipleValue ? 'TagItem' : 'TagGroup'
                        })
                    );
                    if(updateFlag){
                        const findIndex = copyTagLabels.findIndex((item) => item.tagGroupId === dt.tagGroupId); //判断当前数据是否在之前的数据里。有则更新数据。
                        if(findIndex < 0){
                            this.tagLabels.push(dt)
                        }
                    }else{
                        dt.valueList.forEach((lb) => this.chooseTagLabels.push({
                            tagId: lb.id || lb.tagId
                        }));
                    }
                });
                if (updateFlag) {
                    await this.saveTags();
                }else{
                    this.tagLabels = data;
                }
                this.tagDialogFlag = false;
            },
            /**
             * @desc
             * <AUTHOR>
             * @date 2022/8/5 17:05
             **/
            async addTagGroups () {
                if(this.formOverallReadonly){
                    return
                }
                const list = await this.$object(this.tagsGroupOption, {
                    multiple: true,
                    pageTitle: '选择标签组'
                });
                this.tagLabels = this.tagLabels.concat(list.map((
                    i => ({
                        tagGroupId: i.id,
                        tagGroupName: i.tagGroupName,
                        tagUniqVerify: i.tagUniqVerify
                    })
                )));
            },
            /**
             * 新建消费者-老客取消
             * <AUTHOR>
             * @date 2020-11-04
             */
            cancel() {
                this.$refs.oldCustomerPoster.hide();
                if (this.oldCustomerItem && this.oldCustomerItem.phoneNumber) {
                    this.$nav.back();
                } else {
                    this.formData.phoneNumber = '';
                }
            },
            /**
             * 新建消费者-老客选择
             * <AUTHOR>
             * @date 2020-11-04
             * 2021-01-19 UPDATE 使用场景：一、市场活动-消费者子对象新建 二、活动名单提报-嘉宾新建 三、市场活动-宴席主家新建
             * 场景一：选择老客后需要插入市场活动-活动人群中间表。场景二三不需要。
             * 场景二：选择老客后需要将选择的消费者数据携带到【名单提报模块-子对象嘉宾名单的的新建界面】中
             * 场景三：选择老客后需要携带老客数据返回，供界面选对象展示。
             */
            async pick() {
                if(this.pageFrom === 'Activity'){
                    let consumerDataList = [];
                    const data = {
                        'mcActId': this.marketActivityId,
                        'openId': this.oldCustomerItem.openId,
                        'company': this.oldCustomerItem.companyName,
                        'jobTitle': this.oldCustomerItem.position,
                        'salemanId': this.oldCustomerItem.postnId,
                        'accntChannelId': this.oldCustomerItem.id,
                        'acctName': this.oldCustomerItem.name,
                        'phone': this.oldCustomerItem.phoneNumber,
                        'gender': this.oldCustomerItem.gender,
                        'birthDate': this.oldCustomerItem.birth,
                        'relevanceType': 'New',
                        'birthDateType': this.oldCustomerItem.birthType,
                        'empFlag': 'N',
                        'terminalFlag': 'N',
                        'saleman': this.oldCustomerItem.fstName
                    };
                    // 执行反馈新增消费者
                    if (this.pageParam && this.pageParam.pageSource && this.pageParam.pageSource === 'executiveFeedback') {
                        data.sourceType = 'BackTracking'; // 来源类型
                        data.matchStatus = 'Interaction'; // 匹配状态
                    }
                    consumerDataList.push(data);
                    await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/batchInsert', consumerDataList, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`插入数据失败：${response.result}`);
                        }
                    });
                }
                this.$refs.oldCustomerPoster.hide();
                this.$bus.$emit('initConsumers');

                if (this.pageFrom === 'ListApply') {
                    //ids 名单提报嘉宾名单行id
                    this.$nav.push('/pages/lj-market-activity/activity-list-report/activity-detail-add-page',
                        {data: this.oldCustomerItem, source: 'oldCustomerItem',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, ids: this.pageParam.ids})
                } else if (this.pageFrom === 'visitRegisterItem' || this.pageFrom === 'visitApplyItem' || this.pageFrom === 'masterActivity' || this.pageFrom === 'BookedOrder' || this.pageFrom === 'visitPresent') {
                    this.pageParam.callback(this.oldCustomerItem);
                    this.$nav.back(null, 2);
                } else if (this.pageFrom === 'ListReport') {
                    this.oldCustomerItem.idCardNumber = null;
                    this.$nav.redirect('/pages/lj-consumers/list-report/guest-detail-add-page',
                        // {data: this.oldCustomerItem, source: 'oldCustomerItem',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, activityStatus: this.pageParam.activityStatus, brand: this.pageParam.brand, activityName: this.pageParam.activityName, lineStatus: 'Improved'});
                        {LangChao25BtnShow:true,data: this.oldCustomerItem, source: 'oldCustomerItem',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, activityStatus: this.pageParam.activityStatus, activityName: this.pageParam.activityName, lineStatus: 'Improved', lineDataType: this.pageParam.lineDataType});
                } else if (this.pageFrom === 'Activity' && this.pageParam.pageSource && (['other', 'preview'].includes(this.pageParam.pageSource))) {
                    this.$nav.back();
                } else if (this.pageFrom === 'scheduleDetail') {
                    this.pageParam.callback(this.oldCustomerItem);
                    this.$bus.$emit('queryScheduleConsumer');
                    this.$nav.back(null, 2);
                } else {
                    this.$nav.back(null, 2);
                }
            },
            /**
             * @desc 分配此客户
             * <AUTHOR>
             * @date 2022/3/16 14:24
             * @param type 分配此客户或者分配且选择此客户
             **/
            applicationTransfer(type) {
                this.$refs.oldCustomerPoster.hide();
                this.oldCustomerItem.idCardNumber = null;
                // 250224 和后端业务确认，点击【选择申请跟进此客户】，先把消费者添加到排期中，然后再打开分配申请页面
                if (this.pageFrom === 'scheduleDetail') {
                    this.pageParam.callback(this.oldCustomerItem);
                    this.$bus.$emit('queryScheduleConsumer');
                }
                this.$nav.push('/pages/lj-consumers/account/account-apply-page', {
                    item: this.oldCustomerItem,
                    pageFrom: this.pageFrom,
                    pageSource: this.pageParam.pageSource,
                    source: 'accountApply',
                    ids: this.pageParam.ids,
                    lineDataType:this.pageParam.lineDataType,
                    marketActivityId: this.marketActivityId,
                    activityType: this.activityType,
                    activityStatus: this.pageParam.activityStatus || '',
                    // brand: this.pageParam.brand || '',
                    activityName: this.pageParam.activityName || '',
                    visitRegisterItemCustInfoItem: this.pageParam.visitRegisterItemCustInfoItem || {},
                    visitRegisterItem: this.pageParam.visitRegisterItem || {},
                    callback: (data) => {
                        this.pageParam.callback(data);
                    },
                })
            },
            /**
             * 编辑保存客户
             * <AUTHOR>
             * @date 2020-10-13
             */
            async saveAccount(type, data) {
                this.formData = data;
                delete this.formData['identityLevel'];
                delete this.formData['fromType'];
                this.formData.templateId = this.templateId;
                this.customFields.forEach((item) => {
                    item.componentList.forEach((items) => {
                        if (items.type === 'Photo') {
                            this.customFieldsData[items.values.field] = this.imgList.length > 0 ? this.imgList.length : null
                        }
                    })
                })
                this.formData.extendField = JSON.stringify(this.customFieldsData);
                if (!await this.checkData()) {
                    return;
                }
                const brandList = await this.$lov.getLovByParentTypeAndValue({type: 'BRAND', parentType: 'ACTIVITY_COMPANY', parentVal: this.userInfo.coreOrganizationTile['l3Id']});
                if (this.formData.belongToBrand) {
                    if (Array.isArray(this.formData.belongToBrand)) {
                        if (this.formData.belongToBrand.length) {
                            // 剔除this.formData.belongToBrand不包含在this.brandList中的值
                            this.formData.belongToBrand = this.formData.belongToBrand.filter((item) => brandList.find((brand) => brand.val === item));
                            // this.formData.belongToBrand，格式为"Tequ/Boda/Other"
                            this.formData.belongToBrand = this.formData.belongToBrand.join('/');
                        } else {
                            this.formData.belongToBrand = null;
                        }
                    } else {
                        // 有值且不是数组，说明时提交失败的情况，此时数据已做处理
                    }
                } else {
                    this.formData.belongToBrand = null;
                }
                if (this.insertApproval === 'Y' && this.formData.row_status === ROW_STATUS.NEW) {
                    await this.saveTags();
                }
                delete this.formData['enterpriseName'];
                delete this.formData['listOfTags'];
                this.formData.dataSource = 'MarketingPlatform';
                this.formData.yxFlag = 'Y';
                this.formData.interfaceSource = 'Artificial';
                const needApproval = await this.needApproval();
                // 250310逻辑变更，判断是否为v3审批不走参数配置，调接口判断
                const approvalV3Flag = await this.isApprovalV3();
                if (needApproval && approvalV3Flag) {
                    // 新建和变更走一套逻辑，仅approvalType传参不一致
                    this.type = type;
                    // 预生成一个申请单id：futureAppTempId
                    const newId = await this.$newId();
                    this.$set(this.formData, 'futureAppTempId', newId);
                    const extendField = JSON.parse(this.formData.extendField);
                    this.formData = Object.assign(this.formData, extendField);
                    //在预览接口前端需要将发生变更的字段多加一个“pre_字段名”进行传参
                    const submitData = Object.assign({}, this.formData,  this.compareDiff(this.pageParam.data,data))
                    this.isGotoPreCreate = await this.gotoPreCreateFlow();
                    if (this.isGotoPreCreate === 'Y') {
                        await this.submit(type);
                    } else {
                        this.$nav.push('/pages/lzlj/approval-v3/approval-flow-page.vue', {
                            submitData,
                            submitUser: this.userInfo,
                            flowObjId: newId,
                            source: 'save',
                            // 审批类型编码
                            approvalType: this.formData.row_status === ROW_STATUS.NEW ? 'ConsumerInsert' :'ConsumerUpdate'
                        });
                    }
                } else {
                    await this.submit(type);
                }
            },
            /**
             * @description 比较出已修改的属性返回修改前的值，属性名前+ pre_
             * <AUTHOR>
             * @date 2025/3/21
             */
            compareDiff(obj1, obj2) {
                const diff = {};
                const prefixedDiff = {};
                const allKeys = new Set([...Object.keys(obj1), ...Object.keys(obj2)]);
                allKeys.forEach(key => {
                    if (obj1[key]!== obj2[key] && (obj1[key] || obj2[key])) {
                        diff[key] = {
                            oldValue: obj1[key],
                            newValue: obj2[key]
                        };
                        prefixedDiff[`pre_${key}`] = diff[key].oldValue || '空值';
                        prefixedDiff[key] = diff[key].newValue || '空值';
                    }
                });
                return  prefixedDiff;
            },
            /**
             * @desc 判断是否是v3需要审批
             * <AUTHOR>
             * @date 2025-03-06
             **/
            async isApprovalV3 () {
                return new Promise(async (res, rej) => {
                    const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/fieldTemApp/qwAppVersion', {
                        companyId: this.userInfo.coreOrganizationTile['l3Id'] || '',
                        applyType: 'InvoiceBased'
                    });
                    console.log('result', result)
                    if (success && result === 'v3') {
                        res(true);
                    } else {
                        res(false);
                    }
                });
            },
            // 是否需要审批
            async needApproval() {
                return new Promise(async (res, rej) => {
                    const {success, result} = await this.$http.post(this.$env.dmpURL + '/action/link/consumer/toApproval', this.formData);
                    if (success && result === 'Y') {
                        res(true);
                    } else {
                        res(false);
                    }
                });
            },
            async onBack(param) {
                if (param && param.flag === 'flow') {
                    this.$set(this.formData, 'nodeApprovers', param.nodeDtos);
                    if(param.source === 'submit') {
                        await this.confirmSubmit(param.nodeDtos);
                    }
                    if(param.source === 'save') {
                        await this.submit(this.type);
                    }
                }
            },
            async submit(type) {
                let url = '/action/link/consumer/upsert';
                try{
                    this.$utils.showLoading();
                    const data = await this.$http.post(this.$env.dmpURL + url, {
                        ...this.formData,
                        jumpApproval: this.isGotoPreCreate
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            let content = response.result;
                            if(response.result.indexOf('详细地址') !== -1){
                                content = '地址维护错误，请检查“详细地址”是否填写正确';
                            } else if (response.result.indexOf('地址') !== -1) {
                                content = '地址维护错误，请检查“所在地区”是否填写正确';
                            }
                            this.$utils.hideLoading();
                            this.$taro.showModal({
                                title: '提示',
                                content: content,
                                showCancel: false,
                                success: async (res) => {
                                    if (res.confirm) {
                                        if (this.pageFrom === 'accountItem') {
                                            this.pageParam.callback();
                                        }
                                    }
                                }
                            });
                        }
                    });
                    if (data.success) {
                        if (this.insertApproval === 'N' && this.formData.row_status === ROW_STATUS.NEW) {
                            await this.saveTags();
                        }
                        // if (this.formData.row_status === ROW_STATUS.UPDATE) {
                        //     await this.$utils.handleTODOListNumber('del', 'unDoAccount');
                        // }
                        this.$utils.hideLoading();
                        if (data.insertApproval) {
                            this.$message.success({message: '消费者已成功提交审批，审批通过后可正常使用', customFlag:true});
                        } else {
                            if (this.formData.followFlag === 'N' && data.newRow.attr7 === 'claim') {
                                this.$message.success('认领申请提交成功，审批通过后可正常维系');
                            } else if (this.formData.followFlag === 'N' && data.newRow.attr7 !== 'claim') {
                                this.$message.success('认领消费者成功');
                            } else if (data.newRow.auditStatus === 'Reviewing' || data.newRow.auditStatus=== 'FollowReviewing') {
                                this.$message.success('消费者已成功提交审批，审批通过后更新修改内容');
                            } else {
                                this.$message.success('保存消费者成功');
                            }
                        }
                        if (type === 'New') {
                            if(this.pageFrom === 'accountItem') {
                                this.pageParam.callback();
                            }
                            const id = await this.$newId();
                            this.formData = this.$utils.deepcopy(this.copyFormData);
                            this.formData.id = id;
                            this.customFieldsData = this.$utils.deepcopy(this.copyCustomFieldsData);
                            this.tagLabels = [];
                            if(data.fwApiFlowMsg&&data.fwApiFlowMsg.fwSingleToken&&data.fwApiFlowMsg.fwRequestId){
                                // 跳转到泛微审批
                                this.$nav.push('/pages/core/web-page/web-page',
                                    {   isFanwei: 1,
                                        requestId: data.fwApiFlowMsg.fwRequestId,
                                        approvalType: "NEW"
                                    })
                                return;
                            }
                        } else {
                            if(this.pageParam.__fromCache){
                                //从缓存引导而来，直接返回到首页。
                                this.$nav.backAll();
                            }else{
                                if (!!this.pageFrom && (this.pageFrom === 'accountItem' || this.pageFrom === 'vCard')) {
                                    this.pageParam.callback();
                                }
                                // 企业参访新建拜访客户（不需要审批）
                                if (!!this.pageFrom && this.pageFrom === 'visitApplyItem'
                                    && !!this.pageParam.source && this.pageParam.source === 'corporate'
                                    && !data.insertApproval) {
                                    // 消费信息同步至添加拜访客户页面
                                    this.$bus.$emit('setAcctInfo', data.newRow);
                                }
                                // 市场活动新建消费者，消费者不需要审批
                                if (!!this.pageFrom && this.pageFrom === 'Activity' && !data.insertApproval) {
                                    if (!!this.pageParam.source && this.pageParam.source === 'Activity' || this.pageParam.source === 'Yanxi') {
                                        // 消费信息同步至活动页面
                                        this.pageParam.updateConsumers(data.newRow)
                                        if (this.pageParam.source === 'Yanxi') {
                                            // 消费信息同步到宴席主家字段上
                                            this.pageParam.viewUpdateMaster(data.newRow);
                                        }
                                    }
                                }
                                // 活动，宴席主家，动销订单，拜访登记，拜访申请，礼赠
                                if (!!this.pageFrom && !data.insertApproval && (this.pageFrom === 'Activity' || this.pageFrom === 'masterActivity' || this.pageFrom === 'BookedOrder' || this.pageFrom === 'visitRegisterItem' || this.pageFrom === 'visitApplyItem' || this.pageFrom === 'visitPresent')) {
                                    this.pageParam.callback(data.newRow);
                                }
                                if(data.fwApiFlowMsg&&data.fwApiFlowMsg.fwSingleToken&&data.fwApiFlowMsg.fwRequestId){
                                    // 跳转到泛微审批
                                    this.$nav.push('/pages/core/web-page/web-page',
                                        {   isFanwei: 1,
                                            requestId: data.fwApiFlowMsg.fwRequestId,
                                            approvalType: "UPDATE"
                                        })
                                    return;
                                }
                                if(this.pageFrom === 'ListApply' && !data.insertApproval){
                                    //ids 名单提报嘉宾名单行id
                                    this.$bus.$emit('refreshAccountList')
                                    this.$nav.redirect('/pages/lj-market-activity/activity-list-report/activity-detail-add-page',
                                        {data: data.newRow, source: 'oldCustomerItem',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, ids: this.pageParam.ids})
                                    return;
                                }
                                if (this.pageFrom === 'ListReport' && !data.insertApproval) {
                                    this.$nav.redirect('/pages/lj-consumers/list-report/guest-detail-add-page',
                                        // {data: data.newRow, source: 'oldCustomerItem',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, activityStatus: this.pageParam.activityStatus, brand: this.pageParam.brand, activityName: this.pageParam.activityName, lineStatus: 'Improved', prodPick: this.pageParam.prodPick})
                                        {LangChao25BtnShow:true,data: data.newRow, source: 'oldCustomerItem',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, activityStatus: this.pageParam.activityStatus, activityName: this.pageParam.activityName, lineStatus: 'Improved', prodPick: this.pageParam.prodPick, lineDataType: this.pageParam.lineDataType})
                                    return;
                                }
                                if (this.pageFrom === 'scheduleDetail') {
                                    this.$bus.$emit('queryScheduleConsumer');
                                }

                                setTimeout(() => {
                                    this.$nav.back();
                                }, 1500);
                            }
                        }
                    }
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * @desc 判断是否需要唤起预览页
             * <AUTHOR>
             * @date 2025-03-20
             **/
            async gotoPreCreateFlow() {
                const {success, result} = await this.$http.post('action/link/flow/v3/preCreateFlow', {
                    // 审批对象ID
                    objectId: this.formData.id,
                    // 审批类型编码
                    approvalType: this.formData.row_status === ROW_STATUS.NEW ? 'ConsumerInsert' :'ConsumerUpdate',
                    // 业务对象JSON字符串
                    flowObjDetail: JSON.stringify(this.formData),
                    // flowObjId传订单id
                    flowObjId: this.formData.id,
                    // 提交用户id
                    submitUserId: this.userInfo.id,
                    // 提交用户职位id
                    submitPostnId: this.userInfo.postnId,
                    // 提交用户组织id
                    submitOrgId: this.userInfo.orgId
                });
                if(success) {
                    return result.jumpApproval;
                } else {
                    this.$showError('预生成审批记录失败！' + result);
                }
            },
            /**
             * 检验数据
             * <AUTHOR>
             * @date 2020-10-13
             */
            async checkData() {
                if (this.customFields.length > 0) {
                    await this.$refs.customFieldsData.validate();
                    for (let key in this.customFieldsData) {
                        if (this.customFieldsData[key] && this.customFieldsData[key].province &&
                            !this.$utils.isEmpty(this.customFieldsData[key].province)
                            && (this.$utils.isEmpty(this.customFieldsData[key].city) || this.$utils.isEmpty(this.customFieldsData[key].district))) {
                            this.$message.warn('请检查省市区字段是否填写完整')
                            return false;
                        }
                    }
                    // 校验自定义地址必填
                    const components = this.customFields.map((item) => item.componentList).flat();
                    for (const item of components) {
                        if (item.ctrlCode === 'link-address' && item.values.required) {
                            // {province: '', city: '', district: ''} 为之前的默认值，现默认值改为{}
                            const addressObj = this.customFieldsData[item.values.field];
                            const defaultVal = addressObj.province === '' && addressObj.city === '' && addressObj.district === ''
                            if (JSON.stringify(addressObj) === '{}' || defaultVal) {
                                this.$message.warn(`${item.values.fieldName}必填，请检查！`)
                                return false;
                            }
                        }
                    }
                }
                for (const tagPart of this.labelField) {
                    if (!this.tagLabels.find((item) => 'tag' + item.tagGroupId === tagPart.id && item.itemTags.length > 0) && tagPart.required === 'Y') {
                        this.$message.warn('请检查必填标签' + tagPart.label);
                        return false;
                    }
                }
                return true;
            }
        }
    }
</script>

<style lang="scss">
    .account-item-edit-page {
        /*deep*/ .line-title {
            margin-bottom: 24px;
        }
        .old-cus-o {
            border-radius: 8px 8px 100px 100px;
            background-color: #2F69F8;
            display: flex;
            padding: 40px 0 40px 48px;
            justify-content: flex-start;
            align-items: center;
            .o-left {
                width: 128px;
                border: 12px solid rgba(255, 255, 255, 0.12);
                border-radius: 100px;
                height: 128px;
                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .o-right {
                width: 50%;
                padding-left: 24px;
                .name {
                    font-size: 32px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 32px;
                }

                .tel {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding-top: 20px;
                }
            }

            .cancel-v {
                width: 15%;
                float: right;
                height: 60px;
                margin-top: 50px;
                font-size: 36px;
                text-align: center;
                color: white;
            }
        }
        .old-cus-dialog-view {
            width: 676px;
            border-radius: 16px;
            background-color: white;
            .old-cus-t {
                height: 316px;

                .t-left {
                    margin: 28px 48px 24px 48px;
                    height: 60px;
                    .icon-v {
                        width: 10%;
                        float: left;
                        color: #8C8C8C;
                    }
                    .title {
                        font-size: 28px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 44px;
                        width: 48%;
                        float: left;
                        white-space: nowrap;

                    }
                    .val {
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: right;
                        line-height: 44px;
                        width: 42%;
                        display: flex;
                        justify-content: flex-end;
                    }
                }
            }

            .old-cus-s {
                width: 100%;
                height: 100px;
                margin-top: 30px;


                .cancel-v {
                    width: 90%;
                    height: 74px;
                    float: left;
                    color: white;
                    margin-left: 5%;
                    border: 2px solid #2F69F8;
                    border-radius: 4px;

                    .title {
                        font-size: 32px;
                        color: #2F69F8;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 74px;
                    }
                }

                .pike-v {
                    width: 90%;
                    height: 74px;
                    float: left;
                    background: #2F69F8;
                    box-shadow: 0 16px 48px 0 rgba(47, 105, 248, 0.50);
                    border-radius: 8px;
                    margin-left: 5%;

                    .title {
                        font-size: 32px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 74px;
                    }
                }
            }
        }
        .terminal-item {
            width: 100%;
            height: 40px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            color: #262626;
            font-size: 28px;
            line-height: 28px;
            padding-bottom: 16px;
            &__left {
                width: 50%;
                display: flex;
                flex-direction: column;
            }
            &__right {
                width: 50%;
                text-align: right;
                display: flex;
                flex-direction: column;
            }
            .terminal-item:last-child{
                padding-bottom: 0;
            }
        }
        .title-line{
            margin-top: 24px;
        }

        .address-color {
            display: flex;
            align-items: center;
            flex-wrap: wrap;
            justify-content: flex-end;
            /*deep*/
            .link-input-inner {
                overflow-x: auto;
                width: 300px;
            }
        }
        .img-watermark-tips {
            font-size: 28px;
            color: red;
        }
        .label-info{
            background: white;
            font-size: 28px;
            .group-label {
                display: flex;
                justify-content: space-between;
                margin: 0 28px;
                padding: 28px 0;
                border-bottom: 1px solid #efefef;
                .label-operate {
                    color: #e0e0e0;
                }
            }
            .label-data-info{
                margin: 0 28px;
                padding: 0 0 28px;
                border-bottom: 1px solid #efefef;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                height: auto;
                .label-item{
                    background-color: #2f69f8;
                    color: white;
                    display: inline-flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    font-size: 24px;
                    border-radius: 8px;
                    padding: 8px 16px;
                    margin-right: 24px;
                    margin-top: 24px;
                }
            }
        }
        .other-object-data{
            margin-bottom: 24px;
        }
        .blank {
            height: 376px;
            width: 100%;
        }
        .group-dialog {
            .model-title {
                display: flex;
                .title {
                    font-family: PingFangSC-Regular;
                    font-size: 32px;
                    color: #212223;
                    letter-spacing: 0;
                    font-weight: 400;
                    text-align: center;
                    line-height: 112px;
                    height: 112px;
                    width: 90%;
                    padding-left: 0!important;
                    //margin-right: 80px;
                }
                .icon-left{
                    width: 48px;
                    height: 48px;
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 48px;
                    margin: 32px;
                }
                .icon-close {
                    width: 48px;
                    height: 48px;
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 48px;
                    margin: 32px;
                }
            }
        }
    }
</style>


