<template>
    <link-page class="account-apply-page">
        <approval-history-point :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)"></approval-history-point>
        <link-form>
            <view class="item main-item">
                <view class="account-info-container">
                    <image class="head-back" :src="$imageAssets.homeMenuBgImage"></image>
                    <view class="top-bg">
                        <view class="top-bg-1"></view>
                        <view class="top-bg-2">
                            <image :src="accountItem.headUrl|headImgAccount(accountItem)"
                                   class="top-bg-2-img"></image>
                        </view>
                        <view class="top-bg-3">
                            <view style="text-align: center;margin-top: 20px">
                                <view class="top-bg-3-text">{{accountItem.name}}
                                </view>
                            </view>
                            <view style="text-align: center;">
                                <view class="top-bg-3-text">{{accountItem.gender | lov('GENDER')}}
<!--                                    {{accountItem.subAcctType | lov('ACCT_SUB_TYPE')}}-->
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view>
                    <view class="account-label-container">
                        <view class="list list--noborder">
                            <view class="label-group-container">
                                <view class="label-group-name customer-label">
                                    <span class="personal-label">个性标签</span>
                                </view>
                                <view class="label-item-container">
                                    <view v-if="!$utils.isEmpty(accountItem.listOfTags)">
                                        <view v-for="item in accountItem.listOfTags['list']">
                                            <view class="label-item label-item-selected">
                                                {{item['tagId']}}
                                            </view>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <view class="account-detail-container">
                        <link-form-item label="手机号码">
                            {{accountItem.phoneNumber}}
                        </link-form-item>
<!--                        <link-form-item label="生日">-->
<!--                            {{accountItem.birth | date('YYYY-MM-DD')}}-->
<!--                        </link-form-item>-->
<!--                        <link-form-item label="身份证号">-->
<!--                            {{accountItem.idCardNumber}}-->
<!--                        </link-form-item>-->
                        <link-form-item label="单位名称">
                            {{accountItem.companyName}}
                        </link-form-item>
                        <link-form-item label="职务">
                            {{accountItem.position}}
                        </link-form-item>
                        <link-form-item label="所属行业">
                            {{accountItem.subordinateIndustry | lov('INDUSTRY')}}
                        </link-form-item>
                        <link-form-item label="消费者等级" v-if="accountItem.type === 'GroupPurchaseConsumer'|| accountItem.type === 'KOL'">
                            {{accountItem.loyaltyLevel | lov('ACCT_MEMBER_LEVEL')}}
                        </link-form-item>
                        <link-form-item label="地区" v-if="accountItem.provinceCityDistrict">
                            {{accountItem.provinceCityDistrict}}
                        </link-form-item>
                        <link-form-item label="地区" v-else-if="accountItem.province">
                            {{accountItem.province}}{{accountItem.city}}{{accountItem.county}}{{accountItem.street ? accountItem.street : ''}}
                        </link-form-item>
                        <link-form-item label="详细地址">
                            {{accountItem.detailedAddress}}
                        </link-form-item>
                        <link-form-item label="附件" vertical v-if="imgList && imgList.length > 0">
                            <lnk-img-watermark :parentId="businessId"
                                               moduleType="consumerattachment"
                                               isZlFlag
                                               :pathKeyArray="imgList"
                                               filePathKey="/consumerattachment/"
                                               ref="fakeTerminalPhoto">
                            </lnk-img-watermark>
                        </link-form-item>
                        <view class="list-bottom-width-line"></view>
                        <link-form-item label="申请人">
                            {{applyName}}
                        </link-form-item>
                        <link-form-item label="申请时间">
                            {{applyTime}}
                        </link-form-item>
                        <view class="list-bottom-width-line"></view>
                        <link-form-item required label="申请说明" vertical>
                            <view class="comments">
                                <link-textarea placeholder="请填写申请说明，100字以内" padding-start padding-end
                                               v-model="comments" :nativeProps="{maxlength:100}" :readonly="!!approvalId"></link-textarea>
                            </view>
                        </link-form-item>
                    </view>
                    <link-sticky>
                        <link-button block @tap="isScheduling()" v-if="$utils.isEmpty(approvalId)">提交</link-button>
                    </link-sticky>
                </view>
            </view>
        </link-form>
        <link-sticky>
            <approval-operator :approvalId="approvalId" v-if="!$utils.isEmpty(approvalId)" @approvalInfoResult="approvalInfoResult"></approval-operator>
        </link-sticky>
        <view class="blank" v-if="!$utils.isEmpty(approvalId)"></view>
        <water-mark></water-mark>
    </link-page>
</template>

<script>
    import Taro from "@tarojs/taro";
    import ApprovalHistoryPoint from "../../lzlj/approval/components/approval-history-point";
    import ApprovalOperator from "../../lzlj/approval/components/approval-operator";
    import waterMark from "../../lzlj/components/water-mark";
    import LnkImgWatermark from "../../core/lnk-img-watermark/lnk-img-watermark.vue";
    export default {
        name: "account-apply-page",
        data() {
            return {
                pageFrom: this.pageParam.pageFrom,
                comments: null,
                userInfo: {},
                applyName: '' ,
                applyTime: this.$date.format(new Date(), 'YYYY-MM-DD HH:mm'),
                accountItem: {},
                imgList: [],  // 附件
                marketActivityId: this.pageParam.marketActivityId,
                activityType: this.pageParam.activityType,
                accountApplyId: null,
                approvalId: null,
                businessId: null
            }
        },
        components: {
            LnkImgWatermark,
            ApprovalHistoryPoint,
            ApprovalOperator,
            waterMark
        },
        async created() {
            if (this.pageParam.pageFrom !== 'Account') {
                this.$bus.$emit('accountApplyPage',{BackpageFrom: 'accountApplyPage'})
            }
            this.userInfo = Taro.getStorageSync('token').result
            this.applyName = this.userInfo.firstName
            let code = this.pageParam.source;//页面来源
            let sceneObj = await this.$scene.ready();//that.$store.getters['scene/getScene'];//消息场景对象
            console.log(sceneObj,'sceneObj')
            const approval_from = sceneObj.query['approval_from'];
            console.log(sceneObj,'sceneObj')
            if (code === 'accountApply') {
                this.accountItem = this.pageParam.item;
            }else if(code === 'approval'){
                this.approvalId = this.pageParam.data.id;//审批传过来的审批数据ID
                this.accountApplyId = this.pageParam.data.flowObjId;//审批传过来的业务对象ID
                if(this.$utils.isNotEmpty( this.accountApplyId)){
                    await this.queryItemById()
                }else{
                    this.$utils.showAlert('请联系管理员，未获取到客户转交信息！', {icon: 'none'});
                    return
                }
            }else{
                if(approval_from === 'qw') { //从小程序审批消息而来
                    this.approvalId = sceneObj.query['approval_id'];
                    this.accountApplyId = sceneObj.query['flowObjId'];
                    if(this.$utils.isNotEmpty( this.accountApplyId)){
                        await this.queryItemById()
                    }else{
                        this.$utils.showAlert('请联系管理员，未获取到客户转交信息！', {icon: 'none'});
                        return
                    }
                }else{
                    this.accountItem = this.pageParam.item;
                }
            }
        },
        methods: {
            /**
             * 消费者附件查询
             * @author:  胡益阳
             * @date:  2024/7/25
             */
            queryCusAttachment () {
                try {
                    this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`,
                        {
                            pageFlag: false,
                            rows: 50,
                            page: 1,
                            dmpUrl: '/link/cusAttachment/queryByExamplePage',
                            order: 'desc',
                            sort: 'created',
                            filtersRaw: [
                                {id: 'sourceId', operator: '=', property: 'sourceId', value: this.businessId},
                                {id: 'uploadStatus', operator: 'in', property: 'uploadStatus', value: '[Effective]'}
                            ]
                        }
                    ).then((res) => {
                        if (res.success) {
                            this.imgList = res.rows;
                        }
                    })
                } catch (e) {}
            },
          /**
           * @createdBy  张丽娟
           * @date  2021/4/8
           * @methods approvalInfoResult
           * @para
           * @description 赋值，提交人，提交时间，以及申请备注
           */
          approvalInfoResult(info){
            this.applyName = info.flowStartPsnName;
            this.applyTime = this.$date.format(info.created, 'YYYY-MM-DD HH:mm')
          },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/10
             * @methods queryItemById
             * @para
             * @description 获取表单详情
             */
            async queryItemById(){
                this.$utils.showLoading();
                await this.beforeQueryId(this.accountApplyId);
                await this.queryCusAttachment()
                const data = await this.$http.post(this.$env.appURL + '/action/link/consumer/queryById', {
                    id: this.businessId
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError(`获取消费者信息出错：${response.result}`);
                    }
                });
                this.accountItem = {
                    name: data.result.acctNameSecret || '',
                    type: data.result.subAcctType || '',
                    gender: data.result.gender || '',
                    listOfTags: data.result.listOfTags || '',
                    phoneNumber: data.result.mobilePhone1Secret,
                    // birth: data.result.birthday || '',
                    // idCardNumber: data.result.idNumber || '',
                    companyName: data.result.company || '',
                    position: data.result.position || '',
                    subordinateIndustry: data.result.acctCategory || '',
                    provinceCityDistrict: data.result.provinceCityDistrict || '',
                    detailedAddress: data.result.address || ''
                };
                // this.comments = data.result.comments;
                this.$utils.hideLoading();
            },
            async beforeQueryId(id) {
                try {
                    const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmp/send', {
                        dmpUrl: '/link/fieldTemApp/queryByExamplePage',
                        filtersRaw: [{id: 'id', property: 'id', value: id}]
                    })
                    if(data.rows){
                        this.businessId = data.rows[0].businessId;
                        this.comments = data.rows[0].appExplain;
                    } else {
                        this.$showError(`调用fieldTemApp接口失败：${data.result}`);
                        return;
                    }
                } catch (e) {
                    this.$showError(`调用fieldTemApp接口失败：${e}`);
                    console.log(e)
                }
            },
            /**
             * 剪切反馈说明变量的长度，使其不超过200长度
             * <AUTHOR>
             * @date 2020/11/04
             */
            cutContext() {
                if (this.comments.length > 100) {
                    this.comments = this.comments.substr(0, 99);
                }
            },
            /**
             * @desc 判断消费者是否在排期中
             * <AUTHOR>
             * @date 2025-04-10
             **/
            async isScheduling() {
                try {
                    const data = await this.$http.post(this.$env.dmpURL + '/link/consumerScheduleTarget/checkConsumerSchedule', {
                        acctId: this.accountItem.id
                    });
                    if (data.result) {
                        this.$dialog({
                            title: '提示',
                            content: '当前消费者正排期运营中，是否确认申请跟进当前消费者？',
                            confirmText: '确定',
                            onConfirm: async () => {
                                await this.applySubmit();
                            },
                            onCancel: () => {
                                return;
                            },
                            cancelButton: true
                        })
                    } else {
                        await this.applySubmit();
                    }
                } catch (e) {
                    // this.$showError(`查询消费者是否在排期中失败：${e}`);
                    console.log('e', e)
                }
            },
            /**
             *  @description: 提交消费者转交申请
             *  @author: songyanrong
             *  @date: 2020/11/04
             * 2021-01-19 UPDATE 使用场景：一、市场活动-消费者子对象新建 二、活动名单提报-嘉宾新建 三、市场活动-宴席主家新建
             * 场景一：转交老客后需要调用newTransAndCreateFlow，并且插入市场活动-活动人群中间表。场景二三不需要。
             * 场景二：转交老客后需要调用newTransAndCreateFlow，并且将选择的消费者数据携带到【名单提报模块-子对象嘉宾名单的的新建界面】中
             * 场景三：转交老客后需要调用newTransAndCreateFlow，并且携带老客数据返回，供界面选对象展示。
             */
            async applySubmit() {
                if (this.$utils.isEmpty(this.comments)) {
                    this.$message.primary("请填写申请理由");
                    return;
                }
                let params = {
                    custId: this.accountItem.id,    // 渠道消费者ID
                    applyType: 'CustTrans',
                    name: '消费者分配申请' + this.accountItem.name,
                    comments: this.comments,  // 申请说明
                    custUserId: this.accountItem.userId,
                    custPostnId: this.accountItem.postnId
                };
                if (this.pageParam.pageFrom === 'Account') {
                    await this.$http.post(this.$env.appURL + '/action/link/srconsumer/newTransAndCreateFlow', params);
                    this.$nav.back(null, 2)
                } else if (this.pageParam.pageFrom === 'Activity' || this.pageParam.pageFrom === 'ListApply' || this.pageParam.pageFrom === 'masterActivity' || this.pageParam.pageFrom === 'visitRegisterItem' || this.pageParam.pageFrom === 'BookedOrder' || this.pageParam.pageFrom === 'visitApplyItem' || this.pageParam.pageFrom === 'ListReport' || this.pageParam.pageFrom === 'visitPresent'|| this.pageParam.pageFrom === 'scheduleDetail') {
                    await this.$http.post(this.$env.appURL + '/action/link/srconsumer/newTransAndCreateFlow', params);
                    if(this.pageParam.pageFrom === 'Activity'){
                        let consumerDataList = [];
                        const data = {
                            'mcActId': this.marketActivityId,
                            'openId': this.accountItem.openId,
                            'company': this.accountItem.companyName,
                            'jobTitle': this.accountItem.position,
                            'salemanId': this.accountItem.postnId,
                            'accntChannelId': this.accountItem.id,
                            'headUrl': this.accountItem.headUrl,
                            'acctName': this.accountItem.name,
                            'phone': this.accountItem.phoneNumber,
                            'gender': this.accountItem.gender,
                            'birthDate': this.accountItem.birth,
                            'relevanceType': 'New',
                            'birthDateType': this.accountItem.birthType,
                            'empFlag': 'N',
                            'terminalFlag': 'N',
                            'saleman': this.accountItem.fstName
                        };
                        // 执行反馈新增消费者
                        if (this.pageParam.pageSource && this.pageParam.pageSource === 'executiveFeedback') {
                            data.sourceType = 'BackTracking'; // 来源类型
                            data.matchStatus = 'Interaction'; // 匹配状态
                        }
                        consumerDataList.push(data);
                        await this.$http.post(this.$env.appURL + '/marketactivity/link/interCust/batchInsert', consumerDataList, {
                            autoHandleError: false,
                            handleFailed: (response) => {
                                this.$showError(`消费者分配失败：${response.result}`);
                            }
                        });
                        this.$bus.$emit('initConsumers');
                    }
                    if(this.pageParam.pageFrom === 'ListApply'){
                        // mcInterListId 活动id
                        // ids 活动id
                        this.$nav.push('/pages/lj-market-activity/activity-list-report/activity-detail-add-page',
                            {data: this.accountItem,source: 'accountApply',mcInterListId: this.pageParam.marketActivityId,activityType: this.pageParam.activityType,editable: true,ids: this.pageParam.ids})
                    } else if (this.pageParam.pageFrom === 'masterActivity' || this.pageParam.pageFrom === 'BookedOrder' || this.pageParam.pageFrom === 'visitRegisterItem' || this.pageParam.pageFrom === 'visitApplyItem' || this.pageParam.pageFrom === 'visitPresent') {
                        this.pageParam.callback(this.accountItem);
                        this.$nav.back({data: this.accountItem,source: 'accountApply'}, 3);
                    } else if (this.pageParam.pageFrom === 'ListReport') {
                        this.$nav.redirect('/pages/lj-consumers/list-report/guest-detail-add-page',
                            {LangChao25BtnShow:true,data:this.accountItem,lineDataType:this.pageParam.lineDataType, source: 'accountApply',mcInterListId: this.marketActivityId, activityType: this.activityType, editable: true, activityStatus: this.pageParam.activityStatus, brand: this.pageParam.brand, activityName: this.pageParam.activityName, lineStatus: 'Improved'})
                    } else if (this.pageFrom === 'Activity' && this.pageParam.pageSource && (['other', 'preview'].includes(this.pageParam.pageSource))) {
                        this.$nav.back({data: this.accountItem,source: 'accountApply'}, 2);
                    } else {
                            this.$nav.back({data: this.accountItem,source: 'accountApply'}, 3);
                    }
                }
            }
        }
    }
</script>

<style lang="scss">
    .account-apply-page {
        .blank {
            height: 376px;
            width: 100%;
        }
        .main-item {
            width: 100%;
            .head-back{
                width: 100%;
            }

            .top-bg {
                background-repeat: no-repeat;
                background-size: 100% 100%;
                width: 100%;
                height: 500px;
                position: absolute;
                z-index: 1;
                top: 68px;
                .top-bg-1 {
                    width: 100%;
                    height: 12%;
                }

                .top-bg-2 {
                    width: 100%;
                    height: 35%;
                    text-align: center;

                    .top-bg-2-img {
                        width: 164px;
                        height: 164px;
                        border: 6px solid #569bf5;
                        border-radius: 50%;
                    }
                }

                .top-bg-3 {
                    width: 100%;
                    height: 35%;

                    .top-bg-3-text {
                        text-align: center;
                        font-size: 34px;
                        color: white;
                        text-overflow: ellipsis;
                        overflow: hidden;
                        white-space: nowrap;
                    }
                }

                .top-bg-4 {
                    width: 100%;
                    height: 25%;

                    .top-bg-4-1 {
                        height: 88px;
                        width: 100%;
                        display: flex;
                        background-color: rgba(255, 255, 255, 0.2);
                        position: absolute;
                        bottom: 0;

                        .top-bg-4-1-btn {
                            width: 50%;
                            height: 88px;
                            color: white;
                            line-height: 88px;
                            text-align: center;
                        }
                    }
                }
            }
        }
        .account-info-container {
            background-repeat: no-repeat;
            background-size: 100% 100%;
            width: 100%;
            height: 400px;
            position: relative;
            .message-button-box {
                height: 44px;
                width: 100%;
                display: flex;
                background-color: rgba(255, 255, 255, 0.2);
                position: absolute;
                bottom: 0;

                .message-button {
                    width: 50%;
                    height: 44px;
                    color: white;
                    line-height: 44px;
                    text-align: center;
                }
            }
        }

        .account-label-container {
            .list {
                .list-header {
                    font-size: 16px;
                    color: #41484D;
                    min-height: 54px;
                }

                @include label-item-container();

                .label-item-selected {
                    background: rgba(0, 118, 255, 0.75) !important;
                    border: none !important;
                    padding: 2px 24px !important;
                }

                .personal-label {
                    font-size: 16px;
                }
            }
        }

        .account-detail-container {
            margin-top: 10px;
            background-color: white;

            .list-bottom-width-line {
                width: 100%;
                height: 20px;
                background: #F2F2F2;
            }
            .comments {
                width: 90%;
                margin: 0 5%;

                link-textarea {
                    height: 120px;
                    font-size: 14px;
                    padding: 3px;
                }

                .count {
                    text-align: right;
                    padding: 5px 10px;
                    color: #999;
                }

                link-textarea {
                    background: #EEF3F5;
                }
            }
        }
    }
</style>
