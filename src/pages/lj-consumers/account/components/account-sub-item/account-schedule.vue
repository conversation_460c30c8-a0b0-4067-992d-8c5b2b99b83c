<!--
消费者详情-消费者排期-tab
<AUTHOR>
@date 2025-02-17
@file account-schedule.vue
-->
<template>
    <view class="account-schedule">
        <view class="schedule-list-content">
            <link-auto-list :option="scheduleList" hideCreateButton :key="0">
                <template slot-scope="{data,index}">
                    <item :key="index" :data="data" :arrow="false" class="perform-case-list-item"
                          style="padding: 4px 4px 20px 12px;">
                        <view slot="note">
                            <view class="media-list">
                                <view class="media-top">
                                    <status-button>{{ data.status | lov('SCHE_STATUS') }}</status-button>
                                </view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">排期运营名称</view>
                                <view class="name">{{ data.scheduleName ? data.scheduleName : '' }}</view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">排期运营时间类型</view>
                                <view class="name">{{ data.scheTimeType | lov('SCHE_TIME_TYPE') }}</view>
                            </view>
                            <view class="content-middle" v-if="data.conStartTime">
                                <view class="lable-name">消费者排期开始时间</view>
                                <view class="name">{{ (data.conStartTime).substr(0,10) }}</view>
                            </view>
                            <view class="content-middle" v-if="data.conEndTime">
                                <view class="lable-name">消费者排期结束时间</view>
                                <view class="name">{{ (data.conEndTime).substr(0,10) }}</view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">消费者达成状态</view>
                                <view class="name">{{ data.targetStatus | lov('TARGET_STATUS_SCHE') }}</view>
                            </view>
                            <view class="content-middle">
                                <view class="lable-name">消费者排期计划</view>
                                <view class="detail"  @tap="gotoItem(data)">{{ '查看详情' }}</view>
                            </view>
                        </view>
                    </item>
                </template>
            </link-auto-list>
        </view>
    </view>
</template>

<script>
import {$logger} from "@/utils/log/$logger"
import StatusButton from "../../../../lzlj/components/status-button.vue";

export default {
    name: 'account-schedule',
    components: {StatusButton},
    props: {
        oauth: {
            type: Object,
            default: () => {
            }
        },
        accountItem: {
            type: Object,
            required: true
        }
    },
    data() {
        const scheduleList = new this.AutoList(this, {
            url: {
                queryByExamplePage: this.$env.dmpURL + '/link/consumerScheduleTarget/queryConsumerScheduleByAcctId'
            },
            loadOnStart: false,
            param: {
                // dmpUrl: '/link/consumerScheduleTarget/queryConsumerScheduleByAcctId',
                acctId: this.accountItem.id
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.filtersRaw;
                },
                afterLoad: async (data) => {
                }
        },
        });
        return {
            scheduleList,  // 消费者排期列表
        }
    },
    created() {

    },
    async mounted() {
        await this.scheduleList.methods.reload();
    },
    methods: {
        /**
         * @desc 跳转到排期执行情况页面
         * <AUTHOR>
         * @date 2025-02-17
         **/
        gotoItem(data) {
            // 消费者排期详情埋点：事件编码、排期id、排期消费者id（row_Id）、消费者id、菜单id
            try{
                $logger.info('CONSUMER_SCHEDULE_002', 'Click', `菜单id：consumer_schedule_排期id：${data.scheduleId}_排期消费者行id：${data.id}_消费者id：${data.acctId}`);
            } catch (e) {
                console.log('e', e)
                $logger.error('CONSUMER_SCHEDULE_002', 'Click', `菜单id：consumer_schedule_排期id：${data.scheduleId}_排期消费者行id：${data.id}_消费者id：${data.acctId}_报错信息：${e}`);
            }
            this.$nav.push('/pages/lj-consumers/consumers-schedule/consumers-schedule-detail-implementation-page.vue', {
                data: data,
                scheduleId: data.scheduleId,
                scheInfoId: data.id,
                consumerId: data.acctId,
                oauth: this.oauth
            })
        }
    }
}
</script>

<style lang="scss">
.account-schedule {
    .perform-case-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }

    .media-list {
        //line-height: 56px;
        //height: 56px;
        float: right;

        .media-top {
            width: 100%;
            @include flex-start-center;
            @include space-between;

            .num-view {
                width: 75%;
                overflow-x: auto;
                display: flex;
                line-height: 50px;

                .num {
                    flex-shrink: 0;
                    //letter-spacing: 0;
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                    background: #A6B4C7;
                    border-radius: 8px;
                }
            }

            .status-view {
                min-width: 120px;
                height: 36px;
                line-height: 36px;
                text-align: center;
                color: #ffffff;
                background: #2F69F8;
                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                border-radius: 8px;
                font-size: 20px;
                margin-right: 8px;
                -webkit-transform: skewX(-30deg);
                -ms-transform: skewX(-30deg);
                transform: skewX(-30deg);

                &-refuse {
                    background: #FF5A5A !important;
                    box-shadow: 0 3px 4px 0 rgba(255, 90, 90, 0.35) !important;
                }

                .status {
                    transform: skewX(30deg);
                    font-size: 20px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 36px;
                }
            }
        }
    }

    .content-middle {
        width: 95%;
        display: flex;
        align-items: center;
        font-size: 28px;
        letter-spacing: 0;
        font-family: PingFangSC-Regular;
        line-height: 52px;
        overflow-x: auto;

        .lable-name {
            color: #8C8C8C;
            margin-right: 10px;
            min-width: 180px;
            flex-shrink: 0;
            width: 270px;
        }

        .name {
            color: #262626;
            width: calc(100% - 270px);
            //    超出部分滑动显示
            flex-shrink: 1;
            white-space: nowrap;
            overflow: auto;
        }
        .detail {
            color: #306cff;
            //width: 60%;
            //    超出部分滑动显示
            flex-shrink: 1;
            white-space: nowrap;
            overflow: auto;
        }
    }
}
</style>
