<!--
@createdBy 黄鹏
@date 2024/02/27
@description: --- 消费者基础信息
-->
<template>
    <view class="basic-info">
        <link-form :value="accountItem" hideEditButton>
            <view class="item main-item">
                <view class="top-content">
                    <view class="top-content-info"
                          :style="{'background-image': 'url(' + $imageAssets.consumerCardBg + ')'}">
                        <view class="top-content-head">
                            <view class="account-info">
                                <view class="top-content-name">{{ accountItem.name }}</view>
                                <view class="top-content-text" style="max-width: 80px;margin-right: 18px;">{{
                                    accountItem.position }}
                                </view>
                            </view>
                            <view class="is-certify" v-if="accountItem.certify">
                                <view v-if="accountItem.certify === 'certified'" style="height: 100%">
                                    <view class="certify-status"
                                          v-if="!$utils.isEmpty(accountItem.certifyTime) && accountItem.certifyTime !== '9999-12-31 23:59:59'"
                                          :style="{'background-image': 'url(' + $imageAssets.certifyTimeBg + ')'}">
                                        <view class="certify-time">{{ `已认证-${accountItem.certifyTime.split(' ')[0]}到期`
                                            }}
                                        </view>
                                    </view>
                                    <view class="certify-status" v-else
                                          :style="{'background-image': 'url(' + $imageAssets.storeStatusVerifiedImage + ')'}"></view>
                                </view>
                                <view class="certify-status" v-else
                                      :style="{'background-image': 'url(' + $imageAssets.storeStatusUnverifiedImage + ')'}"></view>
                            </view>
                        </view>
                        <view class="top-content-item">
                            <view class="six-model" :class="accountItem.sixToModel" v-if="accountItem.sixToModel">
                                {{accountItem.sixToModel | lov('SIX_TO_MODEL')}}
                            </view>
                            <view class="top-content-label" v-if="accountItem.type">{{ accountItem.type |
                                lov('ACCT_SUB_TYPE') }}
                            </view>
                            <view class="top-content-label" v-if="accountItem.loyaltyLevel">{{
                                accountItem.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}
                            </view>
                            <view class="top-content-label" v-if="accountItem.gender">{{ accountItem.gender |
                                lov('GENDER') }}
                            </view>
                            <view class="black-gold-account" v-if="accountItem.identityLevel" @tap="gotoMemberItem">{{ accountItem.identityLevel |
                                lov('ACCT_SUB_TYPE') }}
                            </view>
                        </view>
                        <view class="top-content-basic">
                            <view class="top-content-basic-left">
                                <view class="top-content-basic-row">{{ accountItem.phoneNumber || '-'}}</view>
                                <view class="top-content-basic-row">{{ accountItem.belongToStore || '-'}}</view>
                                <view class="top-content-basic-row">{{ accountItem.companyName || '-'}}</view>
                            </view>
                            <view class="top-content-basic-right">
                                {{getLabelFlag(accountItem)}}
                            </view>
                        </view>
                    </view>
                    <view class="top-content-btn" :class="btnNum < 2 ? 'only-one-btn' : ''">
                        <!--仅开瓶消费者(已更名为目标消费者)会在此处展示分配按钮-->
                        <view class="top-content-btn-item"
                              v-if="showDistribute"
                              @tap="$emit('openRedistribution', 'OpenBottle')">
                            <link-icon icon="icon-zhongduanzoufang"/>
                            分配
                        </view>
                        <!--国窖和鼎昊有转交按钮 鼎昊没有编辑按钮-->
                        <view class="top-content-btn-item"
                              @tap="transferAccount"
                              v-if="showTransfer">
                            <link-icon icon="icon-zhuanjiao"/>
                            转交
                        </view>
                        <view class="top-content-btn-item"
                              @tap="gotoEditAccountInfo()"
                              v-if="showEdit">
                            <link-icon icon="icon-bianji"/>
                            编辑
                        </view>
                        <view class="top-content-btn-item"
                              @tap="invalidAccount()"
                              v-if="showFollow">
                            <link-icon icon="icon-shixiao"/>
                            取消跟进
                        </view>
                        <!--当前登录职位===消费者跟进职位展示-->
                        <view :class="buttonActive? 'top-content-btn-item in-active' : 'top-content-btn-item'"
                              @tap="apply"
                              v-if="showCertify">
                            <link-icon icon="icon-shixiao"/>
                            认证申请
                        </view>
                    </view>
                </view>
                <view class="detail-tabs">
                    <sub-tabs :currentTab="detailTab" :tabs="detailTabs" @switchTab="detailTabChange"></sub-tabs>
                </view>
                <scroll-view scroll-x="false"
                             scroll-y="true"
                             :style="'height: calc(100vh - 320px);'"
                             @scrolltolower="loadNextPage">
                    <view class="sub-content-con">
                        <link-form v-if="detailTab === 'basic'">
                            <view v-for="(item, index) in standardFieldsShow" :key="index + 'field'">
                                <link-form-item :label="labelText(item)" v-show="!(['companyName','position','detailedAddress'].includes(item.field)&&consumer_display_control==='Y')"
                                                v-if="item.field !== 'city' && item.field !== 'county' && item.field !== 'street' && item.hide !== 'Y'">
                                    <view v-if="accountItem[item.field]">
                                        <view @tap="makePhoneCall" v-if="item.field === 'phoneNumber'">{{
                                            accountItem[item.field] }}
                                        </view>
                                        <view v-else-if="item.field === 'birth'">{{ accountItem[item.field] |
                                            date('YYYY-MM-DD') }}
                                        </view>
                                        <view v-else-if="item.field === 'positionLevel'">{{ accountItem[item.field] |
                                            lov('POSITION_LEVEL') }}
                                        </view>
                                        <view v-else-if="item.field === 'subordinateIndustry'">{{
                                            accountItem[item.field] |
                                            lov('INDUSTRY') }}
                                        </view>
                                        <view v-else-if="item.field === 'companyProperty'">{{ accountItem[item.field] |
                                            lov('COMPANY_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'oldSocialStatus'">{{ accountItem[item.field] |
                                            lov('ACCT_SOCIAL_STATUS') }}
                                        </view>
                                        <view v-else-if="item.field === 'oldAcctRank'">{{ accountItem[item.field] |
                                            lov('ACCT_RANK') }}
                                        </view>
                                        <view v-else-if="!!item.type">{{accountItem[item.field] | lov(item.type)}}
                                        </view>
                                        <view v-else-if="item.field === 'type'">{{ accountItem[item.field] |
                                            lov('ACCT_SUB_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'loyaltyLevel'">{{ accountItem[item.field] |
                                            lov('ACCT_MEMBER_LEVEL') }}
                                        </view>
                                        <view v-else-if="item.field === 'memberType'">{{ accountItem[item.field] |
                                            lov('MEMBER_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'affiliatedUnitType'">{{ accountItem[item.field] |
                                            lov('AFFI_UNIT_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'unit'">{{ accountItem[item.field] |
                                            lov('UNIT') }}
                                        </view>
                                        <view v-else-if="item.field === 'office'">{{ accountItem[item.field] |
                                            lov('OFFICE') }}
                                        </view>
                                        <!--                                    <view v-else-if="item.field === 'memberId'">{{ accountItem[item.field] ? '是' : '否' }}</view>-->
                                        <view v-else-if="item.field === 'registeredMember'">{{ accountItem[item.field]
                                            }}
                                        </view>
                                        <view v-else-if="item.field === 'bringInto'">{{ accountItem[item.field] |
                                            lov('BRINGINTO') }}
                                        </view>
                                        <view v-else-if="item.field === 'province'"> {{ accountItem.province }}{{
                                            accountItem.city }}{{ accountItem.county }}{{ accountItem.street }}
                                        </view>
                                        <view v-else-if="item.field === 'sourceFrom'">{{ accountItem[item.field] |
                                            lov('SOURCE_FROM') }}
                                        </view>
                                        <view v-else-if="item.field === 'empFlag'">{{ accountItem[item.field] |
                                            lov('IMP_FLAG') }}
                                        </view>
                                        <view v-else-if="item.field === 'terminalFlag'">{{ accountItem[item.field] |
                                            lov('YES_OR_NO_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'birthType'">{{ accountItem[item.field] |
                                            lov('BIRTHDAY_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'gender'">{{ accountItem[item.field] |
                                            lov('GENDER')
                                            }}
                                        </view>
                                        <view v-else-if="item.field === 'schedulFlag'">{{ accountItem[item.field] |
                                            lov('YES_OR_NO_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'impFlag'">{{ accountItem[item.field] |
                                            lov('YES_OR_NO_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'accntSourceFrom'">{{ accountItem[item.field] |
                                            lov('ACCNT_SOURCE_FROM') }}
                                        </view>
                                        <view v-else-if="item.field === 'followFlag'">{{ accountItem[item.field] |
                                            lov('FOLLOW_FLAG') }}
                                        </view>
                                        <view v-else-if="item.field === 'auditStatus'">{{ accountItem[item.field] |
                                            lov('CON_AUDIT_STATUS') }}
                                        </view>
                                        <view v-else-if="item.field === 'acctLevel'">{{ accountItem[item.field] |
                                            lov('ACCT_LEVEL') }}
                                        </view>
                                        <view v-else-if="item.field === 'belongToBrand'">{{ accountItem[item.field] |
                                            lov('BRAND') }}
                                        </view>
                                        <view v-else-if="item.field === 'attr06'">{{ accountItem[item.field] |
                                            lov('YES_OR_NO') }}
                                        </view>
                                        <view v-else-if="item.field === 'attr07'">{{ accountItem[item.field] |
                                            lov('YES_OR_NO') }}
                                        </view>

                                        <view v-else>{{ accountItem[item.field]}}</view>
                                    </view>
                                    <view v-else>-</view>
                                </link-form-item>
                            </view>
                            <view>
                                <link-form-item :label="'核心消费者'">
                                    <view v-if="accountItem['attr08']">{{ accountItem['attr08'] |
                                        lov('REAL_CONSUMER_TYPE') }}
                                    </view>
                                    <view v-else>-</view>

                                </link-form-item>
                            </view>
                            <!--                            <view class="label-info" v-for="(item, index) in tagLabels" :key="index + 'tag'">
                                                            <view class="group-label">
                                                                <view class="label-data">{{item.tagGroupName}}</view>
                                                            </view>
                                                            <view class="label-data-info" v-if="item['itemTags']">
                                                                <view v-for="(itemTag, itemIndex) in item['itemTags']" :key="itemIndex" class="label-data-item">
                                                                    <view v-if="itemTag['ifActive'] !== 'N'" class="label-item" >
                                                                        {{itemTag.tagName}}
                                                                    </view>
                                                                </view>
                                                            </view>
                                                        </view>-->

                        </link-form>
                        <link-form v-if="detailTab === 'from'">
                            <view v-for="(item, index) in fromFieldsShow" :key="index + 'field'">
                                <link-form-item :label="labelText(item)"
                                                v-if="item.field!=='city' && item.field !== 'county'&& item.field !== 'street' && item.hide !== 'Y'">
                                    <view v-if="item.field === 'acctLevel'">
                                        <view v-if="accountItem[item.field]">{{ accountItem[item.field] |
                                            lov('ACCT_LEVEL') }}
                                        </view>
                                        <view v-else>-</view>
                                    </view>
                                    <view v-else-if="item.hide !== 'Y'">{{ accountItem[item.field] || '-'}}</view>
                                </link-form-item>
                            </view>
                            <view class="title-item">所属客户</view>
                            <!--                        <link-form-item label="公司开瓶瓶数(瓶)">-->
                            <!--                            <view>{{openedBottles}}</view>-->
                            <!--                        </link-form-item>-->
                            <link-card-list v-for="(item,index) in terminalList.showList"
                                            :key="index + 'store'"
                                            class="store-card">
                                <link-card :class="item.accntId === accountItem.belongToStoreId ? 'acct-main' : ''">
                                    <link-card-item label="客户名称" :content="item.acctName"/>
                                    <link-card-item label="客户规划等级">
                                        <view slot="content">
                                            <text v-if="item.acctLevel">{{item.acctLevel | lov('ACCT_LEVEL')}}</text>
                                            <text v-else>-</text>
                                        </view>
                                    </link-card-item>
                                    <link-card-item label="客户编码" :content="item.acctCode"/>
                                    <!--                                <link-card-item label="客户状态">-->
                                    <!--                                    <view slot="content">-->
                                    <!--                                        <text>{{item.acctStatus | lov('ACCT_STATUS')}}</text>-->
                                    <!--                                    </view>-->
                                    <!--                                </link-card-item>-->
                                </link-card>
                            </link-card-list>
                            <view class="title-item">首次接触客户</view>
                            <link-card-list class="store-card">
                                <link-card :class="'acct-main'" v-if="terminalList.firstContactCustomer">
                                    <link-card-item label="客户名称" :content="terminalList.firstContactCustomer.acctName"/>
                                    <link-card-item label="客户规划等级">
                                        <view slot="content">
                                            <text v-if="terminalList.firstContactCustomer.acctLevel">{{terminalList.firstContactCustomer.acctLevel | lov('ACCT_LEVEL')}}</text>
                                            <text v-else>-</text>
                                        </view>
                                    </link-card-item>
                                    <link-card-item label="客户编码" :content="terminalList.firstContactCustomer.acctCode"/>
                                </link-card>
                                <!-- <view style="color: #BBBBBB;text-align: center;width:100%;margin-bottom: 6rpx;" v-else>暂无首次接触客户</view> -->
                            </link-card-list>
                        </link-form>
                        <link-form v-if="detailTab === 'follow'">
                            <link-form-item label="是否认证" v-if="accountItem.certify">
                                <view>{{ accountItem.certify | lov('CERTIFY') }}</view>
                            </link-form-item>
                            <view v-for="(item, index) in followFieldsShow" :key="index + 'field'">
                                <link-form-item :label="labelText(item)"
                                                v-if="item.field!=='city' && item.field !== 'county' && item.field !== 'street' && item.hide !== 'Y'">
                                    <view v-if="accountItem[item.field]">
                                        <view v-if="item.field === 'impFlag'">{{ accountItem[item.field] |
                                            lov('YES_OR_NO_TYPE') }}
                                        </view>
                                        <view v-else-if="item.field === 'certify'">{{ accountItem[item.field] |
                                            lov('CERTIFY') }}
                                        </view>
                                        <view v-else-if="item.field === 'followFlag'">{{ accountItem[item.field] |
                                            lov('FOLLOW_FLAG') }}
                                        </view>
                                        <view v-else-if="item.field === 'auditStatus'">{{ accountItem[item.field] |
                                            lov('CON_AUDIT_STATUS') }}
                                        </view>
                                        <view v-else-if="item.hide !== 'Y'">{{ accountItem[item.field]}}</view>
                                    </view>
                                    <view v-else>-</view>
                                </link-form-item>
                            </view>
                        </link-form>
                        <link-form v-if="detailTab === 'custom'">
                            <view v-for="(custItem, custIndex) in customFields" :key="custIndex + 'head'">
                                <view v-for="(item,index) in custItem.componentList" :key="index + 'cust'">
                                    <link-form-item :label="item.values.fieldName"
                                                    readonly
                                                    :arrow="false"
                                                    v-if="item.hide !== 'Y'"
                                                    :vertical="['link-textarea', 'lnk-img-watermark'].includes(item.ctrlCode)">
                                        <view v-if="customData[item.values.field]">
                                            <view v-if="item.ctrlCode === 'view-line'"
                                                  style="display: flex;align-items: center;flex-wrap: wrap;justify-content: flex-end;">
                                                <text v-if="!$utils.isEmpty(customData[item.values.field].province)">{{
                                                    customData[item.values.field].province }}
                                                </text>
                                                <text v-if="!$utils.isEmpty(customData[item.values.field].city)">{{
                                                    customData[item.values.field].city }}
                                                </text>
                                                <text v-if="!$utils.isEmpty(customData[item.values.field].district)">{{
                                                    customData[item.values.field].district }}
                                                </text>
                                                <text v-if="!$utils.isEmpty(customData[item.values.field].street)">{{
                                                    customData[item.values.field].street }}
                                                </text>
                                            </view>
                                            <view v-else-if="item.ctrlCode === 'lnk-img-watermark'">
                                                <lnk-img-watermark :parentId="accountItem.id"
                                                                   moduleType="consumerattachment"
                                                                   :pathKeyArray="imgList"
                                                                   :addLength="item.values.maxUploadNum"
                                                                   filePathKey="/consumerattachment/"
                                                                   ref="fakeTerminalPhoto">
                                                </lnk-img-watermark>
                                            </view>
                                            <account-detail-jsx-component v-else :option="item" :formData="customData"/>
                                        </view>
                                        <view v-else-if="item.ctrlCode === 'lnk-img-watermark'">
                                            <lnk-img-watermark :parentId="accountItem.id"
                                                               moduleType="consumerattachment"
                                                               :pathKeyArray="imgList"
                                                               :addLength="item.values.maxUploadNum"
                                                               filePathKey="/consumerattachment/"
                                                               ref="fakeTerminalPhoto">
                                            </lnk-img-watermark>
                                        </view>
                                        <view v-else>-</view>
                                    </link-form-item>
                                </view>
                            </view>
                        </link-form>
                    </view>
                    <view class="no-more">- 没有更多了 -</view>
                </scroll-view>
            </view>
        </link-form>
        <link-dialog ref="transferDialog"
                     :noPadding="true"
                     v-model="dialogFlag"
                     height="30vh"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">请选择职位</view>
                <view class="iconfont icon-close" @tap="dialogFlag = false"></view>
            </view>
            <scroll-view scroll-y="true">
                <link-form ref="form" :value="selectedpostn">
                    <link-form-item label="职位" field="postnName">
                        <link-input v-model="selectedpostn.postnName" @tap="gotoPostnList()" placeholder="请选择职位"
                                    suffixIcon="icon-right" :readonly="Boolean('true')"> >
                        </link-input>
                    </link-form-item>
                </link-form>
            </scroll-view>
            <link-sticky>
                <link-button block @tap="confirmTransfer">确认转交</link-button>
            </link-sticky>
        </link-dialog>
    </view>
</template>

<script>
    import {ROW_STATUS} from "../../../../../utils/constant";
    import {ComponentUtils} from "link-taro-component";
    import ConsumerCommon from "../../../consumer-common";
    import AccountDetailJsxComponent from '../../account-detail-jsx-component';
    import subTabs from '../sub-tabs/sub-tabs';
    import LnkImgWatermark from '../../../../core/lnk-img-watermark/lnk-img-watermark';

    export default {
        name: "basic-info",
        components: {LnkImgWatermark, AccountDetailJsxComponent, subTabs},
        mixins: [ConsumerCommon()],
        props: {
            pageFrom: {
                type: String,
                default: ''
            },
            consumer_display_control: { //是否控制 'companyName','position','detailedAddress'展示与否
                type: String,
                default: 'N' // N展示 Y不展示
            },
            showButton: {
                type: Boolean,
                default: false
            },
            accountItem: {
                type: Object,
                default: () => {
                }
            },
            customData: {
                type: Object,
                default: () => {
                }
            },
            highMemberItem: {
                type: Object,
                default: () => {
                }
            }
        },
        data() {
            return {
                detailTabs: [
                    {name: "消费者信息", val: "basic"},
                    {name: "消费者来源", val: "from"},
                    {name: "跟进信息", val: "follow"},
                    {name: "自定义信息", val: "custom"}
                ],
                detailTab: 'basic',
                fromFields: ['recommenderAcctName', 'acctLevel', 'assessTerminalName'], // 推荐人（客户）、客户规划等级、考核终端
                followFields: ['impFlag', 'certify', 'followFlag', 'auditStatus'], // 是否重点跟进、未查到、跟进状态、审核状态
                standardFieldsShow: [],
                followFieldsShow: [],
                fromFieldsShow: [],
                transferType: '',                       // 转交操作
                tagLabels: [],
                imgList: [],                  // 图片数组
                terminalList: {
                    list: [],
                    showList: [],
                    firstContactCustomer: null,
                    total: 0,
                    page: 1
                },            // 门店信息
                openedBottles: 0,   //开瓶瓶数
                postnList: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/position/postnOfUserForMp'
                    },
                    param: {
                        sort: '',
                        order: 'desc',
                        oauth: 'ALL',
                        rows: 20,
                        page: 1,
                        companyId: '',
                        filtersRaw: []
                    },
                    searchFields: ['fstName', 'postnName', 'orgName'],
                    sortOptions: null,
                    hooks: {
                        async beforeLoad(options) {
                            options.param.sort = 'created';
                            let companyName = this.userInfo.coreOrganizationTile.brandCompanyCode === '5600' ? '鼎昊公司' : '国窖公司';
                            options.param.companyId = await this.$lov.getValByTypeAndName('ACTIVITY_COMPANY', companyName);
                            const lovData = await this.$lov.getLovByType('ASSIGN_POSITION');
                            let str = '';
                            lovData.forEach((item) => {
                                str = item.val + ',' + str
                            });
                            str = str.substring(0, str.length - 1);
                            options.param.filtersRaw = [
                                ...options.param.filtersRaw,
                                {id: 'positionType', property: 'positionType', value: `[${str}]`, operator: 'IN'}

                            ];
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.fstName} data={data} note={data.postnName}
                                  desc={data.orgName}>
                            </item>
                        )
                    }
                }),
                dialogFlag: false,                //职位弹框
                selectedpostn: {},                //选中职位对象
                showBrand: [],                    // 品牌类型
                exceptCompanyIds: null
            }
        },
        computed: {
            /**
             * 认证申请是否置灰
             * @author:  胡益阳
             * @date:  2024/8/13
             */
            buttonActive() {
                return this.accountItem.type === 'ToBeFollowed' || this.accountItem.certify === 'certified' && (this.$utils.isEmpty(this.accountItem.certifyTime) || this.accountItem.certifyTime === '9999-12-31 23:59:59')
            },
            showDistribute () {
                return this.pageFrom === 'OpenBottle';
            },
            showTransfer () {
                return false
                // return this.showButton && (this.userInfo.coreOrganizationTile.brandCompanyCode === '5600' || this.userInfo.coreOrganizationTile.brandCompanyCode === '1210');
            },
            showEdit () {
                return this.showButton && this.userInfo.coreOrganizationTile.brandCompanyCode !== '1210';
            },
            showFollow () {
                return this.showButton && this.userInfo.coreOrganizationTile.brandCompanyCode !== '1210';
            },
            showCertify () {
                return this.showButton && this.userInfo.postnId === this.accountItem.postnId && this.exceptCompanyIds && !this.exceptCompanyIds.includes(this.accountItem.belongToCompanyId);
            },
            btnNum () {
                const arr = [this.showDistribute, this.showTransfer, this.showEdit, this.showFollow, this.showCertify].filter((item) => item);
                return arr.length;
            }
        },
        async created() {
            await this.getExceptCompanyIds();
            this.standarFields = this.standarFields.filter(item => item.field !== 'city' && item.field !== 'county' && item.field !== 'street')
            await this.checkFieldsInfo('view');
            // 过滤所属客户
            this.standarFields = this.standarFields.filter(item => item.field !== 'belongToStore')
            // 如果登陆职位所属公司不是窖龄、怀旧公司，则隐藏会员类型、所属单位类型、单位、职务层级、身份分字段
            // 查询企业参数配置-参数键New_Type_Company
            const obj = await this.$utils.getCfgProperty('New_Type_Company');
            const cfgArray = obj.split(',')
            if (!cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                this.standarFields = this.standarFields.filter(item => item.field !== 'memberType' && item.field !== 'affiliatedUnitType' && item.field !== 'unit' && item.field !== 'office' && item.field !== 'identityScore')
            }
            if(cfgArray.includes(this.userInfo.coreOrganizationTile['l3Id'])) {
                // 如果数组 kvAllFields 中的元素在 accountItem 中不存在或为空，则隐藏
                const kvAllFields = ['socialCircle', 'positionGrade', 'enterpriseLevel', 'enterpriseSize', 'personnelType'];
                kvAllFields.forEach((item) => {
                    if (!this.accountItem[item]) {
                        this.standarFields = this.standarFields.filter((field) => field.field !== item);
                    }
                })
            }
            this.standardFieldsShow = this.standarFields.filter((item) => !this.fromFields.includes(item.field) && !this.followFields.includes(item.field))
            this.fromFieldsShow = this.standarFields.filter((item) => this.fromFields.includes(item.field))
            this.followFieldsShow = this.standarFields.filter((item) => this.followFields.includes(item.field))
            await this.initTerminal();
            this.queryCusAttachment();
            // 构建首次推荐人（消费者）字段
            const exceptField = {
                label: '首次推荐人（消费者）',
                field: 'firstCustomersName',
                hide: 'N'
            };
            // 在推荐人（员工）字段前面插入 exceptField 对象
            const index = this.standardFieldsShow.findIndex(item => item.field === 'interReference');
            if (index !== -1) {
                this.standardFieldsShow.splice(index, 0, exceptField);
            }
        },
        watch:{
            'accountItem.attr10'(v){
                this.getAttr10FirstList();
            }
        },
        methods: {
            async getExceptFields() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/fieldTempConfig/queryByExamplePage');
            },
            /**
             * @createdBy 杨剑飘
             * @date 2024/10/16
             * @methods: getAttr10FirstList
             * @para:
             * @description: 刷新首次接触客户
             **/
            async getAttr10FirstList(){
                if (this.$utils.isEmpty(this.accountItem.attr10)) {
                   return console.log('暂无attr10');
                }

                const data = await this.$http.post(this.$env.appURL + '/action/link/accnt/queryById', {
                    id: this.accountItem.attr10,
                }, {
                    autoHandleError: false,
                    handleFailed: (data) => {
                        if (!data.success) {
                            this.$showError('查询首次接触客户数据失败！' + data.result);
                        }
                    }
                });
                if (data.success) {
                    this.terminalList.firstContactCustomer = data.result;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/09/03
             * @methods: getExceptCompanyIds
             * @para:
             * @description: 获取不显示认证申请的公司id
             **/
            async getExceptCompanyIds() {
                const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/publicGetCfg', {key: 'DING_ID'});
                if (data.success) {
                    this.exceptCompanyIds = data.value.split(',');
                } else {
                    this.$message.error('查询是否展示按钮失败：' + data.result);
                }
            },
            /**
             * 消费者认证申请
             * @author:  胡益阳
             * @date:  2024/8/13
             */
            apply() {
                if (this.accountItem.type === 'ToBeFollowed') {
                    this.$message.warn('普通会员不可申请数字化认证!');
                    return
                }
                if (this.buttonActive) {
                    this.$message.warn('当前消费者已认证！');
                    return
                }
                this.$nav.push('/pages/lj-consumers/account/account-authentication-apply-page', {
                    accountItem: this.accountItem,
                    pageFrom: 'AccountItem'
                });
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: detailTabChange
             * @para:
             * @description: 消费者详情tab切换
             **/
            detailTabChange(val) {
                this.detailTab = val;
            },
            /**
             * @desc 跳转超高价值身份详情
             * <AUTHOR>
             * @date 2023/6/19 20:09
             **/
            async gotoMemberItem() {
                this.$nav.push('/pages/lj-consumers/high-member/high-member-item-page', {
                    accountItem: this.accountItem,
                    data: this.highMemberItem,
                    pageFrom: 'AccountItem'
                });
            },
            /**
             * @desc 消费者转交
             * <AUTHOR>
             * @date 2023/6/14 10:45
             **/
            transferAccount() {
                this.transferType = 'transferAccount';
                this.dialogFlag = true;
                this.selectedpostn = {};
            },
            /**
             * @desc kv序列显示值
             * <AUTHOR>
             * @date 2023/5/10 11:55
             * @param item 对应的字段对象
             **/
            labelText(item) {
                if (item.field === 'province') {
                    return '所在地区'
                } else if (item.field === 'type') {
                    return '影响力（K序列）等级'
                } else if (item.field === 'loyaltyLevel') {
                    return '购买力（V序列）等级'
                } else {
                    return item.label
                }
            },
            /**
             * @desc 将该消费者状态改为未分配
             * <AUTHOR>
             * @date 2022/4/22 16:10
             **/
            invalidAccount() {
                this.$taro.showModal({
                    title: '提示',
                    content: '取消后无法继续跟进消费者，是否确认取消跟进？',
                    success: async (res) => {
                        if (res.confirm) {
                            this.$utils.showLoading();
                            const data = await this.$http.post(this.$env.dmpURL + '/link/consumer/unassignedUpdate', {
                                id: this.accountItem.id,
                                interfaceSource: 'Artificial'
                            }, {
                                autoHandleError: false,
                                handleFailed: (response) => {
                                    this.$utils.hideLoading();
                                    this.$showError('取消跟进消费者失败！' + response.result);
                                }
                            });
                            if (data.success) {
                                this.$utils.hideLoading();
                                this.$message.success('取消跟进成功！');
                                this.$bus.$emit('consumerListFresh');
                                this.$nav.back();
                            }
                        }
                    }
                });
            },
            /**
             * 消费者附件查询
             * @author:  胡益阳
             * @date:  2024/7/25
             */
            queryCusAttachment() {
                try {
                    this.$http.post(`${this.$env.appURL}/action/link/sendDmp/send`,
                        {
                            pageFlag: false,
                            rows: 50,
                            page: 1,
                            dmpUrl: '/link/cusAttachment/queryByExamplePage',
                            order: 'desc',
                            sort: 'created',
                            filtersRaw: [
                                {id: 'sourceId', operator: '=', property: 'sourceId', value: this.accountItem.id},
                                {
                                    id: 'uploadStatus',
                                    operator: 'in',
                                    property: 'uploadStatus',
                                    value: '[Effective, NewlyBuild]'
                                }
                            ]
                        }
                    ).then((res) => {
                        if (res.success) {
                            this.imgList = res.rows;
                        }
                    })
                } catch (e) {
                }
            },
            /**
             * @desc 拨打电话
             * <AUTHOR>
             * @date 2021/8/9 09:24
             **/
            makePhoneCall() {
                this.$taro.makePhoneCall({
                    phoneNumber: this.accountItem.phoneNumber
                })
            },
            /**
             * @createdBy  张丽娟
             * @date  2021/3/17
             * @description 确认转交职位
             */
            confirmTransfer: ComponentUtils.debounce(async function () {
                if (this.$utils.isEmpty(this.selectedpostn.postnName)) {
                    this.$message.warn('请选择职位')
                    return
                }
                this.$utils.showLoading("提交中");
                let url = this.$env.dmpURL + '/action/link/consumer/transfer';
                let param = {
                    id: this.accountItem.id,
                    postnId: this.selectedpostn.postnId,
                    phoneNumber: this.accountItem.phoneNumber,
                    belongToCompanyId: this.accountItem.belongToCompanyId,
                    interfaceSource: 'Artificial'
                };
                if (this.transferType === 'transferMember') {
                    url = this.$env.appURL + '/link/highIdentity/followChange';
                    param = {
                        id: this.accountItem.identityId,
                        followId: this.selectedpostn.postnId
                    }
                }
                const data = await this.$http.post(url, param, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`转交失败：${response.result}`);
                        this.dialogFlag = false;
                    }
                })
                if (data.success) {
                    this.$utils.hideLoading();
                    this.dialogFlag = false;
                    this.$message.success('转交成功!');
                }

            }, 1000),
            /**
             * @createdBy  张丽娟
             * @date  2021/3/17
             * @description 跳转到转交列表返回选中职位数据
             */
            async gotoPostnList() {
                const data = await this.$object(this.postnList, {
                    pageTitle: '选择职位',
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} title={data.fstName} data={data} note={data.deptName}
                                  desc={data.postnName}>
                            </item>
                        )
                    }
                })
                this.selectedpostn = data
            },
            /**
             *  初始化数据
             *
             *  <AUTHOR>
             *  @date        2020-07-27
             */
            async init() {
                this.initTerminal();
            },
            /**
             * 查询门店信息列表
             * <AUTHOR>
             * @date 2020-10-12
             * */
            async initTerminal() {
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    return;
                }
                let filtersRaw = [{
                    id: 'consumerId',
                    property: 'consumerId',
                    operator: '=',
                    value: this.accountItem.id
                }];
                const data = await this.$http.post(this.$env.appURL + '/action/link/interCustTerminal/queryByExamplePage', {
                    filtersRaw: filtersRaw,
                    pageFlag: true,
                    rows: 2000,
                    page: this.terminalList.page,
                }, {
                    autoHandleError: false,
                    handleFailed: (data) => {
                        if (!data.success) {
                            this.$showError('查询所属门店数据失败！' + data.result);
                        }
                    }
                });
                if (data.rows && data.rows.length) {
                    const arr = data.rows;
                    const find = arr.find((item) => item.accntId === this.accountItem.belongToStoreId);
                    const tempArr = arr.filter((item) => item.accntId !== this.accountItem.belongToStoreId);
                    if (find) {
                        tempArr.unshift(find);
                    }
                    this.terminalList.list = tempArr;
                    this.terminalList.showList = this.terminalList.list.slice(0, this.terminalList.page * 20);
                    this.terminalList.total = data.total;

                }
                this.getAttr10FirstList(); // 刷新首次接触客户
            },
            /**
             * 前往客户编辑
             * <AUTHOR>
             * @date 2020-07-27
             */
            gotoEditAccountInfo() {
                this.accountItem.row_status = ROW_STATUS.UPDATE;
                this.$nav.push('/pages/lj-consumers/account/account-item-edit-page', {
                    data: this.accountItem,
                    userInfo: this.userInfo,
                    pageFrom: 'accountItem',
                    consumer_display_control: this.consumer_display_control,
                    callback: () => {
                        // 编辑返回刷新数据
                        this.terminalList = {
                            list: [],
                            firstContactCustomer: null,
                            showList: [],
                            total: 0,
                            page: 1
                        };
                        this.init();
                        this.$emit('fresh');
                    }
                });
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/05
             * @methods: loadNextPage
             * @para:
             * @description: 终端列表加载下一页
             **/
            loadNextPage() {
                if (this.detailTab === 'from') {
                    if (this.terminalList.showList.length >= this.terminalList.total) return;
                    this.terminalList.page = this.terminalList.page + 1;
                    this.terminalList.showList = this.terminalList.list.slice(0, this.terminalList.page * 20);
                }
            },
            /**
             * @desc 查询公司开瓶瓶数
             * <AUTHOR>
             * @date 2023/8/28 15:49
             **/
            async queryOpenedBottles() {
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    this.$showError('请检查消费者数据是否正确');
                    return;
                }
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.dmpURL + '/link/cdcPubConsumer/queryOpenedBottles', {id: this.accountItem.id}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError(`查询开瓶瓶数数据失败：${response.result}`);
                    }
                });
                if (data.success) {
                    this.openedBottles = data.result
                    this.$utils.hideLoading();
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/10/10
             * @methods: getLabelFlag
             * @para:
             * @description: 终端和重点客户同时出现时，优先展示终端
             **/
            getLabelFlag (data) {
                // console.log("getLabelFlag??????", data);
                if (data.impFlag === 'Y' && this.pageFrom!=='PeopleScreen' && data.terminalFlag === 'Y') {
                    return '终端';
                } else {
                    if (data.impFlag === 'Y' && this.pageFrom!=='PeopleScreen') {
                        return '重点客户';
                    } else if (data.terminalFlag === 'Y') {
                        return '终端';
                    } else {
                        return '';
                    }
                }
            }
        }
    }
</script>

<style lang="scss">
    .basic-info {
        background-color: #F2F2F2;
        font-size: 28px;
        /*position: fixed;*/
        .main-item {

            .no-more {
                text-align: center;
                color: #BBBBBB;
                margin-bottom: 60px;
            }


            .top-content {
                background-repeat: no-repeat;
                background-size: 100% 100%;
                box-sizing: border-box;
                display: flex;
                flex-direction: column;
                margin: 16px 24px 20px 24px;
                /*padding: 32px;*/
                border-radius: 24px;
                background-color: #2A55EC;

                .top-content-info {
                    display: flex;
                    flex-direction: column;
                    background-size: 100% 100%;
                    background-repeat: no-repeat;
                    padding: 26px;
                    border-radius: 26px;
                    border-bottom: 1px solid #8DA6FF;
                    overflow: hidden;

                    .top-content-head {
                        display: flex;
                        justify-content: space-between;
                        flex-wrap: nowrap;
                        margin: 16px 0;

                        .account-info {
                            display: flex;
                            align-items: flex-end;
                            .top-content-name {
                                max-width: 250px;
                                height: 44px;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 600;
                                font-size: 32px;
                                color: #FFFFFF;
                                line-height: 44px;
                                text-align: left;
                                font-style: normal;
                                margin-right: 16px;
                                overflow: hidden;
                                text-overflow: ellipsis;
                                white-space: nowrap;
                            }

                            .top-content-text {
                                max-width: 424px;
                                height: 34px;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 24px;
                                color: #FFFFFF;
                                line-height: 34px;
                                text-align: left;
                                font-style: normal;
                                white-space: nowrap;
                                overflow: hidden;
                                text-overflow: ellipsis;
                            }
                        }

                        .is-certify {
                            height: 44px;

                            .certify-status {
                                min-width: 120px;
                                height: 100%;
                                background-size: 100% 100%;
                                background-repeat: no-repeat;

                                .certify-time {
                                    margin-left: 46px;
                                    margin-right: 20px;
                                    height: 44px;
                                    font-family: PingFangSC, PingFang SC;
                                    font-weight: 500;
                                    font-size: 20px;
                                    color: #212223;
                                    line-height: 44px;
                                    text-align: left;
                                    font-style: normal;
                                    background: linear-gradient(0deg, #FFF1CF 0%, #FFCF96 100%);
                                    -webkit-background-clip: text; /*将设置的背景颜色限制在文字中*/
                                    -webkit-text-fill-color: transparent;
                                }
                            }
                        }
                    }

                    .top-content-item {
                        display: flex;
                        align-items: center;
                        flex-wrap: wrap;

                        .top-content-label {
                            margin-right: 12px;
                            margin-bottom: 16px;
                            height: 36px;
                            width: auto;
                            background: #E6E6E6;
                            border-radius: 4px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 22px;
                            color: #6A6D75;
                            line-height: 36px;
                            text-align: left;
                            font-style: normal;
                            padding: 0 12px;
                            letter-spacing: 0;
                        }

                        .black-gold-account {
                            margin-right: 12px;
                            margin-bottom: 16px;
                            padding: 0 12px;
                            height: 36px;
                            background: #262626;
                            border-radius: 4px;
                            width: auto;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 22px;
                            color: #F0BE94;
                            line-height: 36px;
                            text-align: left;
                            font-style: normal;
                            white-space: nowrap; /* 防止文本换行 */
                            overflow: hidden; /* 隐藏超出的内容 */
                            text-overflow: ellipsis; /* 使用省略号表示超出的内容 */
                        }

                        .six-model {
                            margin-right: 12px;
                            margin-bottom: 16px;
                            padding: 0 12px;
                            height: 36px;
                            width: auto;
                            border-radius: 4px;
                            font-family: PingFangSC, PingFang SC;
                            font-weight: 400;
                            font-size: 22px;
                            line-height: 36px;
                            text-align: left;
                            font-style: normal;

                            &.Hear {
                                color: #167EE3;
                                background: linear-gradient(335deg, #CEE5FD 0%, #EAF8FF 37%, #C0E4FF 100%);
                            }

                            &.RepeatBuy {
                                color: #B27300;
                                background: linear-gradient(335deg, #FEE5B7 0%, #FFF9EA 37%, #F3D59F 100%);
                            }

                            &.Drink {
                                color: #554BFF;
                                background: linear-gradient(335deg, #E0DDFF 0%, #F1F0FF 37%, #D6D3F9 100%);
                            }

                            &.BecomeFan {
                                color: #E051D5;
                                background: linear-gradient(326deg, #FFD2FC 0%, #FFF9FF 49%, #FFD2FC 100%);
                            }

                            &.See {
                                color: #E68536;
                                background: linear-gradient(142deg, #FFD9BB 0%, #FFF8F2 45%, #FFE5D1 100%);
                            }

                            &.Buy {
                                color: #05C49B;
                                background: linear-gradient(142deg, #A2EFDE 0%, #EDFDFA 45%, #C5F2E8 100%);
                            }
                        }
                    }

                    .top-content-basic{
                        display: flex;
                        align-items: flex-start;
                        justify-content: space-between;

                        .top-content-basic-left{
                            display: flex;
                            align-items: flex-start;
                            justify-content: flex-start;
                            flex-direction: column;
                            .top-content-basic-row{
                                max-width: 520px;
                                overflow-x: scroll;
                                height: 42px;
                                font-family: PingFangSC, PingFang SC;
                                font-weight: 400;
                                font-size: 24px;
                                color: #FFFFFF;
                                line-height: 42px;
                                text-align: left;
                                font-style: normal;
                            }
                        }
                        .top-content-basic-right{
                            width: 134px;
                            font-family: YouSheBiaoTiHei;
                            font-size: 72px;
                            color: #333333;
                            line-height: 60px;
                            text-align: center;
                            background: linear-gradient(239.81787217365064deg, rgba(220,238,255,0.32) 0%, #DAE6FF 35%, rgba(235,241,255,0.42) 67%, rgba(209,222,253,0) 100%);
                            -webkit-background-clip: text;/*将设置的背景颜色限制在文字中*/
                            -webkit-text-fill-color: transparent;
                        }
                    }

                }

                .top-content-btn {
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                    padding: 32px;

                    .only-one-btn{
                        justify-content: flex-start;
                    }

                    .top-content-btn-item {
                        /*width: 33%;*/
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #FFFFFF;
                        font-weight: 400;
                        text-align: center;

                        .link-icon {
                            /*margin-right: 8px;*/
                        }
                    }

                    .in-active {
                        color: #abaaba !important;
                    }

                    .btn-split-line {
                        width: 0;
                        height: 20px;
                        border-left: 1px solid #8DA6FF;
                    }
                }
            }

            .detail-tabs {
                margin: 10px 24px;
            }

            .sub-content-con {
                margin: 24px;
                border-radius: 24px !important;
                background-color: #fff;
                /*deep*/
                .link-item {
                    border-radius: 24px !important;
                    min-height: 68px;
                    padding: 8px 24px;
                }

                /*deep*/
                .link-item-title {
                    color: #999 !important;
                }

                /*deep*/
                .link-item-content {
                    color: #333 !important;
                }

                .acct-main {
                    /*deep*/
                    .link-card-content {
                        background-color: #F6F8FE;
                    }
                }

                /*deep*/
                .link-card-item-label {
                    color: #999 !important;
                }

                /*deep*/
                .link-card-item-content {
                    color: #333 !important;
                }

                /*deep*/
                .link-date {
                    .link-input-inner {
                        padding-right: 28px !important;
                    }
                }
            }


        }

        .store-card .link-card, .store-card {
            padding: 0;
        }

        .store-card .link-card .link-card-body {
            border-radius: 0 !important;
        }

        .store-card .link-card .link-card-body .link-card-content .link-card-item .link-card-item-label, .store-card .link-card .link-card-body .link-card-content .link-card-item .link-card-item-content {
            color: inherit;
        }

        .title-item {
            background: white;
            font-size: 32px;
            line-height: 48px;
            color: #3F66EF;
            padding: 28px 0;
            margin: 0 28px;
            border-top: 1px solid #EDEEF1;
        }

        .label-info {
            background: white;
            font-size: 28px;

            .group-label {
                display: flex;
                justify-content: space-between;
                margin: 0 28px;
                padding: 28px 0;
                border-bottom: 1px solid #efefef;
            }

            .label-data-info {
                margin: 0 28px;
                padding: 0 0 28px;
                border-bottom: 1px solid #efefef;
                display: flex;
                flex-wrap: wrap;
                align-items: center;
                height: auto;

                .label-item {
                    background-color: #2f69f8;
                    color: white;
                    display: inline-flex;
                    align-items: center;
                    flex-wrap: nowrap;
                    font-size: 24px;
                    border-radius: 8px;
                    padding: 8px 16px;
                    margin-right: 24px;
                    margin-top: 24px;
                }
            }
        }

        .model-title {
            display: flex;
            align-items: center;
            justify-content: flex-end;
            padding: 20px;

            .title {
                width: 56%;
            }

            .iconfont {
                font-size: 40px;
            }
        }
    }
</style>

