<!--
@createdBy 黄鹏
@date 2024/03/01
@description: ---  触达记录
-->
<template>
    <view class="touch-record">
        <view class="sub-tab-con">
            <sub-tabs :currentTab="currentTab" :tabs="tabs" :is-scroll="true" @switchTab="detailTabChange"></sub-tabs>
        </view>
        <view class="content-con">
            <select-button :current-dimension="currentDimension" @change-btn="changeDimension"></select-button>
            <view v-if="currentTab === 'board'" class="board-con">
                <view class="tip-con">
                    <view class="tips" @tap="queryInfo">
                        <link-icon icon="mp-info-lite" status="info"/>
                        <view class="tips-content">数据指标说明</view>
                    </view>
                    <view class="deadline">截止至{{cusTouchObj.endTime}}数据统计</view>
                </view>
                <view class="cus-touch">
                    <view class="cus-touch-item" v-for="item in cusTouchList">
                        <view class="item-row">{{item.name}}</view>
                        <view class="item-row">{{typeof cusTouchObj[item.field] === 'number'?cusTouchObj[item.field]:'-'}}
                        </view>
                    </view>
                </view>
            </view>
            <view v-else-if="currentTab === 'onLine'" class="activity-con">
                <link-auto-list :option="currentDimension === 'year' ? activityFyOption :  activityOption"
                                :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="activity-item-con">
                            <view class="activity-dec">
                                <view class="activity-start-time">{{data.created | date('YYYY-MM-DD') }}</view>
                                <view class="activity-status"> {{ data.status | lov('MC_STATUS') }}</view>
                            </view>
                            <view class="activity-name">{{data.activityName}}</view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view v-else-if="currentTab === 'offLine'" class="activity-con">
                <link-auto-list :option="currentDimension === 'year' ? activityFyOption :  activityOption"
                                :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="activity-item-con">
                            <view class="activity-dec">
                                <view class="activity-start-time">{{data.created | date('YYYY-MM-DD') }}</view>
                                <view class="activity-status"> {{ data.status | lov('MC_STATUS') }}</view>
                            </view>
                            <view class="activity-name">{{data.activityName}}</view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view v-else-if="currentTab === 'present'" class="present-con">
                <view class="present-title">
                    <view class="present-type">礼赠类型</view>
                    <view class="creator">创建人</view>
                    <view class="audit-time">登记时间</view>
                </view>
                <link-auto-list :option="presentOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="present-line" @tap="gotoPresentItem(data)">
                            <view class="present-type">{{ data.preType | lov('PRE_TYPE') }}</view>
                            <view class="creator">{{data.createdName}}</view>
                            <view class="audit-time">{{ data.auditTime |date('YYYY-MM-DD') }}</view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
            <view v-else-if="currentTab === 'visit'" class="visit-con">
                <link-auto-list :option="visitOption" :key="currentTab + currentDimension">
                    <template slot-scope="{data,index}">
                        <view class="visit-item-con" @tap="gotoVisitRegisterItem(data)">
                            <view class="visit-item-row">
                                <view class="row-title">拜访方式：</view><view class="row-content">{{ data.visitWay | lov('CSM_VISIT_WAY') }}</view>
                            </view>
                            <view class="visit-item-row">
                                <view class="row-title">拜访时间：</view><view class="row-content">{{ data.visitTime | date('YYYY-MM-DD HH:mm:ss') }}</view>
                            </view>
                            <view class="visit-item-row">
                                <view class="row-title">拜访单位：</view><view class="row-content">{{data.visitCompany}}</view>
                            </view>
                            <view class="visitor-name"> {{ data.visitorName }}</view>
                        </view>
                    </template>
                </link-auto-list>
            </view>
        </view>
    </view>
</template>

<script>
    import subTabs from '../sub-tabs/sub-tabs';
    import selectButton from './components/select-button';
    import Taro from "@tarojs/taro";

    export default {
        name: "touch-record",
        components: {selectButton, subTabs},
        props: {
            isAllData: {
                type: Boolean,
                required: false,
                default:false,
            },
            accountItem: {
                type: Object,
                required: true
            }
        },
        data() {
            const userInfo = Taro.getStorageSync('token').result;         // 获取用户信息

            return {
                userInfo,
                currentDimension: 'year',
                cusTouchList: [{name: "品", field: "pinQty"},
                    {name: "赠", field: "donateQty"},
                    {name: "游", field: "wanderQty"},
                    {name: "深度体验", field: "deepExperQty"},
                    {name: "事件营销", field: "eventsQty"},
                    {name: "其他活动", field: "otherActQty"},
                    {name: "线上活动", field: "onlineActQty"},
                    {name: "拜访次数", field: "visitQty"},
                    {name: "礼赠次数", field: "presentQty"}],
                cusTouchObj: {},
                currentTab: 'board',
                tabs: [
                    {name: "触达看板", val: "board"},
                    {name: "线上活动", val: "onLine"},
                    {name: "线下活动", val: "offLine"},
                    {name: "礼赠记录", val: "present"},
                    {name: "拜访记录", val: "visit"}
                ],
                btns: [{name: '本财年', id: 'year'}, {name: '合计', id: 'sum'}],
                activityFyOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/marketactivity/link/marketActivity/queryFyActDetail'
                    },
                    param: {
                        consumerId: this.accountItem.id,
                        // attr1: 'Y',
                        // attr2: this.accountItem.belongToCompanyId,
                        activityChannel: '',  // 线上OnlineActivities 线下OfflineActivities
                        filtersRaw: [
                            // {
                            //     id: 'status',
                            //     property: 'status',
                            //     value: '[ActualAmount, Closed, Processing]',
                            //     operator: 'IN'
                            // }
                        ],
                    },
                    sortField: 'created',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            option.param.activityChannel = this.currentTab === 'onLine' ? 'OnlineActivities' : 'OfflineActivities';
                            if(this.isAllData){
                                option.param.touchCountFlag = true;
                                option.param.companyId = this.accountItem.belongToCompanyId;
                                option.param.mobilePhone = this.accountItem.phoneNumber;
                            }
                        }
                    }
                }),
                activityOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/marketactivity/link/marketActivity/queryActDetail'
                    },
                    param: {
                        consumerId: this.accountItem.id,
                        // attr1: 'Y',
                        // attr2: this.accountItem.belongToCompanyId,
                        filtersRaw: [
                            // {
                            //     id: 'status',
                            //     property: 'status',
                            //     value: '[ActualAmount, Closed, Processing]',
                            //     operator: 'IN'
                            // }
                        ],
                    },
                    sortField: 'created',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            option.param.activityChannel = this.currentTab === 'onLine' ? 'OnlineActivities' : 'OfflineActivities';
                            if(this.isAllData){
                                option.param.touchCountFlag = true;
                                option.param.companyId = this.accountItem.belongToCompanyId;
                                option.param.mobilePhone = this.accountItem.phoneNumber;
                            }
                        }
                    }
                }),
                presentOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/present/queryByExamplePage'
                    },
                    param: {
                        filtersRaw: [
                            {id: 'consumerId', operator: '=', property: 'consumerId', value: this.accountItem.id}, // 这个参数会动态设置
                            {id: 'status', operator: '=', property: 'status', value: 'Registered'}
                        ]
                    },
                    sortField: 'created',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            const consumerFiltersRaw = option.param.filtersRaw.find(e=>e.property=='consumerId')
                            if(this.isAllData){
                                consumerFiltersRaw.operator='<>';
                                consumerFiltersRaw.value='1';
                                option.param.touchCountFlag = true;
                                option.param.companyId = this.accountItem.belongToCompanyId;
                                option.param.consumerPhone = this.accountItem.phoneNumber;
                            }else{
                                consumerFiltersRaw.operator = '=';
                                consumerFiltersRaw.value = this.accountItem.id;
                            }
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter();
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                }),
                visitOption: new this.AutoList(this, {
                    url: {
                        queryByExamplePage: this.$env.appURL + '/action/link/visit/queryByExamplePage'
                    },
                    param: {
                        type: 'VisitRecord',
                        attr1: this.accountItem.id,
                        filtersRaw: [
                            {id: 'attr1', operator: '=', property: 'attr1', value: this.accountItem.id},
                            {id: 'visitStatus', operator: '=', property: 'visitStatus', value: 'Registered'}
                        ]
                    },
                    sortField: 'created',
                    sortOptions: null,
                    // loadOnStart: false,
                    hooks: {
                        beforeLoad(option) {
                            if(this.isAllData){
                                delete option.param.attr1;
                                option.param.touchCountFlag = true;
                                option.param.companyId = this.accountItem.belongToCompanyId;
                                option.param.consumerPhone = this.accountItem.phoneNumber;
                            }else{
                                option.param.attr1 = this.accountItem.id;
                            }
                            if (this.currentDimension === 'year') {
                                const arr = this.getFyFilter();
                                option.param.filtersRaw = option.param.filtersRaw.concat(arr);
                            }
                        }
                    }
                })
            }
        },
        async created() {
            console.log(this.accountItem,'---------------companyIdcompanyIdcompanyIdcompanyId')
            await this.initCusTouch();
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: changeDimension
             * @para:
             * @description: 维度切换
             **/
            changeDimension(val) {
                this.currentDimension = val;
                this.detailTabChange(this.currentTab, false);
            },
            /**
             @desc: 查询数据指标说明
             @author: wangbinxin
             @date 2023-10-20 10-05
             **/
            async queryInfo() {
                this.$utils.showLoading();
                const data = await this.$http.post(this.$env.dmpURL + '/link/reportTaskConfig/queryByExamplePage', {deployNo:this.isAllData?"SINGLE_CONSUMER_TOUCH_SUM": "SINGLE_CONSUMER_TOUCH_REPORT"}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$utils.hideLoading();
                        this.$showError('查询失败' + response.result);
                    }
                });
                this.$utils.hideLoading();
                if (data.success) {
                    const description = data.rows[0]?.content;
                    this.$emit('show-rule', description, '数据指标说明');
                }
            },
            /**
             @desc: 查询触达指标数据
             @author: wangbinxin
             @date 2023-10-18 16-48
             **/
            async initCusTouch() {
                let consumerIdList;
                if(this.isAllData){
                    consumerIdList = await this.$http.post(this.$env.appURL + '/action/link/present/queryTouchCountConsumerIdList', {
                        companyId: this.userInfo.coreOrganizationTile['l3Id'],
                        consumerPhone:this.accountItem.phoneNumber,
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            this.$showError(`查询看板数据失败：${response.message}`);
                        }
                    });
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', {
                    touchCountFlag:this.isAllData?true: undefined,
                    // companyId: this.isAllData?this.accountItem.belongToCompanyId: undefined,
                    touchCountConsumerList: this.isAllData?consumerIdList.result: undefined,
                    consumerId:this.isAllData?undefined: this.accountItem.id,
                    dataType: this.currentDimension === 'year' ? 'thisFiscalYear' : 'allTime',
                    dmpSrUrl: '/link/ConsumerReachMetrics/queryConsumerReachMetrics'
                }, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                        this.$showError(`查询看板数据失败：${response.result}`);
                    }
                });
                this.cusTouchObj = data.result || {};
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/02/04
             * @methods: detailTabChange
             * @para:
             * @description: 消费者详情tab切换
             **/
            detailTabChange(val, changeTab = true) {
                if (changeTab) {
                    this.currentTab = val;
                    this.currentDimension = this.btns[0].id;
                }
                if (this.$utils.isEmpty(this.accountItem.id)) {
                    return;
                }
                switch (val) {
                    case 'board':
                        this.initCusTouch();
                        break;
                    case 'onLine':
                    case 'offLine':
                        // if (this.currentDimension === 'year') {
                        //     this.activityFyOption.methods.reload();
                        // } else {
                        //     this.activityOption.methods.reload();
                        // }
                        break;
                    case 'present':
                        // this.presentOption.methods.reload();
                        break;
                    case 'visit':
                        // this.visitOption.methods.reload();
                        break;
                    default:
                        break;
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2023/03/05
             * @methods: getFyFilter
             * @para:
             * @description: 财年筛选参数
             **/
            getFyFilter() {
                const year = new Date().getFullYear();
                let arr = [];
                if (new Date().getTime() < new Date(`${year}/11/01 00:00:00`).getTime()) {
                    arr = [
                        {id: 'auditTime', property: 'auditTime', operator: '>=', value: `${year - 1}-11-01 00:00:00`},
                        {id: 'auditTime', property: 'auditTime', operator: '<=', value: `${year}-10-31 23:59:59`}
                    ]
                } else {
                    arr = [
                        {id: 'auditTime', property: 'auditTime', operator: '>=', value: `${year}-11-01 00:00:00`},
                        {id: 'auditTime', property: 'auditTime', operator: '<=', value: `${year + 1}-10-31 23:59:59`}
                    ]
                }
                return arr;
            },
            /**
             * @createdBy 曾宇
             * @date 2022/7/12
             * @methods: gotoPresentItem
             * @description: 礼赠详情
             **/
            gotoPresentItem(item) {
                this.$nav.push('/pages/lj-consumers/visit-present/visit-present-item-page', {
                    module: this.$env.appURL + "/action/link/present",
                    data: item,
                    operator: 'READ',
                    source: 'consumer_touch_record'
                })
            },
            /**
             *  前往拜访
             *  <AUTHOR>
             *  @date        2020-06-23
             */
            gotoVisitRegisterItem(item) {
                item.row_status = 'UPDATE';
                this.$nav.push('/pages/lj-consumers/visit-register/visit-register-item-page', {
                    data: item
                })
            }
        }
    }
</script>

<style lang="scss">
    .touch-record {
        .sub-tab-con {
            margin: 24px 24px 0 24px;
        }

        .content-con {
            display: block;
            background-color: #f1f1f1;
            border-radius: 32px 32px 0 0;
            box-sizing: border-box;
            padding: 24px;

            .board-con {
                .tip-con {
                    position: relative;
                    display: flex;
                    justify-content: space-between;

                    .deadline {
                        font-size: 24px;
                        line-height: 40px;
                        color: #666;
                    }

                    .buttons {
                        display: flex;
                    }

                    .select-button {
                        background: #FFFFFF;
                    }

                    .tips {
                        display: flex;
                        font-size: 24px;
                        color: #666;
                        line-height: 40px;
                        box-sizing: border-box;
                        align-items: center;

                        .link-icon.link-icon-status-info {
                            margin-top: 4px;
                            margin-right: 8px;
                        }
                    }
                }

                .cus-touch {
                    display: flex;
                    flex-wrap: wrap;
                    justify-content: space-between;

                    .cus-touch-item {
                        width: 32%;
                        background: #FFFFFF;
                        border-radius: 16px;
                        margin-top: 24px;
                        padding: 24px 0;
                        text-align: center;
                        .item-row{
                            height: 40px;
                            line-height: 40px;
                            font-size: 26px;
                            color: #333333;
                        }
                    }
                }
            }

            .activity-con {
                .activity-item-con {
                    box-sizing: border-box;
                    padding: 24px;
                    background-color: white;
                    border-radius: 24px;
                    margin-bottom: 24px;

                    .activity-dec {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 28px;
                        line-height: 68px;

                        .activity-start-time {
                            color: #9EA1AE;
                        }

                        .activity-status {
                            color: #3F66EF;
                            height: 48px;
                            background-color: #F0F3FE;
                            border-radius: 8px;
                            box-sizing: border-box;
                            padding: 0 24px;
                            display: flex;
                            align-items: center;
                        }
                    }

                    .activity-name {
                        width: 100%;
                        height: 68px;
                        font-size: 28px;
                        line-height: 68px;
                        color: #333;
                        overflow: scroll;
                        white-space: nowrap;
                    }
                }
            }

            /*deep*/
            .link-card {
                padding: 0;
            }

            .present-con {
                width: 100%;
                background-color: white;
                font-size: 28px;
                height: 84px;
                line-height: 84px;
                border-radius: 24px 24px 0 0;

                .present-title {
                    border-radius: 24px 24px 0 0;
                    background-color: #F6FBFF;
                    color: #6A6D75;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .present-line {
                    height: 60px;
                    line-height: 60px;
                    background-color: #fff;
                    color: #333;
                    display: flex;
                    align-items: center;
                    justify-content: space-between;
                }

                .present-type {
                    width: 38%;
                    text-align: left;
                    margin-left: 2%;
                }

                .creator {
                    width: 20%;
                    text-align: center;
                }

                .audit-time {
                    width: 38%;
                    text-align: right;
                    margin-right: 2%;
                }
            }

            .visit-con {
                .visit-item-con {
                    position: relative;
                    box-sizing: border-box;
                    padding: 24px;
                    background-color: white;
                    border-radius: 24px;
                    margin-bottom: 24px;

                    .visit-item-row {
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                        font-size: 28px;
                        line-height: 68px;
                        color: #333;

                        .row-title {
                            min-width: 140px;
                            height: 68px;
                            overflow: scroll;
                            white-space: nowrap;
                        }
                        .row-content{
                            flex: 1;
                            overflow: scroll;
                            white-space: nowrap;
                        }
                    }
                    .visitor-name {
                        position: absolute;
                        top: 36px;
                        right: 24px;
                        color: #3F66EF;
                        height: 48px;
                        background-color: #F0F3FE;
                        border-radius: 8px;
                        box-sizing: border-box;
                        padding: 0 24px;
                        display: flex;
                        align-items: center;
                    }
                }
            }
        }
    }
</style>
