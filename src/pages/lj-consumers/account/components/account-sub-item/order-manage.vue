<!--
消费者详情-订单管理-tab
<AUTHOR>
@date 2025-03-25
@file order-manage.vue
-->
<template>
  <view class="order-manage">
    <view class="sub-tab-con">
      <sub-tabs :currentTab="currentTab" :tabs="tabs" :is-scroll="true" @switchTab="detailTabChange"></sub-tabs>
    </view>
    <view class="schedule-list-content">
      <link-auto-list :option="scheduleList" hideCreateButton :key="0">
        <template slot-scope="{data,index}">
          <item :key="index" :data="data" :arrow="false" class="perform-case-list-item" style="padding: 4px 4px 20px 12px;">
            <view slot="note">
              <view class="media-list">
                <view class="media-top">
                  <status-button>{{ data.status | lov('ORDER_STATUS') }}</status-button>
                </view>
              </view>
              <view class="content-middle">
                <view class="lable-name">订单编号</view>
                <view class="name">{{ data.orderNo || '' }}</view>
              </view>
              <view class="content-middle">
                <view class="lable-name">订单类型</view>
                <view class="name">{{ data.orderChildType | lov('ORDER_SUB_TYPE') }}</view>
              </view>
              <view class="content-middle">
                <view class="lable-name">活动名称</view>
                <view class="name">{{ currentTab=='UCOrder'? data.sellerComment: data.mcActName|| '' }}</view>
              </view>
              <view class="content-middle">
                <view class="lable-name">订单时间</view>
                <view class="name">{{ data.created || ''}}</view>
                <link-button :disabled="!data.logisticsNo" mode="stroke" size="mini" label="查看物流" @tap="gotoItem(data.logisticsNo,data.logisticsCompany )"/>
              </view>
              <view class="content-middle">
                <view class="lable-name">物流公司</view>
                <view class="name" v-if="data.logisticsCompany">{{ data.logisticsCompany | lov('LOGISTICS_TYPE') }}</view>
                <view class="name" v-if="!data.logisticsCompany">暂无发运信息</view>
              </view>
              <view class="content-middle">
                <view class="lable-name">物流编号</view>
                <view class="name">
                    {{ data.logisticsNo || '暂无发运信息'}}
                    <link-icon icon="icon-qiehuan" v-if="data.logisticsNo" @tap="copyData(data.logisticsNo)"></link-icon>
                </view>
              </view>
            </view>
          </item>
        </template>
      </link-auto-list>
    </view>
  </view>
</template>

<script>
import StatusButton from "../../../../lzlj/components/status-button.vue";
import subTabs from '../sub-tabs/sub-tabs';

export default {
  name: 'order-manage',
  components: { StatusButton,subTabs },
  props: {
    oauth: {
      type: Object,
      default: () => {
      }
    },
    accountItem: {
      type: Object,
      required: true
    }
  },
  data () {
    const scheduleList = new this.AutoList(this, {
      url: {
        queryByExamplePage: this.$env.appURL + '/link/saleorder/queryConsumerOrderPage'
      },
      loadOnStart: false,
      param: {
        acctId: this.accountItem.id,
        filtersRaw:[]
      },
      hooks: {
        beforeLoad (option) {
            let arr;
            if(this.currentTab=='UCOrder'){
                arr = [
                    {"id":"orderType_1_auto","property":"orderChildType","operator":"=","value":this.currentTab}
                ]
            }else{
                arr = [
                    {"id":"orderType_1_auto","property":"orderChildType","operator":"=","value":this.currentTab},
                    {"id":"mcActId_2_auto","property":"mcActId","operator":"NOT NULL","value":""}
                ]
            }
            option.param.filtersRaw = arr;
        },
      },
    });
    return {
      tabs: [
        { name: "开瓶实物订单", val: "UCOrder" },
        { name: "线下邮寄订单", val: "OfflineMailOrder" },
        { name: "名单提报订单", val: "ListSubmitOrder" },
        { name: "名单提报补发订单", val: "ReSubmitOrder" },
      ],
      currentTab: "UCOrder",
      scheduleList,  // 消费者排期列表
    }
  },
  created () {

  },
  async mounted () {
    await this.scheduleList.methods.reload();

  },
  methods: {
    detailTabChange (val) {
        this.currentTab = val;
        this.scheduleList.methods.reload()
    },
    copyData (data) {
      wx.setClipboardData({
        data: data,
        success: function () {
          // 添加下面的代码可以复写复制成功默认提示文本`内容已复制`
          wx.showToast({
            title: '复制成功',
            duration: 3000
          });
          wx.getClipboardData({
            success: function (res) {
            }
          })
        }
      })
    },
    /**
     * @desc 跳转到排期执行情况页面
     * <AUTHOR>
     * @date 2025-02-17
     **/
    gotoItem (id, type) {
      this.$nav.push('/pages/lj-consumers/list-report/logistics-detail-page', {
        id, type
      })
    }
  }
}
</script>

<style lang="scss">
.order-manage {
    .sub-tabs-con .sub-tabs-scroll .sub-tab-item-active{
        background-size: cover!important;
    }
  .sub-tab-con {
    margin: 24px 24px 0 24px;
  }
  .perform-case-list-item {
    background: #ffffff;
    margin: 24px;
    border-radius: 16px;
  }

  .media-list {
    float: right;

    .media-top {
      width: 100%;
      @include flex-start-center;
      @include space-between;

      .num-view {
        width: 75%;
        overflow-x: auto;
        display: flex;
        line-height: 50px;

        .num {
          flex-shrink: 0;
          font-size: 28px;
          color: #ffffff;
          letter-spacing: 0;
          line-height: 40px;
          padding: 2px 8px;
          background: #a6b4c7;
          border-radius: 8px;
        }
      }

      .status-view {
        min-width: 120px;
        height: 36px;
        line-height: 36px;
        text-align: center;
        color: #ffffff;
        background: #2f69f8;
        box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
        border-radius: 8px;
        font-size: 20px;
        margin-right: 8px;
        -webkit-transform: skewX(-30deg);
        -ms-transform: skewX(-30deg);
        transform: skewX(-30deg);

        &-refuse {
          background: #ff5a5a !important;
          box-shadow: 0 3px 4px 0 rgba(255, 90, 90, 0.35) !important;
        }

        .status {
          transform: skewX(30deg);
          font-size: 20px;
          color: #ffffff;
          letter-spacing: 2px;
          text-align: center;
          line-height: 36px;
        }
      }
    }
  }

  .content-middle {
    width: 95%;
    display: flex;
    align-items: center;
    font-size: 28px;
    letter-spacing: 0;
    font-family: PingFangSC-Regular;
    line-height: 52px;
    overflow-x: auto;

    .lable-name {
      color: #8c8c8c;
      margin-right: 10px;
      min-width: 180px;
      flex-shrink: 0;
    //   width: 270px;
    }

    .name {
      color: #262626;
      width: calc(100% - 270px);
      //    超出部分滑动显示
      flex-shrink: 1;
      white-space: nowrap;
      overflow: auto;
    }
    .detail {
      color: #306cff;
      //width: 60%;
      //    超出部分滑动显示
      flex-shrink: 1;
      white-space: nowrap;
      overflow: auto;
    }
  }
}
</style>
