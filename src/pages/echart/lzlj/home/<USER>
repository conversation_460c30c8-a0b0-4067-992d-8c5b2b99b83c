<template>
    <link-page class="me">
        <image :src="$imageAssets.homeMenuBgImage"
               style="visibility: hidden;position:absolute;left: 9999px;top:9999px;height: 0;width: 0;"></image>
        <view class="menu-container">
            <!--菜单顶部-->
            <view :class="`${this.tabNow === 'board'?'menu-top-board':'menu-top'}`" :style="menuTopStyle">
                <view class="page-title" :style="{'padding-top':statusBarHeight + 'px'}">融营销服务</view>
                <view class="user-info">
                    <view class="user-img" @tap="goToPersonCenter">
                        <image v-if="userInfo.userProfile" :src="userInfo.userProfile"></image>
                        <open-data v-else type="userAvatarUrl" :default-avatar="defaultAvatar"></open-data>
                    </view>
                    <view class="user-post" @tap="goToPersonCenter">
                        <view class="first-name">{{userInfo.firstName}} |
                            <view class="staff-type">{{userInfo.staffType | lov('STAFF_TYPE')}}</view>
                            <view class="four-color" v-if="fourColorLabel" :style="fourColorStyle[fourColorLabel]">
                                {{fourColorLabel | lov('Four_Color_Label')}}
                            </view>
                        </view>
                        <view class="post-name">{{userInfo.postnName}}</view>
                        <view class="post-name dealer-name" v-if="userInfo.dealerName">{{userInfo.dealerName}}</view>
                    </view>
                    <view class="view-qrcode" v-if="companyName === '国窖公司'" @tap="viewInviteMemberCode">
                        <link-icon icon="icon-qrcode" style="color: #FFFFFF;font-size: 32px;"/>
                    </view>
                </view>
            </view>
            <view v-if="tabNow === 'board'">
                <view class="tag-box">
                    <view @tap='changeTag(true)' class="tag-item" :style="{'color': showTag ? '#2f69f8':'', 'border': showTag ? '1rpx solid #2f69f8' : '1rpx solid #eee'}">员工积分</view>
                    <view @tap='changeTag(false)' class="tag-item" :style="{'color': !showTag ? '#2f69f8':'', 'border': !showTag ? '1rpx solid #2f69f8' : '1rpx solid #eee'}">提醒事项</view>
                </view>
                <!-- 员工积分 -->
                <view v-if="showTag">
                    <staff-score/>
                </view>
                <!-- 业代-提醒事项 -->
                <today-reminder v-if="!showTag && isNewWaring && !isDealer"/>
                <!-- 提醒事项 -->
                <view class="menu-content" v-if="!showTag && !isNewWaring">
                    <view>
                        <view class="menu-stair">
                            <view class="line">
                                <view class="line-top"></view>
                                <view class="line-bottom"></view>
                            </view>
                            <view class="stair-title">今日提醒事项</view>
                        </view>
                        <view class="board-content">
                            <view v-if="noteList.length > 0">
                                <view :class="[item.class || 'board-normal-content', {'no-padding':item.paddingFlag}]" v-for="(item, index) in noteList" @tap="goto(item)" :key="index">
                                    <image v-if="item.name === 'daily'?item.val.dailyIsSubmit !== 'Y':item.val.num > 0" class="con-img" :src="item.img"></image>
                                    <view v-else class="con-bg"></view>
                                    <view class="con-mess">
                                        <view class="mess-item">
                                            <view v-if="item.name !== 'daily'">
                                                <view class="mess-num">{{item.val.num}}</view>
                                                <view>{{item.unit}}</view>
                                            </view>
                                            <view v-else>
                                                {{new Date() | date('MM')}}/
                                                <view class="mess-date">{{new Date() | date('DD')}}</view>
                                            </view>
                                        </view>
                                        <view class="mess-text">{{ item.val.typeName }}</view>
                                    </view>
                                </view>
                            </view>
                            <view v-else class="board-no-content">
                                <view>
                                    <image class="con-img" :src="$imageAssets.homeNoData"></image>
                                    <view class="no-mess">暂无提醒事项！</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <!-- 经销商人员只保留今日事项提醒 -->
                <view class="menu-content-info" v-if="!isDealer && oldBoardFlag">
                    <view class="tap-container">
                        <view class="lnk-tabs">
                            <view class="lnk-tabs-item" :class="{'active': tab.val === dataBoardActive.val}" v-for="(tab, index) in dataBoardOption" :key="index" @tap="switchTab(tab)">
                                <view class="label-name">
                                    {{tab.name}}
                                </view>
                                <view class="line"  v-if="tab.val === dataBoardActive.val"></view>
                            </view>
                        </view>
                    </view>
                    <!-- 鹰计划 -->
                    <view v-if="dataBoardActive.val === 'corpwx_home_eagle_plan'">
                        <eagle-plan-board/>
                    </view>
                    <!--考勤-->
                    <view v-if="dataBoardActive.val === 'corpwx_home_own_checkin'">
                        <own-checkin-report></own-checkin-report>
                    </view>
                    <!--经销商-->
                    <view v-if="dataBoardActive.val === 'corpwx_home_city_board'">
                        <data-board></data-board>
                    </view>
                    <!--活动-->
                    <view v-if="dataBoardActive.val === 'corpwx_home_activity_board'">
                        <market-act-home-report></market-act-home-report>
                    </view>
                    <!--终端-->
                    <view v-if="dataBoardActive.val === 'corpwx_home_terminal_role'">
                        <role-board></role-board>
                    </view>
                    <!--消费者看板(新)-->
                    <view v-if="dataBoardActive.val === 'consumer_overview_board_new'">
                        <consumer-kanban :secMenus="dataBoardActive.secMenus"/>
                    </view>
                    <!--消费者看板-->
                    <view v-if="dataBoardActive.val === 'consumer_overview_cockpit'">
                        <consumer-overview-cockpit :secMenus="dataBoardActive.secMenus"/>
                    </view>
                    <!--消费者开瓶看板-->
                    <view v-if="dataBoardActive.val === 'consumer_scan_board'">
                        <consumer-scan-board :secMenus="dataBoardActive.secMenus"/>
                    </view>
                    <!--企业-->
                    <view v-if="dataBoardActive.val === 'corpwx_home_visit_board'">
                        <cpr-board></cpr-board>
                    </view>
                </view>
            </view>
            <view v-if="tabNow === 'work'">
                <!--菜单-->
                <view v-if="menuList.length <= 0" class="menu-loading">正在加载中，请稍后...</view>
                <view v-for="(item, index) in menuList" :key="index" class="menu-content" v-if="menuList && item.moduleCode !== 'corpwx_home_board'">
                    <view class="menu-stair">
                        <view class="line">
                            <view class="line-top"></view>
                            <view class="line-bottom"></view>
                        </view>
                        <view class="stair-title">{{item.moduleName}}</view>
                    </view>
                    <view class="second-menu-content">
                        <!--  -->
                        <view v-for="(data, key) in filtersMenus(item.subMenus)" :key="key" class="menu-second"
                              @tap="goMenuDetails(data)">
                            <view class="num-tips" v-if="data.moduleCode === 'corpwx_own_approve' && $utils.isNotEmpty(tipsNumObj)">
                                {{tipsNumObj.myRunningNum}}
                            </view>
                            <view class="num-tips" v-if="data.moduleCode === 'corpwx_own_approve_fanwei' && $utils.isNotEmpty(tipsNumObj)">
                                {{tipsNumObj.myRunningNumFanwei}}
                            </view>
                            <view class="num-tips" v-if="data.moduleCode === 'corpwx_own_message'&& $utils.isNotEmpty(tipsNumObj)">
                                {{tipsNumObj.unreadMessage}}
                            </view>
                            <view class="num-tips" v-if="data.moduleCode === 'corpwx_own_anounce'&& $utils.isNotEmpty(tipsNumObj)">
                                {{tipsNumObj.ownAnnounceNum}}
                            </view>
                            <view class="num-tips" v-if="data.moduleCode === 'corpwx_work_order' && staffFlag && $utils.isNotEmpty(tipsNumObj)">
                                {{tipsNumObj.workOrderNum}}
                            </view>
                            <view class="num-tips" v-if="data.moduleCode === 'consumer_open_bottle' && $utils.isNotEmpty(tipsNumObj)">
                                {{tipsNumObj.openBottleNum}}
                            </view>
                            <view class="iconfont icon-style" :class="data.iconName" :style="data.iconColor"></view>
                            <view class="second-title">{{data.moduleName}}</view>
                        </view>
                    </view>
            </view>
            </view>
        </view>
        <link-dialog ref="imageShare" position="poster" :initial="true">
            <view :style="{'background-image': 'url('+ $imageAssets.adornImg + ')'}" style="width: 90vw;height: 90px;background-repeat: no-repeat;background-size: 100% 100%;
                border-radius: 10px 10px 0 0;background-color: white">
                <view
                    style="font-family: PingFangSC-Regular;font-size: 16px;color: #FFFFFF;letter-spacing: 0;text-align: center;line-height: 90px;">
                    会员邀约
                </view>
            </view>
            <view style="width: 100%;height: 248px;text-align: center;background: white">
                <image style="padding-top: 44px;width: 160px;height: 160px" :src="imgUrl.src"
                       mode="widthFix"></image>
            </view>
            <view style="width: 100%;padding-top: 10px;padding-bottom: 5px;height: 35px;background: white">
                <view style="position: absolute;border: 1px #DADEE9 dashed;margin-top: 8px;width: 100%;"></view>
            </view>
            <view style="width: 100%;height: 60px;background: white;border-radius: 0 0 10px 10px">
                <view style="width: 50%;height: 100%;float: left;text-align: center;">
                    <button open-type="share" class="button-private">
                        <image :src="$imageAssets.interShareImg" style="width: 40px;height: 40px"/>
                    </button>
                </view>
                <view @tap="saveShareImg"
                      style="width: 50%;height: 100%;float: left;text-align:center;">
                    <image :src="$imageAssets.interSaveImg" style="width: 40px;height: 40px"/>
                </view>
            </view>
        </link-dialog>

        <link-dialog ref="scanCodeMenuDialog" class="scanCode" :initial="true">
            <view slot="head" class="headBox">
                    <view class="title">选择扫码业务</view>
                    <view class="iconfont icon-close tipClose" @tap="$refs.scanCodeMenuDialog.hide();"></view>
            </view>
            <view class="contentBox">
                <view class="scanCodeBox">
                    <view class="one iconfont icon-kaipinghexiao" @tap="goToOpenScan"></view>
                    <text class="text">开瓶核销</text>
                </view>
                <view class="scanCodeBox">
                    <view class="two iconfont icon-zengsonghexiao" @tap="goToGiftScan"></view>
                    <text class="text">赠送核销</text>
                </view>
            </view>
        </link-dialog>
        <!-- 电子协议推送 -->
        <link-dialog ref="protocolPushDialog" class="scanCode" :initial="true">
            <view slot="head" class="headBox">
                    <view class="title">选择推送方式</view>
                    <view class="iconfont icon-close tipClose" @tap="$refs.protocolPushDialog.hide();"></view>
            </view>
            <view class="contentBox">
                <view class="scanCodeBox" @tap="goSinglePush">
                    <view class="one iconfont icon-sms_send"></view>
                    <text class="text">单条推送</text>
                </view>
                <view class="scanCodeBox" @tap="goMutilPush">
                    <view class="two iconfont icon-smses_send"></view>
                    <text class="text">批量推送</text>
                </view>
                <view class="scanCodeBox" @tap="goUpgradePush">
                    <view class="three iconfont icon-sms_send"></view>
                    <text class="text">升级推送</text>
                </view>
                <view class="scanCodeBox" @tap="goRebatePush">
                    <view class="four iconfont icon-sms_send"></view>
                    <text class="text">{{  isGuojiao ? "返利推送" : "返利查询" }}</text>
                </view>
            </view>
        </link-dialog>

        <link-tabs autoSetNavigationTitleText :value="tabNow">
            <link-tab :title="tabList.tabsOne.tabTitle" :val="tabList.tabsOne.tabVal" icon="icon-kanban1" @tap-item="changeTab"></link-tab>
            <link-tab :title="tabList.tabsTwo.tabTitle" :val="tabList.tabsTwo.tabVal" icon="icon-gongzuotai" @tap-item="changeTab"></link-tab>
        </link-tabs>
        <announce-dialog/>
    </link-page>
</template>

<script>
    import {$http} from "@/utils/$http"
    import {$logger} from "@/utils/log/$logger"
    import Taro from '@tarojs/taro'
    import {loginService} from "@/utils/login";
    import {PageCacheManager} from "@/utils/PageCacheManager";
    import { checkUpdate, compareVersion } from '@/utils/version/version';
    import lnkTaps from "../../../core/lnk-taps/lnk-taps";
    import ownCheckinReport from '../own-checkin-data-board/own-checkin-report';
    import dataBoard from '../dealer-data-board/data-board';
    import RoleBoard from "../role-board/role-board";
    import MarketActHomeReport from "../marketing-activity-report/market-act-home-report";
    import CprBoard from  "../corporate-relation-board/cpr-board";
    import ConsumerKanban from "../consumer-kanban-new/consumer-kanban";
    import ConsumerScanBoard from '../consumer-scan-board/consumer-scan-board';
    import EaglePlanBoard from '../eagle-plan/eagle-plan-board';
    import staffScore from './components/staff-score'
    import ConsumerOverviewCockpit from '../../../new-board/normal-business/consumer/consumer-overview-cockpit';
    import TodayReminder from "../../../new-board/components/today-reminder";
    import AnnounceDialog from "../../../lzlj-II/announce-dialog/announce-dialog";

    export default {
        name: "me",
        data() {
            // 界面结构dom节点渲染与登录并行，为了避免dom中用到的用户信息属性不存在，需要提前初始化
            const userInitObject = {
                userProfile: '',
                firstName: '',
                postnName: '',
                staffType: ''
            };
            const userInfo = Object.assign({}, userInitObject, this.$taro.getStorageSync('token').result);
            return {
                // includesCompany:['58932888267128832','256730394522026120','GS100264','GS100001'],// 高光、优选才[经销商]和【经销商拜访】菜单
                includesCompany:[],
                // 是否展示旧看板（业代使用新看板）
                oldBoardFlag: false,
                isGuojiao: userInfo.coreOrganizationTile?.brandCompanyCode === '5600',
                showTag: true,  //展示员工积分
                isDealer: false, // 是否走特定公司的经销商人员安全性
                isShowMenu: false, // 是否展示消费者看板菜单
                defaultType: '', // 当前用户职位类型去匹配对应的值
                dataBoardActive: {},
                dataBoardOption: [],
                tabNow: 'work',
                tabList: {
                    tabsOne: {
                        tabTitle: '看板',
                        tabVal: 'board'
                    },
                    tabsTwo: {
                        tabTitle: '工作台',
                        tabVal: 'work'
                    }
                },
                companyName: '',
                defaultAvatar: '',
                menuHeight: '',
                locationType: 'gcj02',
                statusBarHeight: 0,
                menuTopStyle: '',
                userInfo,
                colorList: [
                    'background-image: linear-gradient(180deg, #6392FA 0%, #2F69F8 100%);',
                    'background-image: linear-gradient(180deg, #3FE0E2 0%, #2EB3C2 100%);',
                    'background-image: linear-gradient(180deg, #FF8560 0%, #FF4D2D 100%);',
                    'background-image: linear-gradient(180deg, #FFD869 0%, #FFC637 100%);',
                    'background-image: linear-gradient(180deg, #B9E778 0%, #6AB524 100%);'
                ],
                noteList: [], // 提醒事项展示
                menuData: [],
                tipsNumObj: {},
                menuList: [],
                imgUrl: {src: ''},
                qrCodeData: '',
                staffFlag: true, // 前线作战人员标识
                isNewWaring: false, // 新版今日提醒只针对业代
                fourColorLabel: '', // 人员四色标签
                fourColorStyle: {
                    Black: {
                        'background-color': '#000000',
                        'color': 'white'
                    },
                    Yellow:{
                        'background-color': '#fff006',
                        'color': 'black'
                    },
                    Red:{
                        'background-color': '#e12e13',
                        'color': 'white'
                    },
                    White:{
                        'background-color': '#ffffff',
                        'color': 'black'
                    }
                }
            }
        },
        components:{
            AnnounceDialog,
            TodayReminder,
            ConsumerKanban,
            EaglePlanBoard,
            MarketActHomeReport,
            RoleBoard,
            lnkTaps,
            ownCheckinReport,
            dataBoard,
            CprBoard,
            ConsumerScanBoard,
            staffScore,
            ConsumerOverviewCockpit
        },
        async created() {
             const prodPartCom = await this.$utils.getCfgProperty('QDHY_DEALER_MODULE_PERMISSION');
             this.includesCompany = prodPartCom ?  prodPartCom.split(',') : []
            this.isNewWaring = ['Salesman'].includes(this.userInfo.positionType);
            // 顶部背景图样式
            this.menuHeight = this.$device.isIphoneX ? '215px' : '196px';
            this.menuTopStyle = `background-image: url('${this.$imageAssets.homeMenuBgImage}');height: ${this.menuHeight};top:${this.$device.isIphoneX ? '-95px' : '-85px'}`;
            // 设备导航bar的高度
            this.statusBarHeight = this.$device.systemInfo.statusBarHeight + 14;
            await this.initData();
            await this.getPosition();
            await this.handleMenuList1();
            await this.versionCheck();
            // 首次加载首页
            await this.clearModuleCache();
            // 查询待办事项
            this.requestTips().then(() => {});
            this.checkQyVersion();
            this.dataBoardActive = this.dataBoardOption[0];
            this.getFourColorData();
        },
        async mounted() {
            this.$bus.$on('updateNoteList', async () => {
                await this.setNoteList();
            });
            // 首页加载完，判断是否存在非正常退出页面路径存在，存在则记录非正常退出日志
            const activePageRoute = Taro.getStorageSync('activePageRoute');
            if(this.$utils.isNotEmpty(activePageRoute)) {
                this.$aegis.report({
                    msg: '存在未正常退出页面',
                    ext1: JSON.stringify(activePageRoute), // 日志对象
                    trace: 'log' // 日志类型
                });
                Taro.removeStorageSync('activePageRoute');
            }
            //@@PAGE_CACHE代表是否表单续填写, 当不用续填且首次进入页面时存在活动缓存,则清空活动缓存;
            let PAGECACHE = Taro.getStorageSync('@@PAGE_CACHE');
            let MarketActivity = Taro.getStorageSync('MarketActivity');
            if(this.$utils.isNotEmpty(MarketActivity) && this.$utils.isEmpty(PAGECACHE)){
                Taro.removeStorageSync('MarketActivity');
            }
            await this.getGlobalCfgProperty();
        },
        methods: {
            filtersMenus(list){
                if(!list || list.length === 0){
                    return [];
                }

                // 经销商拜访菜单，只有在高光和优选公司才展示，所以要过滤一下。..
                const { companyId,orgCode } = this.userInfo;
                const dealerMenuCode = ['dealer_visit_list'];
                if(!this.includesCompany.includes(companyId) && !this.includesCompany.includes(orgCode)){
                    list = list.filter(item => !dealerMenuCode.includes(item.moduleCode));
                }

                return list;

            },
            /**
             * 获取人员四色标签展示
             * <AUTHOR>
             * @date    2025/03/25
             */
            async getFourColorData() {
                try {
                    const rows = await this.$http.post('action/link/fourColorMeasure/selectTodayLabel');
                    if (rows.success) {
                        this.fourColorLabel = rows.data;
                    } else {
                        console.log('获取人员四色标签出错：' + rows.message);
                    }
                } catch(err) {
                    console.log('获取人员四色标签出错：' + err);
                }
            },
            /**
             * 获取角色看板权限
             * <AUTHOR>
             * @date    2024/9/25 11:32
             */
            async getPosition() {
                try {
                    const queryData = await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                        filtersRaw: [
                            // Terminal_Role_SalesSupervisor
                            {id: 'key', property: 'key', operator: 'in', value: '[Terminal_Role_Salesman]'},
                            {id: 'value', property: 'value', operator: 'like', value: this.userInfo.positionType}
                        ]
                    });
                    if (queryData.success) {
                        if (queryData.rows.length > 0) {
                            queryfor:
                                for (let i = 0; i < queryData.rows.length; i++) {
                                    let arr = queryData.rows[i].value.split(',');
                                    for (let j = 0; j < arr.length; j++) {
                                        if (arr[j] === this.userInfo.positionType) {
                                            let userType = queryData.rows[i].key.split('_');
                                            this.userType = userType[userType.length - 1];
                                            break queryfor;
                                        }
                                    }
                                }
                        }
                        this.oldBoardFlag = !['Salesman'].includes(this.userType)
                    }
                } catch (e) {
                    console.log('获取用户信息错误' + e);
                }
            },
             /**
             * @desc 切换员工积分、提醒事项
             * @auth tanshaoqi
             * @date 2024/04/15
             * @param Boolean status :true:员工积分 false:提醒事项
             **/
            changeTag(status){
                this.showTag = status
            },
            /**
             * @desc 根据系统参数配置展示消费者看板菜单
             * <AUTHOR>
             * @date 2023/7/10 09:44
             **/
            async queryPublicCfg () {
                try {
                    const data = await this.$utils.getCfgProperty('Consumer_Report');
                    if (data !== 'noMatch') {
                        const companyIds = data.split(',');
                        this.isShowMenu = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1;
                    }
                } catch(e) {
                    return false;
                }
            },
            /**
             @desc: 切换职位后，刷新看版界面及工作台信息
             @author: wangbinxin
             @date 2022-08-08 20-25
             **/
            afterRefresh(){
                if(this.tabNow === 'board'){
                    this.tabNow = '';
                    setTimeout(()=> {
                        this.getManger();
                        this.tabNow = 'board';
                    }, 10);
                }
            },
            async setNoteList(){
              this.$utils.showLoading();
              this.noteList = [];
              let oauth = 'MY_POSTN';
              let noteList = [
                {
                  img: this.$imageAssets.homeCase,
                  name: 'actProdOverdue'
                }, {
                  img: this.$imageAssets.homeFeedback,
                  name: 'actOverdue'
                },
                {
                  img: this.$imageAssets.homeApproval,
                  name: 'notApprovalTimeout'
                }
              ];
              if(this.defaultType === 'Business_Representative'){
                noteList = [
                  {
                    img: this.$imageAssets.homeCase,
                    name: 'actProdOverdue'
                  }, {
                    img: this.$imageAssets.homeFeedback,
                    name: 'actOverdue'
                  }, {
                    img: this.$imageAssets.homeStart,
                    name: 'actStart'
                  }, {
                    img: this.$imageAssets.homeDaily,
                    name: 'daily',
                    class: 'board-small-content'
                  }, {
                    img: this.$imageAssets.homeVisit,
                    name: 'notVisit',
                    class: 'board-small-content'
                  },
                ];
                oauth = 'MY_ORG';
              }else if(this.defaultType === 'Head_of_Business'){
                noteList = [
                  {
                    img: this.$imageAssets.homeCase,
                    name: 'actProdOverdue'
                  }, {
                    img: this.$imageAssets.homeFeedback,
                    name: 'actOverdue'
                  }, {
                    img: this.$imageAssets.homeStart,
                    name: 'actStart'
                  },
                  {
                    img: this.$imageAssets.homeApproval,
                    name: 'notApprovalTimeout'
                  }, {
                    img: this.$imageAssets.homeDaily,
                    name: 'daily'
                  }, {
                    img: this.$imageAssets.homeVisit,
                    name: 'notVisit'
                  }
                ];
                oauth = 'MY_ORG';
              }
              const noticeData = await this.$http.post('export/link/remindToday/queryStatistics', {oauth});
              this.$utils.hideLoading();
              if(noticeData.success){
                noteList.forEach((item) => {
                  item.unit = item.name === 'actProdOverdue'?'个':'条';
                  switch (item.name){
                    case 'notApprovalTimeout':
                      item.url = '/pages/lzlj/approval/approval-list-page';
                      break;
                    case 'daily':
                      item.url = '/pages/lzlj-II/daily-paper-new/daily-list-page';
                      break;
                    case 'actStart':
                      item.url = '/pages/lj-market-activity/market-activity/market-activity-list-page';
                      break;
                    case 'actOverdue':
                      item.url = '/pages/lj-market-activity/work-order/work-order-list-page';
                      break;
                    case 'actProdOverdue':
                      item.url = '/pages/lj-market-activity/perform-case/perform-case-list-page';
                      break;
                  }
                  for (let i = 0; i < noticeData.rows.length; i++) {
                    if(noticeData.rows[i].typeCode === item.name){
                      item.val = noticeData.rows[i];
                      if(item.val && item.val.errorStatus !== 'error'){
                        this.noteList.push(item);
                      }
                      break;
                    }
                  }
                });
                let sum = 0;
                this.noteList.forEach((item) => {
                  sum += item.class?item.class === 'board-small-content'?25:50:33.5;
                  item.paddingFlag = sum > 99;
                  sum = sum > 99?0:sum;
                });
              }else{
                this.$showError('获取提醒数据失败' + noticeData.result);
                this.noteList = [];
              }
            },
            /**
             @desc: 根据职责展示对应的提醒面板
             @author: wangbinxin
             @date 2022-08-23 15-56
             **/
            async getManger(){
                this.userInfo = Taro.getStorageSync('token').result;
                this.$utils.showLoading();
                const queryData =  await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [{
                        id: 'key',
                        property: 'key',
                        operator: 'in',
                        value: '[Business_Representative,Head_of_Business,Business_backoffice,Business_Manager]'
                    }, {
                        id: 'value',
                        property: 'value',
                        operator: 'like',
                        value: this.userInfo.positionType
                    }]
                });
                if(queryData.success){
                    this.defaultType = ''; // 清空职位
                    if(queryData.rows.length > 0){
                        queryfor:
                        for (let i = 0; i < queryData.rows.length; i++) {
                            let arr = queryData.rows[i].value.split(',');
                            for (let j = 0; j < arr.length; j++) {
                                if(arr[j] === this.userInfo.positionType){
                                    this.defaultType = queryData.rows[i].key;
                                    break queryfor;
                                }
                            }
                        }
                    }
                    if(this.defaultType){
                      await this.setNoteList();
                    }else {
                      this.$utils.hideLoading();
                      this.noteList = []; // 清空提醒list
                    }
                }else{
                    this.$utils.hideLoading();
                    this.$showError('获取用户信息失败' + queryData.result);
                    this.noteList = [];
                }
            },
            switchTab(tab){
                this.dataBoardActive = tab;
            },
            /**
             @param{number}
             @desc: 点击tab
             @author: wangbinxin
             @date 2022-08-04 16-31
             **/
            async changeTab(e) {
                this.tabNow = e.val;
                if(e.val === 'board') {
                    if (this.$utils.isEmpty(this.noteList) && this.$utils.isEmpty(this.defaultType)) {
                        await this.getManger();
                    } else {
                        this.setNoteList();
                    }
                }
                if(e.val === 'work'){
                  this.requestTips();
                }
            },
            /**
             * 品鉴酒-开瓶核销
             * <AUTHOR>
             * @date 2022-05-23
             */
            goToOpenScan(){
                this.$nav.push('/pages/lj-market-activity/tastin-wine/receive_code/create/create-receive-code-page', {target: 'OpenScan'});
            },
            /**
             * 品鉴酒-赠送核销
             * <AUTHOR>
             * @date 2022-05-23
             */
            goToGiftScan(){
                this.$nav.push('/pages/lj-market-activity/tastin-wine/receive_code/create/create-receive-code-page', {target: 'GiftScan'});
            },
            /**
             * 电子协议推送 - 单挑推送：选终端-选协议
             * @author: 邓佳柳
             * @Date: 2024-08-14 14:12:47
             */
            goSinglePush(){
                this.$nav.push('/pages/terminal2/protocol-push/protocol-push-terminal-page', {source: 'protocolPush',type:'single'});
                this.$refs.protocolPushDialog.hide()
            },
             /**
             * 电子协议推送 - 批量推送：选协议-选终端
             * @author: 邓佳柳
             * @Date: 2024-08-14 14:12:47
             */
            goMutilPush() {
                // 业务代表和主管 可使用
                if (['SalesSupervisor', 'Salesman'].includes(this.userInfo.positionType)) {
                    this.$nav.push('/pages/terminal2/protocol-push/protocol-push-list-page',{source: 'protocolPush',type:'mutil'});
                    this.$refs.protocolPushDialog.hide()
                } else {
                    this.$dialog({
                        title: '提示',
                        content: '暂未开通',
                        cancelButton: false,
                        onConfirm: async () => {}
                    });
                }
            },
            /**
             * 电子协议推送 - 升级推送：选终端-选协议-选政策
             * @author: lld
             * @Date: 2024-09-18 09:31:47
             */
             goUpgradePush() {
                this.$nav.push('/pages/terminal2/protocol-push/protocol-push-terminal-page', {source: 'upgradePush',type:'single'});
                this.$refs.protocolPushDialog.hide()
            },
            /**
             * 电子协议推送 - 升级推送：选终端-选协议-选政策
             * @author: lld
             * @Date: 2024-09-18 09:31:47
             */
             goRebatePush() {
                this.$nav.push('/pages/terminal2/protocol-push/protocol-push-terminal-page', {source: 'protocolPush',type:'rebate'});
                this.$refs.protocolPushDialog.hide()
            },
            /**
             * 分享活动码
             * <AUTHOR>
             * @date 2020-08-18
             */
            viewInviteMemberCode() {
                this.createMiniProgramCode();
            },
            /**
             * 生成分享二维码
             *  <AUTHOR>
             *   @date        2020-07-28
             * */
            async createMiniProgramCode() {
                this.$utils.showLoading();
                const queryData = await this.$http.post(this.$env.appURL + '/loyalty/loyalty/member/getMemQrCode', {partnerId: this.userInfo.coreOrganizationTile['l3Id']}, {
                    autoHandleError: false,
                    handleFailed: (response) => {
                    this.$utils.hideLoading();
                    this.$showError('生成二维码失败' + response.result);
                    }
                });
                this.imgUrl.src = 'data:image/jpeg;base64,' + queryData.result;
                this.qrCodeData = queryData.result;
                this.$refs.imageShare.show();
                this.$utils.hideLoading();
            },
            /**
             * 保存分享图
             * <AUTHOR>
             * @date 2020-07-28
             */
            async saveShareImg() {
                this.$utils.showLoading();
                const save = wx.getFileSystemManager();
                const number = Math.random();
                save.writeFile({
                    filePath: wx.env.USER_DATA_PATH + '/pic' + number + '.png',
                    data: this.qrCodeData,
                    encoding: 'base64',
                    success: res => {
                        wx.saveImageToPhotosAlbum({
                            filePath: wx.env.USER_DATA_PATH + '/pic' + number + '.png',
                            success: function (data) {
                            },
                            fail: function (err) {
                                if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                                    this.$message.info('打开设置窗口');
                                    wx.openSetting({
                                        success(settingdata) {
                                            if (settingdata.authSetting['scope.writePhotosAlbum']) {
                                                that.$message.info('获取权限成功，请再次点击保存图片到相册。')
                                            } else {
                                                that.$message.info('获取权限失败,无法使用该功能，请授权再使用。');
                                            }
                                        }
                                    })
                                }
                            }
                        })
                        console.log(res)
                    }, fail: err => {
                        console.log(err)
                    }
                })
                this.$utils.hideLoading();
            },
            async handleMenuList() {
                await this.queryPublicCfg();
                this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
                this.dataBoardOption = [];
                // lzlj-002-3778 2022年9月30日 by王雅琪 应用被中断弹窗提醒优化
                if (!this.$utils.isEmpty(Taro.getStorageSync('token').menus)) {
                    this.menuData = Taro.getStorageSync('token').menus; // 获取菜单信息
                }
                // this.detailMenu(Taro.getStorageSync('token').menus);         // 获取菜单信息
                if (!this.menuData || this.menuData && !this.menuData.length) {
                    Taro.showModal({title: '提示', content: '请联系管理员维护小程序职责！', showCancel: false});
                }
                let data = this.menuData;
                if (this.$utils.isUndefined(data)) return;
                let ignoreSubMenus = ['corpwx_terminal_visit', 'corpwx_visit_check', 'corpwx_daily', 'corpwx_quote', 'corpwx_order', 'corpwx_scan_code', 'corpwx_inventory_check', 'corpwx_terminal_board']
                let menuList = [];
                let ignoreDataBoard = [
                {
                    name: '终端',
                    seq: 1,
                    val: 'corpwx_home_terminal_role'
                }, {
                    name: '鹰计划',
                    seq: 2,
                    val: 'corpwx_home_eagle_plan'
                },{
                    name: '考勤',
                    seq: 3,
                    val: 'corpwx_home_own_checkin'
                }, {
                    name: '经销商',
                    seq: 4,
                    val: 'corpwx_home_city_board'
                }, {
                    name: '活动',
                    seq: 5,
                    val: 'corpwx_home_activity_board'
                },  {
                    name: '企业公关',
                    seq: 6,
                    val: 'corpwx_home_visit_board'
                }];
                let ignoreConsumerBoard = ['consumer_overview_board_new', 'consumer_scan_board', 'consumer_overview_cockpit'];
                let colorList = [
                    'background-image: linear-gradient(180deg, #6392FA 0%, #2F69F8 100%);',
                    'background-image: linear-gradient(180deg, #3FE0E2 0%, #2EB3C2 100%);',
                    'background-image: linear-gradient(180deg, #FF8560 0%, #FF4D2D 100%);',
                    'background-image: linear-gradient(180deg, #FFD869 0%, #FFC637 100%);',
                    'background-image: linear-gradient(180deg, #B9E778 0%, #6AB524 100%);'
                ];
                let dataBoard = [];
                let consumerBoard  = [];
                data.forEach((item) => {
                    if (item.menuSource === "CorpWx" && !this.$utils.isEmpty(item.subMenus)) {
                        let subMenusArr = []
                        item.subMenus.forEach((value, index) => {
                            this.$set(value, 'iconColor', colorList[index % 5]);          // 给图标设置颜色
                            // 当前用户登录职位的所属品牌公司编码为“1210”时，将终端模块的【拜访】、【拜访检查】、【日报】、【配额】、【订单】、【入库】、【库存】【终端看板】全部隐藏
                            if (Taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1210' && item.moduleCode === "corpwx_terminal") {
                                if (!ignoreSubMenus.includes(value.moduleCode)) {
                                    subMenusArr.push(value)
                                }
                            } else if (this.isDealer) {
                                // 特定公司的经销商人员隐藏入库菜单
                                if (!['corpwx_scan_code'].includes(value.moduleCode)) {
                                    subMenusArr.push(value)
                                }
                            } else if (!ignoreConsumerBoard.includes(value.moduleCode)){
                                subMenusArr.push(value)
                            }
                            // 消费者看板
                            if ((value.moduleCode === 'consumer_scan_board') || (value.moduleCode === 'consumer_overview_cockpit') || (value.moduleCode === 'consumer_overview_board_new' && this.isShowMenu)) {
                                const tempUrl = value.urlPath.split('?');
                                let isLeader = '';
                                let secMenus = [];
                                if (tempUrl.length > 1) { // 为了区分消费者数据看板是领导看板还是业代看板
                                    isLeader = this.getQueryVariable('isLeader', value.urlPath.split('?')[1])
                                }
                                if (value.moduleCode === 'consumer_overview_board_new') {
                                    secMenus = value.secMenus;
                                }
                                if (value.moduleCode === 'consumer_scan_board') {
                                    secMenus = value.secMenus;
                                }
                                if (value.moduleCode === 'consumer_overview_cockpit') {
                                    secMenus = value.secMenus;
                                }
                                consumerBoard.push({name: value.moduleName, seq: value.seq, val: value.moduleCode, isLeader: isLeader, secMenus: secMenus})
                            }
                            let result = ignoreDataBoard.find(ele => ele.val === value.moduleCode)
                            if (result) dataBoard.push(result);
                        });
                        item.subMenus = subMenusArr
                        menuList.push(item)
                    }
                });
                this.menuList = menuList
                this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', Taro.getStorageSync('token').result.coreOrganizationTile.l3Id);
                this.dataBoardOption = dataBoard.concat(consumerBoard);
                //对seq字段做处理然后排序
                this.dataBoardOption.forEach((item)=>{
                    if (typeof item.seq === 'string'){
                        item.seq = !!item.seq ? Number(item.seq) : item.seq
                    }
                })
                this.dataBoardOption = this.$utils.ArraySortByProperty(this.dataBoardOption,'seq');
            },
            /**
             * 新版优化后的菜单处理方法，待运行稳定，去除原先的菜单处理方法
             * <AUTHOR>
             * @date 2024-05-23
             */
            async handleMenuList1() {
                const newFlag = await this.$utils.getCfgProperty('newEditionMenuHandleFlag');
                if (newFlag !== 'Y') {
                    await this.handleMenuList();
                    return;
                }
                // 获取经销商标识
                this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
                // 获取菜单信息
                this.menuData = this.$utils.isNotEmpty(Taro.getStorageSync('token').menus) ? Taro.getStorageSync('token').menus : [];
                // 菜单信息为空提示用户
                if (!this.menuData || this.menuData && !this.menuData.length) {
                    Taro.showModal({title: '提示', content: '请联系管理员维护小程序职责！', showCancel: false});
                    return;
                }
                // 工作台菜单
                let workMenuData = this.menuData.filter((item) => { return item.moduleCode !== 'corpwx_home_board' && item.moduleCode !== 'corpwx_new_board'; });
                // 看板菜单
                let boardMenuData = this.menuData.filter((item) => { return item.moduleCode === 'corpwx_home_board'; });
                // 菜单颜色队列
                let colorList = [
                    'background-image: linear-gradient(180deg, #6392FA 0%, #2F69F8 100%);',
                    'background-image: linear-gradient(180deg, #3FE0E2 0%, #2EB3C2 100%);',
                    'background-image: linear-gradient(180deg, #FF8560 0%, #FF4D2D 100%);',
                    'background-image: linear-gradient(180deg, #FFD869 0%, #FFC637 100%);',
                    'background-image: linear-gradient(180deg, #B9E778 0%, #6AB524 100%);'
                ];
                // 查询终端检查菜单是否处理
                const checkData = await this.$http.post('action/link/inspectors/checkIcon', {});
                let showTerminalCheckMenu = false;
                if (checkData.success) {
                    showTerminalCheckMenu = checkData.result
                }
                // 处理工作台菜单
                this.menuList = [];
                workMenuData.forEach((item) => {
                    if (item.menuSource === "CorpWx" && !this.$utils.isEmpty(item.subMenus)) {
                        let subMenusArr = []
                        item.subMenus.forEach((value, index) => {
                            // 给菜单图标设置颜色
                            this.$set(value, 'iconColor', colorList[index % 5]);
                            // 当前用户登录职位的所属品牌公司编码为“1210”时，将终端模块的【拜访】、【拜访检查】、【日报】、【配额】、【订单】、【入库】、【库存】【终端看板】全部隐藏
                            if (Taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1210' && item.moduleCode === "corpwx_terminal") {
                                let ignoreSubMenus = ['corpwx_terminal_visit', 'corpwx_visit_check', 'corpwx_daily', 'corpwx_quote', 'corpwx_order', 'corpwx_scan_code', 'corpwx_inventory_check', 'corpwx_terminal_board'];
                                if (!ignoreSubMenus.includes(value.moduleCode)) {
                                    subMenusArr.push(value);
                                }
                            } else if (this.isDealer) {
                                // 特定公司的经销商人员隐藏入库菜单
                                if (!['corpwx_scan_code'].includes(value.moduleCode)) {
                                    subMenusArr.push(value);
                                }
                            } else {
                                subMenusArr.push(value);
                            }
                        });
                        // 终端检查菜单只能满足特定白名单校验的用户可见
                        subMenusArr.forEach((val, num) => {
                            if (val.moduleCode === 'corpwx_terminal_check' && !showTerminalCheckMenu) {
                                subMenusArr.splice(num, 1);
                            }
                        })
                        item.subMenus = subMenusArr;
                        this.menuList.push(item);
                    }
                });
                // 处理看板菜单
                this.dataBoardOption = [];
                for (const item of boardMenuData) {
                    if (item.menuSource === "CorpWx" && !this.$utils.isEmpty(item.subMenus)) {
                        for (let value of item.subMenus) {
                            // 处理排序字段为数值
                            if (typeof value.seq === 'string') {
                                value.seq = !!value.seq ? Number(value.seq) : value.seq
                            }
                            // 240618消费者看板取消参数配置限值、所有组织可见，仅销售公司/集团可编辑选择公司按钮
                            this.dataBoardOption.push({name: value.moduleName, seq: value.seq, val: value.moduleCode, secMenus: value.secMenus});
                            // 消费者看板需要判断展示品牌公司
                            // if (value.moduleCode === 'consumer_overview_board_new') {
                            //     const data = await this.$utils.getCfgProperty('Consumer_Report');
                            //     console.log('参数配置data', data);
                            //     if (data !== 'noMatch') {
                            //         const companyIds = data.split(',');
                            //         const isShowMenu = companyIds.indexOf(this.userInfo.coreOrganizationTile['l3Id']) !== -1;
                            //         console.log('isShowMenu', isShowMenu)
                            //         if (isShowMenu) {
                            //             this.dataBoardOption.push({name: value.moduleName, seq: value.seq, val: value.moduleCode, secMenus: value.secMenus});
                            //         }
                            //     }
                            // } else {
                            //     this.dataBoardOption.push({name: value.moduleName, seq: value.seq, val: value.moduleCode, secMenus: value.secMenus});
                            // }
                        }
                    }
                }
                // 看板菜单重新排序
                this.dataBoardOption = this.$utils.ArraySortByProperty(this.dataBoardOption,'seq');
                // 处理完菜单再获取公司名称
                this.companyName = await this.$lov.getNameByTypeAndVal('ACTIVITY_COMPANY', Taro.getStorageSync('token').result.coreOrganizationTile.l3Id);
            },
            /**
             * 获取审批待办数量和消息未读数量
             * <AUTHOR>
             * @date 1/26/21
             * @param param
             */
            async requestTips() {
                this.userInfo = Taro.getStorageSync('token').result;
                if (this.$utils.isEmpty(this.userInfo)) {
                    return;
                }
                let arr = [
                    'SalesSupervisor',         // 业务主管
                    'Salesman',                // 业务代表
                    'GroupBuyManager',         // 团购经理
                    'AccountManager',          // 客户经理
                    'RegionalManager',         // 区域经理
                    'CustServiceManager',      // 客服经理
                    'VipManager',              // VIP经理
                    'CustServiceSpecialist',   // 客服专员
                    'CustServiceSupervisor',   // 客服主管
                    'BattleCommander',         // 会战指挥长
                    'SalesTeamLeader',         // 小组组长
                    'CityManager',             // 城市经理
                    'SalesChannelManger'       // 渠道经理
                ];
                this.staffFlag = arr.includes(this.userInfo.positionType);
                const prodPartCom = await this.queryCfgProperty('bot_result_postn');
                // 目标消费者场景问题
                let paraBot = []
                let pushStatus = ['DataPush']
                let paraFLAG = ''
                if (prodPartCom && prodPartCom.includes(this.userInfo.positionType)) { // 当前职位是否是匹配场景职位
                     paraBot = [{id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='},
                     { id: 'pushStatus', property: 'pushStatus', value: '[DataPush, DataDistribution]', operator: 'in' }]// 查已分配
                    paraFLAG = 'Y';
                    pushStatus.push('DataDistribution')
                }else{
                    // 查未分配的,加参数
                    paraFLAG = 'N'
                    paraBot = [ {id: 'followFlag', property: 'followFlag', value: 'N', operator: '='},
                    {id: 'pushStatus', property: 'pushStatus', value: 'DataPush', operator: '='}
                    ]
                }
                //220712 新增通过菜单权限,判断是否查询待办
                let fanwei = false;
                let approve = false;
                let message = false;
                let openBottle = false;
                let announce = false;
                let undoAccount = false;
                const corpwx_own = this.menuData.find((item) => { return item.moduleCode === 'corpwx_own'; });
                if (this.$utils.isNotEmpty(corpwx_own) && this.$utils.isNotEmpty(corpwx_own.subMenus)) {
                    corpwx_own.subMenus.forEach((item) => {
                        if (item.moduleCode === 'corpwx_own_approve') {
                            approve = true;
                        }
                        if (item.moduleCode === 'corpwx_own_approve_fanwei') {
                            fanwei = true;
                        }
                        if (item.moduleCode === 'corpwx_own_message') {
                            message = true;
                        }
                        if (item.moduleCode === 'corpwx_own_anounce') {
                            announce = true;
                        }
                    });
                }
                const consumer = this.menuData.find((item) => { return item.moduleCode === 'corpwx_consumer'; });
                if (this.$utils.isNotEmpty(consumer) && this.$utils.isNotEmpty(consumer.subMenus)) {
                    consumer.subMenus.forEach((item) => {
                        if (item.moduleCode === 'consumer_open_bottle') {
                            openBottle = true;
                        }
                    });
                }
                //lzlj-002-2817 2022年4月2日 by吕志平 增加执行反馈菜单右上角提示
                let accessGroupOauth = '';
                let menuFlag = false;
                const market_activity = this.menuData.find((item) => { return item.moduleCode === 'corpwx_market_activity'; });
                if(this.$utils.isNotEmpty(market_activity) && this.$utils.isNotEmpty(market_activity.subMenus)) {
                    market_activity.subMenus.forEach((item) => {
                        if (item.moduleCode === 'corpwx_work_order' && this.$utils.isNotEmpty(item.id)) {
                            menuFlag = true;
                            accessGroupOauth = this.$utils.getMenuAccessGroup(item.id);
                        }
                    });
                }
                let param = {
                    filtersRaw: [
                        {id: 'status', property: 'status', value: '[Published,Processing,Closed]', operator: 'in'},
                        {id: 'aproStatus', property: 'aproStatus', value: '[Approve,Refeedback,RefeedWithdraw]', operator: 'in'}
                    ],
                    onlyCountFlag: true
                }
                if (!this.$utils.isEmpty(accessGroupOauth)) {
                    param.oauth = accessGroupOauth;
                }
                if(undoAccount || message || approve || announce || openBottle || (this.staffFlag && menuFlag)) {
                    Promise.all([
                        !fanwei ? '' : this.$utils.handleTODOListNumber('get','unReadFanwei', this.$env.appURL + '/action/link/sendDmp/send',
                        {dmpUrl: '/link/fieldTemApp/queryFwToDoListCount'}, (data) => {
                            return data.total;
                        }),
                        !message ? '' : this.$utils.handleTODOListNumber('get','unReadMessage', 'export/link/pushNotice/queryFieldsByExampleCount', {userId: this.userInfo.id}, (data) => {
                            return data.unreadMessage;
                        }),
                        !approve ? '' : this.$utils.handleTODOListNumber('get', 'unApproval','export/link/flow/v2/queryFlowCount', {oauth: "ALL"}, (data) => {
                            return data.myRunningNum;
                        }),
                        !openBottle ? '' : this.$utils.handleTODOListNumber('get', 'allOpenBottle',this.$env.appURL + '/action/link/sendDmp/send', {
                            oauth: "MY_POSTN_ONLY",
                            // dmpUrl: '/link/cusOpenBotResult/queryCount',
                            dmpUrl: '/link/cusOpenBotResult/queryCountByDistinct',
                            rows: 20,
                            page: 1,
                            pushStatus: pushStatus,
                            followFlag: paraFLAG,
                            ruleType: 'ConsumerModel',
                            // filtersRaw: [
                            //     { id: 'ruleType', property: 'ruleType', value: 'ConsumerModel', operator: '=' }
                            // ],
                            }, (data) => {
                            return data.total;
                        }),
                        //公告列表新增提示
                        !announce ? '' : this.$utils.handleTODOListNumber('get', 'unReadAnnounce','export/link/announce/queryByExamplePage',
                            { filtersRaw: [{id: 'isRead', property: 'isRead', value: 'N'}, {property: 'anneModule', operator: '=', value: 'WeCom'}, {property: "anneStatus", operator: "=", value: "Published"}], attr5: "byScope", onlyCountFlag: true}),
                        !(this.staffFlag && menuFlag) ? '' :  this.$utils.handleTODOListNumber('get', 'unFeedbackWork','export/link/marketAct/queryByExamplePage', param),
                        // !undoAccount ? '' : this.$utils.handleTODOListNumber('get', 'unDoAccount',this.$env.dmpURL + '/link/cdcPubConsumer/queryBacklogTotal', {}),
                    ]).then(([approvalTipsFanwei,messageTips, approvalTips, openBottleTips, announceData, workOrder, accountNum]) => {
                        this.tipsNumObj = {
                            unreadMessage: messageTips || 0,
                            myRunningNum: approvalTips || 0,
                            myRunningNumFanwei: approvalTipsFanwei || 0,
                            openBottleNum: openBottleTips || 0,
                            ownAnnounceNum: announceData || 0,
                            workOrderNum: workOrder || 0,
                            // accountNum: accountNum || 0
                        };
                    });
                    //用来刷新界面代办数量不可去除
                    this.$emit('firstCallBack', true);
                }
            },
            /**
             @desc: 仅在获取token时做的操作
             @author: wangbinxin
             @date 2022-09-07 15-21
             **/
            async requestToken() {
                // 用户登录
                await loginService.requestNewToken();
                // 获取用户信息
                this.userInfo = Taro.getStorageSync('token').result;
                // 获取菜单信息
                this.detailMenu(Taro.getStorageSync('token').menus);
                // 异步插入设备信息
                this.equipmentInfoInsert().then(() => {});
            },
            async queryCfgProperty (key) { // 获取参数配置
                const data = await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [{ id: 'key', property: 'key', value: key }]
                });
                if (data.success && data.rows && data.rows.length) {
                    return data.rows[0].value;
                } else {
                    return 'noMatch';
                }
            },
            /**
             * 跳转个人中心
             * <AUTHOR>
             * @date 2020-09-14
             */
            goToPersonCenter() {
                this.$nav.push('/pages/lzlj-II/person-center/person-center-page', {
                    data: this.userInfo
                })
            },
            /**
             * 数据初始化
             * <AUTHOR>
             * @date 2020-09-04
             */
            async initData() {
                // 判断缓存信息是否存在
                let tokenInfo = Taro.getStorageSync('token');
                if (this.$utils.isEmpty(tokenInfo) && this.$device.systemInfo.environment) {
                    await this.requestToken();
                    return;
                }
                if (tokenInfo) {
                    if (this.$device.systemInfo.environment && this.$device.systemInfo.environment !== tokenInfo.loginEnv) {
                        await this.requestToken();
                        return;
                    }
                    if (tokenInfo.baseURL === this.$env.baseURL) {
                        this.userInfo = Taro.getStorageSync('token').result;         // 获取用户信息
                        this.detailMenu(Taro.getStorageSync('token').menus);         // 获取菜单信息
                    } else {
                        await this.requestToken();
                    }
                    this.$store.commit('hotUpdateFlag/setHotUpdateFlag', true);
                }
                this.defaultAvatar = this.userInfo.gender === 'FEMALE' ? this.$imageAssets.femaleImage : this.$imageAssets.maleImage;
            },
            /**
             * 获取地址
             * <AUTHOR>
             * @date 2020-07-14
             */
            async getAddress() {
                this.coordinate = await this.$locations.getCurrentCoordinate()
                // 校验用户是否授权地理位置
                if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                    this.$store.commit('coordinate/setCoordinate', this.coordinate)
                } else {
                    this.$store.commit('coordinate/setCoordinate', false)
                }
            },
            /**
             * 进入菜单详情页面
             * <AUTHOR>
             * @date 2020-07-29
             * @param data 菜单对象
             */
            async goMenuDetails(data) {
                const tempUrl = data.urlPath.split('?');
                let param = ''
                if (tempUrl.length > 1) { // 为了区分消费者数据看板是领导看板还是业代看板
                    param = this.getQueryVariable('isLeader', data.urlPath.split('?')[1])
                }
                //领用-非费用核销采用提示框
                if(data.moduleCode==='corpwx_non_cost_verification'){
                    this.$refs.scanCodeMenuDialog.show()
                    return false
                }
                // 电子协议推送 采用提示框
                if(data.moduleCode==='corpwx_protocol_push'){
                    this.$refs.protocolPushDialog.show()
                    return false
                }
                if(data.moduleCode==='consumer_schedule') {
                    try {
                        $logger.info('CONSUMER_SCHEDULE_001', 'Click', `菜单id：consumer_schedule`);
                    } catch (e) {
                        console.log('e', e)
                        $logger.error('CONSUMER_SCHEDULE_001', 'Click', `菜单id：consumer_schedule_报错信息：${e}`);
                    }
                }
                if(data.moduleCode==='consumer_schedule_calendar') {
                    try {
                        $logger.info('CONSUMER_SCHEDULE_CALENDAR_001', 'Click', '菜单id：consumer_schedule_calendar');
                    } catch (e) {
                        $logger.error('CONSUMER_SCHEDULE_CALENDAR_001', 'Click', `菜单id：consumer_schedule_calendar_报错信息：${e}`);
                    }
                }
                const parentIds = await this.$utils.getCfgProperty("corpwx_consumer");
                if(parentIds.split(',').includes(data.parentId)){
                    await $http.post( this.$env.appURL + '/action/link/position/checkLoginPostn', {
                        id: this.userInfo.postnId,
                        source: 'qw'
                    }, {
                        autoHandleError: false,
                        handleFailed: () => {
                            this.$utils.hideLoading();
                            this.$showError('当前职位切换失败，请清除缓存重新进行职位切换');
                        }
                    });
                }
                this.$nav.push(data.urlPath, {
                    source: data.description,
                    menuId: data.id,
                    secMenus: data.secMenus || [],
                    param: param
                })
            },
            /**
             * @desc 解析URL参数
             * <AUTHOR>
             * @date 2021/6/9 17:11
             * @param name 参数变量名
             * @param path 路径
             **/
            getQueryVariable(name, path) {
                const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
                const result = path.match(reg);
                if (result != null) {
                    return decodeURI(result[2]);
                } else {
                    return null;
                }
            },
            async insertSystemInfo(qyVersion) {
                const that = this;
                let coordinate = await this.$locations.getCurrentCoordinate();
                if (this.$utils.isNotEmpty(coordinate.latitude) && this.$utils.isNotEmpty(coordinate.longitude)) {
                    let equipmentInfoObj = {
                        brand: that.$device.systemInfo.brand,                                                           // 设备品牌
                        model: that.$device.systemInfo.model,                                                           // 设备型号
                        pixelRatio: that.$device.systemInfo.pixelRatio,                                                 // 设备像素比
                        screenWidth: that.$device.systemInfo.screenWidth,                                               // 屏幕宽度，单位px
                        screenHeight: that.$device.systemInfo.screenHeight,                                             // 屏幕高度，单位px
                        windowWidth: that.$device.systemInfo.windowWidth,                                               // 可使用窗口宽度，单位px
                        windowHeight: that.$device.systemInfo.windowHeight,                                             // 可使用窗口高度，单位px
                        statusBarHeight: that.$device.systemInfo.statusBarHeight,                                       // 状态栏的高度，单位px
                        language: that.$device.systemInfo.language,                                                     // 微信设置的语言
                        version: qyVersion ? qyVersion : that.$device.systemInfo.version,                                                       // 微信版本号
                        system: that.$device.systemInfo.system,                                                         // 操作系统及版本
                        platform: that.$device.systemInfo.platform,                                                     // 客户端平台
                        fontSizeSetting: that.$device.systemInfo.fontSizeSetting,                                       // 用户字体大小（单位px）。以微信客户端「我-设置-通用-字体大小」中的设置为准
                        sDKVersion: that.$device.systemInfo.SDKVersion,                                                 // 客户端基础库版本
                        environment: that.$device.systemInfo.environment,                                               // 小程序当前运行环境
                        latitude: coordinate.latitude,                                                                  // 维度
                        longitude: coordinate.longitude,                                                                // 经度
                        locType: that.locationType,                                                                     // 定位类型，分为两种 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
                        accuracy: coordinate.accuracy,                                                                  // 精度
                        cacheSize: Taro.getStorageInfoSync().currentSize                                                // 本地缓存大小，单位kb
                    };
                    await $http.post('action/link/corpWxDevice/insert', equipmentInfoObj);
                } else {
                    this.$utils.showAlert('请授权定位权限')
                }
            },
            /**
             * 插入设备信息
             * <AUTHOR>
             * @date 2020-07-16
             */
            async equipmentInfoInsert() {
                const that = this;
                let qyVersion = '';
                if (that.$device.systemInfo.environment === 'wxwork') {
                   wx.qy.getSystemInfo({
                       complete(res) {
                           qyVersion = res.version;
                           that.insertSystemInfo(qyVersion);
                        }
                    });
                } else {
                    let coordinate = await this.$locations.getCurrentCoordinate();
                    if (this.$utils.isNotEmpty(coordinate.latitude) && this.$utils.isNotEmpty(coordinate.longitude)) {
                        let equipmentInfoObj = {
                            brand: that.$device.systemInfo.brand,                                                           // 设备品牌
                            model: that.$device.systemInfo.model,                                                           // 设备型号
                            pixelRatio: that.$device.systemInfo.pixelRatio,                                                 // 设备像素比
                            screenWidth: that.$device.systemInfo.screenWidth,                                               // 屏幕宽度，单位px
                            screenHeight: that.$device.systemInfo.screenHeight,                                             // 屏幕高度，单位px
                            windowWidth: that.$device.systemInfo.windowWidth,                                               // 可使用窗口宽度，单位px
                            windowHeight: that.$device.systemInfo.windowHeight,                                             // 可使用窗口高度，单位px
                            statusBarHeight: that.$device.systemInfo.statusBarHeight,                                       // 状态栏的高度，单位px
                            language: that.$device.systemInfo.language,                                                     // 微信设置的语言
                            version: that.$device.systemInfo.version,                                                       // 微信版本号
                            system: that.$device.systemInfo.system,                                                         // 操作系统及版本
                            platform: that.$device.systemInfo.platform,                                                     // 客户端平台
                            fontSizeSetting: that.$device.systemInfo.fontSizeSetting,                                       // 用户字体大小（单位px）。以微信客户端「我-设置-通用-字体大小」中的设置为准
                            sDKVersion: that.$device.systemInfo.SDKVersion,                                                 // 客户端基础库版本
                            environment: that.$device.systemInfo.environment,                                               // 小程序当前运行环境
                            latitude: coordinate.latitude,                                                                  // 维度
                            longitude: coordinate.longitude,                                                                // 经度
                            locType: that.locationType,                                                                     // 定位类型，分为两种 wgs84 返回 gps 坐标，gcj02 返回可用于 wx.openLocation 的坐标
                            accuracy: coordinate.accuracy,                                                                  // 精度
                            cacheSize: Taro.getStorageInfoSync().currentSize                                                // 本地缓存大小，单位kb
                        };
                        $http.post('action/link/corpWxDevice/insert', equipmentInfoObj);
                    } else {
                        this.$utils.showAlert('请授权定位权限');
                    }
                }
            },
            /**
             * 菜单处理
             * <AUTHOR>
             * @date 2020-07-20
             * @param data 菜单数据
             */
            detailMenu(data) {
                if (this.$utils.isEmpty(data)) return
                this.menuData = Taro.getStorageSync('token').menus

                /*初始化用户信息以及菜单信息之后才处理缓存*/
                PageCacheManager.checkPageCache({
                    afterChangePostn: ({postnId, postnName}) => {
                        Object.assign(this.userInfo, {postnId, postnName})
                    }
                })
            },
            /**
             @desc: 当前提醒跳转，跳转至对应界面
             @author: wangbinxin
             @date 2022-08-08 20-06
             **/
            goto(item){
                if(item.url){
                    this.$nav.push(item.url, {
                        source: 'home'
                    });
                }
            },
            /**
             * 版本更新校验
             * <AUTHOR>
             * @date 2022/7/9
             */
            async versionCheck() {
                await checkUpdate();
            },
            /**
             * 清空小程序模板缓存
             * <AUTHOR>
             * @date 2022/7/9
             */
            async clearModuleCache() {
                const QW_MP_TMPL_KEY = 'link_qw_mp_tmpl';
                await Taro.removeStorage({
                    key: QW_MP_TMPL_KEY,
                    success: () => {},
                    fail: () => {}
                });
            },
            /**
             * 检查企业微信app版本以及基础库，低于指定版本则弹窗指引用户进行升级
             * <AUTHOR>
             * @date 2022/8/25
             */
            checkQyVersion() {
                const wxSystemInfo = wx.getSystemInfoSync();
                // 代表小程序在企业微信中运行
                if (wxSystemInfo.environment === 'wxwork' && wxSystemInfo.platform !== 'devtools') {
                    wx.qy.getSystemInfo({
                        success: (res) => {
                            const minVersion = '4.0.9';
                            if (compareVersion(res.version, minVersion) < 0) {
                                this.$dialog({
                                    title: '提示',
                                    content: () => (<view style="padding: 10px;">当前企业微信版本过低，小程序部分功能将无法使用，请升级到最新版本后重新进入。</view>),
                                    initial: true
                                });
                            }
                        }
                    })
                }

            },
            /**
             * 获取需要全局使用的参数配置，并初始化全局变量
             * <AUTHOR>
             * @date 2025/4/2
             */
            async getGlobalCfgProperty() {
                //把单个字符串从短斜杠命名转换为驼峰命名
                const kebabToCamel = (str)=> {
                    return str.split('-').map((word, index) => {
                        if (index === 0) {
                            return word;
                        }
                        return word.charAt(0).toUpperCase() + word.slice(1);
                    }).join('');
                }
                // 根据key值获取参数配置，并返回key值为驼峰形式的参数键，value为参数值的json对象
                const getCfgPropertyWithKey = async (key) => {
                    return new Promise(async (resolve, reject) => {
                        try {
                            const res = await this.$utils.getCfgProperty(key, false);
                            const camelKey = kebabToCamel(key);
                            resolve({
                                [camelKey]: res
                            })
                        }catch (e) {
                            console.log(`获取全局参数失败: ${JSON.stringify(e)}`);
                            reject(e)
                        }
                    })
                }
                try {
                    // aegis-report-locations: 是否上传地图组件调用情况
                    //use-cached-reverse-address: 是否使用缓存的逆解析地址
                    const globalCfgPropertyKey = ['aegis-report-locations', 'use-cached-reverse-address']
                    const promises = globalCfgPropertyKey.map((key) => getCfgPropertyWithKey(key));
                    const results = await Promise.all(promises);
                    const linkGlobalCfgProperty = {}
                    results.map((res)=>Object.assign(linkGlobalCfgProperty,res))
                    // 放入全局参数
                    Taro.setStorageSync('link-global-cfgProperty', linkGlobalCfgProperty);
                } catch (e) {
                    console.log(`获取全局参数失败: ${JSON.stringify(e)}`);
                }
            }
        },
        onBack(param) {}
    }
</script>
<style lang="scss">
    .me {
        width: 100%;
        .tag-box{
            padding-top: 20px;
            .tag-item{
                display: inline-block;
                background: white;
                font-size: 26px;
                padding: 10px 35px;
                border-radius: 35px;
                margin-left: 20px;
            }
        }
        .scanCode{
            .headBox{
                display: flex;
                width: 100%;
                .title{
                    text-align: right;padding-right: 1em;flex: 3;
                }
                .tipClose{
                    flex: 1;
                    font-size: 32px;
                    line-height: 48px;
                    text-align: right;
                    color: #999999;
                }
            }
            .contentBox{
                height: 264px;
                width: 100%;
                display: flex;
            }
            .scanCodeBox{
                flex: 1;
                display: flex;
                flex-direction: column;
                justify-content: center;
                .one{
                    font-size: 72px;
                    text-align: center;
                    color: #E72C2C;
                }
                .two{
                    font-size: 72px;
                    text-align: center;
                    color:  #F7A71B;
                }
                .three{
                    font-size: 72px;
                    text-align: center;
                    color:  #1bb1f7;
                }
                .four{
                    font-size: 72px;
                    text-align: center;
                    color:  #d3e089;
                }
                .text{
                    text-align: center;
                    padding-top: 28px;
                    font-size: 24px;
                }
            }
        }
        .button-private {
            display: inline;
            padding-left: 0;
            padding-right: 0;
            box-sizing: border-box;
            font-size: 18px;
            text-align: center;
            text-decoration: none;
            line-height: 2.55555556;
            border-radius: 5px;
            -webkit-tap-highlight-color: transparent;
            overflow: hidden;
            color: #000;
        }

        .button-private:after {
            width: 200%;
            height: 200%;
            position: absolute;
            top: 0;
            left: 0;
            border: none;
            -webkit-transform: scale(.5);
            transform: scale(.5);
            -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
            box-sizing: border-box;
        }

        .menu-container {
            padding-bottom: 24px;

            .menu-top, .menu-top-board {
                width: 750px;
                background-repeat: no-repeat;
                background-size: 100% 100%;
                /*background-image: linear-gradient(180deg, #6392FA 0%, #2F69F8 100%);*/
                border-bottom-left-radius: 80px;
                border-bottom-right-radius: 80px;
                position: sticky;
                z-index: 3;

                .page-title {
                    width: 100%;
                    text-align: center;
                    font-family: PingFangSC-Semibold, serif;
                    font-size: 34px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 34px;
                }

                .user-info {
                    margin-top: 100px;
                    @include flex-start-center;

                    .user-img {
                        margin-left: 48px;
                        width: 112px;
                        height: 112px;
                        border-radius: 50%;
                        overflow: hidden;
                        border: 5px solid #fff;
                        box-shadow: 0 12px 36px 0 #0034B5;

                        img {
                            width: 100%;
                            height: 100%;
                            border-radius: 50%;
                        }

                        image {
                            width: 120px;
                            height: 120px;
                        }
                    }

                    .user-post {
                        margin-left: 32px;
                        width: 60%;

                        .first-name {
                            font-family: PingFangSC-Semibold, serif;
                            font-size: 40px;
                            color: #FFFFFF;
                            letter-spacing: 1px;
                            line-height: 40px;
                            .staff-type {
                                font-size: 24px;
                                display: inline-block;
                                font-family: PingFangSC-Regular, serif;
                            }
                        }

                        .post-name {
                            margin-top: 16px;
                            font-family: PingFangSC-Regular, serif;
                            font-size: 28px;
                            color: #FFFFFF;
                            letter-spacing: 1px;
                            line-height: 28px;
                        }
                        .dealer-name {
                            font-size: 20px;
                        }
                        .four-color {
                            font-size: 24px;
                            width: 60px;
                            line-height: 40px;
                            text-align: center;
                            display: inline-block;
                            margin-left: 20px;
                            font-weight: bold;
                        }
                    }

                    .icon-xiaoxi {
                        margin-left: 28px;
                        font-size: 48px;
                        color: #fff;
                    }
                }
            }

            .menu-top-board{
                position: relative;
                top: 0 !important;
            }

            .menu-loading {
                line-height: 50vh;
                text-align: center;
                width: 100%;
            }

            .menu-content {
                padding-bottom: 16px;
            }

            .menu-content, .menu-content-info {
                width: 702px;
                background: #fff;
                margin: 24px auto auto auto;
                border-radius: 16px;

                .menu-stair {
                    margin-left: 24px;
                    padding-top: 24px;
                    @include flex-start-center;

                    .line {
                        .line-top {
                            width: 8px;
                            height: 16px;
                            background: #3FE0E2;
                        }

                        .line-bottom {
                            width: 8px;
                            height: 16px;
                            background: #2F69F8;
                        }
                    }

                    .stair-title {
                        margin-left: 16px;
                        font-family: PingFangSC-Semibold, serif;
                        font-size: 32px;
                        color: #262626;
                        letter-spacing: 1px;
                        line-height: 32px;
                    }
                }

                .second-menu-content {
                    @include flex-start-center;
                    @include wrap;
                    padding-bottom: 20px;
                    padding-top: 20px;

                    .menu-second {
                        text-align: center;
                        width: 25%;
                        padding-bottom: 20px;
                        padding-top: 20px;
                        position: relative;

                        .num-tips {
                            position: absolute;
                            right: 0;
                            top: 0;
                            background-color: #FF5A5A;
                            color: #ffffff;
                            font-size: 24px;
                            border-radius: 50%;
                            padding-left: 6px;
                            padding-right: 6px;
                            margin-right: 24px;
                            height: 40px;
                            line-height: 40px;
                            min-width: 40px;
                        }

                        .icon-style {
                            font-size: 44px;
                            background-image: linear-gradient(180deg, #6392FA 0%, #2F69F8 100%);
                            -webkit-background-clip: text;
                            color: transparent;
                        }

                        .second-title {
                            font-family: PingFangSC-Regular, serif;
                            font-size: 24px;
                            color: #595959;
                            letter-spacing: 1px;
                            text-align: center;
                            line-height: 24px;
                            padding-top: 18px;
                        }
                    }
                }

                .board-content{
                    padding: 24px;
                    .board-normal-content, .board-small-content, .board-big-content{
                        width: 33.33%;
                        height: 148px;
                        position: relative;
                        display: inline-block;
                        box-sizing: border-box;
                        padding-right: 12px;
                        .con-img, .con-bg{
                            width: 100%;
                            height: 100%;
                            border-radius: 16px;
                        }
                        .con-bg{
                            background: #DDDDDD;
                        }
                        .con-mess{
                            position: absolute;
                            top: 30px;
                            left: 16px;
                            view{
                                font-family: PingFangSC-Regular;
                                font-size: 24px;
                                color: #FFFFFF;
                                line-height: 24px;
                                font-weight: 400;
                            }
                            .mess-item{
                                view{
                                    display: inline-block;
                                }
                                .mess-num, .mess-date{
                                    font-family: HelveticaNeue-Bold;
                                    font-size: 44px;
                                    color: #FFFFFF;
                                    line-height: 44px;
                                    font-weight: 700;
                                }
                            }
                            .mess-text{
                                padding-top: 20px;
                                padding-right: 12px;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 2;
                                overflow: hidden;
                            }
                        }
                    }
                    .board-small-content{
                        width: 25%;
                        height: 172px;
                        .con-mess{
                            .mess-text{
                                line-height: 28px;
                                padding-right: 16px;
                                padding-top: 40px;
                                display: -webkit-box;
                                -webkit-box-orient: vertical;
                                -webkit-line-clamp: 2;
                                overflow: hidden;
                            }
                        }
                    }
                    .board-big-content{
                        width: 50%;
                    }
                    .no-padding{
                        padding-right: 0;
                    }
                    .board-no-content{
                        display: flex;
                        justify-content: center;   /* 子元素水平居中 */
                        align-items: center;       /* 子元素垂直居中 */
                        display: -webkit-flex;
                        .con-img{
                            width: 160px;
                            height: 200px;
                        }
                        .no-mess{
                            text-align: center;
                            color: #cccccc;
                            font-size: 24px;
                        }
                    }
                }

                .tap-container {
                    width: 100%;
                    display: flex;
                    height: 92px;
                    overflow: hidden;
                    .lnk-tabs::-webkit-scrollbar {
                        display:none
                    }
                    .lnk-tabs {
                        overflow-x: scroll;
                        white-space: nowrap;
                        border-top: 1px solid #f2f2f2;
                        display: flex;
                        color: #595959;
                        width: 670px;
                        &.marginTop {
                            margin-top: 80px;
                        }
                        .active {
                            color: $color-primary;
                        }
                        .lnk-tabs-item {
                            height: 92px;
                            line-height: 92px;
                            text-align: center;
                            padding-right: 20px;
                            .label-name {
                                width: 100%;
                                font-size: 28px;
                                margin-left: 10px;
                            }
                            .line {
                                height: 8px;
                                width: 56px;
                                border-radius: 16px 16px 0 0;
                                background-color: $color-primary;
                                box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                                margin: -12px auto auto auto;
                            }
                        }
                    }
                }
            }
        }
    }
</style>
