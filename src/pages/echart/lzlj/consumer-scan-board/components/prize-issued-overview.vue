<!--
@createdBy 黄鹏
@date 2024/04/24
@description: --- 奖品发放总览
-->
<template>
    <view class="prize-issued-overview table">
        <view class="table-con">
            <view class="table-left">
                <view class="table-head">
                    <view class="word4 table-cell" v-if="dataRange === 'Area'">区域</view>
                    <view class="word4 table-cell" v-else-if="dataRange === 'Team'">业务代表</view>
                    <view v-else class="display-flex">
                        <view class="word4 table-cell">经销商/分销商</view>
                        <view class="word4 table-cell">客户名称</view>
                    </view>
                    <view class="word4 table-cell">产品大类</view>
                </view>
                <view v-if="tableList.length > 0">
                    <view class="table-desc-line" v-for="(item, index) in tableList" :key="index">
                        <view v-if="item.isTotal" class="desc-item-con table-cell">总计</view>
                        <view v-else>
                            <view v-if="dataRange === 'Area'" class="desc-item-con">
                                <view class="word4 table-cell">{{item.orgName}}</view>
                                <view class="desc-sub-item-con">
                                    <view class="word4 table-cell" :class="row.type === '合计' ? 'border-bottom' : ''"
                                          v-for="(row, ins) in item.childs" :key="ins">
                                        {{row.type === '合计' ?
                                        '合计' : row.matLClassName}}
                                    </view>
                                    <view class="word4 table-cell load-more" v-if="item.showMore"></view>
                                </view>
                            </view>
                            <view v-else-if="dataRange === 'Team'" class="desc-item-con">
                                <view class="word4 table-cell">{{item.staffName}}</view>
                                <view class="desc-sub-item-con">
                                    <view class="word4 table-cell" :class="row.type === '合计' ? 'border-bottom' : ''"
                                          v-for="(row, ins) in item.childs" :key="ins">
                                        {{row.type === '合计' ? '合计' : row.matLClassName}}
                                    </view>
                                    <view class="word4 table-cell load-more" v-if="item.showMore"></view>
                                </view>
                            </view>
                            <view v-else class="desc-item-con">
                                <view class="word4 table-cell">{{item.supplyDisName}}</view>
                                <view class="desc-sub-item-con">
                                    <view v-for="(row, ins) in item.childs" :key="ins" class="border-con"
                                          :class="row.type === '合计' ? 'border-bottom' : ''">
                                        <view class="word8 table-cell" v-if="row.type === '合计'">合计</view>
                                        <view v-else class="display-flex">
                                            <view class="word4 table-cell">{{row.acctName}}</view>
                                            <view class="word4 table-cell">{{row.matLClassName}}</view>
                                        </view>
                                    </view>
                                    <view class="word4 table-cell load-more" v-if="item.showMore"></view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="table-right">
                <view class="table-head">
                    <view class="word6 table-cell">开瓶扫码瓶数</view>
                    <view class="word9 table-cell">奖品发放数量（个）</view>
                    <view class="word10 table-cell">特等奖发放数量（个）</view>
                    <view class="word10 table-cell">一等奖发放数量（个）</view>
                </view>
                <view v-if="tableList.length > 0">
                    <view v-for="(item, index) in tableList" :key="index">
                        <view class="table-line" v-for="(row, ins) in item.childs" :key="ins">
                            <view class="word6 table-cell">{{row.openBottleQty}}</view>
                            <view class="word9 table-cell">{{row.prizeQty}}</view>
                            <view class="word10 table-cell">{{row.specialprizeQty}}</view>
                            <view class="word10 table-cell">{{row.firstprizeQty}}</view>
                        </view>
                        <view class="table-line word4 load-more" v-if="!item.isTotal && item.showMore">
                            <view class="word6 table-cell" @tap="gotoListItemPage(item)">查看更多</view>
                            <view class="word9 table-cell"></view>
                            <view class="word10 table-cell"></view>
                            <view class="word10 table-cell"></view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="tableList.length">
            <view v-if="total >= tableList.length" class="load-more border-top" @tap="gotoListPage">查看更多</view>
            <view v-else class="no-more border-top"></view>
        </view>
        <view v-else class="no-data">暂无数据</view>
    </view>
</template>

<script>

    export default {
        name: "prize-issued-overview",
        props: {
            userInfo: {
                type: Object,
                default: {}
            },
            isListPage: {
                type: Boolean,
                default: false
            },
            listPageParam: {
                type: Object,
                default: () => {}
            }
        },
        data() {
            return {
                tableList: [],
                totalRow: {},
                total: 0,
                childRows: 3,
                page: 1,
                dateType: '',
                orgId: '',
                dataRange: ''
            }
        },
        created() {
            if (this.isListPage) {
                this.queryData(this.listPageParam.dataRange, this.listPageParam.orgId, this.listPageParam.dateType);
            }
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/04/22
             * @methods: queryData
             * @para:
             * @description: 查询表格数据
             **/
            async queryData(dataRange, orgId, dateType, nextPage) {
                if (nextPage) this.page += 1;
                this.dateType = dateType;
                this.orgId = orgId;
                this.dataRange = dataRange;
                this.$utils.showLoading();
                const param = {
                    dmpSrUrl: '/link/consumerDeckleReportSR/queryPrizePage',
                    dataAccess: dataRange,
                    postnId: this.userInfo.postnId,
                    // orgId: orgId,
                    dateType: dateType || 'thisFiscalYear',
                    page: this.page,
                    rows: this.isListPage ? 10 : 1,
                    childRows: this.childRows
                };
                if (this.dataRange === 'Area') {
                    param.orgId = orgId;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', param);
                if (data.success) {
                    if (nextPage) {
                        this.tableList = this.tableList.concat(data.rows);
                    } else {
                        this.tableList = data.rows || [];
                    }
                    if (this.tableList.length && data.totalResult && !nextPage) {
                        this.totalRow = data.totalResult;
                        const obj = {
                            isTotal: true,
                            childs: [this.totalRow]
                        };
                        this.tableList.unshift(obj)
                    }
                    this.total = data.total;
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$message.error({message: '查询消费者奖品发放总览数据失败！' + data.result, customFlag: true});
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: gotoListItemPage
             * @para:
             * @description: 跳转最小维度页面
             **/
            gotoListItemPage (item) {
                this.$nav.push('/pages/echart/lzlj/consumer-scan-board/table-list-item-page.vue', {
                    title: '奖品发放总览',
                    dataRange: this.dataRange,
                    dateType: this.dateType,
                    orgId: item.orgId,
                    staffId: item.staffId,
                    supplyDisId: item.supplyDisId,
                    orgName: item.orgName,
                    staffName: item.staffName,
                    supplyDisName: item.supplyDisName
                })
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: gotoListPage
             * @para:
             * @description: 跳转最大维度页面
             **/
            gotoListPage () {
                if (this.isListPage) {
                    this.queryData(this.listPageParam.dataRange, this.listPageParam.orgId, this.listPageParam.dateType, true);
                } else {
                    this.$nav.push('/pages/echart/lzlj/consumer-scan-board/table-list-page.vue', {
                        title: '奖品发放总览',
                        dataRange: this.dataRange,
                        dateType: this.dateType,
                        orgId: this.orgId
                    })
                }
            }
        }
    }
</script>

<style lang="scss">
    @import "../css/board.scss";

    .prize-issued-overview {
        padding: 0 24px;
        margin-bottom: 36px;
        background: white;
        border-radius: 28px;
        font-size: 28px;
        font-weight: 400;

        .table-con {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .table-left {
                .table-head {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    color: #999999;
                    background: #f8faff;

                    view {
                        flex-shrink: 0;
                        /*padding: 0 12px;*/
                        text-align: center;
                        color: #999999;
                        background: #f8faff;
                    }
                }

                .table-desc-line {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    flex-direction: column;
                    box-shadow: 2px 2px 0px 0px #f0f2f8;

                    .desc-item-con {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        view {
                            flex-shrink: 0;
                            /*padding: 0 12px;*/
                        }

                        .desc-sub-item-con {
                            box-shadow: -2px 0px 0px 0px #f0f2f8;

                            .border-con {
                            }

                            view {
                                /*padding: 0;*/
                            }
                        }
                    }
                }
            }

            .table-right {
                flex: 1;
                overflow: scroll;

                .table-head {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    color: #999999;
                    height: 80px;
                    line-height: 80px;
                    background: #f8faff;

                    view {
                        color: #999999;
                        background: #f8faff;
                    }
                }

                .table-line {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    height: 80px;
                    line-height: 80px;

                    &:first-child {
                        view {
                            box-shadow: 0px 4.5px 0px -1px #f0f2f8;
                        }
                    }

                    &:last-child {
                        view {
                            box-shadow: 0px 4.5px 0px -1px #f0f2f8;
                        }
                    }
                }
            }
        }
    }
</style>
