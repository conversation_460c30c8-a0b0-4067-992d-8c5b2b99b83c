<!--
@createdBy 黄鹏
@date 2024/04/25
@description: --- 核心消费者开瓶总览（按K序列）
-->
<template>
    <view class="core-consumer-scan-overview-k table">
        <view class="table-con">
            <view class="table-left">
                <view class="table-head">
                    <view class="word4 table-cell" v-if="dataRange === 'Team'">业务代表</view>
                    <view class="word4 table-cell" v-else>区域</view>
                    <view class="word8 table-cell">影响力K序列等级</view>
                </view>
                <view v-if="tableList.length > 0">
                    <view class="table-desc-line" v-for="(item, index) in tableList" :key="index">
                        <view v-if="item.isTotal" class="desc-item-con table-cell">总计</view>
                        <view v-else class="desc-item-con">
                            <view class="word4 table-cell">{{dataRange === 'Team' ? item.staffName : item.orgName}}
                            </view>
                            <view class="desc-sub-item-con">
                                <view class="word8 table-cell" :class="row.type === '合计' ? 'border-bottom' : ''"
                                      v-for="(row, ins) in item.childs" :key="ins">
                                    {{row.type === '合计' ?
                                    '合计' : row.kTypeName}}
                                </view>
                                <view class="word4 table-cell load-more" v-if="item.showMore"></view>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="table-right">
                <view class="table-head">
                    <view class="word10 table-cell">动销订单录入数（瓶）</view>
                    <view class="word10 table-cell">动销订单匹配数（单）</view>
                    <view class="word13 table-cell">核心消费者扫码数量（瓶）<link-icon icon="mp-info-lite" @tap="$emit('show-desc', 'openBottleByCoreQty')" status="info"/></view>
                    <view class="word13 table-cell">核心消费者扫码人数（人）<link-icon icon="mp-info-lite" @tap="$emit('show-desc', 'openBottleCoreCsmQty')" status="info"/></view>
                </view>
                <view v-if="tableList.length > 0">
                    <view v-for="(item, index) in tableList" :key="index">
                        <view class="table-line" v-for="(row, ins) in item.childs" :key="ins">
                            <view class="word10 table-cell">{{row.bookedorderBottleQty}}</view>
                            <view class="word10 table-cell"
                                  @tap="gotoDetailPage(item.isTotal, row.bookedorderMatchQty, item, row, false)"
                                  :class="(item.isTotal || row.type === '合计' || row.bookedorderMatchQty === 0) ? '' : 'item-info'">
                                {{row.bookedorderMatchQty}}
                            </view>
                            <view class="word13 table-cell">{{row.openBottleByCoreQty}}</view>
                            <view class="word13 table-cell"
                                  @tap="gotoDetailPage(item.isTotal, row.openBottleCoreCsmQty, item, row, true)"
                                  :class="(item.isTotal || row.type === '合计' || row.openBottleCoreCsmQty === 0) ? '' : 'item-info'">
                                {{row.openBottleCoreCsmQty}}
                            </view>
                        </view>
                        <view class="table-line word4 load-more" v-if="!item.isTotal && item.showMore">
                            <view class="word10 table-cell" @tap="gotoListItemPage(item)">查看更多</view>
                            <view class="word10 table-cell"></view>
                            <view class="word13 table-cell"></view>
                            <view class="word13 table-cell"></view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
        <view v-if="tableList.length">
            <view v-if="total >= tableList.length" class="load-more border-top" @tap="gotoListPage">查看更多</view>
            <view v-else class="no-more border-top"></view>
        </view>
        <view v-else class="no-data">暂无数据</view>
    </view>
</template>

<script>

    export default {
        name: "prize-issued-overview",
        props: {
            isCheckDetail: {
                type: Boolean,
                default: false
            },
            userInfo: {
                type: Object,
                default: {}
            },
            isListPage: {
                type: Boolean,
                default: false
            },
            listPageParam: {
                type: Object,
                default: () => {}
            }
        },
        data() {
            return {
                tableList: [],
                totalRow: {},
                total: 0,
                childRows: 3,
                page: 1,
                dateType: '',
                orgId: '',
                dataRange: ''
            }
        },
        created() {
            if (this.isListPage) {
                this.queryData(this.listPageParam.dataRange, this.listPageParam.orgId, this.listPageParam.dateType);
            }
        },
        methods: {
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: queryData
             * @para:
             * @description: 查询表格数据
             **/
            async queryData(dataRange, orgId, dateType, nextPage) {
                if (nextPage) this.page += 1;
                this.dateType = dateType;
                this.orgId = orgId;
                this.dataRange = dataRange;
                this.$utils.showLoading();
                const param = {
                    dmpSrUrl: '/link/consumerDeckleReportSR/queryKSequencePage',
                    dataAccess: dataRange,
                    postnId: this.userInfo.postnId,
                    // orgId: orgId,
                    dateType: dateType || 'thisFiscalYear',
                    page: this.page,
                    rows: this.isListPage ? 10 : 1,
                    childRows: this.childRows
                };
                if (this.dataRange === 'Area') {
                    param.orgId = orgId;
                }
                const data = await this.$http.post(this.$env.appURL + '/action/link/sendDmpSr/send', param);
                if (data.success) {
                    if (nextPage) {
                        this.tableList = this.tableList.concat(data.rows);
                    } else {
                        this.tableList = data.rows || [];
                    }
                    if (this.tableList.length && data.totalResult && this.dataRange !== 'Mine' && !nextPage) {
                        this.totalRow = data.totalResult;
                        const obj = {
                            isTotal: true,
                            childs: [this.totalRow]
                        };
                        this.tableList.unshift(obj)
                    }
                    this.total = data.total;
                    this.$utils.hideLoading();
                } else {
                    this.$utils.hideLoading();
                    this.$message.error({message: '查询核心消费者开瓶总览（按K序列）数据失败！' + data.result, customFlag: true});
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: gotoListItemPage
             * @para:
             * @description: 跳转最小维度页面
             **/
            gotoListItemPage (item) {
                this.$nav.push('/pages/echart/lzlj/consumer-scan-board/table-list-item-page.vue', {
                    title: '核心消费者开瓶总览（按K序列）',
                    dataRange: this.dataRange,
                    dateType: this.dateType,
                    orgId: item.orgId,
                    staffId: item.staffId,
                    supplyDisId: item.supplyDisId,
                    orgName: item.orgName,
                    staffName: item.staffName,
                    supplyDisName: item.supplyDisName
                })
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/04/26
             * @methods: gotoListPage
             * @para:
             * @description: 跳转最大维度页面
             **/
            gotoListPage () {
                if (this.isListPage) {
                    this.queryData(this.listPageParam.dataRange, this.listPageParam.orgId, this.listPageParam.dateType, true);
                } else {
                    this.$nav.push('/pages/echart/lzlj/consumer-scan-board/table-list-page.vue', {
                        title: '核心消费者开瓶总览（按K序列）',
                        dataRange: this.dataRange,
                        dateType: this.dateType,
                        orgId: this.orgId
                    })
                }
            },
            /**
             * @createdBy 黄鹏
             * @date 2024/05/07
             * @methods: gotoDetailPage
             * @para:
             * @description: 指标下钻
             **/
            gotoDetailPage (isTotal, value, item, row, byConsumer) {
                if (isTotal || !value) return;
                this.$nav.push('/pages/echart/lzlj/consumer-scan-board/target-detail-page.vue', {
                    byConsumer: byConsumer,  // false:动销订单匹配数（单）   true: 核心消费者扫码人数（人）
                    isK: true,               // false:按产品统计  true:按K序列统计
                    dataRange: this.dataRange,
                    dateType: this.dateType,
                    staffId: item.staffId,
                    orgId: item.orgId,
                    kType: row.kTypeName,
                    matLClassName: row.matLClassName
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "../css/board.scss";

    .core-consumer-scan-overview-k {
        padding: 0 24px;
        margin-bottom: 36px;
        background: white;
        border-radius: 28px;
        font-size: 28px;
        font-weight: 400;

        .table-con {
            display: flex;
            align-items: center;
            justify-content: space-between;

            .table-left {
                .table-head {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    color: #999999;
                    background: #f8faff;

                    view {
                        flex-shrink: 0;
                        /*padding: 0 12px;*/
                        text-align: center;
                        color: #999999;
                        background: #f8faff;
                    }
                }

                .table-desc-line {
                    width: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    flex-direction: column;
                    box-shadow: 2px 2px 0px 0px #f0f2f8;

                    .desc-item-con {
                        width: 100%;
                        display: flex;
                        align-items: center;
                        justify-content: center;

                        view {
                            flex-shrink: 0;
                            /*padding: 0 12px;*/
                        }

                        .desc-sub-item-con {
                            box-shadow: -2px 0px 0px 0px #f0f2f8;

                            .border-con {
                            }

                            view {
                                /*padding: 0;*/
                            }
                        }
                    }
                }
            }

            .table-right {
                flex: 1;
                overflow: scroll;

                .table-head {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    color: #999999;
                    height: 80px;
                    line-height: 80px;
                    background: #f8faff;

                    view {
                        color: #999999;
                        background: #f8faff;
                    }
                }

                .table-line {
                    display: flex;
                    align-items: center;
                    justify-content: flex-start;
                    height: 80px;
                    line-height: 80px;

                    &:first-child {
                        view {
                            box-shadow: 0px 4.5px 0px 0px #f0f2f8;
                        }
                    }

                    &:last-child {
                        view {
                            box-shadow: 0px 4.5px 0px -1px #f0f2f8;
                        }
                    }
                }
            }
        }
    }
</style>
