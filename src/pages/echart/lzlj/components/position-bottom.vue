<template>
  <view class="position-bottom">
      <link-dialog ref="positionBottom" position="bottom" height="85vh" class="dialog-bottom" noPadding v-model="show" @hide="dialogHide">
          <view class="model-title">
              <view class="iconfont icon-left"  v-show="!(autoList.list.length>0 && autoList.list[0].id === userInfo.orgId) && searchFlag" @tap="goBackOrg"></view>
              <view class="iconfont icon-close" @tap="dialogHide"></view>
              <view class="title">组织</view>

          </view>
          <view class="dialog-content" style="height: calc(100% - 44px)">
              <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}" @scrolltolower="autoList.methods.loadMore">
                  <link-auto-list :option="autoList" hideCreateButton :scrollContent="true">
                      <template slot-scope="{data,index}">
                          <view slot="note">
                              <item :key="index" :data=data @tap="gotoItemOrg(data)" style="padding-top: 0;padding-bottom:0">
                                  <link-radio-group v-model="tempOrgId">
                                      <item :arrow="false">
                                          <link-checkbox :val=data.id slot="thumb" toggleOnClickItem @tap="tempOrgInfo(data)"/>
                                      </item>
                                  </link-radio-group>
                                  <view class="list-item">
                                      {{data.text}}
                                  </view>
                              </item>
                          </view>
                      </template>
                  </link-auto-list>
              </scroll-view>
              <view class="link-dialog-foot-custom">
                  <link-button shadow @tap="chooseOrg" label="确定" style="width:100vw"/>
              </view>
          </view>
      </link-dialog>
  </view>
</template>

<script>
  export default {
    name: "position-bottom",
    props: {
      userInfo: {},
      show: false,
      showCompanyArr: '',
      from:''
    },
    data() {
        const accessGroupOauth = this.$utils.getMenuAccessGroup('','');
        let autoList = new this.AutoList(this, {
          module: 'action/link/orgnization',
            url: {
                queryByExamplePage: 'export/link/orgnization/queryByExamplePage',
            },
            searchFields: ['text'],
          loadOnStart: false,
          param: {
            oauth: 'MY_ORG',
            filtersRaw: []
          },
          hooks: {
            beforeLoad(option) {
              let listIndex = option.param.filtersRaw.map(item => item.property);
              if (listIndex.includes("[text]")) {
                this.searchFlag = false;
                // var index = listIndex.findIndex((val) => {
                //   return val == 'parentOrgId'
                // });
                // option.param.filtersRaw.splice(index, 1);
                  option.param.filtersRaw = [
                      {"id": "isEffective", "property": "isEffective", "value": "Y"},
                      {id: 'text', property: '[text]', value: this.autoList.option.searchText, operator: 'or like'}
                  ]
                option.param.oauth = 'MY_ORG'
              }else{
                this.searchFlag = true;
              }
            }
          },
          sortOptions: null
        });
        return {
            searchFlag: true,
            tempOrgId: null, //选中的组织
            accessGroupOauth,
            autoList,
            selectData: {},
            orgIdArr: [], //可返回的组织数组
        }
    },
    watch: {
      async show(val){
        this.autoList.option.searchText = "";
        if(val){
          let filtersRaw = [{"id": "isEffective", "property": "isEffective", "value": "Y"}];
          if(this.orgIdArr.length === 0){
              filtersRaw.push({'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='});
              filtersRaw.push({'id': 'orgType', 'property': 'orgType', 'value': this.userInfo.orgType});
            // filtersRaw.push({'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.userInfo.orgId, 'operator': '='});
            this.orgIdArr.push({orgId: this.userInfo.orgId, orgName: this.userInfo.orgName, orgType: this.userInfo.orgType});
          }else{
            var index = this.orgIdArr.length - 1;
            filtersRaw.push({'id': 'parentOrgId', 'property': 'parentOrgId', 'value': this.orgIdArr[index].orgId, 'operator': '='});
          }
          if (this.orgIdArr[this.orgIdArr.length - 1].orgType === 'Company' && !!this.showCompanyArr) {
            filtersRaw.push({id: 'id', property: 'id', value: this.showCompanyArr, operator: 'in'})
          }
          await this.search(filtersRaw);
        }else {
          this.autoList.list = [];
        }
      }
    },
    methods: {
        async search(filtersRaw){
          this.autoList.option.param.filtersRaw = filtersRaw;
          if(!this.$utils.isEmpty(this.accessGroupOauth)){
            //2021-07-29配了访问组安全性-查询组织的安全性为访问组安全性
            this.orgOption.option.param.oauth = this.accessGroupOauth;
          }
          await this.autoList.methods.reload();
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods goBackOrg
         * @para
         * @description 返回上一级组织列表
         */
        goBackOrg(){
          let filtersRaw = [{"id": "isEffective", "property": "isEffective", "value": "Y"}];
          if(this.$utils.isEmpty(this.accessGroupOauth)){
            if(this.orgIdArr.length === 0) return;
            if(this.orgIdArr.length === 1){
              filtersRaw.push({'id': 'orgId', 'property': 'orgId', 'value': this.userInfo.orgId, 'operator': '='});
              filtersRaw.push({'id': 'orgType', 'property': 'orgType', 'value': this.userInfo.orgType});
            }else {
              filtersRaw.push({
                'id': 'parentOrgId',
                'property': 'parentOrgId',
                'value': this.orgIdArr[this.orgIdArr.length - 2].orgId,
                'operator': '='
              })
              if (this.orgIdArr[this.orgIdArr.length - 2].orgType === 'Company') {
                if(!!this.showCompanyArr) {
                  filtersRaw.push({id: 'id', property: 'id', value: this.showCompanyArr, operator: 'in'})
                }
                filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
              }
            }
            this.orgIdArr.pop();
          }
          this.search(filtersRaw);
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods gotoItemOrg
         * @para
         * @description 跳转到子组织
         */
      async gotoItemOrg(data){
            if(!this.searchFlag)return;
            let filtersRaw = [
                {id: 'parentOrgId', property: 'parentOrgId', value: data.id, operator: '='},
                {"id": "isEffective", "property": "isEffective", "value": "Y"},
            ]
            if(data.orgType === 'Company') {
                filtersRaw.push({"id": "orgType", "property": "orgType", "value": 'BranchCompany'})
                if(!!this.showCompanyArr) {
                    filtersRaw.push({id: 'id', property: 'id', value: this.showCompanyArr, operator: 'in'})
                }
            }
            this.autoList.option.param.filtersRaw = filtersRaw;
            if(!this.$utils.isEmpty(this.autoList.option.param.oauth)){
                delete this.autoList.option.param.oauth;
            }
            let  orgParam = this.$utils.deepcopy(this.autoList.option.param);
            orgParam.onlyCountFlag = true;
            let orgData = await this.$http.post('export/link/orgnization/queryByExamplePage', orgParam);
            if (orgData.rows[0].total === 0){
                this.$message.error(`当前组织下没有下级组织`);
                return
            }
            this.autoList.methods.reload()
            this.orgIdArr.push({orgId: data.id,orgName : data.text, orgType: data.orgType})
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/11/9
         * @methods tempOrgInfo
         * @para
         * @description 存储选中的行信息
         */
        tempOrgInfo(data){
            this.selectData = data;
        },
        /**
         * @createdBy  张丽娟
         * @date  2020/10/28
         * @methods chooseOrg
         * @para
         * @description 片区弹框、品牌公司弹框确认按钮 (点确定之后的组织，再次打开弹框，会打开所在层级，只勾选没有点确认，则不会)
         */
        chooseOrg(){
            if( this.selectData.id == '58929085107142656' && this.from === 'terminal'){
                this.$showError('暂不支持查看此组织终端看板，请切换至其他组织')
                return
            }
            this.$emit('choose', this.selectData);
            this.$emit('update:show', false);
        },
        dialogHide(){
            this.$emit('update:show', false);
        }
    }

  }
</script>

<style lang="scss">
.position-bottom{
    .link-dialog-foot-custom{
        width: auto !important;
    }
  .link-dialog-body{
    position: relative;
  }
  .link-auto-list .link-auto-list-top-bar{
    border:none;
  }
  .link-item .link-item-body-right{
    margin: 0 24px;
  }
  .link-radio-group{
    width: 70px;
    .link-item{
      padding:24px 24px 24px 0;
      .link-item-thumb{
        padding-right: 0;
      }
      .link-item-icon{
        display:none;
      }
    }
    .link-item-active{
      background-color: #f6f6f6;
    }
  }
  .list-item{
    flex: 1;
  }
  .link-radio-group .link-item:active,.link-item-active{
    background-color: #f6f6f6;
  }
  .link-auto-list-no-more{
    display: none;
  }
  .dialog-bottom{
    .dialog-content{
      padding: 0 20px;
      position: relative;
      //.link-button{
      //  position: absolute;
      //  bottom: 0
      //}
    }
    .model-title {
      .title {
        font-family: PingFangSC-Regular,serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 96px;
        height: 96px;
        width: 90%;
        margin-right: 80px;
        margin-left: 10vw;
        padding-left:0;
      }
      .icon-left{
        color: #BFBFBF;
        font-size: 48px;
        line-height: 96px;
        height: 96px;
        float: left;
        width: 80px;
      }
      .icon-close {
        color: #BFBFBF;
        font-size: 48px;
        line-height: 96px;
        height: 96px;
        float:right;
        margin-right: 30px;
      }
    }
  }
}
</style>
