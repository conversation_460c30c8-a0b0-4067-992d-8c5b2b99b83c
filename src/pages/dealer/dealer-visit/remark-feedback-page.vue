<template>
    <link-page class="remark-feedback-page">
        <link-form ref="form" :value="options" hideSaveButton>
            <link-form-item label="拜访备注" vertical>
                <textarea maxlength="-1" :placeholder="msgHash[field]" v-model="options[field]" :disabled="!isEditFlag"></textarea>
            </link-form-item>
            <link-form-item label="上传附件" vertical>
                <lnk-img-watermark :parentId="pageParam.visitId"
                                   ref="visitAttach"
                                   moduleType="visitAttach"
                                   :album="true"
                                   :maxCount="9"
                                   isCount
                                   :addLength="9"
                                   :delFlag="isEditFlag"
                                   :newFlag="isEditFlag"
                                   moduleName="上传附件"></lnk-img-watermark>
            </link-form-item>
        </link-form>
        <link-sticky class="bottom-btn" v-if="isEditFlag">
            <link-button class="sure-btn" size="normal" @tap="submit">提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark.vue';

definePageConfig({
    navigationBarTitleText: '反馈与备注'
});

export default {
    name: "remark-feedback-page",
    components: {LnkImgWatermark},
    data () {
        return {
            options: {},
            isEditFlag: this.pageParam.isEditFlag,
            msgHash: {
                comments: '请输入拜访备注，或店老板的反馈等信息',
                communicationRecord: '请输入沟通内容',
                solution: '请输入解决措施',
                unfinished: '请输入未完成事项',
            },
            titleHash: {
                comments: '反馈与备注',
                communicationRecord: '沟通内容',
                solution: '解决措施',
                unfinished: '未完成事项',
            }
        }
    },
    created () {
        this.queryData()
        this.$taro.setNavigationBarTitle({title: this.titleHash[this.field]});
    },
    computed: {
        msg() {
            return this.msgHash[this.field]
        },
        field() {
            return this.pageParam.field || 'comments'
        }
    },
    methods: {
        /**
             * 请求默认数据
             * <AUTHOR>
             * @date 2020-09-08
             * @param param
        */
        queryData () {
            this.$http.post('action/link/accntVisit/queryById', {
                id: this.pageParam.visitId
            }).then(data => {
                if (data.success) {
                    this.options = data.result
                }
            })
        },
        /**
             * 提交
             * <AUTHOR>
             * @date 2020-09-08
        */
        submit () {
            const that = this
            if (this.$utils.isEmpty(this.options[this.field])) {
                this.$message['warn'](this.msgHash[this.field]);
                return
            }
            this.$http.post('action/link/accntVisit/update', this.options).then(data => {
                if (data.success) {
                    let param = {refreshFlag: true, visitRecordData: data.newRow};
                    setTimeout(function () {that.$nav.back(param);}, 2000);
                }
            })
        }
    }
}
</script>

<style lang="scss">
    .remark-feedback-page {
        background: #ffffff;
        textarea {
            width: auto;
            color: #000;
            padding: 12px 0 0 12px;
            margin: 0 24px;
            border: 1px solid #e4e4e4;
            font-size: 28px;
            border-radius: 12px;
        }
        .bottom-btn {
            padding-top: 16px;
            padding-bottom: 34px;
            .sure-btn {
                box-shadow: 0 8px 24px 0 rgba(47,105,248,0.50);
                width: 340px;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
            }
        }
    }
</style>
