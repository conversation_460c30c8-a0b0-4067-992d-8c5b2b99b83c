<template>
  <link-page class="competition-prod-edit-page" ref="competitionProdEditPage">
      <link-form :value="formData" ref="form" :rules="formRules" hideEditButton hideSaveButton>
        <view class="competition-select" @tap="openPicker('competitorBrandOption')">
          <view class="competition-select-content">
            <view class="brand"><text class="asterisk">*</text>品牌</view>
            <view class="brand-value" v-if="formData.competitorBrand">{{formData.competitorBrand}}<text class="link-component-icon mp-arrow-right"></text></view>
            <view class="brand-placeholder" v-else>请选择品牌<text class="link-component-icon mp-arrow-right"></text></view>
          </view>
        </view>
        <view class="competition-select" @tap="openPicker('competitorSeriesOption')">
          <view class="competition-select-content">
            <view class="brand">系列</view>
            <view class="brand-value" v-if="formData.competitorSeries">{{formData.competitorSeries}}<text class="link-component-icon mp-arrow-right"></text></view>
            <view class="brand-placeholder" v-else>请选择系列<text class="link-component-icon mp-arrow-right"></text></view>
          </view>
        </view>
        <list-title></list-title>
        <link-form-item label="当前库存">
          <link-number v-model="formData.inventoryNum"/>
        </link-form-item>
        <link-form-item label="本月动销件数">
          <link-number v-model="formData.startSellNum"/>
        </link-form-item>
        <link-form-item label="主流成交价">
          <link-number-keyboard placeholder="请输入主流成交价" v-model="formData.mainTransPrice"/>
        </link-form-item>
        <link-form-item label="采购价">
          <link-number-keyboard placeholder="请输入采购价" v-model="formData.purchasePrice"/>
        </link-form-item>
        <link-form-item label="陈列方式" required>
          <link-lov v-model="formData.displayWay" type="DISPLAY_WAY"></link-lov>
        </link-form-item>
        <link-form-item label="陈列面" required>
          <link-number-keyboard placeholder="请输入陈列面" v-model="formData.displayArea"/>
        </link-form-item>
        <link-form-item label="是否堆头" >
          <link-switch v-model="formData.duitouFlag"></link-switch>
        </link-form-item>
        <link-form-item label="促销物料">
          <link-lov :style="isChangeShow?'position:absolute;left:0;top:0;opacity:0;z-index:99;height: 100%;':'opacity:1'" type="PROMOTION_MATERIAL" @change="changeVal" v-model="formData.promotionMaterial" multiple/>
          <view style="width: 100%;z-index:0;text-align: right;color:#3b4144;" v-if="isChangeShow">
            <text v-for="(cItem, cIndex) in changeArr" :key="cIndex">{{cItem|lov('PROMOTION_MATERIAL')}}<text v-if="(cIndex + 1) < changeArr.length">,</text></text></view>
        </link-form-item>
        <link-form-item label="备注" vertical>
          <link-textarea v-model="formData.comments"/>
        </link-form-item>
        <view class="image-container">
          <view class="image-title">图片</view>
<!--          <custom-camera :moduleId="formData.id"-->
<!--                         moduleType="competition"-->
<!--                         :delFlag="delFlag"-->
<!--                         :newFlag="newFlag"-->
<!--                         :rootId="rootId"-->
<!--                         :useAlbumFlag="false"-->
<!--                         @imageArrLength="imageArrLength"-->
<!--                         :useModuleName="useModuleName" :moduleName="moduleName" ref="customCamera"></custom-camera>-->
          <lnk-img-watermark :parentId="formData.id"
                             moduleType="competition"
                             :delFlag="delFlag"
                             :album="false"
                             :rootId="rootId"
                             :pathKeyArray="formData.attachmentList"
                             :useModuleName="useModuleName"
                             :moduleName="moduleName"
                             @imgUploadSuccess="imageArrLength"
                             :newFlag="newFlag"></lnk-img-watermark>
        </view>
      </link-form>
    <link-sticky>
      <link-button block @tap="confirm">确认</link-button>
    </link-sticky>
  </link-page>
</template>

<script>
import customCamera from "../../../../components/custom-camera/custom-camera"
import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';

definePageConfig({
    navigationBarTitleText: '添加竞品'
});

export default {
  name: "competition-prod-edit",
  components: {customCamera,LnkImgWatermark},
  data () {
    // 品牌
    let companyCode = this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode
    const brandOption = new this.AutoList(this, {
      module: 'action/link/competitorAbout',
      searchFields: null,
      sortField: 'seq',
      sortDesc: false,
      param: {
        filtersRaw: [
                {"id": "type", "property": "type", "value": "BRAND"},
                {"id": "companyCode", "property": "companyCode", "value": companyCode},
                ],
        rows: 5,
        oauth: 'ALL',
        order: 'desc',
        sort: 'seq'
      },
      renderFunc: (h, {data, index}) => {
        return (
                <item key={index} data={data} class="select-box" arrow="false">
                  <link-checkbox val={data.id} toggleOnClickItem></link-checkbox>
                  <view class="select-left">
                    <view class="store-name">{data.name}</view>
                  </view>
                </item>
        )
      }
    });
    // 系列
    const seriesOption = new this.AutoList(this, {
      module: 'action/link/competitorAbout',
      searchFields: null,
      sortField: 'seq',
      sortDesc: false,
      param: {
        filtersRaw: [{"id": "type", "property": "type", "value": "SERIES"}],
        oauth: 'ALL',
        order: 'desc',
        sort: 'seq'
      },
      renderFunc: (h, {data, index}) => {
        return (
                <item key={index} data={data} class="select-box" arrow="false">
                  <link-checkbox val={data.id} toggleOnClickItem></link-checkbox>
                  <view class="select-left">
                    <view class="store-name">{data.name}</view>
                  </view>
                </item>
        )
      }
    });
    return {
      brandOption,
      seriesOption,
      isChangeShow: false,
      formData: {
        competitorBrand: null,
        competitorBrandId: null,
        competitorSeries: null,
        competitorSeriesId: null,
        inventoryNum: 1,
        startSellNum: 1,
        id: this.pageParam.id,
        duitouFlag: 'N',
        visitStatus: 'Y'
      },
      moduleName: '竞品采集',                                                                              // 模块名称
      delFlag: true,                                                                                      // 添加标志
      newFlag: true,                                                                                      // 删除标志
      rootId: this.pageParam.visitId,                                                                     // 来源id即拜访id
      useModuleName: this.pageParam.terminalData.acctName,                                                // 使用模块名称
      editFlag: false,                                                                                    // 情景只读标志
      formRules: {},
      competitionImgList: [],
      changeArr: [],
      selectBrand: [],                                                                                    // 品牌选中对象
      selectSeries: [],                                                                                   // 系列选中对象
      clickBrandFlag: false,
      openPicker: null                                                                                    //控制连点出现多个弹框
    }
  },
  created () {
    // 竞品编辑数据
    if (this.pageParam.editFlag) {
      this.isChangeShow = true;
      this.changeArr = this.pageParam.editData.promotionMaterial;
    }
    if (!this.$utils.isEmpty(this.pageParam.editData)) {
      this.formData = this.pageParam.editData;
    }
    this.openPicker = this.throttle(1000)
  },
  watch: {
      'formData.competitorBrand' (val) {
          if (val) {
              let param = this.seriesOption.option.param.filtersRaw;
              if (param.length > 1) {
                  param.forEach((item) => {
                      if (item.property === 'parentId') {
                          item.value = this.formData.competitorBrandId
                      }
                  })
              } else {
                  param.push({"id": "parentId", "property": "parentId", "value": this.formData.competitorBrandId})
              }
          }
      }
  },
  methods: {
    /**
     * 校验水印相机是否上传完毕
     * <AUTHOR>
     * @date 2022-7-6
     */
    checkWaterImg() {
      let check = this.$refs['competitionProdEditPage'].utils.checkImages();
      if(!check) this.$showError('图片上传中!!')
      return check
    },
    /**
     * lzlj-002-2842弹框同时出现
     * <AUTHOR>
     * @date 2022-4-7
     */
    throttle(wait) {
      let pre = 0;
      let next = 0
      return function (func) {
        next = +new Date()
        if((next - pre) > wait) {
          pre = next;
          this[func]()
        }
      }
    },
    /**
      * 系列
      * <AUTHOR>
      * @date 2020-11-25
    */
    async competitorSeriesOption() {
      const that = this;
      that.selectSeries = await that.$object(that.seriesOption, {
        showInDialog: true,
        pageTitle: '系列',
      });
      that.formData.competitorSeries = that.selectSeries.name;
      that.formData.competitorSeriesId = that.selectSeries.id;
    },
    /**
      * 品牌
      * <AUTHOR>
      * @date 2020-11-25
    */
    async competitorBrandOption() {
      const that = this;
      that.selectBrand = await that.$object(that.brandOption, {
        showInDialog: true,
        pageTitle: '品牌',
      });
      that.formData.competitorBrand = that.selectBrand.name;
      that.formData.competitorBrandId = that.selectBrand.id;
      if (!that.$utils.isEmpty(that.selectBrand)) {
        that.formData.competitorSeries = '';
      }
    },
      /**
       * 促销物料栏改变
       * <AUTHOR>
       * @date 2020-11-19
       * @param val
       */
      changeVal (val) {
          if (val) {
              this.isChangeShow = true
              this.changeArr = val
          }
      },
    /**
     * 监控页面返回参数
     * <AUTHOR>
     * @date 2020-08-31
     * @param param
     */
    onBack (param) {
      // this.$refs.customCamera.getImgKeyList();
    },
    /**
     * 获取竞品采集图片
     * <AUTHOR>
     * @date 2020-08-31
     * @param param 打卡图片长度
     */
    imageArrLength (param) {
      this.competitionImgList = param;
    },
    /**
      * 检查数据
      * <AUTHOR>
      * @date 2020-09-29
      * @param param
    */
    checkedData () {
      const that = this;
        if (that.$utils.isEmpty(that.formData.competitorBrand)) {
            that.$message['warn']('请选择品牌');
            return false
        }
      if (that.$utils.isEmpty(that.formData.displayWay)) {
        that.$message['warn']('请选择陈列方式');
        return false
      }
      if (that.$utils.isEmpty(that.formData.displayArea)) {
        that.$message['warn']('请输入陈列面');
        return false
      }
      return true
    },
    /**
      * 插入数据
      * <AUTHOR>
      * @date 2020-09-29
      * @param param
    */
    insertData () {
      const that = this;
      return  new Promise (resolve => {
        if(that.$utils.isEmpty(that.formData.row_status)) {
          that.formData.row_status = that.pageParam.rowStatus
        }
        if(that.formData.row_status === 'NEW') {
          that.formData.accntId = that.pageParam.terminalData.id;
          that.formData.visitId = that.pageParam.visitId;
          that.formData.createdBy = that.pageParam.terminalData.createdBy;
          that.formData.postnId = that.$taro.getStorageSync('token').result.postnId;
          that.formData.orgId = that.$taro.getStorageSync('token').result.orgId;
          that.formData.existFlag = 'Y';
        }
        // if (that.formData.row_status === 'UPDATE') {
        //   delete that.formData.attachmentList
        // }
        let insertArr = [];
        insertArr.push(that.formData);
        let storeArr = that.$store.getters['prodCollect/getProdCollect'];
        // store数据为空时——直接插入
        if (that.$utils.isEmpty(storeArr)) {
          resolve(insertArr);
        }
        let initIndex = storeArr.map(item => item.id);
        let insertFlag = initIndex.includes(that.formData.id);
        // store数据不为空时——包含当前数据时——修改数据值
        if (!that.$utils.isEmpty(storeArr) && insertFlag) {
          storeArr.forEach(item => {
            if (item.id === that.formData.id) {
              item.competitorBrand = that.formData.competitorBrand;
              item.competitorBrandId = that.formData.competitorBrandId;
              item.competitorSeries = that.formData.competitorSeries;
              item.competitorSeriesId = that.formData.competitorSeriesId;
              item.inventoryNum = that.formData.inventoryNum;
              item.mainTransPrice = that.formData.mainTransPrice;
              item.purchasePrice = that.formData.purchasePrice;
              item.displayWay = that.formData.displayWay;
              item.displayArea = that.formData.displayArea;
              item.duitouFlag = that.formData.duitouFlag;
              item.promotionMaterial = that.formData.promotionMaterial;
              item.attachmentList = that.competitionImgList;
            }
          });
          resolve(storeArr);
        }
        // store数据不为空时——不包含当前数据时——插入
        if (!that.$utils.isEmpty(storeArr) && !insertFlag) {
          let jointArr = storeArr.concat(insertArr);
          resolve(jointArr);
        }
      })
    },
    /**
     *  @description: 确认，暂时缓存到前端
     *  @author: 马晓娟
     *  @date: 2020/8/14 10:14
     */
    async confirm() {
      if(!this.checkWaterImg()) return
      const that = this;
      if (that.checkedData()) {
        let insertArr = await that.insertData();
        //需要对添加的竞品做去重处理，因为最后提交时，数据源是store里的数据
        let map = new Map();
        for (let item of insertArr) {
          if(!(item.competitorSeries)) item.competitorSeries = '';
          map.set(item.competitorBrand + item.competitorSeries, item);
        }
        insertArr =  [...map.values()];
        that.$store.commit('prodCollect/setProdCollect', insertArr);
        let param = {addCompetitionFlag: true};
        that.$nav.back(param);
      }
    }
  }
}
</script>

<style lang="scss">
  .competition-prod-edit-page{
    /*deep*/.link-icon {
              font-size: 28px;
            }
    /*deep*/.list-title {
              padding: 12px;
            }
    /*deep*/.link-input-text-align-left {
              text-align: right;
            }
    /*deep*/.link-item {
              padding: 24px;
            }
    /*deep*/.link-form {
              margin-bottom: 24px;
            }
    .competition-select {
      padding: 24px 24px 0 24px;
      background-color: #ffffff;
      font-size: 28px;
      .competition-select-content{
        @include flex-start-center;
        @include space-between;
        padding-bottom: 24px;
        border-bottom: 1px solid #F7F8F9;
        .brand {
          .asterisk {
            color: #FF5A5A;
            margin-left: -14px;
            padding-right: 2px;
          }
        }
        .brand-value {
          color: #3b4144;
          margin-right: -4px;
        }
        .brand-placeholder {
          color: #E0E0E0;
          margin-right: -4px;
        }
        .mp-arrow-right {
          font-size: 30px;
          color: #c6c6c6;
        }
      }
    }
    .image-container{
      background: #ffffff;
      padding-bottom: 24px;
      .image-title {
        padding-left: 24px;
        padding-top: 24px;
        font-size: 28px;
        color: #595959;
      }
    }
    .select-box {
      @include flex-start-center;
      border-bottom: 1px solid #F2F2F2;
      padding: 24px!important;
      /*deep*/.link-icon {
                font-size: 40px;
      }
      .select-left {
        width: 100%;
        padding-left: 24px;
        .store-name {
          width: 100%;
          font-family: PingFangSC-Regular,serif;
          font-size: 32px;
          color: #262626;
          letter-spacing: 0;
        }
      }
    }
  }
</style>
