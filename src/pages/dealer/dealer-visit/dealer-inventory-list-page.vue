<template>
    <link-page class="dealer-inventory-list-page">
        <lnk-taps :taps="inventoryStatusOptions" v-model="inventoryStatusActive" @switchTab="switchTab"></lnk-taps>
        <view class="blank"></view>
        <link-auto-list :option="inventoryOption" :searchInputBinding="{props:{placeholder:'产品编码/产品名称/创建人'}}">
            <template slot="other" v-if="inventoryStatusActive.val === 'newest'">
                <link-fab-button  @tap="creatInventory"/>
            </template>
            <view slot="searchRight" class="filter" v-if="inventoryStatusActive.val === 'history'">
                <link-filter v-model="filterOption"/>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="inventory-list-rows">
                    <view slot="note">
                        <view class="media-list">
                            <view class="top-rows">
                                <view class="num-view">
                                    <view class="num">{{data.prodCode}}</view>
                                </view>
                                <view class="store-level">采集时间 <text class="date-name">{{data.created | date('YYYY-MM-DD')}} {{data.createdName}}</text></view>
                            </view>
                        </view>
                        <view class="media-list">
                            <view class="store-text">
                                <view class="content-middle">
                                    <view class="name">{{data.prodName}}</view>
                                </view>
                                <view class="store-input">
                                    <view class="store-input-rows">
                                        <view class="value">{{data.qty1 || 0}}</view>
                                        <view class="label">{{data.saleUnit | lov('PROD_UNIT') || '件'}}</view>
                                        <view class="value">{{data.qty2 || 0}}</view>
                                        <view class="label">{{data.retailUnit | lov('PROD_UNIT') || '瓶'}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import lnkTaps from '../../core/lnk-taps/lnk-taps'
    import {getFiltersRaw} from "link-taro-component";
    export default {
        name: "dealer-inventory-list-page",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            const inventoryStatusOptions = [
                {name: "最新库存", seq: "1",  val: "newest"},
                {name: "历史库存", seq: "2",  val: "history"},
            ];
            const inventoryOption = new this.AutoList(this, {
                module: 'action/link/visitInventory',
                param: () => {
                    const filtersRaw = [{id: 'visitStatus', property: 'visitStatus', value: 'Y', operator: '='}];
                    getFiltersRaw(this.filterOption).forEach(item => {
                        item.value && filtersRaw.push(item);
                    });
                    // 特定公司的经销商人员只能看到自己职位创建的数据
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        oauth: 'ALL',
                        sort: 'created',
                        order: 'desc',
                        accntId: this.pageParam.data.id,
                        partOauthFlag: this.isPartPort ? 'Y':'', //分品项-安全性
                        filtersRaw
                    }
                },
                loadOnStart: false,
                queryFields: 'id,prodCode,created,createdName,prodName,qty1,saleUnit,qty2,retailUnit,postnId',
                sortOptions: null,
                searchFields: ['prodName', 'prodCode', 'createdName'],
                filterBar: {},
                hooks: {
                    beforeLoad(option) {
                        if (this.inventoryStatusActive.val === 'newest') {
                            option.param.tabFlag = 'Y';
                        }
                    }
                }
            });
            return {
                userInfo,
                // 是否是分品项公司
                isPartPort: false,
                isDealer: false, // 是否走特定公司的经销商人员安全性
                inventoryStatusOptions,
                inventoryStatusActive: inventoryStatusOptions[0],
                inventoryOption,
                copyISOptions: null,
                statusLeft: true,
                filterOption: [
                    {label: '采集日期', field: 'created', type: 'date'}
                ]
            }
        },
        components: {
            lnkTaps,
        },
        async created () {
            this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
            const prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
            this.isPartPort = prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
            await this.inventoryOption.methods.reload();
            this.copyISOptions = this.$utils.deepcopy(this.inventoryOption.option.param.filtersRaw);
        },
        watch: {
            filterOption () {
                this.filterList(getFiltersRaw(this.filterOption))
            }
        },
        methods: {
            /**
             * 筛选函数
             * <AUTHOR>
             * @date 2020-11-02
             * @param param
             */
            async filterList(param) {
                const that = this;
                await that.inventoryOption.methods.reload();
            },
            /**
              * 监控返回刷新
              * <AUTHOR>
              * @date 2020-10-26
              * @param param
            */
            onBack (param) {
                if (param) {
                    param.refreshList ? this.inventoryOption.methods.reload() : '';
                }
            },
            /**
              * 跳转新建页面
              * <AUTHOR>
              * @date 2020-10-26
              * @param data
            */
            creatInventory (data) {
                this.$nav.push('/pages/terminal/visit/inventory-acquisition-page', {
                    accntId: this.pageParam.data.id,
                    data: this.pageParam.data,
                    source: 'inventoryList',
                    originFlag: 'inventoryList',
                    isEditFlag: true
                })
            },
            /**
              * taps菜单切换
              * <AUTHOR>
              * @date 2020-10-26
              * @param param
            */
            switchTab () {
                this.inventoryOption.methods.reload();
            },
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .dealer-inventory-list-page {
        .blank {
            width: 100%;
            height: 96px;
            background: #F2F2F2;
        }
        font-family: PingFangSC-Regular,serif;
        /*deep*/.link-auto-list-top-bar {
                    border-bottom: none;
                }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-item {
                    padding: 24px;
                }
        /*deep*/.link-item-active {
                    background: #ffffff;
                }
        /*deep*/.link-icon {
                    font-size: 28px;
                }
        /*deep*/.link-search-input-no-padding-bottom {
                    padding-bottom: 24px!important;
                }
        .filter {
            padding-left: 24px;
        }
        .inventory-list-rows {
            border-radius: 16px;
            margin: 24px auto auto auto;
            width: 702px;
            .media-list {
                @include media-list();
                font-size: 28px;
                .top-rows {
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    .date-name {
                        color: #262626;
                        font-size: 24px;
                    }
                    .store-level {
                        font-size: 24px;
                        color: #8C8C8C;
                    }
                }
                .store-input {
                    padding-top: 16px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    .store-input-rows {
                        @include flex-start-center();
                        .label {
                            color: #8C8C8C;
                            padding-left: 8px;
                            padding-right: 8px;
                        }
                        .value {
                            color: #000000;
                        }
                    }
                }
            }
        }
    }
</style>
