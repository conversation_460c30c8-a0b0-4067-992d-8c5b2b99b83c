<template>
    <link-page class="dealer-visit-perform-page">
        <!--顶部-->
        <view class="top-container">
            <navigation-bar :backVisible="true"
                            :navBarAllHeight="203"
                            :backgroundImg="$imageAssets.homeMenuBgImage"
                            :title="navigationBarTitle"
                            :titleColor="navigationBarTitleColor"
                            :delta="delta"
                            :navBackgroundColor="navBackgroundColor">
                <view class="top-content">
                    <view class="store-image">
                        <image :src="terminalDetails.storeUrl"></image>
                    </view>
                    <view class="store-content" style="color: #fff;">
                        <view>{{clientDetails.acctName}}</view>
                        <view>编码：{{clientDetails.acctCode}}</view>
                        <view style="font-size: 12px;">{{clientDetails.orieLocation}}</view>
                    </view>
                    <!-- <view class="store-right">
                        <image :src="$imageAssets.rightImage"></image>
                    </view> -->
                </view>
            </navigation-bar>
        </view>
        <lnk-no-auth v-if="templateData.length === 0"></lnk-no-auth>
        <view class="visit-container" v-else>
            <view class="collect-content" v-if="pageParam.source === 'onlyView'">
                <view class="iconfont icon-time-circle"></view>
                <view class="last-collect-text">到店打卡时间</view>
                <view class="last-time">{{clientDetails.visitTime | date('YYYY-MM-DD HH:mm')}}</view>
            </view>
<!--            拜访执行类的小程序配置-->
            <view :class="item.values.cardType === 'quickEnter' ? 'visit-column' : 'visit-rows'"
                  v-for="(item, index) in templateData" :key="index">
                <view class="visit-rows-btn" v-if="item.values.cardType === 'button' && pageParam.source !== 'onlyView'"
                      :style="{ 'background': item.values.status === 'Y' || !isEditFlag || !btnEditFlag ? '#A6B4C7' : '#2F69F8'}"
                      @tap="item.values.status === 'Y' || !isEditFlag || !btnEditFlag || pageParam.source === 'onlyView' ? blankClick('hasClock') : pageEntrance(item, index)">
                    <view class="iconfont icon-btn" :class="item.values.icon"></view>
                    <view class="base-label">{{item.base.label}}</view>
                </view>
                <view class="visit-rows-content" v-if="item.values.cardType === 'notButton'"
                      @tap="otherClickFlag || pageParam.source === 'viewDetail'? pageEntrance(item, index) : blankClick('notClock')">
                    <view class="rows-left">
                        <view class="icon-background">
                            <view class="iconfont icon" :class="item.values.icon"></view>
                        </view>
                        <view class="base-label">{{item.base.label}} <text class="must-input" v-if="item.base.require">*</text></view>
                    </view>
                    <view class="rows-right">
                        <view class="iconfont icon-check checked-iconfont" v-if="item.values.status === 'Y'"></view>
                        <view class="iconfont icon-right arrow-right"></view>
                    </view>
                </view>
                <view class="visit-block-content" v-if="item.values.cardType === 'quickEnter' && item.quickEnterAcctTypeFlag && pageParam.source !== 'onlyView'"
                      @tap="otherClickFlag || pageParam.source === 'viewDetail'? pageEntrance(item, index) : blankClick('notClock')">
                    <view class="icon-background">
                        <view class="iconfont icon" :class="item.values.icon"></view>
                    </view>
                    <view class="base-label">{{item.base.label}}</view>
                </view>
            </view>
            <view class="collect-content" v-if="pageParam.source === 'onlyView'">
                <view class="iconfont icon-time-circle"></view>
                <view class="last-collect-text">离店打卡时间</view>
                <view class="last-time">{{clientDetails.visitEndTime | date('YYYY-MM-DD HH:mm')}}</view>
            </view>
        </view>
    </link-page>
</template>

<script>
    import navigationBar from 'link-taro-component'
    import lnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth"
    export default {
        name: "dealer-visit-perform-page",
        data () {
            return {
                acctLatitude:0,
                acctLongitude:0,
                isEditFlag: false,                  // 可编辑标志
                dateTime: null,                     // 当前时间
                coordinate: null,                   // 定位坐标
                addressData: {
                    province:null,
                    city:null,
                    district:null,
                    street:null,
                    street_number:null
                },                  // 打卡记录
                delta: 1,                           // 返回的页面数，如果 delta 大于现有页面数，则返回到首页
                radius: null,
                checkOutOver:false,
                moduleId: '',
                visitId: '',
                authFlag: true,
                otherClickFlag: false,
                navigationBarTitle: '经销商拜访执行',
                navigationBarTitleColor: '#ffffff',
                navBackgroundColor: 'transparent',
                clientDetails: this.pageParam.data,
                terminalDetails: '',
                backVisitStatus: '',
                templateData: [],
                mustInputArr: [],                    // 拜访采集必填项
                acctIdArr: [],                    // 拜访采集必填项
                requiredItems: '',
                btnEditFlag: false, //按钮可编辑标志-点了到店打卡就设置为false，防止提交数据之后返回之后可以再一次到店打卡。。-区别于isEditFlag【还控制子对象的可否编辑。】
                brandLov: this.$http.post('action/link/basic/queryByExamplePage',{type: 'BRAND'}),
            }
        },
        async created () {
            // 竞品采集数据长度为0的时候，更新采集状态
            this.$bus.$on('updateVisitStatus', ()=> {
                this.updateStatus();
            })
            this.$utils.showLoading();
            if(this.pageParam.source === 'viewDetail') {
                this.visitId = this.pageParam.data.id;
                let userInfo = this.$taro.getStorageSync('token').result;
                this.isEditFlag = userInfo.id === this.pageParam.data.createdBy;
                this.btnEditFlag = userInfo.id === this.pageParam.data.createdBy;
            } else if(this.pageParam.source === 'onlyView'){
                this.visitId = this.pageParam.data.id;
                this.isEditFlag=false
                this.btnEditFlag=false
            }else {
                this.moduleId = await this.$newId();//await this.getId();
                this.visitId = await this.$newId();//await this.getVisitId();
                this.isEditFlag = true;
                this.btnEditFlag = true;
            }
            this.initData();
            this.getTerminalDetails()
            const userInfo = this.$taro.getStorageSync('token').result.coreOrganizationTile
            const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
            if(userInfo.l5Id === chuanDongOrgId) {
                //lzljqw-004-301川东打卡范围缩小至100m
                this.radius = 100
            } else {
                //其他公司-走参数配置
                this.radius = await this.getFirmParam();
            }
        },
        components: {
            navigationBar,
            lnkNoAuth
        },
        mounted() {
            this.$bus.$on('dealerRefreshUpdateVisitStatus',async () => {
                await this.updateStatus();
            })
        },
        methods: {
            /**
              * 查询主户头和子户头终端
              * <AUTHOR>
              * @date 2020-11-20
              * @param id 终端id
            */
            queryAcctId (id) {
                this.acctIdArr = [];
                this.$http.post('action/link/accnt/queryByExamplePage', {
                    multiAcctMainId: id,
                    oauth: 'MY_POSTN',
                    filtersRaw: [
                        {id: 'acctType', property: 'acctType', value: '[Terminal, Distributor]', operator: 'in'},
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                    ]
                }).then(data => {
                    data.rows.forEach(item => {
                        this.acctIdArr.push(item.id)
                    })
                })
            },
            /**
             * 校验拜访采集必输模块
             * <AUTHOR>
             * @date 2020-11-18
             * @param visitId 拜访id
             * @param accntId 终端id
             */
            async verifyMustCollect (visitId, accntId) {
                const that = this;
                //商超校验到点打卡 lzlj-002-2135
                if(that.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode == 5139) {
                    if(!(this.otherClickFlag || this.pageParam.source === 'viewDetail')) {
                        return 'shangChaoNotInClock'
                    }
                }
                this.mustInputArr = that.templateData.filter(item => item.base.require === true);
                const notMustInputArr = that.templateData.filter(item => item.base.require !== true);
                let verifyPass = [];
                let i = 0;
                return new Promise(resolve => {
                    if(that.templateData.length === notMustInputArr.length) {
                        resolve([])
                    }
                    that.templateData.forEach(item => {
                        if (item.base.require) {
                            that.$http.post(item.values.queryUrl, {
                                visitId: visitId,
                                accntId: accntId
                            }).then(data => {
                                i++;
                                if (data.rows.length !== 0) {
                                    item.verifyMustFlag = true;
                                    verifyPass.push(item);
                                    if (i === this.mustInputArr.length) {
                                        resolve(verifyPass)
                                    }
                                } else {
                                    if (i === that.mustInputArr.length) {
                                        resolve(verifyPass)
                                    }
                                }
                            })
                        }
                    });
                    if(that.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode == 5139) {
                        //商超没有采集模块必输 lzlj-002-2135
                        resolve([])
                    }
                })
            },
            /**
             * 地位置信息
             * <AUTHOR>
             * @date 2020-09-06
             */
            async initData() {
                this.coordinate =  await this.$locations.getCurrentCoordinate();
                if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                    let address =  await this.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '终端拜访');
                    this.addressData = address['originalData'].result.addressComponent
                }
            },
            /**
             * 获取系统允许打卡半径范围
             * <AUTHOR>
             * @date 2020-09-02
             */
            getFirmParam () {
                return new Promise(resolve => {
                    if (this.$taro.getStorageSync('radius')) {
                        resolve(this.$taro.getStorageSync('radius'));
                    } else {
                        this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                            key: 'signInLimitDistance'
                        }).then(data => {
                            if (data.success) {
                                this.$taro.setStorageSync('radius', Number(data.rows[0].value));
                                resolve(Number(data.rows[0].value));
                            }
                        })
                    }
                })
            },
            /**
             * 更新拜访执行状态
             * <AUTHOR>
             * @date 2020-09-01
             */
            async updateStatus() {
                let visitStatus = await this.$dealerConfigTemplate.getDealerVisitPerformStatus(this.visitId);
                this.templateData.forEach(item => {
                    if (!this.$utils.isEmpty(visitStatus[item.ctrlCode])) {
                        this.$set(item.values, 'status', visitStatus[item.ctrlCode]);
                    }
                    if (item.ctrlCode === 'dealerStorePunchCard') {
                        this.otherClickFlag = item.values.status !== 'N';
                    }
                    //川东片区新增三个控件，再反馈与备注控件上做了调整，所以根据对应的field判断内容是否填写
                    if(item.ctrlCode === 'remarkFeedback' && item.values && item.values.field) {
                        this.$set(item.values, 'status', visitStatus[item.values.field]);
                    }
                });
                this.$utils.hideLoading()
            },
            /**
             * 监控返回函数
             * <AUTHOR>
             * @date 2020-09-01
             * @param param 返回参数
             */
            async onBack(param) {
                if(this.pageParam.source === 'viewDetail') {
                    let userInfo = this.$taro.getStorageSync('token').result;
                    this.isEditFlag = userInfo.id === this.pageParam.data.createdBy;
                    this.btnEditFlag = userInfo.id === this.pageParam.data.createdBy;
                } else if(this.pageParam.source === 'onlyView'){
                    this.isEditFlag = false;
                    this.btnEditFlag = false;
                }else {
                    this.isEditFlag = true;
                    this.btnEditFlag = true;
                }
                const that = this;
                if (param) {
                    if (param.refreshFlag) {
                        this.btnEditFlag = false;
                        if (!that.$utils.isEmpty(param.visitRecordData)) {
                            that.clientDetails = param.visitRecordData;
                        }
                        await that.updateStatus();
                        that.backVisitStatus = param.visitApplicationStatus;
                        this.btnEditFlag = true;
                    }
                    if (!that.$utils.isEmpty(param.newVisitRecord)) {
                        that.clientDetails = param.newVisitRecord;
                    }
                    if (that.$utils.isEmpty(that.addressData)) {
                        await that.initData();
                    }
                }

            },
            /**
              * 获取拜访id
              * <AUTHOR>
              * @date 2020-09-01
              * @param param
            */
            getVisitId () {
                const that = this;
                return new Promise(resolve => {
                    that.$http.post('action/link/accntVisit/preDefaultValue').then(data => {
                        if (data.success) {
                            resolve(data.result.id)
                        }
                    })
                })
            },
            /**
             * 获取默认打卡id
             * <AUTHOR>
             * @date 2020-08-31
             */
            getId () {
                const that = this;
                return new Promise(resolve => {
                    that.$http.post('action/link/accntSignInDetails/preDefaultValue').then(data => {
                        if (data.success) {
                            resolve( data.result.id)
                        }
                    })
                })
            },
            /**
              * 获取终端信息
              * <AUTHOR>
              * @date 2020-08-26
              * @param param
            */
            getTerminalDetails () {
                const that = this;
                let userInfo = this.$taro.getStorageSync('token');
                /*this.$aegis.report({
                    msg: 'visit-perform-page: 获取终端信息,getTerminalDetails',
                    ext1: 'this.pageParam.data.accntId' + JSON.stringify(this.pageParam.data.accntId),
                    ext2: 'this.pageParam.data.id' + JSON.stringify(this.pageParam.data.id),

                    trace: 'log'
                });*/
                if (this.$utils.isEmpty(this.pageParam.data.accntId) && this.$utils.isEmpty(this.pageParam.data.id)) {
                    return;
                }
                that.$http.post('action/link/accnt/queryById', {
                    id: that.pageParam.data.accntId || that.pageParam.data.id
                }).then(async (data) => {
                    if (data.success) {
                        if (!that.$utils.isEmpty(data.result.storePicPreKey)) {
                            let urlData = await that.$image.getSignedUrl(data.result.storePicPreKey);
                            that.$set(data.result, 'storeUrl', urlData);
                        } else {
                            that.$set(data.result, 'storeUrl', that.$imageAssets.terminalDefaultImage);
                        }
                        this.terminalDetails = data.result;
                        that.acctLatitude = data.result.latitude;
                        that.acctLongitude = data.result.longitude;
                        this.queryAcctId(this.terminalDetails.id);
                        // 获取模板数据
                        await this.$dealerConfigTemplate.dealerVisitPerformTemp();
                        const templateData = this.$store.getters['dealerConfigTemplate/getDealerVisitPerformTemp'];
                        const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
                        this.positionChuanDong = this.$taro.getStorageSync('token').result.coreOrganizationTile.l5Id === chuanDongOrgId
                        if(this.positionChuanDong && this.clientDetails.accntPartner !== 'cooperation') {
                            templateData.forEach(item => {
                                item.base.require = false
                            })
                        }
                        this.templateData = templateData
                        // 处理模板数据
                        let mustImputItem = [];
                        this.templateData.forEach(item => {
                            if (item.base.require) {
                                mustImputItem.push(item.ctrlCode)
                            }
                            if (item.ctrlCode === 'advanceOrder') {
                                this.$set(item, 'quickEnterAcctTypeFlag', true)
                            }
                            if (item.ctrlCode === 'quotaApply') {
                                this.terminalDetails.acctType === 'Terminal' ||
                                (this.terminalDetails.acctType === 'Distributor' && userInfo.result.coreOrganizationTile.brandCompanyCode === '5137') ?
                                    this.$set(item, 'quickEnterAcctTypeFlag', true) :  this.$set(item, 'quickEnterAcctTypeFlag', false)
                            }
                        });
                        this.requiredItems = mustImputItem.join(',');
                        if (this.requiredItems !== this.pageParam.data.requiredItems) {
                            this.updateVisit();
                        }
                        this.updateStatus();
                    }
                })
            },
            /**
              * 更新拜访记录
              * <AUTHOR>
              * @date 12/9/20
              * @param param
            */
            updateVisit () {
                this.pageParam.data.requiredItems = this.requiredItems;
                this.$http.post('action/link/accntVisit/update', this.pageParam.data).then(data => {
                    if (data.success) {
                    }
                });
            },
            /**
              * 空函数
              * <AUTHOR>
              * @date 2020-08-31
              * @param flag
            */
            blankClick (flag) {
         
                switch (flag) {
                    case 'hasClock':
                        this.isEditFlag ? this.$utils.showAlert('您已到店打卡，无需重复打卡', {icon: 'none'}) : this.$utils.showAlert('您无打卡权限', {icon: 'none'});
                        break;
                    case 'notClock':
                        this.$utils.showAlert('您还未店打卡，请优先完成到店打卡', {icon: 'none'});
                        break;
                }
            },
            /**
             * 跳转对应页面
             * <AUTHOR>
             * @date 2020-08-11
             * @param data 页面入口数据
             */
            async pageEntrance(data) {
                const brandLov = await this.brandLov
                const excludeBrandData = [];
                brandLov.rows.forEach((item) => {
                    if(item.useFlag === 'N' && item.activeFlag==='Y'){
                        excludeBrandData.push(item.val)
                    }
                })
                const that = this;
                //back为结束拜访按钮
                if (data.values.url ===  'back') {
                    const flag = this.positionChuanDong && this.clientDetails.accntPartner !== 'cooperation' && !this.otherClickFlag
                    that.dateTime = new Date(await that.$utils.getTimestamp());
                    let distance = await this.$locations.getDistance(Number(that.coordinate.latitude), Number(that.coordinate.longitude), Number(that.acctLatitude), Number(that.acctLongitude));
                    that.checkOutOver=distance* 1000>that.radius;
                    let verifyPassArr = await that.verifyMustCollect(that.visitId, that.terminalDetails.accntId);// 拜访执行必输采集校验
                    if (verifyPassArr === 'shangChaoNotInClock' || verifyPassArr.length !== that.mustInputArr.length || flag) {
                        that.$dialog({
                            title: '提示',
                            content: '当前拜访有必填项尚未填写，您可以强制关闭或者继续执行当前拜访',
                            cancelButton: true,
                            initial: true,
                            cancelText: '强制关闭',
                            confirmText: '继续执行',
                            onConfirm:() => {
                            },
                            onCancel: () => {
                                that.checkOutDialog(that.visitId)
                            }
                        });
                    } else {
                        that.$dialog({
                            title: '提示',
                            content: '是否确认结束拜访',
                            cancelButton: true,
                            initial: true,
                            cancelText: '取消',
                            confirmText: '确定',
                            onConfirm:() => {
                                that.checkOutUpdateDialog('closeVisit')
                            },
                            onCancel: () => {
                            }
                        });

                    }
                } else {
                    if(
                        data.base.label === '预订单' &&
                        this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '5600' &&
                        that.terminalDetails.acctStage !== 'ykf'
                    ) {
                        this.$message.warn('当前终端未认证，不可下预订单')
                        return
                    }
                    that.btnEditFlag = false;
                    that.$nav.push(data.values.url, {
                        data: that.terminalDetails,
                        moduleId: that.moduleId,
                        visitId: that.visitId,
                        visitApplicationStatus: that.pageParam.data.visitApplicationStatus || that.backVisitStatus,
                        radius: that.radius,
                        isEditFlag: that.isEditFlag,
                        noStoreCoreFlag: that.pageParam.noStoreCoreFlag,
                        pageFrom: 'visit',
                        acctIdArr: that.acctIdArr,
                        sourcePage: that.pageParam.source,
                        requiredItems: that.requiredItems,
                        createdVisitOriginal: that.pageParam.createdVisitOriginal,
                        field: data.values.field,
                        excludeBrandData: excludeBrandData //陈列采集陈列品项排除列表
                    })
                }
            },
            /**
             * 强制关闭前判断确认超距离离店
             * @param visitId
             * @returns {Promise<void>}
             */
            checkOutDialog(visitId){
                if(this.checkOutOver){
                    this.$dialog({
                        title: '提示',
                        content: '您已超距离离店是否强制结束拜访',
                        cancelButton: true,
                        initial: true,
                        cancelText: '取消',
                        confirmText: '确定',
                        onConfirm:() => {
                            this.forceClose(visitId)
                        },
                        onCancel: () => {
                        }
                    });
                }else{
                    this.forceClose(visitId)
                }
            },


            /**
             * 强制关闭当前拜访
             * <AUTHOR>
             * @date 2020-11-18
             * @param visitId
             */
            async forceClose(visitId) {
                const that = this;
                let visitEndTime = this.$date.format(that.dateTime, 'YYYY-MM-DD HH:mm:ss');                                                                 // 打卡时间转换格式
                let visitStartTime = that.$utils.isEmpty(that.clientDetails.visitTime) ? that.clientDetails.created.replace(/-/g, '/') : that.clientDetails.visitTime.replace(/-/g, '/');
                let visitDuration = await this.$utils.getYMDHMS(Date.parse(new Date(visitStartTime)), Date.parse(new Date(visitEndTime.replace(/-/g, '/'))));    // 计算拜访时长
                this.$http.post('action/link/accntVisit/closeVisit', {
                    id: visitId,
                    visitDuration: visitDuration,
                    visitEndTime: visitEndTime,
                    checkOutSpecialDescription: that.checkOutOver ? '离店定位偏移' : null,
                }, {
                    handleFailed(error) {
                    }
                }).then(data => {
                    if (data.success) {
                        let opt = {
                            input: {id: visitId, visitDuration: visitDuration, visitEndTime: visitEndTime},
                            output: data
                        };
                       /* that.$aegis.report({
                            msg: '用户触发强制关闭当前拜访成功',
                            ext1: JSON.stringify(opt),
                            trace: 'log'
                        });*/
                        this.updatePunchCardRecord('forceClose');
                    }
                })
            },
            /**
             * 更新列表当前列表状态
             * <AUTHOR>
             * @date 2020-09-01
             */
            async updateVisitStatus() {
                const that = this;
                let visitEndTime = this.$date.format(that.dateTime, 'YYYY-MM-DD HH:mm:ss');                                                                 // 打卡时间转换格式
                let visitStartTime = that.$utils.isEmpty(that.clientDetails.visitTime) ? that.clientDetails.created.replace(/-/g, '/') : that.clientDetails.visitTime.replace(/-/g, '/');
                let visitDuration = await this.$utils.getYMDHMS(Date.parse(new Date(visitStartTime)), Date.parse(new Date(visitEndTime.replace(/-/g, '/'))));    // 计算拜访时长
                that.clientDetails.visitApplicationStatus = 'visited';                                                                                      // 修改拜访状态
                that.clientDetails.visitEndTime = visitEndTime;                                                                                             // 拜访结束时间
                that.clientDetails.visitDuration = visitDuration;// 拜访时长
                that.clientDetails.checkOutSpecialDescription= that.checkOutOver ? '离店定位偏移' : null;
                that.clientDetails.row_status = 'UPDATE';                                                                                                   // 更新
                that.$http.post('action/link/accntVisit/upsert', that.clientDetails).then(async data => {
                    if (data.success) {
                        that.$bus.$emit('visitCreatedSuccess');
                        that.$nav.back();
                    }
                });
            },
            /**
             * 关闭前确认超距离离店
             * @param flag
             * @returns {Promise<void>}
             */
            checkOutUpdateDialog(flag){
                if(this.checkOutOver){
                    this.$dialog({
                        title: '提示',
                        content: '您已超距离离店是否结束拜访',
                        cancelButton: true,
                        initial: true,
                        cancelText: '取消',
                        confirmText: '确定',
                        onConfirm:() => {
                            this.updatePunchCardRecord(flag)
                        },
                        onCancel: () => {
                        }
                    });
                }else{
                    this.updatePunchCardRecord(flag)
                }
            },
            /**
             * 插入离店记录
             * <AUTHOR>
             * @date 2020-09-06
             */
            async updatePunchCardRecord (flag) {
                const that = this;
               if(flag === 'forceClose' && !that.otherClickFlag){
                   that.$nav.back();
               }else{
                   let insetOptions = {
                       signInType: 'checkOut',
                       // signInTime: that.$date.format(that.dateTime, 'YYYY-MM-DD HH:mm:ss'),
                       province: that.addressData.province,
                       city: that.addressData.city,
                       district: that.addressData.district,
                       address: `${that.addressData.street}${that.addressData.street_number}`,
                       longitude: that.coordinate.longitude,
                       latitude: that.coordinate.latitude,
                       signInStatus: that.checkOutOver? 'abnormal' : 'normal',
                       acctId: that.otherClickFlag? that.clientDetails.accntId : that.clientDetails.id,
                       headId: that.otherClickFlag? that.clientDetails.id : that.visitId,
                       visitStatus: flag === 'forceClose' ? 'N' : 'Y'
                   };
                    const {success} = await this.$http.post('action/link/accntSignInDetails/insert', insetOptions)
                    if (success && flag === 'closeVisit') {
                        await that.updateVisitStatus() // 更新当前拜访中记录
                    }
                    if (flag === 'forceClose') {
                        that.$bus.$emit('visitCreatedSuccess');
                        that.$nav.back();
                    }
               }

            },
            /**
             * 获取拜访执行模板信息
             * <AUTHOR>
             * @date 2020-08-04
             */
            async getTemplate () {
                const data = await this.$utils.getQwMpTemplate('VisitPerform');
                if (data.success) {
                    this.authFlag = false;
                    let resultOpt = JSON.parse(data.result);
                    this.templateData = JSON.parse(resultOpt.conf);
                    this.templateData.forEach(item => {
                        this.$set(item.values, 'status', false);
                        this.$set(item, 'verifyMustFlag', false);
                    });
                    this.updateStatus();
                } else {
                    this.authFlag = true;
                }
            },
        }
    }
</script>

<style lang="scss">
    .dealer-visit-perform-page {
        background: #ffffff;
        padding-bottom: 34px;
        .top-container {
            .comp-navbar {
                width: 100vw;
                .placeholder-bar{
                    background-color: transparent;
                    width: 100%;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: start;
                    -ms-flex-pack: start;
                    justify-content: flex-start;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    .icon-left {
                        width: 10%;
                        font-size: 34px;
                        color: #FFFFFF;
                        padding-left: 24px;
                    }
                    .navigator-back {
                        width: 46px;
                        height: 46px;
                        padding-left: 24px;
                    }
                    .bar-title {
                        width: 82%;
                        font-size: 34px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        text-align: center;
                    }
                }
            }
            .top-content {
                @include flex;
                .store-image {
                    margin-left: $margin-normal;
                    margin-top: 16px;
                    width: 128px;
                    height: 128px;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 7px 49px 0 rgba(20,28,51,0.39);

                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
                .store-content {
                    width: 74.5%;
                    margin-top: 16px;
                    padding: 0 20px;
                    box-sizing: border-box;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #ffffff;
                            letter-spacing: 0;
                            line-height: 40px;
                            max-height: 60px;
                        }
                        .store-level {
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                    .store-content-middle {
                        @include flex-start-center;
                        margin-top: 24px;
                        margin-left: 24px;
                        flex-wrap: wrap;
                        .store-type {
                            border: 1px solid #ffffff;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 36px;
                            color: #ffffff;
                            margin-right: 10px;
                            margin-top: 10px;
                        }
                        .store-type-level {
                            border: 1px solid #ffffff;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 36px;
                            color: #ffffff;
                            margin-right: 10px;
                            margin-top: 10px;
                            @include flex;
                            .acct-level {
                                margin-right: 8px;
                                line-height: 36px;
                            }
                            .line {
                                padding: 0 8px;
                            }
                            .capacity-level {
                                line-height: 36px;
                            }
                        }
                    }
                }
                .store-right {
                    padding-top: 80px;
                    width: 24px;
                    height: 32px;
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
        .visit-container {
            .collect-content {
                width: 100%;
                margin-bottom: 24px;
                margin-top: 24px;
                @include flex-start-center();
                height: 76px;
                background: #ffffff;
                text-align: center;
                justify-content: center;
                display: flex;
                .icon-time-circle {
                    font-size: 28px;
                    color: #000;
                }
                .last-collect-text {
                    font-size: 28px;
                    color: #000;
                    padding-left: 8px;
                }
                .last-time {
                    font-size: 28px;
                    color: #000;
                    padding-left: 16px;
                }
            }
            .visit-rows {
                .visit-rows-content {
                    background: #FFFFFF;
                    box-shadow: 0 2px 20px 0 rgba(47,105,248,0.12);
                    border-radius: 16px;
                    width: 702px;
                    height: 128px;
                    margin: 24px auto auto auto;
                    @include flex-start-center();
                    @include space-between();
                    .rows-left {
                        @include flex-start-center();
                        .icon-background {
                            margin-left: 24px;
                            width: 80px;
                            height: 80px;
                            border-radius: 50%;
                            background: rgba(47,105,248,0.10);
                            .icon {
                                font-size: 40px;
                                text-align: center;
                                color: #2F69F8;;
                                line-height: 80px;
                            }
                        }
                        .base-label {
                            font-family: PingFangSC-Regular,serif;
                            font-size: 28px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 28px;
                            padding-left: 24px;
                            .must-input {
                                color: #FF5A5A;
                            }
                        }
                    }
                    .rows-right {
                        @include flex-start-center();
                        .checked-iconfont {
                            font-size: 32px;
                            color: #2F69F8;
                            padding-right: 16px;
                        }
                        .arrow-right {
                            color: #BFBFBF;
                            font-size: 32px;
                            margin-right: 12px;
                        }
                    }
                }
                .visit-rows-btn {
                    @include flex-center-center();
                    border-radius: 16px;
                    height: 96px;
                    width: 702px;
                    margin: 24px auto auto auto;
                    background: #2F69F8;
                    .icon-btn {
                        font-size: 40px;
                        color: #ffffff;
                    }
                    .base-label {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 32px;
                        color: #ffffff;
                        letter-spacing: 0;
                        padding-left: 16px;
                        .must-input {
                            color: #FF5A5A;
                        }
                    }
                }
            }
            .visit-column {
                margin-left: 24px;
                margin-top: 24px;
                float: left;
                margin-bottom: 24px;
                .visit-block-content {
                    width: 339px;
                    background: #FFFFFF;
                    box-shadow: 0 2px 20px 0 rgba(47,105,248,0.12);
                    border-radius: 16px;
                    @include flex-center-center;
                    @include direction-column;
                    .icon-background {
                        margin-top: 48px;
                        margin-bottom: 24px;
                        width: 80px;
                        height: 80px;
                        border-radius: 50%;
                        background: rgba(47,105,248,0.10);
                        .icon {
                            font-size: 40px;
                            text-align: center;
                            color: #2F69F8;;
                            line-height: 80px;
                        }
                    }
                    .base-label {
                        margin-bottom: 24px;
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 28px;
                        .must-input {
                            color: #FF5A5A;
                        }
                    }
                }
            }
        }
    }
</style>
