<template>
    <link-page class="dealer-write-off-prize">
        <link-auto-list :option="writeOffOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item">
                    <view slot="note">
                        <view class="media-list">
                            <view class="num">{{data.prodCode}}</view>
                            <link-lov
                                type="WRITEOFF_STATUS"
                                class="status"
                                :class="{'failed': data.writeOffStatus === 'N'}"
                                v-model="data.writeOffStatus"
                                disabled/>
                        </view>
                        <view class="content-middle">
                            <view class="name">{{data.prodName}}</view>
                        </view>
                        <view class="write-off-info">
                            <view class="info-item">
                                <text class="label">核销人员</text>
                                <text class="val">{{data.userTelephone}}</text>
                            </view>
                            <view class="info-item">
                                <text class="label">核销时间</text>
                                <text class="val">{{data.createTimeWrite}}</text>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: "dealer-write-off-prize",
    data () {
        const writeOffOption = new this.AutoList(this, {
            module: 'action/link/accntWriteoff',
            loadOnStart: true,
            param: {
                filtersRaw: [{id: 'accntId', property: 'accntId', value: this.acctId, operator: '='}]
            }
        })
        return {
            writeOffOption
        }
    },
    props: {
        acctId: {
            type: String,
            default: '',
            required: true
        }
    }
}
</script>

<style lang="scss">
.dealer-write-off-prize {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular,serif;
    .perform-case-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
    .media-list {
        display: flex;
        justify-content: space-between;
        height: 46px;
        .num {
            background: #A6B4C7;
            border-radius: 8px;
            font-size: 28px;
            color: #FFFFFF;
            letter-spacing: 0;
            line-height: 46px;
            padding: 2px 8px;
        }
        .status {
            color: #2F69F8;
        }
        .failed {
            color: #EA3232;
        }
    }
    .content-middle{
        width: 100%;
        @include flex-start-center;
        @include space-between;
        padding: 24px 0 16px 0;
        .name{
            font-family: PingFangSC-Semibold,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
        }
    }
    .write-off-info {
        padding-top: 24px;
        border-top: 1px solid #d6d6d6;
        font-family: PingFangSC-Regular,serif;
        font-size: 25px;
        color: #000000;
        letter-spacing: 0;
        display: flex;
        justify-content: space-between;
        .info-item {
            .label {
                color: #9e9e9e;
            }
        }
    }

}
</style>
