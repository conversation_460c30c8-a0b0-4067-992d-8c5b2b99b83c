<template>
  <view class="dealer-display-records">
    <view class="last-collect" @tap="checkHistory()">
      <view class="collect-content">
        <text class="iconfont icon-time-circle"></text>
        <text class="collect-text">最新采集</text>
        <text>{{lastCollectData.created | date('YYYY-MM-DD')}} {{lastCollectData.createdName}}</text>
      </view>
      <view class="view-history">查看历史</view>
    </view>
    <link-auto-list :option="displayRecordsOption" hideCreateButton>
      <template slot-scope="{data,index}">
        <item :key="index" :data="data" :arrow="false" class="display-records-card" @tap="goDetail(data)">
            <display-collect slot="note" :data="data"/>
        </item>
      </template>
    </link-auto-list>
  </view>
</template>

<script>
  import HeadType from "../../../terminal/components/head-type";
  import LnkImg from "../../../core/lnk-img/lnk-img";
  import DisplayCollect from '../../../terminal2/history-display/components/display-collect';

  export default {
    name: "dealer-display-records",
    components: {LnkImg, HeadType, DisplayCollect},
    props: {
        isDealer: {
            type: Boolean,
            default: false
        },
      tabFlag: {
        type: String,
        default: ''
      },
      queryId: {
        type: String,
        default: '',
        required: true
      },
      defaultType: {
        type: String,
        default: ''
      },
      showHead: {
        type: Boolean,
        default: false
      }
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
      const displayRecordsOption = new this.AutoList(this, {
        module: 'action/link/visitDisplay',
        param: () => {
            const filtersRaw = [{id: 'visitStatus', property: 'visitStatus', value: 'Y', operator: '='}];
            // 特定公司的经销商人员只能看到自己职位创建的数据
            if (this.isDealer) {
                filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
            }
            return {
                accntId: this.queryId,
                oauth: 'ALL',
                sort: 'id',
                order: 'desc',
                filtersRaw,
            }
        },
        sortOptions: null,
        filterBar: {},
        hooks: {
          beforeLoad(option) {
            if (!!this.tabFlag) {
              option.param['rows'] = 1;
            }
          },
          afterLoad(data) {
            data.rows.forEach(async item => {
              const arr = JSON.parse(item.operateItem)
              for(let i = 0; i < arr.length; i++) {
                arr[i] = await this.$lov.getNameByTypeAndVal('PROD_BUS_S_CLASS', arr[i])
              }
              item.operateItem = arr.join(',')
              item.promotionMaterial = JSON.parse(item.promotionMaterial)
                item.attachOther = (item.attachmentList || []).filter((i) => i.moduleType === 'otherSupport');
                item.attachDisplay = (item.attachmentList || []).filter((i) => i.moduleType === 'display');
            });
            if (data.rows.length !== 0) {
              this.lastCollectData = data.rows[0]
            }
          }
        }
      });
      return {
        lastCollectData: {},
        displayRecordsOption          // 陈列记录信息
      }
    },
    methods: {
      goDetail(data) {
        this.$nav.push('/pages/lzlj-II/display-records-detail-page.vue', data);
      },
      /**
       *  @description: 查看历史
       *  @author: 马晓娟
       *  @date: 2020/8/13 21:29
       */
      async checkHistory() {
        this.$nav.push('/pages/terminal2/history-display/history-display-page', {
          data: this.queryId
        });
      }
    },

  }
</script>

<style lang="scss">
  .dealer-display-records {
    margin: 24px;
    font-size: 28px;
    line-height: 28px;
    .link-auto-list-wrapper {
      padding: 0;
    }
    .last-collect {
          background: #FFFFFF;
          border-radius: 8px;
          widows: 702px;
          height: 76px;
          @include flex-start-center;
          @include space-between;
          padding-right: 24px;
          padding-left: 24px;
          margin-bottom: 24px;

          .collect-content {
            .icon-time-circle {
              color: #8C8C8C;
              font-size: 28px;
            }
            .collect-text {
              color: #8C8C8C;
              font-size: 28px;
            }
            .last-time {
              color: #000000;
              font-size: 28px;
            }
          }
          .view-history {
            font-size: 28px;
            color: #2F69F8;
          }
        }
    .display-records-card {
      background: #FFFFFF;
      border-radius: 16px;
      margin-bottom: 24px;
      padding-bottom: 40px;

      .display-records-card-head {
        display: flex;
        align-items: center;
        font-size: 24px;
        border-bottom: 1px dashed #DADEE9;
        padding-bottom: 24px;

        .label {
          color: #8C8C8C;
          margin-right: 10px;
        }

        .get-time {
          margin-right: 10px;
        }
      }
      .display-records-item{
        display: flex;
        justify-content: space-between;
        font-size: 28px;
        line-height: 28px;
        padding: 16px 0;
        &__left{
          width: 40%;
          color: #8C8C8C;
        }
        &__right{
          width: 60%;
          text-align: right;
          color: #262626;
        }
      }

      /*deep*/
      .link-item {
        padding: 0;
      }
      /*deep*/ .link-item-icon{
                 width: 0 !important;
               }
      /*deep*/.link-item {
         &:after {
        height: 0 !important;
        background-color: transparent !important;
        content: '';
        transform: scaleY(0.5);
      }
    }
    .product-image {
        display: flex;
        flex-direction: row;
        /*deep*/.lnk-img-item {
                  width: 180px;
                  height: 180px;
                }
      }
    }
  }
</style>
