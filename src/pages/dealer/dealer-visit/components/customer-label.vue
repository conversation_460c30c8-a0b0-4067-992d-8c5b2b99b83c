<template>
    <view class="dealer-customer-label">
       <view class="label-content" v-if="showAll">
           <!-- 终端进销存 -->
           <view class="sales-and-inventory">
               <view class="title">终端进销存</view>
               <view class="tag-search" v-if="taglist.length">
                   <view class="search-box">
                       <text>品项：</text>
                       <select-button :label="orgText" :selected-flag="true" @tap="showProd=true;" downIcon :showLength='4'></select-button>
                   </view>
               </view>
               <detail-card :show-field='purchaseFileds' :data='terminalTag'></detail-card>
           </view>
           <!-- 终端实时返利--会员渠道对接 -->
           <view class="terminal-model" v-if="showTerminalLabel">
            <RealTimeRebateVue :acctData="acctData" />
           </view>
           <!-- BC抢盘 品牌公司=5600，客户大类=terminal-->
           <view class="terminal-model" v-if="showTerminalLabel">
            <BcGrabPlateVue :acctData="acctData"/>
           </view>

           <!-- 终端扫码 -->
           <view class="terminal-model" v-if="showTerminalFlag">
               <view class="title">终端扫码 <text style="font-size: 12px;color: #F22232;"></text></view>
               <view class="date-pick-box">
                   <view class="date-pick-list">
                       <view class="date-pick-content" v-for="(date, i) in datePickListA" :key="i">
                           <view
                               class="date-pick-item"
                               :class="{ active: dateValA === date.val }"
                               @tap="onTapItemA(date.val)"
                           >{{ date.name }}</view
                           >
                       </view>
                       <view class="date-pick-content">
                           <link-date v-model="dateStart" placeholder="按月" :max="currentDateEnd"
                                      :min="currentDateStart" view="YM" displayFormat="YYYY-MM" valueFormat="YYYY-MM">
                               <template #default="{ display }">
                                   <view
                                       class="date-pick-item"
                                       :class="{ active: dateValA === 'month' }"
                                       @tap="onTapItemA('month')"
                                       style="display: flex"
                                   >{{ dateStart || '按月' }}
                                       <link-icon style="height: auto;" icon="icon-down1"/>
                                   </view>
                               </template>
                           </link-date>
                       </view>
                   </view>
               </view>
               <view class="content">
                   <view class="num-item">
                       <view class="val">{{ terminalAcctScanInfo.purInbound ? terminalAcctScanInfo.purInbound : 0 }}</view>
                       <view class="label">采购入库(件)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalAcctScanInfo.reInbound ? terminalAcctScanInfo.reInbound : 0 }}</view>
                       <view class="label">消费者退货入库(件)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalAcctScanInfo.conOutbound ? terminalAcctScanInfo.conOutbound : 0 }}</view>
                       <view class="label">出库消费者(件)</view>
                   </view>
               </view>
               <view class="content">
                   <view class="num-item">
                       <view class="val">{{ terminalAcctScanInfo.reOutbound ? terminalAcctScanInfo.reOutbound : 0 }}</view>
                       <view class="label">退货出库到上级(件)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item2">
                       <view class="val">{{ terminalAcctScanInfo.bottleNum ? terminalAcctScanInfo.bottleNum : 0 }}</view>
                       <view class="label">开瓶总数(瓶)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item2">
                       <view class="val">{{ terminalAcctScanInfo.cityBottleNum ? terminalAcctScanInfo.cityBottleNum : 0 }}</view>
                       <view class="label">跨城市异地开瓶(瓶)</view>
                   </view>
               </view>
               <view class="content" style="justify-content: flex-start;">
                   <view class="num-item2">
                       <view class="val">{{ terminalAcctScanInfo.disBottleNum ? terminalAcctScanInfo.disBottleNum : 0 }}</view>
                       <view class="label">本城市跨区县异地开瓶(瓶)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item2">
                       <view class="val">{{ terminalAcctScanInfo.localBottleNum ? terminalAcctScanInfo.localBottleNum : 0 }}</view>
                       <view class="label">本地开瓶(瓶)</view>
                   </view>
               </view>
           </view>
           <!-- 终端年度开瓶等级 -->
            <year-level v-if="isPartPort && acctData.acctStage === 'ykf'" :acctData="acctData"></year-level>
           <!-- 终端宴席 -->
           <view class="terminal-model" v-if="showTerminalFlag">
               <view class="title">终端宴席</view>
               <view class="date-pick-box">
                   <view class="date-pick-list">
                       <view class="date-pick-content" v-for="(date, i) in datePickList" :key="i">
                           <view
                               class="date-pick-item"
                               :class="{ active: dateValB === date.val }"
                               @tap="onTapItemB(date.val)"
                           >{{ date.name }}</view
                           >
                       </view>
                       <view class="date-pick-content">
                           <link-date v-model="dateStartB" placeholder="按月" :max="currentDateEnd"
                                      :min="currentDateStart" view="YM" displayFormat="YYYY-MM" valueFormat="YYYY-MM">
                               <template #default="{ display }">
                                   <view
                                       class="date-pick-item"
                                       :class="{ active: dateValB === 'month' }"
                                       @tap="onTapItemB('month')"
                                       style="display: flex"
                                   >{{ dateStartB || '按月' }}
                                       <link-icon style="height: auto;" icon="icon-down1"/>
                                   </view>
                               </template>
                           </link-date>
                       </view>
                   </view>
               </view>
               <view>
                   <view class="banquet-content">宴席累计开瓶(瓶)：{{terminalBanquetInfo.banquetOpenBottleCount ? terminalBanquetInfo.banquetOpenBottleCount : 0}}</view>
                   <view class="content">
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.banquetSessionCount ? terminalBanquetInfo.banquetSessionCount : 0 }}</view>
                           <view class="label">宴席场次(场)</view>
                       </view>
                       <view class="line"></view>
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.banquetUnsuccessfulCount ? terminalBanquetInfo.banquetUnsuccessfulCount : 0 }}</view>
                           <view class="label">未成功(场)</view>
                       </view>
                       <view class="line"></view>
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.banquetSucceededCount ? terminalBanquetInfo.banquetSucceededCount : 0 }}</view>
                           <view class="label">已成功(场)</view>
                       </view>
                   </view>
                   <view class="content">
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.banquetExecutionCount ? terminalBanquetInfo.banquetExecutionCount : 0 }}</view>
                           <view class="label">执行中(场)</view>
                       </view>
                       <view class="line"></view>
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.banquetOutboundCount ? terminalBanquetInfo.banquetOutboundCount : 0  }}</view>
                           <view class="label">已宴席出库(瓶)</view>
                       </view>
                       <view class="line"></view>
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.banquetReturnCount ? terminalBanquetInfo.banquetReturnCount : 0 }}</view>
                           <view class="label">已宴席退货(瓶)</view>
                       </view>
                   </view>
               </view>
               <view>
                   <view class="middle-title">宴席返利金额</view>
                   <view class="content">
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.tqCashVal ? terminalBanquetInfo.tqCashVal : 0 }}</view>
                           <view class="label">头曲现金</view>
                       </view>
                       <view class="line"></view>
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.tqPrizeVal ? terminalBanquetInfo.tqPrizeVal : 0 }}</view>
                           <view class="label">再来一瓶</view>
                       </view>
                       <view class="line"></view>
                       <view class="num-item">
                           <view class="val">{{ terminalBanquetInfo.tqIntegral ? terminalBanquetInfo.tqIntegral : 0 }}</view>
                           <view class="label">头曲币</view>
                       </view>
                   </view>
               </view>
           </view>
           <!-- 终端宴席坎级 -->
           <view class="terminal-model" v-if="showTerminalFlag">
               <view class="title">终端宴席坎级</view>
               <view class="date-pick-box">
                   <view class="date-pick-list">
                       <view class="date-pick-content" v-for="(date, i) in datePickList" :key="i">
                           <view
                               class="date-pick-item"
                               :class="{ active: dateValC === date.val }"
                               @tap="onTapItemC(date.val)"
                           >{{ date.name }}</view>
                       </view>
                       <view class="date-pick-content">
                           <link-date v-model="dateStartC" placeholder="按周期" :max="currentDateEnd"
                                      :min="currentDateStart" view="YM" displayFormat="YYYY-MM" valueFormat="YYYY-MM">
                               <template #default="{ display }">
                                   <view
                                       class="date-pick-item"
                                       :class="{ active: dateValD === 'custom' }"
                                       @tap="onTapItemD()"
                                       style="display: flex"
                                   >{{ dateStartC || '按周期' }}
                                       <link-icon style="height: auto;" icon="icon-down1"/>
                                   </view>
                               </template>
                           </link-date>
                       </view>
                   </view>
                   <view class="customer-month" v-show="dateEndC">至
                       <link-date v-model="dateEndC" placeholder="请选择年份月份" :max="currentDateEnd" :disabled="true"
                                  :min="currentDateStart" view="YM" displayFormat="YYYY-MM" valueFormat="YYYY-MM" />
                   </view>
               </view>
               <view>
                   <view class="content">
                       <view class="cost-put-item">
                           <view class="item-icon">
                               <image :src="$imageAssets.terminalBanquet"></image>
                           </view>
                           <view class="item-num">
                               <view class="val">{{ terminalBanquetInfoB.banquetRewardSessionCount ? terminalBanquetInfoB.banquetRewardSessionCount : 0 }}</view>
                               <view class="label">宴席坎级场次(场)</view>
                           </view>
                       </view>
                       <view class="cost-put-item">
                           <view class="item-icon">
                               <image :src="$imageAssets.terminalBanquetReward"></image>
                           </view>
                           <view class="item-num">
                               <view class="val">{{ terminalBanquetInfoB.banquetRewardVal ? terminalBanquetInfoB.banquetRewardVal : 0 }}</view>
                               <view class="label">宴席坎级奖励(元)</view>
                           </view>
                       </view>
                   </view>
               </view>
           </view>
           <!-- 终端实时返利余额 -->
           <view class="terminal-model" v-if="showTerminalFlag">
               <view>
                   <view class="title">终端实时返利余额</view>
                   <view class="content" v-if="terminalAccountData.length === 0">
                       <view class="num-item">
                           <view class="val">-</view>
                       </view>
                   </view>
                   <view v-else>
                       <view class="content">
                           <view class="num-item" style="padding-bottom: 12px" v-for="(rebate, rIndex) in terminalAccountData" :key="rIndex">
                               <view class="val">{{ (Number(rebate.remainingPoints || rebate.cashWaitNum || 0)).toFixed(2) }}</view>
                               <view class="label">{{ rebate.displayName }}</view>
                           </view>
                       </view>
                   </view>
               </view>
           </view>
           <!-- 客户等级：仅大成、鸿泸、永粮可见  -->
           <acct-level v-if="isPartPort && acctData.acctStage === 'ykf'" :acctData="acctData"/>
           <!-- 终端费投 -->
           <view class="cost-put">
               <view class="title">终端费投</view>
               <view class="content">
                   <view class="cost-put-item" @tap="openYearDetail">
                       <view class="item-icon put-bgc">
                           <link-icon icon="icon-feiyongtouru" class="icon-content"/>
                       </view>
                       <view class="item-num">
                           <view class="val">{{ terminalTag.yearDeliCost }}</view>
                           <view class="label">财年费用投入(万)</view>
                       </view>
                   </view>
                   <view class="cost-put-item" @tap="openSessionDetail">
                       <view class="item-icon activity-bgc">
                           <link-icon icon="icon-huodongchangci" class="icon-content"/>
                       </view>
                       <view class="item-num">
                           <view class="val">{{ terminalTag.saleSession }}</view>
                           <view class="label">财年活动场次(场)</view>
                       </view>
                   </view>
               </view>
           </view>
           <!-- 终端消费者 -->
           <view class="consumer">
               <view class="title">终端消费者</view>
               <view class="content">
                   <view class="num-item">
                       <view class="val">{{ terminalTag.monthConsumerContri }}</view>
                       <view class="label">本月奉献</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalTag.yearConsumerContri }}</view>
                       <view class="label">本年奉献</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalTag.sumConsumerContri }}</view>
                       <view class="label">累计奉献</view>
                   </view>
               </view>
           </view>
           <!-- 终端活跃度 -->
           <view class="consumer">
               <view class="title">终端活跃度</view>
               <view class="content">
                   <view class="num-item">
                       <view class="val">{{ terminalTag.uncodeScanDay }}</view>
                       <view class="label">未进货(天)</view>
                   </view>
                   <view class="num-item" v-if="terminalTag.codeMark&&terminalTag.codeMark!=='0'">
                       <view class="val">{{ terminalTag.codeMark }}</view>
                       <view class="label">财年扫码</view>
                   </view>
               </view>
           </view>
           <!-- 终端拜访 -->
           <view class="consumer" v-if="isChuanDong">
               <view class="title">终端拜访</view>
               <view class="content">
                   <view class="num-item">
                       <view class="val">{{ terminalTag.visitCounts }}</view>
                       <view class="label">总次数(次)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalTag.visitTotalTime }}</view>
                       <view class="label">总时长(分)</view>
                   </view>
               </view>
           </view>
           <!-- 市场活动 -->
           <view class="consumer" v-if="isChuanDong">
               <view class="title">市场活动</view>
               <view class="content">
                   <view class="num-item">
                       <view class="val">{{ terminalTag.yearPinJianHuiCounts }}</view>
                       <view class="label">年品鉴会场次(次)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalTag.yearPinJianHuiAmount }}</view>
                       <view class="label">品鉴会实发金额(万)</view>
                   </view>
                   <view class="line"></view>
                   <view class="num-item">
                       <view class="val">{{ terminalTag.yearPinJianHuiCodeCounts }}</view>
                       <view class="label">品鉴会开瓶扫码(瓶)</view>
                   </view>
               </view>
           </view>
           <!-- 其他标签 -->
           <view class="other-label">
               <view class="title">其他标签</view>
               <view class="content">
                   <view class="label-item" v-if="lovTag.doorSigns">{{ lovTag.doorSigns }}</view>
                   <view class="label-item" v-if="lovTag.ownStores">{{ lovTag.ownStores }}</view>
                   <view class="label-item" v-if="terminalTag.strategicFlag && terminalTag.strategicFlag === 'Y'">{{ terminalTag.strategicFlag | lov('STRATEGIC_TAG') }}</view>
                   <view class="label-item" v-if="terminalTag.yearOfCooperation">合作{{ terminalTag.yearOfCooperation }}年</view>
                   <view class="label-item" v-if="terminalTag.resourceBack">{{ terminalTag.resourceBack }}</view>
                   <view class="label-item" v-if="terminalTag.belongingCircle">{{ terminalTag.belongingCircle }}</view>
               </view>
           </view>
       </view>
        <view class="label-content" v-else>
            <view class="sales-and-inventory" v-if="dataList.length">
                <view class="title">终端进销存</view>
                <view>
                    <view class="content" v-for="(item, index) in dataList" :key="index+'dataList'">
                        <view class="num-item">
                            <view class="vals">{{ item.partName }}</view>
                            <view class="label">品项</view>
                        </view>
                        <view class="line"></view>
                        <view class="num-item">
                            <view class="val">{{ item.cumulativeSalesLevel }}</view>
                            <view class="label">达成等级</view>
                        </view>
                    </view>
                </view>
            </view>
            <view v-else class='noneMsg'>
                暂无数据
            </view>
        </view>
        <!-- 财年费投详情 -->
        <link-dialog class="dialog-bottom" ref="dialog" position="bottom" height="75vh" noPadding>
            <!-- 标题 -->
            <view class="dialog-title">
                <view>财年费用投入(万)</view>
                <link-icon icon="mp-close" class="close-icon" @tap="$refs.dialog.hide()"/>
            </view>
            <!-- 表格内容 -->
            <scroll-view class="list-wrap" :scroll-y="true">
                <view class="table-row-title">
                    <view class="column-title" style="width: 28%">费用小类</view>
                    <view class="column-title" style="width: 25%">现金投入金额</view>
                    <view class="column-title" style="width: 25%">产品投入金额</view>
                    <view class="column-title" style="width: 22%">合计</view>
                </view>
                <link-auto-list :option="listOption" hideCreateButton>
                    <template slot-scope="{data,index}">
                        <view :key="index" :data="data" class="table-row">
                            <view class="column" style="width: 28%">
                                <view class="text">{{data.costTypeName}}</view>
                            </view>
                            <view class="column" style="width: 25%">
                                <view class="text">{{dealAmount(data.cashRealAmount)}}</view>
                            </view>
                            <view class="column" style="width: 25%">
                                <view class="text">{{dealAmount(data.prodRealAmount)}}</view>
                            </view>
                            <view class="column" style="width: 22%">
                                <view class="text">{{dealAmount(data.totalActualAmount)}}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </scroll-view>
        </link-dialog>
        <part-code-tag :list='taglist' :showProd.sync='showProd' @chose='choseTag' />
        <!-- 财年活动场次详情 -->
        <link-dialog class="dialog-bottom" ref="session" position="bottom" height="75vh" noPadding>
            <!-- 标题 -->
            <view class="dialog-title">
                <view>财年活动场次(场)</view>
                <link-icon icon="mp-close" class="close-icon" @tap="$refs.session.hide()"/>
            </view>
            <!-- 表格内容 -->
            <scroll-view class="list-wrap" :scroll-y="true">
                <view class="table-row-title">
                    <view class="column-title" style="width: 50%">费用小类</view>
                    <view class="column-title" style="width: 50%">场次</view>
                </view>
                <link-auto-list :option="sessionOption" hideCreateButton>
                    <template slot-scope="{data,index}">
                        <view :key="index" :data="data" class="table-row">
                            <view class="column" style="width: 50%">
                                <view class="text">{{data.costType}}</view>
                            </view>
                            <view class="column" style="width: 50%">
                                <view class="text">{{data.activityCount}}</view>
                            </view>
                        </view>
                    </template>
                </link-auto-list>
            </scroll-view>
        </link-dialog>
        <view v-show='painterFlag' style="transform:scale(0.1); z-index: -100;height:1px">
            <painter :palette="paletteData" @imgOK="onImgOK"></painter>
        </view>
        <link-sticky class="bottom-sticky" v-if="isDaCheng && acctData.acctType === 'Terminal'">
            <link-button block @tap="savePoster">生成图片</link-button>
        </link-sticky>
    </view>
</template>

<script>
    import partCodeTag from './part-code-tag.vue'
    import SelectButton from "../../../echart/lzlj/components/select-button";
    import AcctLevel from './acct-level.vue';
    import BcGrabPlateVue from './BcGrabPlate.vue';
    import RealTimeRebateVue from './RealTimeRebate.vue';
    import yearLevel from './year-level.vue';
    import labelPoster from '../../../terminal/mixins/label-poster';
    import detailCard from '../../../terminal2/annual-quota-apply/components/quota-detail-card.vue'
export default {
    name: "dealer-customer-label",
    components: {
        partCodeTag,
        SelectButton,
        AcctLevel,
        yearLevel,
        BcGrabPlateVue,
        RealTimeRebateVue,
        detailCard
    },
    mixins:[labelPoster()],
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const isDaCheng = userInfo.coreOrganizationTile.brandCompanyCode === '5161'
        const listOption = new this.AutoList(this, {
            module: 'action/link/marketAct',
            url: {
                queryByExamplePage: 'action/link/marketAct/queryAmountByAccountPage',
            },
            param: () => {
                return {
                    beneficiaryId: this.acctData.id
                }
            }
        });
        const sessionOption = new this.AutoList(this, {
            module: 'action/link/marketAct',
            url: {
                queryByExamplePage: 'action/link/marketAct/queryActCountByAccountPage',
            },
            param: () => {
                return {
                    beneficiaryId: this.acctData.id
                }
            }
        })
        return {
            // 用户信息
            userInfo,
            isDaCheng,
            // 是否是分品项公司
            isPartPort: false,
            dataList: [], // 白城人员查看等级内容
            orgText:'',
            taglist: [],
            showProd: false,
            listOption,
            sessionOption,
            lovTag: {
                doorSigns: '',
                ownStores: ''
            },
            datePickListA: [],
            datePickList: [],
            dateValA: 'total',
            dateValB: 'fiscalYear',
            dateValC: 'fiscalYear',
            dateValD: '',
            dateStart: '',
            dateEnd: '',
            dateStartB: '',
            dateEndB: '',
            dateStartC: '',
            dateEndC: '',
            currentDateStart: '', //当前财年起始月
            currentDateEnd: '', //当前财年结束月
            terminalAccountData: [], //终端实时积分账户
            terminalAcctScanInfo: [], //终端扫码报表
            terminalBanquetInfo: [], //终端宴席报表
            terminalBanquetInfoB: [], //终端宴席坎级报表
            partCode: '', //当前品项的客户中类
            showTerminalFlag: false, //是否展示终端看板
            showTerminalLabel: false, // 终端标签进存销展示flag
            posterConfig: {} // 海报模版配置
        }
    },
    props: {
        acctData: {
            type: Object,
            default: () => ({})
        },
        terminalTag: {
            type: Object,
            default: () => ({})
        },
        showDropdownFlag: {
            type: Boolean,
            default: true
        },
        isChuanDong: {
            type: Boolean,
            default: false
        },
        showAll:{
            type: Boolean,
            default: true
        }
    },
    computed:{
        purchaseFileds(){
            const allLabel = [
                {label: '年度动销(件)', key: 'yearSales'},
                {label: '年度扫码动销(件)', key: 'yearScanSales'},
                {label: '年度进货(件)', key: 'yearScanCode'},
                {label: '本月动销(件)', key: 'monthSales'},
                {label: '本月扫码动销(件)', key: 'monthScanSales'},
                {label: '本月进货(件)', key: 'monthScanCode'},
                {label: '本月库存(件)', key: 'monthInventory'},
                {label: '未入库单数', key: 'unStockedOrders', isShowFiled: true},
                {label: '未入库数量', key: 'unStockedQuantity', isShowFiled: true},
                {label: '达成等级', key: 'cumulativeSalesLevel', default: '无'},
                {label: '开瓶扫码率', key: 'terOpenRate', isShowFiled: true, unit:'%'}, 
                {label: '基础配额', key: 'basicQuantity', isShowFiled: true}, 
                {label: '调整配额', key: 'adjustAppQuantity', isShowFiled: true}, 
                {label: '计划外配额', key: 'unplannedQuantity', isShowFiled: true}, 
                {label: '计划内总额', key: 'planTotalQuantity', isShowFiled: true}, 
                {label: '年度配额总额', key: 'totalQuantity', isShowFiled: true}, 
                {label: '计划内占用', key: 'planOccupy', isShowFiled: true},
                {label: '计划内已执行', key: 'planExecute', isShowFiled: true}, 
                {label: '计划内下单', key: 'planOrder', isShowFiled: true}, 
                {label: '计划内余额', key: 'planBalance', isShowFiled: true}, 
                {label: '计划外占用', key: 'unplanOccupy', isShowFiled: true}, 
                {label: '计划外已执行 ', key: 'unplanExecute', isShowFiled: true},
                {label: '计划外下单', key: 'unplanOrder', isShowFiled: true}, 
                {label: '计划外余额', key: 'unplanBalance', isShowFiled: true}, 
                {label: '年度配额余额', key: 'totalBalance', isShowFiled: true}, 
                {label: '未执行扣减', key: 'unplanDeduction', isShowFiled: true}, 
            ]
            if(!this.showTerminalLabel){
                return allLabel.filter(i=>{
                    return !i.isShowFiled
                })
            }else{
                return allLabel
            }
        }
    },
    async created() {
        const prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');

        const terminalFlag = await this.$utils.getCfgProperty('enableDisplayAccntOderTagCompanyIds');
        this.showTerminalLabel = terminalFlag.split(',').includes(this.acctData.companyId) && this.acctData.acctType === 'Terminal';

        this.isPartPort = prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
        const showTerminalBoard = await this.$utils.getCfgProperty('QW_BOARD_STATISTICS_COMPANY');
        const showTerminalBoardArea = await this.$utils.getCfgProperty('QW_BOARD_STATISTICS_AREA_ID');
        // && showTerminalBoardArea.indexOf(this.acctData.salesmanAreaId) > -1
        this.showTerminalFlag = showTerminalBoard.split(',').includes(this.acctData.mdmCompanyCode)
            && this.acctData.acctType === 'Terminal';
        this.lovTag.doorSigns = await this.$lov.getNameByTypeAndVal('DOOR_SIGNS', this.terminalTag.doorSigns)
        if(this.terminalTag.ownStores === 'Y') this.lovTag.ownStores = '经销商自有门店'
        let datePickList = await this.$lov.getLovByType('PUSH_OPERATE');
        this.datePickListA = datePickList.filter((item)=> item.val === 'total' || item.val === 'fiscalYear')
        this.datePickList = datePickList.filter((item)=> item.val === 'fiscalYear')
        this.init()
        this.getPosterConfig()
    },
    watch: {
        dateStart(val){
            let newVal = new Date(val)
            let startDate = new Date(newVal.setMonth(newVal.getMonth() + 1))
            let month = "";
            if((newVal.getMonth() +1 ) > 12){
                month = newVal.getMonth() + 1 - 12;
                this.dateEnd = startDate.getFullYear() + 1 + '-' + month;
            }else if((newVal.getMonth() +1 )>=10){
                month = newVal.getMonth()+1;
                this.dateEnd = startDate.getFullYear() + '-' + month;
            }else{
                month = "0"+(newVal.getMonth()+1);
                this.dateEnd = startDate.getFullYear() + '-' + month;
            }
            if (this.dateValA === 'month'){
                this.initTerminalAcctScanInfo()
            }
        },
        dateStartB(val){
            let newVal = new Date(val)
            let startDate = new Date(newVal.setMonth(newVal.getMonth() + 1))
            let month = "";
            const lastDay = new Date(startDate.getFullYear(), startDate.getMonth(), 0);
            const day = String(lastDay.getDate()).padStart(2, "0");
            if((lastDay.getMonth() +1 ) > 12){
                month = lastDay.getMonth() + 1 - 12;
                this.dateEndB = startDate.getFullYear() + 1 + '-' + month + '-' + day;
            }else if((lastDay.getMonth() +1 )>=10){
                month = lastDay.getMonth()+1;
                this.dateEndB = startDate.getFullYear() + '-' + month + '-' + day;
            }else{
                month = "0"+(lastDay.getMonth()+1);
                this.dateEndB = startDate.getFullYear() + '-' + month + '-' + day;
            }
            if (this.dateValB === 'month'){
                this.initTerminalBanquetInfo('B')
            }
        },
        dateStartC(val){
            let newVal = new Date(val)
            let startDate = new Date(newVal.setMonth(newVal.getMonth() + 1))
            let month = "";
            const lastDay = new Date(startDate.getFullYear(), startDate.getMonth() + 1, 0);
            const day = String(lastDay.getDate()).padStart(2, "0");
            if((lastDay.getMonth() +1 ) > 12){
                month = lastDay.getMonth() + 1 - 12;
                this.dateEndC = startDate.getFullYear() + 1 + '-' + month + '-' + day;
            }else if((lastDay.getMonth() +1 )>=10){
                month = lastDay.getMonth()+1;
                this.dateEndC = startDate.getFullYear() + '-' + month + '-' + day;
            }else{
                month = "0"+(lastDay.getMonth()+1);
                this.dateEndC = startDate.getFullYear() + '-' + month + '-' + day;
            }
            if (this.dateValD === 'custom'){
                this.initTerminalBanquetInfo('C')
            }
        }
    },
    methods: {
        // 选择品项进行筛选
        async choseTag(val){
            this.orgText = val.partName
            this.partCode = val.partCode
            const url = 'action/link/terminalTag/queryInvoicingTag'
            const param = {
                accntId: this.acctData.id,
                partCode: val.partCode,
                accntType:  this.acctData.acctType
            }
            const {result, success} = await this.$http.post(url,param)
            if(success){
                const {monthInventory, monthSales, yearSales, monthScanSales, yearScanSales, yearScanCode, monthScanCode,quotaBalance} = result
                this.$emit('setChange', result)
            }
            if (this.showTerminalFlag){
                this.initTerminalAccount()
                this.initTerminalAcctScanInfo()
                this.initTerminalBanquetInfo('all')
            }
        },
        /**
         * 初始化分品项数据
         * <AUTHOR>
         * @date	2024/01/22
         */
        async init(){
            if(this.showAll){
                const url = 'action/link/terminalTag/queryTagPartCode'
                const param = {
                    accntId: this.acctData.id
                }
                const {success,rows} = await this.$http.post(url,param)
                if(success && rows.length){
                    this.taglist = rows
                    this.choseTag(rows[0])
                }
            }else{
                const url = 'action/link/terminalTag/queryInvoicingTag'
                const param = {
                    accntId: this.acctData.id,
                    attr2: 'partList'
                }
                const {result, success} = await this.$http.post(url,param)
                if(success){
                    this.dataList = result
                }
            }
            this.initMonth()
        },
        async getPosterConfig() {
            const url = 'action/link/displayBordConfig/queryByCompanyCode'
            const param = {
                companyCode : this.acctData.salesmanBrandComCode,
            }
            const {result, success} = await this.$httpForm.post(url,param)
            if(success){
                this.posterConfig = result
            }
        },
        /**
         * 处理金额(元转换为万元，保留两位小数)
         * <AUTHOR>
         * @date	2023/12/15 15:00
         */
        dealAmount(num) {
            if (!num) return num;
            // 四舍五入保留两位小数
            return Math.round(+(Number(num) / 10000) + 'e' + 2) / Math.pow(10, 2);
        },
        /**
         * 打开费用投入详情弹窗
         * <AUTHOR>
         * @date	2023/12/12 11:53
         */
        openYearDetail() {
            this.$refs.dialog.show();
        },
        /**
         * 弹窗
         * <AUTHOR>
         * @date	2024/1/2
         */
        openSessionDetail() {
            this.$refs.session.show();
        },
        onTapItemA(data) {
            this.dateValA = data
            if (data !== 'month'){
                this.initTerminalAcctScanInfo()
            }else if (this.dateStart) this.initTerminalAcctScanInfo()
        },
        onTapItemB(data) {
            this.dateValB = data
            if (data !== 'month'){
                this.initTerminalBanquetInfo('B')
            }else if (this.dateStartB) this.initTerminalBanquetInfo('B')
        },
        onTapItemC(data) {
            this.dateValC = data
            this.dateValD = ''
            this.initTerminalBanquetInfo('C')
        },
        onTapItemD() {
            this.dateValC = 'custom'
            this.dateValD = 'custom'
            if (this.dateStartC) this.initTerminalBanquetInfo('C')
        },
         /**
         * @method:终端实时积分账户
         * @author: 肖雪
         * @date:  2024/7/9 14:48
         */
        async initTerminalAccount(){
             const url = '/exportloyalty/loyalty/link/qwBoardStatistics/getBoardByMClassAndAcctCodeForQw'
             const param = {
                 acctId: this.acctData.id,
                 prodBusinessMClass: this.partCode,
             }
             const res = await this.$http.post(url, param)
             if (res.success && res.data) {
                 this.terminalAccountData = []
                 let accountData = []
                 if (!!res.data.qwBoardCashWaitNumStatistics && !!res.data.qwBoardPointAccountDtoList){
                     accountData = res.data.qwBoardPointAccountDtoList.concat(res.data.qwBoardCashWaitNumStatistics)
                 }else if (!!res.data.qwBoardCashWaitNumStatistics){
                     accountData = res.data.qwBoardPointAccountDtoList
                 }
                 this.$set(this, 'terminalAccountData', accountData);
             }
        },
         /**
         * @method:  终端扫码指标
         * @author: 肖雪
         * @date:  2024/7/9 16:50
         */
        async initTerminalAcctScanInfo(){
             const url = '/exportterminal/link/acctScanData/queryAcctScanInfoByClassAndTime'
             let param = {
                 acctId: this.acctData.id,
                 item: this.partCode,
                 timeDimension: this.dateValA,
             }
             if (this.dateValA === 'month')
                 param = {
                     acctId: this.acctData.id,
                     item: this.partCode,
                     timeDimension: this.dateValA,
                     month: new Date(this.dateStart).getMonth() + 1
                 }
             const res = await this.$http.post(url, param)
             if (res.success && res.result) {
                 this.terminalAcctScanInfo = res.result
             }
        },
        /**
         * @method:  终端宴席坎级
         * @author: 肖雪
         * @date:  2024/7/9 16:50
         */
        async initTerminalBanquetInfo(type){
             const url = '/exportterminal/link/qwBoardStatistics/getBoardByMClassAndAcctCodeForQw'
             let param = {
                 acctId: this.acctData.id,
                 prodBusinessMClass: this.partCode,
                 startTime: this.currentDateStart + ' 00:00:00',
                 endTime: this.currentDateEnd
             }
             if (this.dateValC === 'custom' && type === 'C') {
                 param = {
                     acctId: this.acctData.id,
                     prodBusinessMClass: this.partCode,
                     startTime: this.dateStartC + '-01 00:00:00',
                     endTime: this.dateEndC + ' 23:59:59'
                 }
                 const res = await this.$http.post(url, param)
                 if (res.success && res.data) {
                     this.terminalBanquetInfoB = res.data
                 }
             }
             else if (this.dateValB === 'month' && type === 'B') {
                 param = {
                     acctId: this.acctData.id,
                     prodBusinessMClass: this.partCode,
                     startTime: this.dateStartB + '-01 00:00:00',
                     endTime: this.dateEndB + ' 23:59:59'
                 }
                 const res = await this.$http.post(url, param)
                 if (res.success && res.data) {
                     this.terminalBanquetInfo = res.data
                 }
             } else {
                 const res = await this.$http.post(url, param)
                 if (res.success && res.data) {
                     this.terminalBanquetInfo = res.data
                     this.terminalBanquetInfoB = res.data
                 }
             }
        },
         /**
         * @method: 计算当前财年起始结束月
         * @author: 肖雪
         * @date:  2024/7/9 16:11
         */
         initMonth(){
             const year = new Date().getFullYear();
             let month = new Date().getMonth() + 1;
             let day = new Date().getDay() + 1;
             this.currentDateStart = `${year - 1}-11-01`
             this.currentDateEnd = this.$date.format(new Date(), 'YYYY-MM-DD HH:mm:ss');
         }
    }
}
</script>

<style lang="scss">
.dealer-customer-label {
    // display: flex;
    // justify-content: center;
    background: #ffffff;

    .lnk-tabs {
        overflow: hidden;
        &::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            color: transparent !important;
        }
    }

    .noneMsg{
        text-align: center;
        line-height: 300px;
    }
    .label-content {
        padding: 24px;
        width: 100%;
        .title {
            font-family: PingFangSC-Medium;
            font-size: 32px;
            font-weight: 500;
        }
        .val {
            font-size: 26px;
            font-weight: 500;
            margin-bottom: 10px;
        }
        .vals{
            font-size: 26px;
            font-weight: 500;
            color: #2F69F8;
            margin-bottom: 10px;
        }
        .label {
            font-size: 22px;
            color: #666666;
            font-weight: 400;
        }
        .content-wrap {
            display: flex;
            flex-wrap: wrap;
            margin-bottom: 24px;
            line-height: 36px;
            .content-item {
                margin-top: 24px;
                width: 33%;
                @include flex-start-center;

                .num-item {
                    width: 100%;
                    text-align: center;
                }

                .line {
                    width: 1px;
                    height: 41px;
                    margin: 16px 0;
                    background-color: #c7c7c7;;
                }

                &:nth-child(3n+1) {
                    .line {
                        display: none;
                    }
                }
            }
        }
        .sales-and-inventory, .consumer, .terminal-model {
            margin-left: 14px;
            position: relative;
            .date-pick-box {
                padding-top: 20px;
                display: flex;
                margin-right: 4px;
                margin-bottom: 8px;
                .customer-month{
                    display: flex;
                    align-items: center;
                }
                .date-pick-list {
                    display: flex;
                    align-items: center;
                    height: 56px;
                    .date-pick-content {
                        padding: 0 4px;
                        .date-pick-item {
                            border-radius: 28px;
                            min-width: 116px;
                            padding: 0 4px;
                            height: 56px;
                            font-size: 28px;
                            color: #212223;
                            text-align: center;
                            line-height: 56px;
                            font-weight: 400;
                        }
                        .active {
                            background: #3F66EF;
                            border-radius: 28px;
                            color: #ffffff;
                        }
                    }
                }
            }
            .tag-search{
                position: absolute;
                top: 0;
                right: 34px;
                .search-box{
                    display: flex;
                    justify-content: center;
                    font-size: 28px;
                    line-height: 48px;
                }
            }
            .banquet-content{
                background: linear-gradient( 90deg, #EBECFF 0%, #EFF6FE 100%);
                border-radius: 8px;
                margin: 16px 32px 12px 18px;
                height: 80px;
                font-size: 28px;
                color: #333333;
                line-height: 80px;
                padding-left: 24px;
            }
            .content {
                display: flex;
                .cost-put-item {
                    display: flex;
                    flex: 1;
                    .item-icon {
                        width: 88px;
                        height: 88px;
                        image{
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .item-num {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        padding: 4px 0;
                        margin-left: 20px;
                    }
                }
                .num-item {
                    flex: 1;
                    text-align: center;
                    .icon{
                        width: 88px;
                        height: 88px;
                        image{
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
                .num-item2 {
                    width: 33%;
                    text-align: center;
                    margin-left: 4px;
                }
                .line {
                    width: 1px;
                    margin: 16px 0;
                    background-color: #c7c7c7;;
                }
            }
            .echartsView {
                width: 100%;
                height: 50vh;
            }
        }
        .terminal-model{
            margin-top: 60px;
            .content{
                flex-wrap: wrap;
                .num-item{
                    min-width: 30%;
                    flex: 0.33;
                }
            }
            .middle-title {
                font-size: 28px;
                padding-left: 42px;
            }
        }
        .activity {
            margin-left: 14px;
            .num-item {
                width: 33%;
                text-align: center;
                margin: 24px 0;
            }
        }
        .cost-put {
            margin-left: 14px;
            .content {
                display: flex;
                .cost-put-item {
                    display: flex;
                    flex: 1;
                    .item-icon {
                        height: 100px;
                        width: 100px;
                        border-radius: 50px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        .icon-content {
                            font-size: 48px;
                            color: white;
                        }
                    }
                    .put-bgc {
                        background-image: linear-gradient(180deg, #FF8154 0%, #FF4D19 100%);
                    }
                    .activity-bgc {
                        background-image: linear-gradient(180deg, #6F61F8 0%, #4E3FF2 100%);
                    }
                    .item-num {
                        display: flex;
                        flex-direction: column;
                        justify-content: space-between;
                        padding: 4px 0;
                        margin-left: 20px;
                    }
                }
            }
        }
        .other-label {
            margin-left: 14px;
            .content {
                display: flex;
                flex-wrap: wrap;
                .label-item {
                    background-color: #eaeaff;
                    color: #2F69F8;
                    padding: 16px 20px;
                    margin: 10px 16px;
                    font-size: 30px;
                    border-radius: 13px;
                }
            }
        }
    }

    .dialog-bottom {

        .dialog-title {
            padding: 24px;
            position: relative;
            font-size: 32px;
            color: #333333;
            font-weight: 500;
            @include flex-center-center;

            .close-icon {
                position: absolute;
                right: 24px;
                color: #999;
                font-size: 32px;
            }
        }

        .list-wrap {

            .table-row-title {
                display: flex;
                width: 100%;
                background: #6D96FA;

                .column-title {
                    font-size: 24px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 72px;
                    line-height: 72px;
                    text-align: center;
                    color: #fff;
                }
            }

            .link-auto-list-wrapper {
                padding: 0;
            }

            .table-row {
                display: flex;
                width: 100%;

                .column {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 112px;
                    line-height: 112px;
                    border-right: 2px solid #DEE7FE;
                    text-align: center;
                    border-bottom: 2px solid #DEE7FE;

                    .text{
                        display: inline-block;
                        color: #262626;
                        font-size: 24px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
    }
}
</style>
