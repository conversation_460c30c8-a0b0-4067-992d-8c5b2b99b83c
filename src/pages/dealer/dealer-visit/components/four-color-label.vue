<!--
 * @Description: 四色标签子页签
 * @Author: 邓佳柳
 * @Date: 2024-11-12
-->
<template>
	<view class="dealer-four-color-label">
		<link-auto-list :option="option">
			<template v-slot="{data, index}">
				<item :key="index" :data="data" :arrow="false" class="four-color-label-wrap">
					<view class="four-color-label-item" slot="note">
						<view class="item-top">
							<color-tag :value="data.tagName" />
							<status-button :type="data.status === 'N' ? 'gray' : 'normal'" :label="data.status | lov('REBATE_LIST_STATUS')" />
						</view>
						<view class="info-item">
							<view class="info-label">生效开始时间</view>
							<view class="info-value">{{ data.activeStartDate | date('YYYY-MM-DD HH:mm') }}</view>
						</view>
						<view class="info-item">
							<view class="info-label">生效结束时间</view>
							<view class="info-value">{{ data.activeEndDate | date('YYYY-MM-DD HH:mm') }}</view>
						</view>
					</view>
				</item>
			</template>
		</link-auto-list>
	</view>
</template>
<script>
	import StatusButton from './status-button';
	import ColorTag from '../../../terminal2/components/ColorTag.vue';

	export default {
		name: 'dealer-four-color-label',
		components: { StatusButton, ColorTag },
		props: ['id'], //终端编码
		data() {
			let url = '/loyalty/loyalty/accntblacklist/queryByExamplePage'
			const option = new this.AutoList(this, {
				url: { queryByExamplePage: url },
				param: {
					filtersRaw: [
						{id: "acctCode", property: "acctCode", value: this.id, operator: "="},
						{id: 'status', property: 'status', operator: '=', value: 'Y'}
					]
				},
				hooks: {}
			});
			return {
				option
			};
		}
	};
</script>
<style lang="scss">
	.dealer-four-color-label-wrap {
		border-radius: 12px;
		margin: 24px 0;
		.item-top {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-bottom: 8px;
			.color-tag {
				margin-top: 0px;
			}
		}
		.info-item {
			width: 100%;
			font-size: 28px;
			display: flex;
			margin-bottom: 8px;
			line-height: 44px;
			align-items: center;
			.info-label {
				color: #666;
				margin-right: 10px;
			}

			.info-value {
				color: #333;
			}
		}
	}
</style>
