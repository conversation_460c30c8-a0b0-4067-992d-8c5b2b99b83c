<!--
陈列检查
<AUTHOR>
@date 2024-05-06
-->
<template>
    <view class="collabora-msg">
        <link-form ref="form" :value="form" class="content" v-if="form.id">
            <view class="can-edit" @tap='goEdit'>编辑</view>
            <link-form-item label="已合作陈列品牌">
                <text>{{form.collaboraBrands | lov('BRAND_COM_NAME')}}</text>
            </link-form-item>
            <link-form-item label="陈列未合作品牌">
                <text>{{form.uncollaboraBrands | lov('BRAND_COM_NAME')}}</text>
            </link-form-item>
             <!-- <check-msg :id='id' :form='form'></check-msg> -->
             <link-form-item label="客户名称是否正确" v-if="form.nameCorrect">
                 <text>{{form.nameCorrect | lov('IS_FLAG')}}</text>
             </link-form-item>
             <link-form-item label="客户名称描述"  v-if="form.nameDescription">
                 <text>{{form.nameDescription}}</text>
             </link-form-item>
             <link-form-item label="门店照片是否合规"  v-if="form.photoQualified">
                 <text>{{form.photoQualified | lov('IS_FLAG')}}</text>
             </link-form-item>
             <link-form-item label="门店照片不合规原因"  v-if="form.photoNoncompliance">
                 <text>{{form.photoNoncompliance | lov('PHOTO_COMP')}}</text>
             </link-form-item>
             <link-form-item label="门店照片描述"  v-if="form.photoDescription">
                 <text>{{form.photoDescription}}</text>
             </link-form-item>
             <link-form-item label="附件照片" ></link-form-item>
             <lnk-img-watermark :parentId="id"
                             v-if="id"
                              :moduleType="moduleType"
                              :moduleName="moduleName"
                              :continueFlag="false"
                              :delFlag="false"
                              :album="false"
                              :useModuleName="formOption.frontPhoto"
                              :maxCount="1"
                              :newFlag="false"/>
             <link-form-item label="地址是否正确"  v-if="form.addressCorrect">
                 <text>{{form.addressCorrect | lov('IS_FLAG')}}</text>
             </link-form-item>
             <link-form-item label="地址描述"  v-if="form.addressDescription">
                 <text>{{form.addressDescription | lov('ADDRESS_DESCRI')}}</text>
             </link-form-item>
        </link-form>
        <view v-else class="none-box">
            暂无数据
        </view>
    </view>
</template>

<script>
    // import checkMsg from "./check-msg";
    import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';
    export default {
        name: 'collabora-msg',
        props:{
            form: Object,
            id: String
        },
        components: {
            LnkImgWatermark
        },
        data(){
            return {
                moduleType: 'terminalCheck',
                moduleName: '门店照片',
                formOption: {
                    acctName: '',
                    storeUrl:'',
                    addrDetailAddr: '',
                    frontPhoto: '',
                }
            }
        },
        methods:{
            /**
             *  编辑
             *  @auth 谭少奇
             *  @date 2024/05/06
             */
            goEdit(){
                this.$emit('toEdit')
            }
        }
    }
</script>

<style lang="scss">
    .collabora-msg{
        .none-box{
            text-align: center;
            padding-top: 100px;
        }
        padding: 20px;
        .content{
            padding: 20px;
            background: #fff;
        }
        .can-edit{
            text-align: right;
            color:#2F69F8;
            font-size: 28px;
        }
        .show-img{
            width: 100px;
            height: 100px;
        }
    }

</style>
