<!-- 转盘收益--这里只展示转盘收益因此不抽组件 -->
<template>
  <view class="my-revenue">
    <!-- 查询维度 -->
    <TimeDimensionCmp title="我的收益" @change="handlerClick"/>
    <!-- 按维度查询展示信息 -->
    <view class="my-revenue__display">
      <view class="my-revenue__display--title">
        <!-- 图标 -->
        <link-icon icon="icon-revenue"></link-icon>
        <text class="revenue">转盘收益</text>
      </view>
      <view class="my-revenue__display--content">
        <view class="item" v-for="(item, index) in result.turntableIncome" :key="index">
            <view class="val">{{ item.value || 0 }}</view>
          <view class="label">{{ item.name }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
import TimeDimensionCmp from './TimeDimensionCmp.vue';
export default {
  name: 'dealer-turn-table-revenue',
  data() {
    return {
      active: 'day',
      result: {}
    }
  },
  components: {
    TimeDimensionCmp
  },
  props: {
    acctData: {
      type: Object,
      default: () => ({}),
    },
    dataBoardActive: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    dataBoardActive: {
      handler(v) {
        const { seq, programId } = this.dataBoardActive;
        const { id } = this.acctData;
        if (seq && programId && id) {
          this.initData();
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    handlerClick(element) {
      this.active = element;
      this.initData();
    },
    async initData() {
      const params =  {
        attr1: 'QWQuery',
        acctId: this.acctData.id,
        conditionId: this.dataBoardActive.seq,
        programId: this.dataBoardActive.programId,
        selectModelType: 'turntableIncome',
        scope: this.active
      }
      try {
       const res = await this.$http.post("/exportloyalty/loyalty/condition/myMpProfit", params, {autoHandleError: false});
        if (res && res.result && Object.keys(res.result).length && res.result.turntableIncome) {
          this.result = res.result
        } else {
          this.result = {}
        }
      } catch (error) {
        console.log(error)
      }
    }
  }
}
</script>
<style lang="scss">
@import "../../../terminal/terminal/style/terminal-label/turnTableRevenue";
</style>
