<template>
    <view class="dealer-visit-progress-list">
        <view class="blank"></view>
        <link-auto-list :option="visitProcessList" hideCreateButton>
            <template slot="other" v-if="$utils.isPostnOauth() !== 'MY_ORG' || postnFlag">
                <link-fab-button  @tap="onClickItem"/>
            </template>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="media-list-rows" @tap="goToItem(data)">
                    <view slot="note" class="visit-list">
                        <view class="visit-content">
                            <view class="visit-top">
                                <view class="store-time">
                                    <view class="out-circle">
                                        <view class="dot-circle"></view>
                                    </view>
                                    <view class="visit-time">
                                        <view class="date">{{data.visitTime | date('YYYY-MM-DD') || data.created | date('YYYY-MM-DD')}}</view>
                                        <view class="week" v-if="data.visitTime">{{data.visitTime | week}}</view>
                                        <view class="week" v-else>{{data.created | week}}</view>
                                        <view class="week">{{data.createdName}}</view>
                                    </view>
                                </view>
                            </view>
                            <view class="visit-start-time">
                                <view class="visit-duration">
                                    <view class="time" v-if="data.visitTime">{{data.visitTime | date('HH:mm')}}</view>
                                    <view class="time" v-else>{{data.created | date('HH:mm')}}</view>
                                    <view class="interim-left">···</view>
                                    <view class="duration">未结束</view>
                                </view>
                                <view class="location" v-if="data.specialDescription">
                                    <text class="iconfont icon-aim"></text> {{data.specialDescription}}
                                </view>
                            </view>
                            <view class="visit-title">{{data.accntName}}</view>
                            <view class="visit-address">{{data.orieLocation}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    import Taro from '@tarojs/taro';
    export default {
        name: "dealer-visit-progress-list",
        data () {
            let detailsOauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();
            if (detailsOauth === 'MY_ORG') {
                detailsOauth = 'MULTI_ORG'
            }
            return {
                radius: null, //打卡半径
                checkOutOver:false, //超距离离店
                postnFlag: ['SalesRegionManager', 'SalesAreaManager'].includes(this.$taro.getStorageSync('token').result.positionType),
                punchCardRecord: {},                // 打卡记录
                coordinate: null,                   // 定位坐标
                addressData: null,                  // 打卡记录
                dateTime: null,                     // 当前时间
                visitRecord: {},                    // 登录用户的拜访记录
                mustInputArr: [],                   // 拜访采集必填项目
                detailsOauth,
                openSettingNum: 1,//授权次数 默认为1 如果没授权的情况下 5G网络 定位问题 第一次先授权只要授过权次数加1 当次数>1 还是拿不到定位就给默认信息吧....
            }
        },
        props: {
            visitProcessList: {
                type: Object,
                default: {}
            },
        },
        async created () {
            await this.initData();
            if (this.$utils.isEmpty(this.$store.getters['dealerConfigTemplate/getDealerVisitPerformTemp'])) {
                this.$dealerConfigTemplate.dealerVisitPerformTemp();
            }
            const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
            if(this.$taro.getStorageSync('token').result.coreOrganizationTile.l5Id === chuanDongOrgId) {
                //lzljqw-004-301川东打卡范围缩小至100m
                this.radius = 100
            } else {
                //其他公司-走参数配置
                this.radius = await this.getFirmParam();
            }
        },
        methods: {
            /**
             * 获取系统允许打卡半径范围
             * @date 2023-05-17
             */
            getFirmParam () {
                return new Promise(resolve => {
                    if (this.$taro.getStorageSync('radius')) {
                        resolve(this.$taro.getStorageSync('radius'));
                    } else {
                        this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                            key: 'signInLimitDistance'
                        }).then(data => {
                            if (data.success) {
                                this.$taro.setStorageSync('radius', Number(data.rows[0].value));
                                resolve(Number(data.rows[0].value));
                            }
                        })
                    }
                })
            },
            queryUserVisit () {
                const that = this;
                return new Promise(resolve => {
                    that.$http.post('action/link/accntVisit/queryByExamplePage', {
                        filtersRaw: [
                            {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: 'visiting', operator: '='},
                            {id: 'visitType', property: 'visitType', value: 'dailySalesCall', operator: '='},
                            {id: 'createdBy', property: 'createdBy', value: that.$taro.getStorageSync('token').result.id, operator: '='},
                        ],
                        oauth: this.detailsOauth
                    }).then(data => {
                        if (data.success) {
                            this.$store.commit('visitRecord/setVisitRecord', data.rows[0]);
                            resolve(data.rows);
                        }
                    })
                });
            },
            async onClickItem() {
                const that = this;
                let userInfo = that.$taro.getStorageSync('token').result;
                let queryUserVisit = await that.queryUserVisit();
                /*this.$aegis.report({
                    msg: '拜访新建方法onClickItem',
                    ext1: JSON.stringify(that.coordinate),
                    trace: 'error'
                });*/
                let net = "";
                await Taro.getNetworkType({
                    success (res) {
                        net = res.networkType
                    }
                });
                //匹配5G 某些情况下 定位获取不到的问题
                if(that.coordinate.errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'
                    || that.coordinate.errMsg === 'getLocation:fail system permission denied'
                    || that.coordinate.errMsg === 'getLocation:fail:system permission denied'){
                    let networkType = "";
                    await Taro.getNetworkType({
                        success (res) {
                            networkType = res.networkType
                        }
                    });
                    if(networkType === '5g' && this.openSettingNum > 1){
                        if (queryUserVisit.length === 0) {
                            that.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {pageFlag: 'visitListPage', createdVisitOriginal: 'visitListCreated'});
                            return
                        }
                        let verifyPassArr = await that.verifyMustCollect(queryUserVisit[0].id, queryUserVisit[0].accntId);        // 拜访执行必输采集校验
                        that.dateTime = new Date(await that.$utils.getTimestamp());
                        if (userInfo.id === that.visitRecord.createdBy || queryUserVisit.length !== 0) {
                            that.$dialog({
                                title: '提示',
                                content: '确认要结束当前拜访，新建拜访吗？',
                                cancelButton: true,
                                initial: true,
                                onConfirm: async () => {
                                    if (verifyPassArr.length !== that.mustInputArr.length) {
                                        that.$dialog({
                                            title: '提示',
                                            content: '上次拜访有必填项尚未填写，您可以强制关闭或者继续执行上次拜访',
                                            cancelButton: true,
                                            initial: true,
                                            cancelText: '强制关闭',
                                            confirmText: '继续执行',
                                            onConfirm:() => {
                                                if(queryUserVisit[0].acctStatus==='N'){
                                                    that.$showError('当前终端已被冻结，请生效后操作！');
                                                    return;
                                                }
                                                that.$nav.push('/pages/dealer/dealer-visit/visit-perform-page', {
                                                    data: queryUserVisit[0],
                                                    source:'viewDetail'
                                                })
                                            },
                                            onCancel: () => {
                                                let visitStart = that.$utils.isEmpty(queryUserVisit[0].visitTime) ? queryUserVisit[0].created : queryUserVisit[0].visitTime;
                                                that.forceClose(queryUserVisit[0].id, visitStart, queryUserVisit[0].accntId)
                                            }
                                        })
                                    } else {
                                        that.updatePunchCardRecord('closeVisit', queryUserVisit[0].accntId);     // 结束拜访插入离店记录
                                    }
                                },
                                onCancel: () => {
                                }
                            })
                        } else {
                            that.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {pageFlag: 'visitListPage', createdVisitOriginal: 'visitListCreated'});
                        }
                    } else {
                        if (this.$utils.isEmpty(this.coordinate.latitude) && this.$utils.isEmpty(this.coordinate.longitude)) {
                            this.$dialog({
                                title: '提示',
                                content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                                cancelButton: false,
                                initial: true,
                                confirmText: '去开启',
                                onConfirm: async () => {
                                    let userLocation = await this.$locations.openSetting();
                                    if (userLocation['scope.userLocation']) {
                                        await that.initData();
                                    }
                                }
                            });
                            this.openSettingNum ++;
                            return
                        }
                    }
                }
                if (that.$utils.isEmpty(that.coordinate.latitude) && that.$utils.isEmpty(that.coordinate.longitude)) {
                    that.$dialog({
                        title: '提示',
                        content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                        cancelButton: false,
                        initial: true,
                        confirmText: '去开启',
                        onConfirm: async () => {
                            let userLocation = await that.$locations.openSetting();
                           /* this.$aegis.report({
                                msg: '拜访新建方法onClickItem-userLocation',
                                ext1: JSON.stringify(userLocation),
                                trace: 'log'
                            });*/
                            if (userLocation['scope.userLocation']) {
                                await that.initData();
                            }
                        }
                    });
                    this.openSettingNum ++;
                    return
                }
                if (queryUserVisit.length === 0) {
                    that.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {pageFlag: 'visitListPage', createdVisitOriginal: 'visitListCreated'});
                    return
                }
                let verifyPassArr = await that.verifyMustCollect(queryUserVisit[0].id, queryUserVisit[0].accntId);        // 拜访执行必输采集校验
                this.getDealer(queryUserVisit[0].accntId)
                that.dateTime = new Date(await that.$utils.getTimestamp());
                if (userInfo.id === that.visitRecord.createdBy || queryUserVisit.length !== 0) {
                    that.$dialog({
                        title: '提示',
                        content: '确认要结束当前拜访，新建拜访吗？',
                        cancelButton: true,
                        initial: true,
                        onConfirm: async () => {
                            if (verifyPassArr.length !== that.mustInputArr.length) {
                                that.$dialog({
                                    title: '提示',
                                    content: '上次拜访有必填项尚未填写，您可以强制关闭或者继续执行上次拜访',
                                    cancelButton: true,
                                    initial: true,
                                    cancelText: '强制关闭',
                                    confirmText: '继续执行',
                                    onConfirm:() => {
                                        if(queryUserVisit[0].acctStatus==='N'){
                                            that.$showError('当前终端已被冻结，请生效后操作！');
                                            return;
                                        }
                                        that.$nav.push('/pages/dealer/dealer-visit/visit-perform-page', {
                                            data: queryUserVisit[0],
                                            source:'viewDetail'
                                        })
                                    },
                                    onCancel: () => {
                                        let visitStart = that.$utils.isEmpty(queryUserVisit[0].visitTime) ? queryUserVisit[0].created : queryUserVisit[0].visitTime;
                                        that.checkOutDialog(queryUserVisit[0].id, visitStart, queryUserVisit[0].accntId)
                                    }
                                })
                            } else {
                                that.checkOutUpdateDialog('closeVisit', queryUserVisit[0].accntId, queryUserVisit[0].id);     // 结束拜访插入离店记录
                            }
                        },
                        onCancel: () => {
                        }
                    })
                } else {
                    that.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {pageFlag: 'visitListPage', createdVisitOriginal: 'visitListCreated'});
                }
            },
            /**
             * 获取终端详情
             * @param id
             */
            getDealer(id){
                this.$http.post('action/link/accnt/queryById', {
                    id: id
                }).then(async (data) => {
                        if (data.success) {
                            let distance = await this.$locations.getDistance(Number(this.coordinate.latitude), Number(this.coordinate.longitude), Number(data.result.latitude), Number(data.result.longitude));
                           /* this.$aegis.report({
                                msg: '离店打卡计算距离',
                                ext1: JSON.stringify(distance * 1000),
                                trace: 'log'
                            });*/
                             this.checkOutOver=distance* 1000>this.radius;
                        }
                })
            },
            async verifyMustCollect (visitId, accntId) {
                const that = this;
                let visitTemplateData = this.$store.getters['dealerConfigTemplate/getDealerVisitPerformTemp'];
                that.mustInputArr = visitTemplateData.filter(item => item.base.require === true);
                let verifyPass = [];
                let i = 0;
                return new Promise(resolve => {
                    visitTemplateData.forEach(item => {
                        if (item.base.require) {
                            that.$http.post(item.values.queryUrl, {
                                visitId: visitId,
                                accntId: accntId
                            }).then(data => {
                                i++;
                                if (data.rows.length !== 0) {
                                    item.verifyMustFlag = true;
                                    verifyPass.push(item);
                                    if (i === that.mustInputArr.length) {
                                        resolve(verifyPass)
                                    }
                                } else {
                                    if (i === that.mustInputArr.length) {
                                        resolve(verifyPass)
                                    }
                                }
                            })
                        }
                    });
                })
            },

             checkOutDialog(visitId, visitTime, accntId){
                if(this.checkOutOver){
                    this.$dialog({
                        title: '提示',
                        content: '您已超距离离店是否强制结束拜访',
                        cancelButton: true,
                        initial: true,
                        cancelText: '取消',
                        confirmText: '确定',
                        onConfirm:() => {
                            this.forceClose(visitId, visitTime, accntId)
                        },
                        onCancel: () => {
                        }
                    });
                }else{
                    this.forceClose(visitId, visitTime, accntId)
                }
            },
            async forceClose(visitId, visitTime, accntId) {
                const that = this;
                let visitEndTime = that.$date.format(that.dateTime, 'YYYY-MM-DD HH:mm:ss');                                                                 // 打卡时间转换格式
                let visitDuration = await that.$utils.getYMDHMS(Date.parse(new Date(visitTime.replace(/-/g, '/'))), Date.parse(new Date(visitEndTime.replace(/-/g, '/'))));    // 计算拜访时长
                this.$http.post('action/link/accntVisit/closeVisit', {
                    id: visitId,
                    visitDuration: visitDuration,
                    visitEndTime: visitEndTime,
                    checkOutSpecialDescription: that.checkOutOver ? '离店定位偏移' : null,
                }, {
                    handleFailed(error) {
                    }
                }).then(data => {
                    if (data.success) {
                        this.updatePunchCardRecord('forceClose', accntId, visitId);
                        let opt = {
                            input: {id: visitId, visitDuration: visitDuration, visitEndTime: visitEndTime},
                            output: data
                        };
                        /*that.$aegis.report({
                            msg: '用户触发强制关闭当前拜访成功',
                            ext1: JSON.stringify(opt),
                            trace: 'log'
                        });*/
                    }
                })
            },
            async updateVisitStatus() {
                const that = this;
                that.visitRecord = that.$store.getters['visitRecord/getVisitRecord'];
                let timestamp = await this.$utils.getTimestamp();                                                               // 服务器获取当前打卡时间
                let visitEndTime = this.$date.format(new Date(timestamp), 'YYYY-MM-DD HH:mm:ss');                               // 打卡时间转换格式
                let visitStartTime = that.$utils.isEmpty(that.visitRecord.visitTime) ? that.visitRecord.created.replace(/-/g, '/') : that.visitRecord.visitTime.replace(/-/g, '/');
                let visitDuration = await this.$utils.getYMDHMS(Date.parse(new Date(visitStartTime)), Date.parse(new Date(visitEndTime.replace(/-/g, '/'))));    // 计算拜访时长
                that.visitRecord.visitApplicationStatus = 'visited';                                               // 修改拜访状态
                that.visitRecord.visitEndTime = visitEndTime;                                                      // 拜访结束时间
                that.visitRecord.visitDuration = visitDuration;// 拜访时长
                that.visitRecord.checkOutSpecialDescription= that.checkOutOver ? '离店定位偏移' : null;
                that.$http.post('action/link/accntVisit/update', that.visitRecord).then(async (data) => {
                    if (data.success) {
                        that.$bus.$emit('visitCreatedSuccess');                                                                 // 广播事件用于刷新拜访列表
                        that.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {pageFlag: 'visitListPage', createdVisitOriginal: 'visitListCreated'});    // 跳转到终端地图列表
                    }
                });
            },
             checkOutUpdateDialog(flag, accntId, visitId){
                if(this.checkOutOver){
                    this.$dialog({
                        title: '提示',
                        content: '您已超距离离店是否结束拜访',
                        cancelButton: true,
                        initial: true,
                        cancelText: '取消',
                        confirmText: '确定',
                        onConfirm:() => {
                            this.updatePunchCardRecord(flag, accntId, visitId)
                        },
                        onCancel: () => {
                        }
                    });
                }else{
                    this.updatePunchCardRecord(flag, accntId, visitId)
                }
            },
            updatePunchCardRecord (flag, accntId, visitId) {
                const that = this;
                let insetOptions = {
                    signInType: 'checkOut',
                    // signInTime: that.$date.format(that.dateTime, 'YYYY-MM-DD HH:mm:ss'),
                    province: that.addressData.province,
                    city: that.addressData.city,
                    district: that.addressData.district,
                    address: `${that.addressData.street}${that.addressData.street_number}`,
                    longitude: that.coordinate.longitude,
                    latitude: that.coordinate.latitude,
                    signInStatus: that.checkOutOver? 'abnormal' : 'normal',
                    acctId: that.visitRecord.accntId || accntId,
                    headId: that.visitRecord.id || visitId,
                    visitStatus: flag === 'forceClose' ? 'N' : 'Y'
                };
                that.$http.post('action/link/accntSignInDetails/insert', insetOptions).then((data) => {
                    if (data.success && flag === 'closeVisit') {
                        that.updateVisitStatus() // 更新当前拜访中记录
                    }
                    if (flag === 'forceClose') {
                        that.$bus.$emit('visitCreatedSuccess');
                        this.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {pageFlag: 'visitListPage', createdVisitOriginal: 'visitListCreated'});
                    }
                })
            },
            async initData() {
                this.coordinate = await this.$locations.getCurrentCoordinate();
                /*this.$aegis.report({
                    msg: '拜访模块地址initData',
                    ext1: JSON.stringify(this.coordinate),
                    trace: 'log'
                });*/
                if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                    let address =  await this.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '拜访定位');
                    this.addressData = address['originalData'].result.addressComponent
                }
            },
            async getContactList(id) {
                if(!id) return []
                const {rows} = await this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: id,
                });
                return rows
            },
            async goToItem(data) {
                if(data.acctStatus === 'N'){
                    this.$showError('当前终端已被冻结，请生效后操作！');
                    return
                }
              const accntId = data.accntId
              try {
                const resp = await this.$http.post('action/link/accntVisit/queryById', {id: data.id});
                if (!resp.success) {
                  this.$showError('查询拜访详情失败！' + resp.result);
                  return;
                }
                data = resp.result;
              } catch (e) {
                this.$showError('查询拜访详情出错！');
              }
              if (this.$utils.isEmpty(this.coordinate)) {
                    let userLocation = await this.$locations.openSetting();
                    if (userLocation['scope.userLocation']) {
                        let coordinate = await this.$locations.getCurrentCoordinate();
                        this.$store.commit('coordinate/setCoordinate', coordinate);
                        await this.initData();
                    }
                } else {
                    if(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1612') {
                      const contactList = await this.getContactList(accntId) || []
                      const flag = contactList.some((item => {
                        return item.isEffective === 'Y'
                      }))
                      if(!flag) {
                        this.$dialog({
                          title: '提示',
                          content: '请完善联系人信息',
                          initial: true,
                          confirmText: '确认'
                        });
                        return
                      }
                    }
                    this.$nav.push('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                        data: data,
                        source:'viewDetail'
                    })
                }
            },
        }
    }
</script>

<style lang="scss">
    .dealer-visit-progress-list {
        /*deep*/.link-item {
        padding: 0;
    }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-dropdown-content {
                    padding: 24px;
                }
        .blank {
            width: 100%;
            height: 92px;
            background: #F2F2F2;
        }
        .media-list-rows {
            background: #FFFFFF;
            border-radius: 32px;
            width: 702px;
            margin: 24px auto auto auto;
            .visit-list {
                padding: 24px 16px 24px 24px;
                .visit-content {
                    .visit-top {
                        @include flex-start-center;
                        @include space-between;
                        .store-time {
                            @include flex-start-center;
                            .out-circle {
                                width: 32px;
                                height: 32px;
                                border-radius: 50%;
                                background: #EFF3FF;
                                .dot-circle {
                                    width: 12px;
                                    height: 12px;
                                    margin: 10px auto auto auto;
                                    background: #2F69F8;
                                    border-radius: 50%;
                                }
                            }
                            .visit-time {
                                padding-left: 16px;
                                @include flex-start-center();
                                .date {
                                    font-family: PingFangSC-Semibold,serif;
                                    font-size: 32px;
                                    color: #262626;
                                }
                                .week{
                                    padding-left: 16px;
                                    font-size: 28px;
                                    color: #8C8C8C;
                                }
                            }
                        }
                    }
                    .visit-start-time {
                        padding-top: 24px;
                        @include flex-start-center;
                        @include space-between;
                        .visit-duration {
                            @include flex-start-center;
                            .time {
                                font-size: 28px;
                                color: #8C8C8C;
                                font-weight: bold;
                            }
                            .interim-left {
                                padding-left: 16px;
                                padding-right: 16px;
                                font-size: 32px;
                                font-weight: bold;
                                background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
                                -webkit-background-clip: text;
                                color:transparent;
                            }
                            .duration {
                                font-size: 24px;
                                color: #8C8C8C;
                            }
                        }
                        .location {
                            color: #FF5A5A;
                            font-size: 24px;
                            .icon-aim {
                                font-size: 28px;
                            }
                        }
                    }
                    .visit-title {
                        font-family: PingFangSC-Semibold,serif;
                        padding-top: 24px;
                        font-size: 32px;
                        color: #262626;
                    }
                    .visit-address {
                        padding-top: 24px;
                        font-size: 24px;
                        color: #262626;
                    }
                }
            }
        }
    }
</style>
