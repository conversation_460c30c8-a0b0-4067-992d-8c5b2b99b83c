<template>
    <view class="dealer-capital-account">
        <picker @change="financialNameChange" :value="subsidiaryIndex" :range="financialArr.map(item => `${item.billTitle}(${item.multiAcctMainFlag === 'Y' ? '主' : '子'})`)" class="subsidiary-picker" v-if="subsidiaryRange.length !== 0">
            <view class="picker-text">{{`${financialArr[financialIndex].billTitle}(${financialArr[financialIndex].multiAcctMainFlag === 'Y' ? '主' : '子'})`}}</view>
            <view class="iconfont icon-down"></view>
        </picker>
        <picker @change="subsidiaryChange" :value="subsidiaryIndex" :range="subsidiaryRange" class="subsidiary-picker" v-if="subsidiaryRange.length !== 0">
            <view class="picker-text">{{subsidiaryRange[subsidiaryIndex]}}</view>
            <view class="iconfont icon-down"></view>
        </picker>
        <view class="capital-loading" v-if="capitalLoadingFlag">
            <link-loading status="info" class="loading"/>
            <view class="capital-loading-text">正在加载数据</view>
        </view>
        <view v-else>
            <lnk-no-auth :label="capitalDataFlag" :authFlag="false" v-if="capitalNoDataFlag"></lnk-no-auth>
            <view v-if="capitalAccount.expensePoolBalance.length !== 0 || capitalAccount.accountBalance.length !== 0 || capitalAccount.marginBalance.length !== 0">
                <view class="account-balance" v-for="(item, index) in capitalAccount.accountBalance" :key="index" v-if="capitalAccount.accountBalance.length !== 0">
                    <view class="text">账户余额</view>
                    <view class="value">¥{{item.yue}}</view>
                </view>
                <!--保证金余额-->
                <view class="earnest-money" v-if="capitalAccount.marginBalance.length !== 0">
                    <view class="earnest-money-text">保证金余额</view>
                    <view class="line"></view>
                    <view class="earnest-money-content">
                        <view class="content" v-for="(item, index) in capitalAccount.marginBalance" :key="index">
                            <view class="content-label">￥{{item.yue}}</view>
                            <view class="content-value">{{item.bzjtype}}</view>
                        </view>
                    </view>
                </view>
                <!--费用池余额-->
                <view class="pool-balance" v-if="capitalAccount.expensePoolBalance.length !== 0">
                    <view class="earnest-money-text">费用池余额</view>
                    <view class="line"></view>
                    <view class="earnest-money-content">
                        <view class="content" v-for="(item, index) in capitalAccount.expensePoolBalance" :key="index">
                            <view class="content-label">￥{{item.yue}}</view>
                            <view class="content-value">{{item.fytype}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import headType from '../../../terminal/components/head-type.vue'
    import lnkNoAuth from '../../../core/lnk-no-auth/lnk-no-auth';
    export default {
        name: "dealer-capital-account",
        data () {
            const earnestMoneyOption = {
                monthReturn: '4000.00',
                quarterReturn: '4000.00',
                halfYearReturn: '4000.00',
                yearReturn: '4000.00',
                intervalMonthReturn: '4000.00',
                deliveryCostReturn: '4000.00',
                fixedEarnestMoney: '4000.00',
                intervalEarnestMoney: '4000.00',
                cash: '4000.00',
                salesDiscount: '4000.00',
                cashDiscount: '4000.00',
                giftRedemption: '4000.00'
            };
            return {
                earnestMoneyOption,
                financialIndex: 0,
                subsidiaryIndex: 0,
                capitalLoadingFlag: true,          // 资金账户loading组件显示控制参数
                capitalNoDataFlag: false,          // 资金账户无数据组件显示控制参数
                capitalDataFlag: '暂无数据',     // 资金账户无数据是提示语
                capitalAccount: {
                    expensePoolBalance: [],                                                                             // 费用池余额
                    accountBalance: [],                                                                                 // 账户余额
                    marginBalance: [],                                                                                  // 保证金余额
                },
            }
        },
        props: {
            financialArr: {
                type: Array,
                default: []
            },
            subsidiaryNcCode: {
                type: Array,
                default: []
            },
            subsidiaryRange: {
                type: Array,
                default: []
            }
        },
        components: {
            headType,
            lnkNoAuth     // 无权限
        },
        created() {
            this.queryCapitalAccount();                // 资金账户
        },
        methods: {
            /**
             * 营业执照名称切换
             * <AUTHOR>
             * @date 2022-09-15
             */
            financialNameChange(e) {
                this.financialIndex = Number(e.detail.value);
                this.capitalLoadingFlag = true;
                this.queryCapitalAccount();
            },
            /**
             * 子公司切换
             * <AUTHOR>
             * @date 2020-09-25
             * @param e
             */
            subsidiaryChange (e) {
                this.subsidiaryIndex = Number(e.detail.value);
                this.capitalLoadingFlag = true;
                this.queryCapitalAccount();
            },
            /**
             * 资金账户（NC接口逻辑）
             * <AUTHOR>
             * @date 2020-10-23
             */
            queryCapitalAccount () {
                const that = this;
                if (that.subsidiaryRange.length === 0) {
                    that.capitalNoDataFlag = true;              // 资金账户无数据组件控制参数
                    that.capitalLoadingFlag = false;            // 资金账户loading组件控制参数
                    return
                }
                this.$http.post('action/link/nc/queryCapitalAccount', {
                    ncOrgCode: that.subsidiaryNcCode[that.subsidiaryIndex],
                    ncAccntCode: [that.financialArr[this.financialIndex].ncAccntCode]
                },{
                    handleFailed (error) {
                        that.capitalNoDataFlag = true;              // 资金账户无数据组件控制参数
                        that.capitalLoadingFlag = false;            // 资金账户loading组件控制参数
                        that.$utils.hideLoading();
                    }
                }).then(data => {
                    that.capitalLoadingFlag = false;                // 资金账户loading组件控制参数
                    if (data.success) {
                        if (data.result.expensePoolBalance.length === 0 && data.result.accountBalance.length === 0 && data.result.marginBalance.length === 0) {
                            that.capitalNoDataFlag = true
                        }
                        that.capitalAccount = data.result;
                    }
                })
            },
        }
    }
</script>

<style lang="scss">
    .dealer-capital-account {
        font-family: PingFangSC-Regular,serif;
        padding-bottom: 32px;
        .account-balance {
            @include flex-start-center();
            @include space-between();
            background: #ffffff;
            border-radius: 16px;
            width: 702px;
            margin: 24px auto auto auto;
            padding: 24px 40px;
            .text {
                font-weight: bold;
                font-size: 32px;
                color: #262626;
            }
            .value {
                font-size: 28px;
                color: #262626;
            }
        }
        .earnest-money {
            width: 702px;
            background: #ffffff;
            border-radius: 16px;
            margin: 24px auto auto auto;
            .earnest-money-text {
                font-size: 32px;
                color: #262626;
                font-weight: bold;
                padding: 24px 32px;
                width: 100%;
            }
            .line {
                margin-left: 24px;
                width: 93.5%;
                border-bottom: 1px #DADEE9 dashed;
            }
            .earnest-money-content {
                @include flex-start-center();
                @include wrap();
                padding-bottom: 32px;
                .content {
                    width: 33.3%;
                    text-align: center;
                    font-size: 28px;
                    .content-label {
                        padding-top: 30px;
                        padding-bottom: 16px;
                        color: #262626;
                    }
                    .content-value {
                        color: #8C8C8C;
                    }
                }
            }
        }
        .pool-balance {
            width: 702px;
            background: #ffffff;
            border-radius: 16px;
            margin: 24px auto auto auto;
            .earnest-money-text {
                font-size: 32px;
                color: #262626;
                font-weight: bold;
                padding: 24px 32px;
                width: 100%;
            }
            .line {
                margin-left: 24px;
                height: 2px;
                width: 93.5%;
                border-bottom: 1px #DADEE9 dashed;
            }
            .earnest-money-content {
                @include flex-start-center();
                @include wrap();
                padding-bottom: 32px;
                .content {
                    width: 50%;
                    text-align: center;
                    font-size: 28px;
                    .content-label {
                        padding-top: 30px;
                        padding-bottom: 16px;
                        color: #262626;
                    }
                    .content-value {
                        color: #8C8C8C;
                    }
                }
            }
        }
    }
</style>
