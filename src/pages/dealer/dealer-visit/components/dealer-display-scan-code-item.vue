<!--
@created<PERSON><PERSON>  yangying
@date  2023/05/11
@description 陈列协议扫码详情
-->
<template>
    <view class="dealer-display-scan-code-item">
        <item :data="data" :index="index" :arrow="false" class="scan-item-wrap">
            <view slot="note" class="scan-item">
                <view class="info-item">
                    <view>箱码：{{data.boxCode}}</view>
                    <view class="num">
                        <text class="bottle-num">{{data.scanTotal}}</text>
                        <text>瓶</text>
                    </view>
                </view>
                <view class="info-item"></view>
                <view class="info-item prod-name">{{data.productName}}</view>
                <view class="info-item">产品编码：{{data.productCode}}</view>
                <view class="info-item">
                    <view v-if="data.isFiveMark == 1" class="tag-green">五码关联</view>
                    <view v-else class="tag-red">未五码关联</view>
                    <view class="infoButton" @tap="unflodFlag = !unflodFlag">
                        <text>{{unflodFlag ? '收起' : '展开'}}</text>
                        <link-icon icon="icon-down" :class="unflodFlag ? 'openicon': 'openicon closeicon'"/>
                    </view>
                </view>
                <!-- 盒码/瓶码 -->
                <view class="code-wrap" v-if="unflodFlag">
                    <!-- 盒码列表：扫的箱码 -->
                    <view class="code-cate" v-if="data.unit === 'box'">
                        <view class="code-item" v-for="(item, index) in data.caseCodeList" :key="index">盒码：{{item}}</view>
                    </view>
                    <!-- 单个盒码：扫的瓶码/盒码 -->
                    <view class="code-cate" v-else>
                        <view class="code-item">盒码：{{data.caseCode}}</view>
                    </view>
                    <!-- 瓶码列表：扫的箱码/盒码 -->
                    <view class="code-cate" v-if="data.unit === 'box' || data.unit === 'case'">
                        <view class="code-item" v-for="(item, index) in data.bottleCodeList" :key="index">瓶码：{{item}}</view>
                    </view>
                    <!-- 单个瓶码：扫的瓶码 -->
                    <view class="code-cate" v-else>
                        <view class="code-item">瓶码：{{data.bottleCode}}</view>
                    </view>
                    <!-- 盖外码列表：所有码类型 -->
                    <view class="code-cate">
                        <view class="code-item" v-for="(item, index) in data.outCodeList" :key="index">盖外码：{{item}}</view>
                    </view>
                </view>
            </view>
        </item>
    </view>
</template>

<script>
    export default {
        name: 'dealer-display-scan-code-item',
        props: {
            data: Object,
            index: Number
        },
        data() {
            return {
                unflodFlag: false
            }
        }
    }
</script>

<style lang="scss">
.dealer-display-scan-code-item {
    .scan-item-wrap {
        margin-bottom: 24px;
        border-radius: 24px;
        box-shadow: 0 2px 36px 0 rgba(135,144,168,0.20);

        .scan-item {
            color: #262626;
            font-size: 28px;

            .info-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 10px;

                .num {
                    display: flex;
                    align-items: center;

                    .bottle-num {
                        font-size: 30px;
                        font-weight: bold;
                        margin-right: 4px;
                    }
                }
            }

            .code-wrap {
                padding: 20px;
                background: #BBD5FF;
                border-radius: 20px;

                .code-cate {
                    margin-bottom: 10px;
                }

                .code-item {
                    font-size: 26px;
                    margin-bottom: 2px;
                }
            }

            .prod-name {
                font-size: 30px;
                font-weight: bold;
            }

            .tag-green {
                color: #95EC69;
            }

            .tag-red {
                color: red;
            }
        }
    }
}
</style>
