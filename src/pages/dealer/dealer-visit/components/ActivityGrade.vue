<!-- BC抢盘 -->
<template>
  <view class="dealer-activity-grade" v-if="typeDataObj.listMap && typeDataObj.listMap.length">
    <view class="bc-grabPlate-view__content--title">BC-抢盘</view>
    <!-- 活动时间 -->
    <view class="activity-grade-time">
      <view class="activity-grade-time__title">
        活动时间：{{ startDate.substring(0, 10) }} - {{ endDate.substring(0, 10) }}
      </view>
    </view>
    <!-- 滚动区域 -->
    <ActivityGradeScrollView v-if="Object.keys(typeDataObj).length" :typeDataObj="typeDataObj" :acctData="acctData"/>
    <!-- 展示信息 -->
    <ActivityGradeInfo v-if="Object.keys(activityGradeInfo).length" :info="activityGradeInfo" :acctData="acctData"/>
  </view>
</template>
<script>
import ActivityGradeInfo from './ActivityGradeInfo.vue';
import ActivityGradeScrollView from './ActivityGradeScrollView.vue';

export default {
  data() {
    const userInfo = this.$taro.getStorageSync('token').result;
    return {
      typeDataObj: {
          listMap: []
      },
      userInfo,
      activityGradeInfo: {},
    };
  },
  components: {
    ActivityGradeInfo,
    ActivityGradeScrollView
  },
  props: {
    // 终端数据
    acctData: {
      type: Object,
      default: () => ({}),
    },
    dataBoardActive: {
      type: Object,
      default: () => ({}),
    },
  },
  watch: {
    dataBoardActive: {
      handler(v) {
        const { seq, programId } = this.dataBoardActive;
        const { memberId, id } = this.acctData;
        if (seq && programId && id && memberId) {
          this.initData();
        }
      },
      deep: true,
      immediate: true,
    },
  },
  computed: {
    getDataBoardActiveVal() {
      return this.dataBoardActive.val;
    },
    dataBoardActiveLen() {
      return Object.keys(this.dataBoardActive).length;
    },
    promotType() {
      return this.typeDataObj.promotType === "INCREMENT_BOTTLE";
    },
    startDate() {
      if (this.typeDataObj && Object.prototype.hasOwnProperty.call(this.typeDataObj, 'startDate')) {
        return this.typeDataObj.startDate;
      }
      return '';
    },
    endDate() {
      if (this.typeDataObj && Object.prototype.hasOwnProperty.call(this.typeDataObj, 'endDate')) {
        return this.typeDataObj.endDate;
      }
      return '';
    },
  },
  methods: {
    async initData() {
      const params = {
        attr1: 'QWQuery',
        acctId: this.acctData.id,
        memberId: this.acctData.memberId,
        conditionId: this.dataBoardActive.seq,
        programId: this.dataBoardActive.programId,
      }
      // try {
       const res = await this.$http.post(
          "/exportterminal/link/openRuleScanLevel/getCurrentLevelInfo",
          params,
          {
            autoHandleError: false,
            handleFailed: (response) => {
                console.log(response);
            },
          }
        );
        if (res.success && res.result) {
          this.typeDataObj = res.result[this.getDataBoardActiveVal];
          if (this.typeDataObj) {
            this.$set(this.activityGradeInfo, 'currentOpenScanNum', this.typeDataObj.currentOpenScanNum)
            if (this.typeDataObj.receiveReward) {
              this.$set(this.activityGradeInfo, 'receiveReward', {
                value: this.typeDataObj.receiveReward.value || '',
                name: this.typeDataObj.receiveReward.name || ''
              });
            } else {
              this.typeDataObj.receiveReward = {
                value: '',
                name: ''
              }
              this.$set(this.activityGradeInfo, 'receiveReward', {
                value: '',
                name: ''
              });
            }
            this.$set(this.activityGradeInfo, 'currentInScanNum', this.typeDataObj.currentInScanNum || 0);
            this.$set(this.activityGradeInfo, 'achieveMemberLevel', this.typeDataObj.achieveMemberLevel || '');
            this.$set(this.activityGradeInfo, 'achieveAcctLevel', this.typeDataObj.achieveAcctLevel || '');
            this.$set(this.activityGradeInfo, 'memberDimension', this.typeDataObj.memberDimension || '');
          }
        } else {
          this.typeDataObj = {}
        }
      // } catch (error) {
      //   this.typeDataObj = {}
      //   console.log(error);
      //   this.$showError(error.result || '服务异常');
      // }
    },
  },
};
</script>
<style lang="scss">
@import "../../../terminal/terminal/style/terminal-label/activityGrade";
.dealer-activity-grade {
    .bc-grabPlate-view__content--title {
        background: #fff!important;
    }
}
</style>
