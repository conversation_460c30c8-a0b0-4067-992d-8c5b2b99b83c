<!--  终端实时返利余额   -->
<template>
  <view class="dealer-real-time-rebate">
    <view class="real-time-rebate-title">终端实时返利余额</view>
    <view
      class="real-time-rebate-content"
      v-if="terminalAccountData.length === 0"
    >
      <view class="real-time-rebate-content__num-item">
        <view class="val">-</view>
      </view>
    </view>
    <view v-else>
      <view class="content">
        <view
          class="num-item"
          style="padding-bottom: 12px"
          v-for="(rebate, rIndex) in terminalAccountData"
          :key="rIndex"
        >
          <view class="val">{{ Number(rebate.value || 0).toFixed(2) }}</view>
          <view class="label">{{ rebate.name }}</view>
        </view>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  name: "dealer-real-time-rebate",
  data() {
    return {
      terminalAccountData: [],
      defaultObj: {
        attr1: "QWQuery",
        companyCode: this.acctData.brandCompanyCode,
        acctCode: this.acctData.acctCode,
        acctType: this.acctData.acctType || "Terminal",
      },
    };
  },
  props: {
    acctData: {
      type: Object,
      default: () => ({}),
    },
  },
  created() {
    this.initData();
  },
  methods: {
    async initData() {
      const res = await this.$http.post(
        "/exportloyalty/loyalty/member/terminalPortalPoint",
        { ...this.defaultObj },
        {autoHandleError: false}
      );
      if (res && res.data && res.data.length) {
        this.terminalAccountData = res.data;
      }
    },
  },
};
</script>
<style lang="scss" scoped>
.dealer-real-time-rebate {
  &-title {
    font-family: PingFangSC-Medium;
    font-size: 32px;
    font-weight: 500;
  }
  &-content {
    display: flex;
    flex-wrap: wrap;
    margin-top: 60px;
    &__num-item {
      flex: 1;
      text-align: center;
      min-width: 30%;
      flex: 0.33;
    }
  }
}
</style>
