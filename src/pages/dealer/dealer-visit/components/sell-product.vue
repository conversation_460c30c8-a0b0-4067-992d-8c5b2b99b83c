<template>
    <link-page class="dealer-sell-product">
        <view class="edit-text" @tap="goSellProduct" v-if="editFlag && !disabledNewEdit && acctData.editApprovalStatus !== 'Submitted'">编辑</view>
        <view class="edit-text"  v-else></view>
        <link-auto-list :option="sellProductOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item">
                    <view slot="note">
                        <view class="media-list">
                            <view class="num-view">
                                <view class="num">
                                    {{data.prodCode}}
                                    <view class="change-field" v-if="data.showMsg">
                                        <text style="border-color: white;color: red">{{`(${data.showMsg})`}}</text>
                                    </view>
                                </view>
                                <view class="prod-tag">
                                    <view class="item-tag">
                                        <view class="tag-content" v-if="data.supplierAcctType">{{data.supplierAcctType | lov('ACCT_TYPE')}}供货</view>
                                    </view>
                                    <view class="item-tag m-w70" :class="{gray: data.status === 'N'}">
                                        <view class="tag-content">{{data.status | lov('EFFICIENT_STATUS')}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="name">{{data.prodName}}</view>
                        </view>
                        <view class="store-content-representative">
                            <view class="terminal-name">
                                {{data.supplierManageMode | lov('CHANNEL_MANAGE_MODE')}}
                                {{data.supplierCategory | lov('ACCNT_CATEGORY')}}
                                <text v-if="(data.supplierManageMode || data.supplierCategory) && data.supplierName"> | </text>
                                {{data.supplierName}}
                            </view>
                        </view>
                        <view class="whether" v-if="isShowWhether">
                            <view class="whether-label">
                                是否自营: {{data.selfSupport | lov('IS_FLAG')}}
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-sell-product",
        data () {
            return {
                isShowWhether: false
            }
        },
        props: {
            isChuanDong: {
                type: Boolean,
                default: false
            },
            isDinghao: {
                type: Boolean,
                default: false
            },
            editToApprovedFlag: {
                type: Boolean,
                default: false
            },
            sellProductOption: {
                type: Object
            },
            acctData: {
                type: Object,
                default: '',
                required: true
            },
            sellRefreshFlag: {
                type: Boolean,
                default: false
            },
            editFlag: {
                type: Boolean,
                default: true
            },
            disabledNewEdit:{
                type: Boolean,
                default: false
            }
        },
        async created(){
            const isShowCompanyCode = await this.$utils.getCfgProperty('IS_AUTARKY');
            if ((this.acctData.isShareHolderShop === 'Y' || this.acctData.ownStores === 'Y') && this.acctData.acctType === 'Terminal' && isShowCompanyCode.indexOf(this.acctData.mdmCompanyCode) > -1) {
                this.isShowWhether = true;
            };
        },
        methods: {
            goSellProduct () {
                if(this.acctData.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                this.$emit("checkEdit",(success) => {
                    if (success) {
                        const url = this.editToApprovedFlag && !this.isDinghao
                            ? '/pages/terminal2/edit-construction/edit-approve-page'
                            : '/pages/terminal2/edit-construction/edit-sell-product-page'
                        this.$nav.push(url, {
                            data: this.acctData,
                            salesmanCityId: this.acctData.salesmanCityId,
                            acctId: this.acctData.id,
                            isChuanDong: this.isChuanDong,
                            acctData: this.$utils.deepcopy(this.acctData),
                            editFlag: 'edit',
                            title: '所售产品'
                        })
                    }
                });
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../../styles/list-card";
    /*deep*/.link-sticky-top:before {
        box-shadow: none!important;
    }
    .dealer-sell-product {
        .change-fielld::after {
            content: '(新增)';
            color: red;
        }
        background-color: #F2F2F2;
        font-family: PingFangSC-Regular,serif;
        .perform-case-list-item {
            background: #FFFFFF;
            margin: auto 24px 24px 24px;
            border-radius: 16px;
        }
        /*deep*/.link-auto-list-top-bar {
                     border-bottom: none;
                }
        .media-list {
            @include media-list;
            .num-view {
                width: 100%;
                display: flex;
                justify-content: space-between;
                border-radius: 8px;
                line-height: 40px;
                background-color: white;
                font-size: 28px;

                .num {
                    background: #A6B4C7;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                    position: relative;
                }

                .change-field {
                    position: absolute;
                    right: -80px;
                    top: 0;
                }

                .orange {
                    color: #FF9900
                }

                .blue {
                    color: #2963F2;
                }

                .prod-tag {
                    display: flex;
                    justify-content: space-around;
                    .item-tag {
                        min-width: 100px;
                        height: 36px;
                        line-height: 36px;
                        text-align: center;
                        color: #ffffff;
                        background: #2F69F8;
                        box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                        border-radius: 8px;
                        padding-left: 27px;
                        padding-right: 27px;
                        font-size: 20px;
                        margin-right: 8px;
                        transform: skewX(-30deg);
                        .tag-content {
                            transform: skewX(30deg);
                        }
                    }
                    .gray {
                        background: rgb(158,161,174);
                        box-shadow: 0 3px 4px 0 rgba(158,161,174,0.35);
                    }

                    .m-w70 {
                        min-width: 70px;
                    }
                }
            }
        }
        .whether {
            color: #000;
            font-size: 28px;
            margin-top: 20px;
            display: flex;
            @include space-between;
            align-items: center;
        }
        .content-middle{
            width: 100%;
            @include flex-start-center;
            @include space-between;
            margin: 24px 0 8px 0;
            .name{
                font-family: PingFangSC-Semibold,serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
            }
        }
        .store-content-representative {
            @include flex;
            margin-top: 24px;
            .terminal-name {
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
            }
        }
    }
</style>
