<template>
    <link-page class="dealer-year-agreement">
        <view class="edit-text" @tap="goHistoryAgreement">历史年度配额</view>
        <link-auto-list :option="guoJiaoOptions" hideCreateButton v-if='isGuojiao && acctType === "Terminal" || userInfo.orgType === "Company" && mdmCompanyCode === "5600"&&acctType === "Terminal"'>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="media-list-rows" @tap='toDetail(data)'>
                    <view slot="note" class="guojiao-list">
                        <view class="item-tag" :style="data.tagColor">
                            <view class="tag-content">{{data.approvalStatus | lov('ANNUAL_QUOTA_STATUS')}}</view>
                        </view>
                        <view class="title-box">
                            <view class="guojiao-title">
                                年度配额申请（{{data.quotaCode}}）
                            </view>
                        </view>
                        <view class="prod">
                            <text class="content-title">{{data.prodPartCode | lov('PROD_BUS_S_CLASS')}}</text>
                        </view>
                        <view class="row-content">
                            <view>年度配额总额：{{data.totalQuantity}}</view>
                            <view>所属财年：{{data.fiscalYear}}</view>
                        </view>
                        <quota-detail-card title="计划内配额明细" :showField="showField" :bg-color="colorIn" :data="data" @click="()=>{}"/>
                        <quota-detail-card title="计划外配额明细" :showField="showFieldOut" :bg-color="colorOut" :data="data" @click="()=>{}"/>
                    </view>
                </item>
            </template>
        </link-auto-list>
        <!-- 特曲年度配额 -->
        <tequ-year-quota-list
            :id="acctId"
            :fiscalYear="fiscalYear"
            :showBtn="allowPostn"
            :flagParam="{
                goDetail:'Y',
                showFilter:'Y'
            }"
            v-else-if='mdmCompanyCode === "5137" && acctType === "Terminal"'/>
        <!-- 窖龄年度配额 -->
         <jiaoLing-year-quota-list 
            v-else-if='mdmCompanyCode === "5151"&&acctType === "Terminal"'
            ref="jiaoling" 
            :id="acctId"
            :fiscalYear="fiscalYear"
            :flagParam="{
                goDetail:'Y'
            }"/>
        <link-auto-list :option="agreementOptions" hideCreateButton v-else>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="media-list-rows">
                    <view slot="note" class="media-list">
                        <image class="media-list-logo" :src="data.storeUrl" lazy-load="true"></image>
                        <view class="store-content">
                            <view class="store-content-top">
                                <view class="store-title">
                                    <view>{{data.agrType | lov('AGR_TYPE')}}</view>
                                    <view class="store-agrNum">({{data.agrNumber}})</view>
                                </view>
                                <view>{{data.agrStatus | lov('AGR_STATUS')}}</view>
                            </view>
                            <view class="store-content-middle">
                                <view class="num-view">
                                    <view class="num">{{data.productCode}}</view>
                                    <view class="store-title">协议时间: {{data.yearQuotaTime}}</view>
                                </view>
                                <view class="store-title">{{data.productName}}</view>
                            </view>
                            <view class="store-content-bottom">
                                <view class="store-title">年度配额量: {{data.yearQuotaNumber}}件</view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import quotaDetailCard from '../../../terminal2/annual-quota-apply/components/quota-detail-card.vue'
    import tequYearQuotaList from "./year-quota/tequ-year-quota-list.vue";
    import JiaoLingYearQuotaList from './year-quota/jiaoling-year-quota-list.vue'

    export default {
        name: "dealer-year-agreement",
        components: {quotaDetailCard,tequYearQuotaList,JiaoLingYearQuotaList},
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            let fiscalYear = this.getCurrentFiscalYear();
            // 年度配额协议
            const guoJiaoOptions = new this.AutoList(this, {
                module: 'action/link/basic',
                url: {
                    queryByExamplePage: 'action/link/annualQuotaDetails/queryQuotaDetailsByAcctId'
                },
                // itemPath: '/pages/terminal2/annual-quota-apply/annual-quota-detail-page.vue',
                param: () => {
                    return {
                        oauth: 'ALL',
                        sort: 'created',
                        order: 'desc',
                        acctId: this.acctId,
                        fiscalYear,
                        filtersRaw: []
                    }
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    afterLoad (data) {
                        data.rows.forEach(item => {
                            switch (item.approvalStatus) {
                                case 'Submitted'://已提交
                                    this.$set(item, 'tagColor','background: #2F69F8;box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);');
                                    this.$set(item, 'tagBgColor','background: #2F69F8;');
                                    break;
                                case 'Approved'://审批通过
                                    this.$set(item, 'tagColor','background: #2EB3C2;box-shadow: 0 3px 4px 0 rgba(46,179,194,0.35);');
                                    this.$set(item, 'tagBgColor','background: #2EB3C2;');
                                    break;
                                case 'Rejected'://审批拒绝
                                    this.$set(item, 'tagColor','background: #FF5A5A;box-shadow: 0 3px 4px 0 rgba(255,90,90,0.35);');
                                    this.$set(item, 'tagBgColor','background: #FF5A5A;');
                                    break;
                                case 'Revoke'://已撤回
                                    this.$set(item, 'tagColor','background: #A1A7AF;box-shadow: 0 3px 4px 0 rgba(191,191,191,0.50);');
                                    this.$set(item, 'tagBgColor','background: #A1A7AF;');
                                    break;
                            }
                        })
                    },
                }
            });
            const agreementOptions = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/agreement/queryFieldsByExamplePage'
                },
                itemPath: '/pages/terminal/protocol/edit-year-protocol-page.vue',
                param: () => {
                    const currentDate = new Date();
                    const currentMonth = currentDate.getMonth() + 1;
                    const yearQuotaTime = currentMonth === 10 ? `[${this.getCurrentFiscalYear()},${this.getCurrentFiscalYear() + 1}]` : this.getCurrentFiscalYear();
                    const yearOperator = currentMonth === 10 ? 'in' : '='
                    let filtersRaw = [
                        {id: 'agrCategory', property: 'agrCategory', value: 'protocol', operator: '='},
                        {id: 'agrType', property: 'agrType', value: 'Year', operator: '='},
                        {id: 'yearQuotaTime', property: 'yearQuotaTime', value: yearQuotaTime, operator: yearOperator},
                        {id: 'accntId', property: 'accntId', value: this.acctId, operator: '='}
                    ];
                    // 特定公司的经销商人员只能看到自己职位创建的数据
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        oauth: 'ALL',
                        sort: 'created',
                        stayFields: "id,agrType,agrStatus,agrPicKey,productId,productCode,productName,yearQuotaTime,yearQuotaNumber,agrNumber",
                        order: 'desc',
                        filtersRaw
                    }
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    afterLoad (data) {
                        data.rows.forEach(async (item) => {
                            if (!this.$utils.isEmpty(item.agrPicKey)) {
                                let urlData = await this.$image.getSignedUrl(item.agrPicKey);
                                this.$set(item, 'storeUrl', urlData);
                            } else {
                                this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                            }
                        })
                    },
                }
            });
            return {
                fiscalYear,//当前财年
                userInfo,
                agreementOptions,
                guoJiaoOptions,
                colorIn: '#eff9ff',
                colorOut: '#fdf4f5',
                showField: [
                    {label: '计划内总额', key: 'planTotalQuantity'},
                    {label: '计划内余额', key: 'planBalance'},
                    {label: '基础配额', key: 'basicQuantity'},
                    {label: '调整配额', key: 'adjustAppQuantity'},
                    {label: '未执行扣减', key: 'unplanDeduction'},
                    {label: '计划内下单', key: 'planOrder'},
                    {label: '计划内占用', key: 'planOccupy'},
                    {label: '计划内已执行', key: 'planExecute'}
                ],
                showFieldOut: [
                    {label: '计划外配额', key: 'unplannedQuantity'},
                    {label: '计划外余额', key: 'unplanBalance'},
                    {label: '计划外下单', key: 'unplanOrder'},
                    {label: '计划外占用', key: 'unplanOccupy'},
                    {label: '计划外已执行', key: 'unplanExecute'}
                ]
            }
        },
        props: {
            editApprovalStatus: {
                type: String,
                default: ''
            },
            acctId: {
                type: String,
                default: ''
            },
            multiAcctMainIds: {
                type: String,
                default: ''
            },
            isDealer: {
                type: Boolean,
                default: false
            },
            isGuojiao: {
                type: Boolean,
                default: false
            },
            mdmCompanyCode: {
                type: String,
                default: ''
            },
            acctType:{
                type: String,
                default: ''
            },
            allowPostn:{
                type: Boolean,
                default: false
            }
        },
        computed: {
            editApprovalFlag() {
                const flag = this.editApprovalStatus !== 'Submitted'
                if (!flag) {
                    this.agreementOptions.option.itemPath = ''
                } else {
                    this.agreementOptions.option.itemPath = '/pages/terminal/protocol/edit-year-protocol-page.vue'
                }
                return flag;
            },
        },
        methods:{
            toDetail(data){
                if(['Rejected', 'Revoke'].includes(data.approvalStatus)){
                    this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-apply-page', {
                        data,
                        isEditFlag: true,
                        type: 'new'
                    });
                }else{
                    this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-detail-page', {
                        data: data,
                        source: 'quotaApply'
                    });
                }
            },
            goHistoryAgreement() {
                this.$nav.push('/pages/terminal/terminal/historical-year-agreement-page', {
                    acctId: this.acctId,
                    multiAcctMainIds: this.multiAcctMainIds,
                    mdmCompanyCode: this.mdmCompanyCode,
                    acctType: this.acctType
                })
            },
            /**
             * 获取当前财年: 财年判断从11月份开始，比如2021-11属于2022财年
             * <AUTHOR>
             * @date 11/2/21
             */
            getCurrentFiscalYear() {
                // 获取当前时间月份
                const currentDate = new Date();
                const currentMonth = currentDate.getMonth() + 1;
                return currentMonth >= 11 ? currentDate.getFullYear() + 1 : currentDate.getFullYear();
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../../styles/list-card";
    .dealer-year-agreement {
        .media-list-rows {
            background: #FFFFFF;
            border-radius: 32px;
            padding: 24px;
            width: 702px;
            margin: auto auto 24px auto;
        }
        .content-title{
            padding: 1px 20px;
            border: 1px solid #C5D7FD;
            background: rgba(239, 243, 254, .5);
        }
        .guojiao-list {
            position: relative;
            color: #595959;
            font-size: 26px;
            line-height: 50px;
            .item-tag {
                position: absolute;
                right: 0;
                top: 0;
                width: 80px;
                height: 36px;
                line-height: 36px;
                display: inline-block;
                text-align: center;
                color: #ffffff;
                background: #2F69F8;
                box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                border-radius: 8px;
                padding-left: 27px;
                padding-right: 27px;
                font-size: 20px;
                margin-right: 8px;
                transform: skewX(30deg);
                flex: 1;
                .tag-content {
                    transform: skewX(-30deg);
                }
            }
            .title-box{
                position: relative;
                .guojiao-title {
                    font-weight: bold;
                }

            }
            .prod{
                color: #2F69F8;
            }
            .row-content{
                @include flex-start-center;
                @include space-between;
            }
            .plan-box {
                border-radius: 20px;
                padding: 20px;
                margin: 20px 0;
                .content-wrap {
                    display: flex;
                    flex-wrap: wrap;
                    margin-bottom: 24px;
                    line-height: 60px;
                    .content-item {
                        margin-top: 24px;
                        width: 33%;
                        @include flex-start-center;
                        .num-item {
                            width: 100%;
                            text-align: center;
                        }
                        .line {
                            width: 1px;
                            height: 61px;
                            margin: 6px 0;
                            background-color: #c7c7c7;;
                        }
                        &:nth-child(3n+1) {
                            .line {
                                display: none;
                            }
                        }
                    }
                }
            }
            .in-plan-box {
                background: #EFF9FF;
            }
            .out-plan-box {
                background: #FDF4F5
            }
            .plan-title{
                color: #2F69F8;
            }
        }
        .media-list {
            @include media-list();
            .store-content {
                .store-content-top{
                    .store-title{
                        display: flex;
                        align-items: center;
                    }
                    .store-agrNum{
                    }
                }
                .store-content-middle{
                    display: block!important;
                    .num-view {
                        display: flex;
                        justify-content: space-between;
                        background: #fff;
                        margin-bottom: 10px;
                        .num {
                            background: #A6B4C7;
                            border-radius: 8px;
                        }
                    }
                    .store-title {
                        font-size: 28px;
                        color: #333;
                    }
                }
                .store-content-bottom{
                    margin-left: 24px;
                    .store-title {
                        font-size: 28px;
                        color: #333;
                        font-weight: bold;
                    }
                }
            }

        }
    }
</style>
