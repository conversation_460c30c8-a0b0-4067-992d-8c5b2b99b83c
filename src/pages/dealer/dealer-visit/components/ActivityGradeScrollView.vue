<template>
  <view class="dealer-activity-grade-scroll">
    <scroll-view
      :enable-flex="true"
      :scroll-x="true"
      :scroll-into-view="'activity-grade-scroll__item-' + typeDataObj.currentOrder || 0"
      :scroll-with-animation="true"
      class="activity-grade-scroll__view"
      :style="scrollStyle"
    >
      <view class="activity-grade-scroll__wrap" v-if="typeDataObj.listMap && typeDataObj.listMap.length">
        <view
          class="activity-grade-scroll__wrap--item"
          :id="'activity-grade-scroll__wrap--item-' + item.itemOrder"
          v-for="(item, index) in typeDataObj.listMap"
          :class="{ active: index + 1 === typeDataObj.currentOrder || 0 }"
          :key="index"
        >
          <view
            v-show="item.itemOrder === 0"
            class="progress-bar-cicle-none"
            :class="{
              'progress-bar-cicle': item.itemOrder === 0
            }"
          ></view>
          <view v-show="item.itemOrder > 0" class="level">{{ item.cusScanQty }}瓶</view>
          <view
            v-show="item.itemOrder > 0"
            class="icon"
            :class="{'icon-circle': item.rebateStatus !== 'NotAchieved'}">{{ item.itemLevel }}</view>
          <view class="progress-bar" :class="{ 'progress-bar-grey':  item.rebateStatus === 'NotAchieved'}" v-show="index !== typeDataObj.listMap.length - 1"></view>
          <view :class="progressBarClasses(item, index)"></view>
          <view
            v-show="item.itemOrder > 0 && typeDataObj.statusButton === 'Y'"
            class="level-name"
            :class="rebateStatusClass(item.rebateStatus)"
          >
            {{rebateStatusText(item.rebateStatus)}}
          </view>
          <view
            v-show="item.itemOrder > 0 && typeDataObj.displayRewards === 'Y'"
            class="advancement-reward"
            style="color: #666666"
            ><text style="font-family: ONEAN">{{ item.advancementReward }}</text>
            <text>{{ item.promotionAwardType }}</text></view
          >
        </view>
      </view>
    </scroll-view>
  </view>
</template>
<script>
export default {
  data() {
    return {}
  },
  props: {
    typeDataObj: {
      type: Object,
      default: () => ({}),
    },
    acctData: {
      type: Object,
      default: () => ({}),
    }
  },
  computed: {
    scrollStyle() {
      // 定义默认样式，如果所有条件都不满足，则返回空字符串或默认样式
      let style = '';
      // 如果没有typeDataObj或typeDataObj的某些属性未定义，则返回一个空字符串或默认样式
      if (!this.typeDataObj || !this.typeDataObj.statusButton || !this.typeDataObj.displayRewards) {
        return ''; // 或者返回默认的样式字符串，如 'height: auto;'
      }

      // 检查两个条件，如果都满足，则设置高度为250rpx
      if (this.typeDataObj.statusButton === 'Y' && this.typeDataObj.displayRewards === 'Y') {
        style = { height: '250rpx' };
      }
      // 如果任一条件不满足，则检查另一个条件，并设置高度为160rpx
      else if (this.typeDataObj.statusButton !== 'Y' || this.typeDataObj.displayRewards !== 'Y') {
        style = { height: '160rpx' };
      }

      // 返回最终的样式
      return style;
    },
  },
  methods: {
    /**
     * @desc 动态class
     */
    progressBarClasses(item, index) {
      const isActive = Number(item.cusScanQty) < Number(this.typeDataObj.currentOpenScanNum);
      const isActiveHalf =
        index + 1 < this.typeDataObj.listMap.length &&
        Number(this.typeDataObj.listMap[index + 1].cusScanQty) > Number(this.typeDataObj.currentOpenScanNum);
      const isActiveZero = Number(item.cusScanQty) === Number(this.typeDataObj.currentOpenScanNum);

      return {
        'progress-bar-active': isActive,
        // 'progress-bar-active-half': isActiveHalf,
        // 'progress-bar-active-zero': isActiveZero
      };
    },
    rebateStatusText(rebateStatus) {
      const statusMap = {  
        'WaitReceive': '可领取',  
        'Receive': '已领取',  
        'Achieved': '已激活',  
        // 默认情况或其他未明确列出的情况  
        default: '未激活'  
      };
      return statusMap[rebateStatus] || statusMap.default;
    },
    rebateStatusClass(rebateStatus) {  
      const classMap = {  
        'Receive': 'receive-button',  
        'WaitReceive': 'active-button',  
        // 默认情况或其他所有情况  
        default: 'inActive-button'  
      };  
      return classMap[rebateStatus] || classMap.default;  
    }
  }
}
</script>
<style lang="scss">
.dealer-activity-grade-scroll {
    .activity-grade-scroll__view {
        overflow: hidden;
        &::-webkit-scrollbar {
            display: none;
            width: 0;
            height: 0;
            color: transparent !important;
        }
    }
}
</style>
