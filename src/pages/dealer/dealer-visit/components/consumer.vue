<template>
    <link-page class="dealer-consumer">
        <view class="consumer-total">
            <view class="text">
                消费者总数： {{this.consumerTotal}}
            </view>
        </view>
        <link-auto-list :option="consumerOptions" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" @tap="toAccountItems(data)" class="consumers-item-wrap">
                    <view class="consumers-item">
                        <view class="acct-name">{{data.acctName}}</view>
                        <view class="top-label">
                            <text class="label-text">{{data.fstName}}跟进</text>
                        </view>
                        <view class="tags-wrap">
                            <view class="tag-item" v-if="data.subAcctType">{{data.subAcctType | lov('ACCT_SUB_TYPE')}}</view>
                            <view class="tag-item" v-if="data.loyaltyLevel">{{data.loyaltyLevel | lov('ACCT_MEMBER_LEVEL') }}</view>
                            <view class="tag-item red" v-if="data.impFlag === 'Y'">重点客户</view>
                        </view>
                        <view class="info-item">
                            <view class="info-label">联系方式</view>
                            <view class="phone">{{data.mobilePhone1}}</view>
                        </view>
                        <view class="info-item">
                            <view class="info-label">单位</view>
                            <view class="info-value">{{data.company}}</view>
                        </view>
                        <view class="info-item">
                            <view class="info-label">职务</view>
                            <view class="info-value">{{data.position}}</view>
                        </view>
                        <view class="info-item">
                            <view class="info-label">所属客户</view>
                            <view class="info-value">{{data.belongToStore}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-consumer",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            // 消费者
            const consumerOptions = new this.AutoList(this, {
                module: this.$env.appURL + '/action/link/consumer',
                url: {queryByExamplePage: this.$env.appURL + '/action/link/consumer/queryByExampleForSpAuthPage',},
                param: () => {
                    const filtersRaw = [
                        // {id: 'belongToStoreId', property: 'belongToStoreId', value: this.pageParam.data.id, operator: '='},
                        {id: "consumerType", property: "consumerType", value: "ChannelConsumer", operator: "="},
                        {id: "accntChannel", property: "accntChannel", value: "MarketingPlatform", operator: "="},
                        {id: "empFlag", property: "empFlag", value: "N", operator: "="},
                        {id: 'followFlag', property: 'followFlag', value: 'Y', operator: '='}
                    ];
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        filtersRaw,
                        belongToStoreIdList: [this.pageParam.data.id],
                        totalFlag: true,
                    }
                },
                sortField: ' ',
                sortOptions: null,
                filterBar: {},
                hooks: {
                    beforeLoad (options) {
                        if (this.$utils.isEmpty(options.param.sort.trim())) {
                            delete options.param.order;
                            delete options.param.sort;
                        }
                    },
                    afterLoad (data) {
                        this.consumerTotal = data.total;
                        data.rows.forEach(async(rowItem)=>{
                            if (!!rowItem.subAcctType) {
                                const parentData = await this.$lov.getLovByParentTypeAndValue({type: 'ACCT_SUB_TYPE', parentType: 'ACTIVITY_COMPANY', parentVal: rowItem.companyId});
                                let tempData = parentData.filter(item => item.val === rowItem.subAcctType);
                                this.$set(rowItem, 'topHeadId', tempData[0].id)
                            }
                        })
                    }
                }
            });
            return {
                consumerOptions,
                consumerTotal: 0,
                cfgPropertyObj: ''
            }
        },
        props: {
            isDealer: {
                type: Boolean,
                default: false
            },
            pageParam: {
                type: Object,
                default: {}
            }
        },
        async created() {
            const data = await this.$http.post(this.$env.appURL + '/action/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{id: 'key', property: 'key', value: 'FIELD_FLAG', operator: '='}]
            });
            this.cfgPropertyObj = data.rows[0].value;
        },
        methods: {
            toAccountItems(data) {
                const url = this.cfgPropertyObj  === 'Y'
                    ? '/pages/lj-consumers/account/account-item-page'
                    : '/pages/lj-consumers/account-old/account-item-page';
                this.$nav.push(url, {
                    data: {id: data.id}
                })
            }
        }
    }
</script>

<style lang="scss">
    /*deep*/.link-sticky-top:before {
        box-shadow: none!important;
    }
    .link-auto-list-wrapper {
        padding: 0 20px;
    }
    .consumers-item-wrap {
        border-radius: 16px;
        position: relative;
        width: 100%;
        margin: 20px 0;
        overflow: hidden;
        padding: 40px 28px 28px 28px;

        .consumers-item {
            width: 100%;

            .acct-name {
                width: 100%;
                font-size: 32px;
                color: #212223;
                line-height: 48px;
                font-weight: 600;
                margin-bottom: 16px;
            }

            .top-label {
                background: #2F69F8;
                color: white;
                transform: skew(30deg, 0);
                display: flex;
                border-bottom-left-radius: 14px;
                position: absolute;
                right: -10px;
                top: 0;

                .label-text {
                    font-size: 24px;
                    transform: skew(-30deg, 0);
                    padding: 8px 32px;
                }
            }

            .status-view {
                position: absolute;
                right: 16px;
                width: 120px;
                transform: skewX(-10deg);
                border-radius: 4px;
                background: #2F69F8;
                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                height: 36px;

                .status {
                    font-size: 22px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 32px;
                }
            }

            .tags-wrap {
                display: flex;
                margin-bottom: 16px;

                .tag-item {
                    padding: 0 10px;
                    font-size: 22px;
                    font-weight: 400;
                    color: #3F66EF;
                    line-height: 36px;
                    background: #F0F5FF;
                    border-radius: 4px;
                    text-align: center;
                    margin-right: 16px;
                }

                .red {
                    color: #FF461E;
                    background: #FFF1EB;
                }
            }

            .info-item {
                width: 100%;
                font-size: 28px;
                display: flex;
                margin-bottom: 8px;
                line-height: 44px;
                position: relative;

                .info-label {
                    color: #999999;
                    width: 132px;
                }

                .info-value {
                    color: #333333;
                }

                .phone {
                    color: #317DF7;
                }
            }
        }
    }
</style>
