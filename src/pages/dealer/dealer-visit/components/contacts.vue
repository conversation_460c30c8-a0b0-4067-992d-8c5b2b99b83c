<template>
    <view class="dealer-contacts">
        <view class="edit-text" v-if="editFlag">
            <view class="add" v-if="!disabledNewEdit && terminalData.editApprovalStatus !== 'Submitted' && !claimFlag" @tap="editAdd('','add')">添加</view>
            <view @tap.stop="goCodeRecord('','all')" v-if="terminalData.showAllScanCodeButtonFlag === 'Y'">全部扫码记录</view>
        </view>
        <link-auto-list :option="contactsOptions" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="consumer-rows">
                    <view slot="note" class="container">
                        <view class="item-container">
                            <view class="item-image">
                                <view class="effect" v-if="data.isEffective === 'N'">失效</view>
                                <image :src="$imageAssets.femaleImage" v-if="data.contactsSex === 'FEMALE'"></image>
                                <image :src="$imageAssets.maleImage" v-else></image>
                            </view>
                            <view class="item-right">
                                <view class="item-top">
                                    <view class="item-left">
                                        <view class="name" :class="{'change-msg': data.showMsg && data.showMsg['contactsName']}"><text v-if="data.mainFlag === 'Y'">[主要]</text>{{data.contactsName}}</view>
                                        <view class="phone approval-phone" v-if="!isEditApproval && isApprovalContacts && data.approvalPhone">{{data.approvalPhone}}</view>
                                        <view class="phone" :class="{'change-msg': data.showMsg && data.showMsg['mobilePhone'] && data.changeType === 'update', 'new-msg': data.showMsg && data.showMsg['mobilePhone'] && data.changeType === 'new'}" v-else >{{data.mobilePhone}}</view>
                                    </view>
                                    <view class="item-tag" v-if="data.winePromoterFlag === 'Y'">荐酒员</view>
                                    <status-button v-if="!isEditApproval && !isApprovalContacts && data.contactsType === 'ShopOwner' && data.approvalStatus && showCompanyFlag" :type="adapter(data.approvalStatus)" :label="data.approvalStatus| lov('CONTACT_STATUS')"></status-button>
                                </view>
                                <view class="item-top">
                                    <view class="item-left">
                                        <view class="label" v-if="data.contactsType">角色</view>
                                        <view class="value" v-if="data.contactsType" :class="{'change-msg': data.showMsg && data.showMsg['contactsType']}">{{data.contactsType | lov('CONTACTS_DUTY_TYPE')}}</view>
                                    </view>
                                    <view class="recep-tag" v-if="data.receptionPerson && data.receptionPerson === 'Y'">接待人员</view>
                                </view>
                                <view class="item-middle">
                                    <view class="label" v-if="data.birthdayType && data.birthYear && data.birthday">生日</view>
                                    <view class="value">{{data.birthdayType | lov('BIRTHDAY_TYPE')}}&nbsp;</view>
                                    <view class="value" :class="{'change-msg': data.showMsg && (data.showMsg['birthdayType'] || data.showMsg['birthYear'] || data.showMsg['birthday'])}">{{data.birthYear}}<text v-if="data.birthYear && data.birthday">-</text>{{data.birthday}}</view>
                                </view>
                                <view class="comments" v-if="data.comments">
                                    <text class="label">备注</text>
                                    <text class="value">{{data.comments}}</text>
                                </view>
                            </view>
                        </view>
                        <view class="button-container" v-if="editFlag && !claimFlag">
                            <view class="button" @tap="setEffective(data)" v-if="!disabledNewEdit && data.approvalStatus !=='Submitted' && !editToApprovedFlag">
                                <text v-if="data.isEffective === 'Y'">失效</text>
                                <text v-if="data.isEffective === 'N'">生效</text>
                            </view>
                            <view class="button" @tap="goCodeRecord(data, 'item')" v-if="data.winePromoterFlag === 'Y'">扫码记录</view>
                            <view class="button" @tap="callPhone(data.mobilePhone)">拨打电话</view>
                            <view class="button" @tap="editAdd(data, 'edit')" v-if="!disabledNewEdit && data.approvalStatus !=='Submitted' && terminalData.editApprovalStatus !== 'Submitted'" >编辑</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import StatusButton from "./status-button";
    export default {
        name: "dealer-contacts",
        components: {
            StatusButton
        },
        props: {
            claimFlag: {
                type: Boolean,
                default: false
            },
            editToApprovedFlag: {
              type: Boolean,
              default: false
            },
            contactsOption: {
                type: Object,
                default: () => ({})
            },
            attr1: {
                type: String
            },
            terminalData: {
                type: Object,
                default: () => ({})
            },
            editFlag: {
                type: Boolean,
                default: true
            },
            disabledNewEdit:{
                type: Boolean,
                default: false
            },
            isApprovalContacts: {
                type: Boolean,
                default: false
            },
            isEditApproval: {
                type: Boolean,
                default: false
            },
            editContactFlag: {
                type: Boolean,
                default: false
            },
        },
        data() {
            // 联系人
            const contactsOptions = new this.AutoList(this, {
                url: {queryByExamplePage: 'action/link/contacts/listByAcctId'},
                param: {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: this.attr1,
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    afterLoad(data) {
                        if (data.rows.length !== 0) {
                            let director = data.rows.filter(item => item.contactsType === 'Director' && item.winePromoterFlag === 'Y');
                            this.directorFlag = director.length > 1;
                        }
                    }
                }
            });
            return {
                contactsOptions: this.contactsOption && this.contactsOption.url ? this.contactsOption : contactsOptions,
                directorFlag: false   // 终端下联系人角色职务为店长的标志
            }
        },
        computed: {
            showCompanyFlag() {
                return this.terminalData.companyId !== '58929586649432064'
                    && this.terminalData.companyId !== '58929495997939712'
                    && this.terminalData.companyId !== '370222645733816897'
                    && this.terminalData.companyId !== '58933296855252992'
                    && this.terminalData.companyId !== '145202148358501210'
            }
        },
        methods: {
            /**
             * 适配status-button样式type
             * <AUTHOR>
             * @date 2023-4-6
             */
            adapter(type) {
                const hash = {
                    Refused: 'invalid'
                }
                return hash[type] || type.toLowerCase()
            },
            /**
             * 联系人生效失效
             * <AUTHOR>
             * @date 2020-11-25
             * @param data
             */
            setEffective (data) {
                const that = this;
                let contentDialog = data.isEffective === 'Y' ? '确认【失效】当前联系人吗' : '确认【生效】当前联系人吗';
                if(!this.editContactFlag && data.winePromoterFlag === 'Y') {
                    this.$dialog({
                        title: '提示',
                        content: '非餐饮类终端不允许编辑荐酒员信息！',
                        cancelButton: false,
                        initial: true,
                        onConfirm: () => {}
                    });
                } else {
                    that.$dialog({
                        title: '提示',
                        content: contentDialog,
                        cancelButton: true,
                        confirmText: '确认',
                        onConfirm:() => {
                            data.isEffective = data.isEffective === 'Y' ? 'N' : 'Y';
                            that.$http.post('action/link/contacts/changeStatus', data, {
                                handleFailed (error) {
                                    data.isEffective = data.isEffective === 'Y' ? 'N' : 'Y';
                                }
                            }).then(() => {
                                this.$bus.$emit("initFinancialVueContactList");//组件重新查询联系人列表
                            })
                        },
                        onCancel: () => {}
                    })
                }
            },
            /**
             * 编辑/添加联系人
             * <AUTHOR>
             * @date 2020-09-21
             * @param data
             * @param flag
             */
            editAdd (data, flag) {
                if(this.terminalData.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                this.$emit("checkEdit",(success) => {
                    if (success) {
                        const url = this.editToApprovedFlag
                            ? '/pages/terminal2/edit-construction/edit-approve-page'
                            : '/pages/terminal2/edit-construction/edit-contacts-page'
                        const targetData = this.editToApprovedFlag
                            ? this.terminalData
                            : JSON.parse(JSON.stringify(data))
                        const hasMain = this.contactsOptions.list.some((item) => item.mainFlag === 'Y' && item.isEffective === 'Y')
                        this.$nav.push(url, {
                            data: targetData,
                            flag: flag,
                            title: flag === 'add' ? '添加联系人' : '编辑联系人',
                            acctData: this.terminalData,
                            directorFlag: this.directorFlag,
                            editContactFlag: this.editContactFlag,
                            editFlag: 'edit',
                            hasMain
                        })
                    }
                });
            },
            /**
             * 拨打电话
             * <AUTHOR>
             * @date 2020-09-21
             * @param phone 电话号
             */
            callPhone (phone) {
                this.$taro.makePhoneCall({
                    phoneNumber: phone
                })
            },
            /**
             * 查看扫码记录
             * <AUTHOR>
             * @date 2020-09-21
             * @param data
             * @param flag
             */
            goCodeRecord (data, flag) {
                this.$nav.push('/pages/terminal2/scan-code-record/scan-code-record-page', {
                    acctId: this.terminalData.id,
                    appId: data.appId,
                    openId: data.openId,
                    viewRangeFlag: flag
                })
            }
        }
    }
</script>

<style lang="scss">
    /*deep*/.link-sticky-top:before {
        box-shadow: none!important;
    }
    .dealer-contacts{
        .change-msg::after {
            content: '(变更)';
            color: red;
        }

        .new-msg::after {
            content: '(新增)';
            color: red;
        }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        .edit-text {
            @include flex-end-center;
            width: 100%;
            padding-bottom: 32px;
            padding-top: 32px;
            text-align: right;
            padding-right: 24px;
            font-family: PingFangSC-Regular,serif;
            font-size: 28px;
            color: #2F69F8;
            letter-spacing: 0;
            line-height: 28px;
            .add {
                padding-right: 24px;
            }
        }
        .consumer-rows {
            background: #FFFFFF;
            border-radius: 32px;
            padding: 40px 24px;
            width: 702px;
            margin: auto auto 24px auto;
            .container  {
                .item-container {
                    width: 100%;
                    @include flex-center-start;
                    padding-bottom: 24px;
                    .item-image {
                        width: 80px;
                        height: 80px;
                        border-radius: 50%;
                        .effect {
                            text-align: center;
                            color: #ffffff;
                            position: absolute;
                            background: rgba(0,0,0,0.50);
                            width: 80px;
                            height: 80px;
                            line-height: 80px;
                            border-radius: 50%;
                        }
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                    .item-right {
                        width: 88%;
                        padding-left: 12px;
                        .item-top {
                            @include flex-center-start;
                            @include space-between;
                            margin-bottom: 24px;
                            .item-left {
                                @include flex-start-center;
                                .name {
                                    font-size: 32px;
                                    color: #262626;
                                    text {
                                        color: #2F69F8;
                                        font-size: 28px;
                                        padding-right: 8px;
                                    }
                                }
                                .phone {
                                    font-size: 28px;
                                    color: #8C8C8C;
                                    padding-left: 16px;
                                }
                                .label{
                                    color: #8C8C8C;
                                    font-size: 28px;
                                    padding-right: 16px;
                                }
                                .value {
                                    font-size: 28px;
                                    color: #262626;
                                }
                                .approval-phone::after {
                                    content: '(变更)';
                                    color: red;
                                }

                            }
                            .item-tag {
                                padding-left: 14px;
                                padding-right: 14px;
                                height: 36px;
                                line-height: 36px;
                                text-align: center;
                                color: #ffffff;
                                box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                                background-image: linear-gradient(180deg, #FF8560 0%, #FF4D2D 100%);
                                border-bottom-right-radius: 100px;
                                border-top-left-radius: 90px;
                                border-top-right-radius: 100px;
                                font-size: 20px;
                                margin-right: 8px;
                            }
                            .recep-tag {
                                padding: 0 14px;
                                height: 36px;
                                line-height: 36px;
                                color: #ffffff;
                                box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                                background-image: linear-gradient(180deg, #B6E3FD 0%, #2FAEF8 100%);
                                border-radius: 100px;
                            }
                        }
                        .item-middle {
                            @include flex-start-center;
                            .label{
                                color: #8C8C8C;
                                font-size: 28px;
                                padding-right: 16px;
                            }
                            .value {
                                font-size: 28px;
                                color: #262626;
                            }
                            .line {
                                width: 2px;
                                height: 28px;
                                margin-left: 16px;
                                margin-right: 16px;
                                background: #EEEEEE;
                            }
                        }
                        .comments {
                            padding-top: 24px;
                            .label{
                                color: #8C8C8C;
                                font-size: 28px;
                                padding-right: 10px;
                            }
                            .value {
                                font-size: 28px;
                                color: #262626;
                            }
                        }
                    }
                }
                .button-container {
                    padding-top: 24px;
                    @include flex-end-center;
                    border-top: 1px solid #F2F2F2;
                    .button {
                        width: 158px;
                        height: 60px;
                        line-height: 60px;
                        border: 1px solid #2F69F8;
                        font-size: 28px;
                        color: #2F69F8;
                        text-align: center;
                        margin-left: 2%;
                        border-radius: 8px;
                    }
                }
            }
        }
    }
</style>
