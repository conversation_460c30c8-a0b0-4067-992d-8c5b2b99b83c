<template>
    <view class="dealer-visit-today-list">
        <view class="blank"></view>
        <link-auto-list :option="visitProcessList" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="media-list-rows">
                    <view slot="note" class="visit-list" >
                        <view class="visit-content">
                            <view  @tap="goToItem(data)">
                            <view class="list-item-row">
                                <view class="visit-title">{{data.accntName}}</view>
                                <view class="status-view"><view> 未执行</view></view>
                            </view>
                            <view class="code-line">
                                <view class="code">{{data.accntCode}}</view>
                               <!-- <view class="code-name">{{data.contactName}}</view>-->
                            </view>
                            <view class="contact-line">
                                <view class="contact-title">联系人</view>
                                <view class="contact-name">{{data.contactName}}</view>
                                <view class="contact-phone">{{data.mobilePhone}}</view>
                            </view>
                            </view>
                            <view class="bottom-line">
                                <view class="visit-address">{{data.addrDetailAddr}}</view>
                                <view class="bottom-delete" @tap="deleteVisit(data)">
                                    <link-icon icon="icon-shanchu" ></link-icon>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    export default {
        name: "dealer-visit-today-list",
        data () {
            return {
                coordinate: null,                   // 定位坐标
            }
        },
        props: {
            visitProcessList: {
                type: Object,
                default: {}
            },
        },
        async created () {
        },
        methods: {
            deleteVisit(data){
                this.$dialog({
                    title: '提示',
                    content: '是否确定取消该拜访计划',
                    cancelButton: true,
                    initial: true,
                    cancelText: '取消',
                    confirmText: '确定',
                    onConfirm: () => {
                      this.cancel(data)
                    },
                    onCancel: () => {
                    }
                })
            },
            async  cancel(item){
                item.status='Cancel'
                const data = await this.$http.post('action/link/visitPlan/update', item);
                if(data.success){
                    this.$message.success("取消成功");
                    this.$emit('fresh-list', {});
                }else{

                }
            },
            async goToItem(item) {
                let data
                try {
                    const resp = await this.$http.post('action/link/accnt/queryById', {id: item.acctId});
                    if (!resp.success) {
                        this.$showError('查询终端详情失败！' + resp.result);
                        return;
                    }
                    data = resp.result;
                } catch (e) {
                    this.$showError('查询终端详情出错！');
                    return;
                }
                if(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1612') {
                    const contactList = await this.getContactList(data) || []
                    const flag = contactList.some((item => {
                        return item.isEffective === 'Y'
                    }))
                    if(!flag) {
                        this.$dialog({
                            title: '提示',
                            content: '请完善联系人信息',
                            initial: true,
                            confirmText: '确认'
                        });
                        return
                    }
                }
                const that = this;
                // 根据用户当前的品牌公司代码 === 父值列表类型的独立源代码的
                let queryUserVisit = await that.queryUserVisit();
                let userBrandCompanyCode = that.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode;
                let param = {type: 'ACCT_LEVEL', val: data.acctLevel, returnType: 'parentVal'};
                let parentVal = await this.$lov.getLovByParentVal(param);
                if (that.$utils.isEmpty(queryUserVisit)) {
                    that.$nav.push('/pages/terminal/visit/visit-perform-page', {
                        data: data,
                        noStoreCoreFlag: userBrandCompanyCode === parentVal && data.acctLevel === 'belowA',
                        source: 'terminalMap',
                        createdVisitOriginal: 'terminalMapList'
                    });
                    return
                }
                that.$dialog({
                    title: '提示',
                    content: '您有未完成的拜访，请在拜访中列表进行查看',
                    initial: true,
                    cancelButton: false,
                    onConfirm: () => {
                        return
                    }
                })
            },
            /**
             * 获取终端联系人
             * <AUTHOR>
             * @date 2022-11-4
             * @param data 终端信息
             */
            async getContactList(data) {
                const {rows} = await this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: data.id,
                });
                return rows
            },

            /**
             * 查询拜访中列表是否含有当前人拜访中的记录
             * <AUTHOR>
             * @date 2020-10-15
             */
            queryUserVisit () {
                const that = this;
                return new Promise(resolve => {
                    that.$http.post('action/link/visit/queryByExamplePage', {
                        filtersRaw: [
                            {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: 'visiting', operator: '='},
                            {id: 'visitType', property: 'visitType', value: 'dailySalesCall', operator: '='},
                            {id: 'createdBy', property: 'createdBy', value: that.$taro.getStorageSync('token').result.id, operator: '='},
                        ],
                        oauth: 'MY_POSTN'
                    }).then(data => {
                        if (data.success) {
                            this.$store.commit('visitRecord/setVisitRecord', data.rows[0]);
                            resolve(data.rows);
                        }
                    })
                });
            },
        }
    }
</script>

<style lang="scss">
    .dealer-visit-today-list {
        /*deep*/.link-item {
        padding: 0;
    }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-dropdown-content {
                    padding: 24px;
                }
        .blank {
            width: 100%;
            height: 92px;
            background: #F2F2F2;
        }
        .media-list-rows {
            background: #FFFFFF;
            border-radius: 32px;
            width: 702px;
            margin: 24px auto auto auto;
            .visit-list {
                padding: 24px 16px 24px 24px;
                .visit-content {
                    .list-item-row {
                        @include flex-start-center;
                        @include space-between;
                        .visit-title {
                            flex: 1;
                            @include flex-start-center;
                            font-family: PingFangSC-Regular;
                            font-size: 32px;
                            letter-spacing: 0;
                            line-height: 60px;
                            height: 60px;
                            font-weight: bold;
                            color: #000000;

                        }
                        .status-view {
                            height: 36px;
                            line-height: 36px;
                            text-align: center;
                            color: #ffffff;
                            border-radius: 8px;
                            padding-left: 27px;
                            padding-right: 27px;
                            font-size: 20px;
                            -webkit-transform: skewX(-30deg);
                            -ms-transform: skewX(-30deg);
                            transform: skewX(-30deg);
                            background: #2F69F8;
                            -webkit-box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
                            box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
                            .status {
                                transform: skewX(30deg);
                                font-size: 20px;
                                color: #FFFFFF;
                                letter-spacing: 2px;
                                text-align: center;
                                line-height: 36px;
                            }
                        }
                    }
                    .code-line{
                        display: -webkit-box;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-box-pack: start;
                        -ms-flex-pack: start;
                        justify-content: flex-start;
                        -webkit-box-align: center;
                        -ms-flex-align: center;
                        align-items: center;
                        .code{
                            background: #A6B4C7;
                            border-radius: 8px;
                            font-size: 28px;
                            color: #FFFFFF;
                            padding: 6px 12px;
                        }
                        .code-name{
                            margin-left: 24px;
                            color: #262626;
                        }

                    }

                    .contact-line{
                        display: -webkit-box;
                        display: -ms-flexbox;
                        display: flex;
                        -webkit-box-pack: start;
                        -ms-flex-pack: start;
                        justify-content: flex-start;
                        -webkit-box-align: center;
                        -ms-flex-align: center;
                        align-items: center;
                        margin-top: 12px;
                        .contact-title{}
                        .contact-name{
                            margin-left: 24px;
                            color: #262626;
                        }
                        .contact-phone{
                            margin-left: 24px;
                            color: #262626;
                        }
                    }
                    .bottom-line{
                        @include flex-start-center;
                        @include space-between;
                        .visit-address {
                            padding-top: 24px;
                            font-size: 24px;
                            color: rgba(38, 38, 38, 0.75);
                        }
                        .bottom-delete{
                            padding-left: 32px;
                            padding-right: 32px;
                        }
                    }
                }
            }
        }
    }
</style>
