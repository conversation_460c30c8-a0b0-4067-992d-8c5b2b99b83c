<!-- 开瓶情况 -->
<template>
  <view class="dealer-bottle-open">
    <view class="bottle-open-title">
      <TimeDimensionCmp title="开瓶情况" @change="handlerClick"/>
    </view>
    <!-- 查询维度 -->
    <view class="bottle-open-search">

    </view>
    <!-- 条件查询展示结果--配额，配额执行率，入库，出库，开瓶，扫码入库率 -->
    <view class="bottle-open-content">
      <!-- 暂时不需要展示 -->
      <!-- <view class="bottle-open-content__row"> -->
        <!-- 配额 -->
        <!-- <view class="bottle-open-content__item">
          <view class="bottle-open-content__item--num">
            {{ info.quotaNum || 0 }}
            <text class="text">件</text>
          </view>
          <text class="bottle-open-content__item--text">配额</text>
        </view> -->
        <!-- 配额执行率 -->
        <!-- <view class="bottle-open-content__item">
          <view class="bottle-open-content__item--num">
            {{ info.quotaRate || 0 }}
            <text class="text">%</text>
          </view>
          <text class="bottle-open-content__item--text">配额执行率</text>
        </view> -->
        <!-- 入库 -->
        <!-- <view class="bottle-open-content__item">
          <view class="bottle-open-content__item--num">
            {{ info.scanInBox }}
            <text class="text">件</text>
            {{ info.scanInBottle }}
            <text class="text">瓶</text>
          </view>
          <text class="bottle-open-content__item--text">
            入库
            <link-icon icon="ly-youjiantou-xi" style="color: #999999"/>
          </text>
          <view class="bottle-open-content__item--text">
            退货入库{{ info.scanRefundInBox || 0 }}件{{ info.scanRefundInBottle || 0 }}瓶
          </view>
        </view>
      </view> -->

      <view class="bottle-open-content__row">
        <!-- 出库 暂时不需要展示-->
        <!-- <view class="bottle-open-content__item">
          <view class="bottle-open-content__item--num">
            {{ info.scanOutBox || 0 }}
            <text class="text">件</text>
            {{ info.scanOutBottle || 0 }}
            <text class="text">瓶</text>
          </view>
          <text class="bottle-open-content__item--text">
            入库
          </text>
          <view class="bottle-open-content__item--text">退货出库{{ info.scanTuiHuoOutBox || 0 }}件{{ info.scanTuiHuoOutBottle || 0 }}瓶</view>
        </view> -->
        <!-- 开瓶 -->
        <view class="bottle-open-content__item">
          <view class="bottle-open-content__item--num">
            {{ info.openBottleSum || 0 }}
            <text class="text">瓶</text>
          </view>
          <text class="bottle-open-content__item--text">
            本地开瓶数
          </text>
        </view>
          <!-- 开瓶人数 -->
          <view class="bottle-open-content__item">
              <view class="bottle-open-content__item--num">
                  {{ info.sumOpenIdNum || 0 }}
                  <text class="text">人</text>
              </view>
              <text class="bottle-open-content__item--text">开瓶人数</text>
          </view>
          <view class="bottle-open-content__item">
              <view class="bottle-open-content__item--num">
                  {{ differData.differAreaOpenBottleSum || 0 }}
                  <text class="text">瓶</text>
              </view>
              <text class="bottle-open-content__item--text">异地开瓶数</text>
          </view>
          <view class="bottle-open-content__item">
              <view class="bottle-open-content__item--num">
                  {{ differData.differAreaOpenBottleRate || 0 }}
                  <text class="text">%</text>
              </view>
              <text class="bottle-open-content__item--text">异地扫码率</text>
          </view>
          <view class="bottle-open-content__item" @tap="goDistriDetail">
              <view class="bottle-open-content__item--num">
                  <text class="text blue-btn">查看详情</text>
                  <link-icon icon="icon-chakan"></link-icon>
              </view>
              <text class="bottle-open-content__item--text">终端异地分布</text>
          </view>
        <!-- 扫码出库率 暂时不需要展示-->
        <!-- <view class="bottle-open-content__item">
          <view class="bottle-open-content__item--num">
            {{ (info.scanInRate && info.scanInRate * 100 || 0).toFixed(2) }}
            <text class="text">%</text>
          </view>
          <text class="bottle-open-content__item--text">扫码出库率</text>
        </view> -->
      </view>
    </view>
      <!-- 终端异地分布详情 -->
      <link-dialog class="dialog-bottom" ref="dialog" position="bottom" height="75vh" noPadding>
          <!-- 标题 -->
          <view class="dialog-title">
              <view>终端异地分布(近30天)</view>
              <link-icon icon="mp-close" class="close-icon" @tap="$refs.dialog.hide()"/>
          </view>
          <!-- 表格内容 -->
          <scroll-view class="list-wrap" :scroll-y="true" :style="{height: 'calc(75vh - 44px)'}">
              <view class="table-row-title">
                  <view class="column-title" style="width: 40%">流出省份</view>
                  <view class="column-title" style="width: 30%">流出瓶数</view>
                  <view class="column-title" style="width: 30%">占比</view>
              </view>
              <view v-for="data in differData.differAreaOpenBottleProvinceGroup" :key="data.id">
                  <view class="table-row">
                      <view class="column" style="width: 40%">
                          <view class="text">{{data.province}}</view>
                      </view>
                      <view class="column" style="width: 30%">
                          <view class="text">{{data.differAreaTotal || 0}}</view>
                      </view>
                      <view class="column" style="width: 30%">
                          <view class="text">{{data.rate}}%</view>
                      </view>
                  </view>
              </view>
          </scroll-view>
      </link-dialog>
  </view>
</template>
<script>
import TimeDimensionCmp from './TimeDimensionCmp.vue';

export default {
  name: 'dealer-bottle-open',
  data() {
    return {
      active: 'day',
      differData: {},
      info: {}
    }
  },
  props: {
    acctData: {
      type: Object,
      default: () => ({}),
    },
    dataBoardActive: {
      type: Object,
      default: () => ({}),
    }
  },
  components: {
    TimeDimensionCmp
  },
  watch: {
    dataBoardActive: {
      handler(v) {
        const { seq } = this.dataBoardActive;
        const { id } = this.acctData;
        if (seq && id) {
          this.initData();
        }
      },
      immediate: true,
      deep: true
    }
  },

  methods: {
      /**
       * 终端异地分布详情
       * <AUTHOR>
       * @date    2024/10/28 15:52
       */
      goDistriDetail() {
          if (!this.differData.differAreaOpenBottleProvinceGroup || !this.differData.differAreaOpenBottleProvinceGroup.length) {
              this.$showError('暂无终端异地分布数据！');
              return;
          }
          this.$refs.dialog.show();
      },
    handlerClick(element) {
      this.active = element;
      this.initData();
    },
    async initData() {
      const params = {
        attr1: 'QWQuery',
        acctId: this.acctData.id,
        period: this.active,
        conditionId: this.dataBoardActive.seq
      }
      const res = await this.$http.post("/exportterminal/link/acctScanData/queryAcctScanNum", params, {autoHandleError: false});
      if (res) {
        this.info = res;
      }
      const differ = await this.$http.post('/terminal/link/acctScanData/queryDifferAreaOpenBottleSum', {
          acctId: this.acctData.id,
          period: this.active,
          conditionId: this.dataBoardActive.seq
      });
      if (differ.success) {
          this.differData = differ.result;
      }
    }
  }
}
</script>
<style lang="scss">
.dealer-bottle-open {
  margin-top: 54px;
  &-content {
    margin-top: 44px;
    &__row {
      display: flex;
      flex-wrap: wrap;
      margin-bottom: 20px;
      &:last-child {
        margin-bottom: 40px;
      }
    }
    &__item {
      flex: 1;
      text-align: center;
      min-width: 30%;
      flex: 0.33;
      &--num {
        font-family: ONEAN;
        font-size: 48px;
        color: #333333;
        text-align: center;
        line-height: 56px;
        font-weight: 700;
        .text {
          font-size: 24px;
          color: #333333;
          padding-left: 4px;
          line-height: 36px;
          font-weight: 400;
        }

          .blue-btn {
              color: #12B7F5;
          }

          .icon-chakan {
              color: #12B7F5;
              font-size: 28px;
          }
      }
      &--text {
        font-family: PingFangSC-Regular;
        font-size: 24px;
        color: #999999;
        text-align: center;
        line-height: 16px;
        font-weight: 400;
      }
    }
  }

    .dialog-bottom {

        .dialog-title {
            padding: 24px;
            position: relative;
            font-size: 32px;
            color: #333333;
            font-weight: 500;
            @include flex-center-center;

            .close-icon {
                position: absolute;
                right: 24px;
                color: #999;
                font-size: 32px;
            }
        }

        .list-wrap {

            .table-row-title {
                display: flex;
                width: 100%;
                background: #6D96FA;

                .column-title {
                    font-size: 24px;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 72px;
                    line-height: 72px;
                    text-align: center;
                    color: #fff;
                }
            }

            .link-auto-list-wrapper {
                padding: 0;
            }

            .table-row {
                display: flex;
                width: 100%;

                .column {
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 112px;
                    line-height: 112px;
                    border-right: 2px solid #DEE7FE;
                    text-align: center;
                    border-bottom: 2px solid #DEE7FE;

                    .text{
                        display: inline-block;
                        color: #262626;
                        font-size: 24px;
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                    }
                }
            }
        }
    }
}

</style>
