/**
* @createdBy  康丰强
* @date  2023/4/18
* @description 商超动销
*/
<template>
    <view class="dealer-shang-chao-sell">
        <link-auto-list :option="shangChaoSellOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item">
                    <view slot="note">
                        <view class="media-list">
                            <view class="num-view">
                                <view class="num">{{data.prodCode}}</view>
                                <view class="time">导入时间 {{data.created }}</view>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="name">{{data.prodName}}</view>
                        </view>
                        <view class="content-foot">
                            <view class="foot-item date">{{`${data.salesDate.slice(0, 4)}年${data.salesDate.slice(4, 6)}月`}}</view>
                            <view class="foot-item">{{data.salesQuantity}}瓶</view>
                            <view class="foot-item">￥{{data.salesAmount}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>

export default {
    name: "dealer-shang-chao-sell",
    props: {
        acctCode: {
            type: String,
            default: '',
            required: true
        },
    },
    data(){
        let oauth = 'MULTI_ORG'
        const arr = ['Salesman', 'SalesSupervisor', 'AccountManager']
        if(arr.includes(this.$taro.getStorageSync('token').result.positionType)) oauth = 'MULTI_POSTN'
        return{
            shangChaoSellOption: new this.AutoList(this, {
                module: 'action/link/superSalesData',
                searchFields: null,
                param: {
                    oauth,
                    filtersRaw: [
                        {id: 'acctCode', property: 'acctCode', value: this.acctCode, operator: '='}
                    ]
                },
                hooks: {
                    beforeLoad({param}) {
                        param.order = 'desc'
                        param.sort = 'salesDate'
                    }
                }
            }),
        }
    }

}
</script>

<style lang="scss">
@import "../../../../styles/list-card";
/*deep*/.link-sticky-top:before {
    box-shadow: none!important;
}
.dealer-shang-chao-sell{
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular,serif;
    .perform-case-list-item {
        background: #FFFFFF;
        margin: auto 24px 24px 24px;
        border-radius: 16px;
    }
    /*deep*/.link-auto-list-top-bar {
                border-bottom: none;
            }
    .media-list {
        @include media-list;
        .num-view {
            width: 100%;
            display: flex;
            justify-content: space-between;
            border-radius: 8px;
            line-height: 40px;
            background-color: white;
            font-size: 28px;

            .num {
                background: #A6B4C7;
                color: #FFFFFF;
                letter-spacing: 0;
                line-height: 40px;
                padding: 2px 8px;
            }

            .time {
                font-size: 24px;
                display: flex;
                align-items: center;
                color: #262626;
            }
        }
    }
    .content-middle{
        width: 100%;
        @include flex-start-center;
        @include space-between;
        margin: 24px 0;
        .name{
            font-family: PingFangSC-Semibold,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
        }
    }
    .content-foot {
        font-size: 30px;
        display: flex;
        justify-content: space-around;
        color: #262626;
        .foot-item {
            flex: 2;
        }
        .date {
            flex: 3
        }
    }
}
</style>
