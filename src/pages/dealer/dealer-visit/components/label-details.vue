<template>
    <view class="dealer-label-details">
        <link-auto-list :option="labelInfoOption" hideCreateButton>
            <template  slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="label-details-item">
                    <view slot="note">
                        <view class="label-details-warp">
                            <view class="label-details-container" v-for="(item, num) in detailsField" :key="`detailsField_${num}`">
                                <view class="label-details-label">
                                    {{item.label}}
                                </view>
                                <view class="label-details-val" v-if="item.field === 'markValue'">
                                    {{data[item.field] | lov(toLine(data.markType))}}
                                </view>
                                <view class="label-details-val" v-else>
                                    {{data[item.field]}}
                                </view>
                            </view>
                            <view class="prod-tag">
                                <view class="item-tag m-w70" :class="{gray: data.effectiveFlag === 'N'}">
                                    <view class="tag-content">{{data.effectiveFlag | lov('POINTS_STATE')}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
                
            </template>
        </link-auto-list>
    </view>
</template>
<script>
export default {
    name: 'dealer-label-details',
    props: {
        acctData: {
            type: Object,
            default() {
                return {}
            }
        }
    },
    data() {
        const labelInfoOption = new this.AutoList(this, {
                module: 'action/link/accountPartMark',
                url: {
                    queryByExamplePage: 'action/link/accountPartMark/queryByAccntIdPage'
                },
                param: {
                    accntId: this.acctData.id,
                },
                sortOptions: null,
                searchFields: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    }
                }
            });
        return {
            labelInfoOption,
            detailsField: [
                {
                    label: '标签名称',
                    field: 'markValue',
                },
                {
                    label: '品项名称',
                    field: 'partName'
                },
                {
                    label: '生效开始时间',
                    field: 'startTime'
                },
                {
                    label: '失效结束时间',
                    field: 'endTime'
                }
            ]
        }
    },
    methods: {
        // 驼峰转换下划线
        toLine(name) {
            let temp = name.replace(/([A-Z])/g, '_$1').toUpperCase();
            // 如果首字母是大写，执行replace时会多一个_，这里需要去掉
            if(temp.slice(0, 1) === '_'){ 
                temp = temp.slice(1);
            }
            return temp;
        }
    },
}
</script>
<style lang="scss">
.dealer-label-details {
    padding-top: 30px;
    .link-item {
        margin-bottom: 30px;
        border-radius: 20px;
    }
    .label-details-item {
        .label-details-container {
            height: 80px;
            line-height: 80px;
            display: flex;
            .label-details-label {
                color: #8C8C8C;
                width: 200px;
            }
            .label-details-val {
                color: #262626;
            }
        }
    }
    .label-details-warp {
        font-size: 28px;
        position: relative;
    }
    .prod-tag {
        display: flex;
        justify-content: space-around;
        position: absolute;
        top: 10px;
        right: 10px;
        .item-tag {
            min-width: 100px;
            height: 36px;
            line-height: 36px;
            text-align: center;
            color: #ffffff;
            background: #2F69F8;
            box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
            border-radius: 8px;
            padding-left: 27px;
            padding-right: 27px;
            font-size: 20px;
            margin-right: 8px;
            transform: skewX(-30deg);
            .tag-content {
                transform: skewX(30deg);
            }
        }
        .gray {
            background: rgb(158,161,174);
            box-shadow: 0 3px 4px 0 rgba(158,161,174,0.35);
        }

        .m-w70 {
            min-width: 70px;
        }
    }
}
</style>