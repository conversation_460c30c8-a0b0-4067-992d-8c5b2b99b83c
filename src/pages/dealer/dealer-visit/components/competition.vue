<template>
  <link-page class="dealer-competition">
    <view class="last-collect" @tap="checkHistory()">
      <view class="collect-content">
        <text class="iconfont icon-time-circle"></text>
        <text class="collect-text">最新采集</text>
        <text>{{lastCollectData.created | date('YYYY-MM-DD')}} {{lastCollectData.createdName}}</text>
      </view>
      <view class="view-history">查看历史</view>
    </view>
    <lnk-no-auth label="该终端暂无竞品" :authFlag="false" v-if="notCompetitionFlag"></lnk-no-auth>
    <link-auto-list :option="competitionOption" hideCreateButton v-else>
      <template slot-scope="{data,index}">
        <item :key="index" :data="data" :arrow="false" class="competition-card">
          <view slot="note">
            <view class="competition-card-head" v-if="defaultType === 'normal'">
              <view class="name">
                {{data['competitorBrand']}}/{{data['competitorSeries']}}
              </view>
              <view class="operator" v-if="!showHead">
                <view class="operator-btn" @tap="deleteFunc(data)">删除</view>
                <view class="operator-btn" @tap="editFunc(data)">编辑</view>
              </view>
            </view>
            <view class="competition-card-head" v-else>
              <view class="competition-head-info">
                <view class="label">采集时间</view>
                <view class="get-time">{{data['created'] | date('YYYY-MM-DD')}}</view>
                <view class="account-name">{{data['accntName']}}</view>
              </view>
              <view class="title">
                {{data['competitorBrand']}}/{{data['competitorSeries']}}
              </view>
            </view>
            <view class="competition-card-content">
              <view class="product-info">
                <view class="product-info-item">
                  <view class="product-info-data">
                    {{data['inventoryNum']}}
                  </view>
                  <view class="product-info-label">
                    库存数量(件)
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                    {{data['mainTransPrice']}}
                  </view>
                  <view class="product-info-label">
                    主流成交价
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                    {{data['purchasePrice']}}
                  </view>
                  <view class="product-info-label">
                    采购价
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                    {{data['displayWay'] | lov('DISPLAY_WAY')}}
                  </view>
                  <view class="product-info-label">
                    陈列方式
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                    {{data['displayArea']}}
                  </view>
                  <view class="product-info-label">
                    陈列面
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                    {{data['duitouFlag'] | lov('IS_FLAG')}}
                  </view>
                  <view class="product-info-label">
                    是否堆头
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                    <text v-for="(val, key) in data.promotionMaterial" :key="key">{{val | lov('PROMOTION_MATERIAL')}}<text v-if="(key + 1) < data.promotionMaterial.length">,</text></text>
                  </view>
                  <view class="product-info-label">
                    促销物料
                  </view>
                </view>
                <view class="product-info-item">
                  <view class="product-info-data">
                      {{data['startSellNum']}}
                  </view>
                  <view class="product-info-label">
                      本月动销件数
                  </view>
                </view>
              </view>
              <view class="product-image">
                <lnk-img :parentId="data.id"
                         :pathKeyArray="data.attachmentList"
                         :moduleType="'competition'"
                         :delFlag="false"
                         :newFlag="false"
                ></lnk-img>
              </view>
              <view class="product-comments" v-if="data&&data['comments']">
                {{data['comments']}}
              </view>
            </view>
          </view>
        </item>
      </template>
    </link-auto-list>
  </link-page>
</template>

<script>
  import HeadType from "../../../terminal/components/head-type";
  import LnkImg from "../../../core/lnk-img/lnk-img";
  import lnkNoAuth from "../../../core/lnk-no-auth/lnk-no-auth"

  export default {
    name: "dealer-competition",
    props: {
        isDealer: {
            type: Boolean,
            default: false
        },
      showHead: {
        type: Boolean, // 页面顶部title是否展示
        default: true
      },
      queryId: {     // 终端ID
        type: String
      },
      freshFlag: {    // 接口是否需要重新请求
        type: Boolean,
        default: false
      },
      defaultType: {  // 卡片title样式设置，默认为normal
        type: String,
        default: 'normal'
      },
      tabFlag: { // 查询一条或列表查询标识
        type: String,
        default: ''
      },
      competitionAll: {     // 所有竞品采集数据
        type: Array
      }
    },
    components: {LnkImg, HeadType, lnkNoAuth},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
      const competitionOption =  new this.AutoList(this, {
        module: 'action/link/visitCompetitor',
        param: () => {
            const filtersRaw = [];
            // 特定公司的经销商人员只能看到自己职位创建的数据
            if (this.isDealer) {
                filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
            }
            return {
                accntId: this.queryId,
                sort: 'created',
                order: 'desc',
                tabFlag: 'Y',
                filtersRaw
            }
        },
        loadOnStart: false,
        sortOptions: null,
        searchFields: null,
        filterBar: {},
        hooks: {
          afterLoad(option) {
            this.notCompetitionFlag = option.rows.filter(item => item.existFlag === 'N').length !== 0;
            if (!this.notCompetitionFlag) {
              option.rows.forEach(item => {
                if (!this.$utils.isEmpty(item.promotionMaterial)) {
                  item.promotionMaterial = item.promotionMaterial.split(',');
                }
              });
              if (option.rows.length !== 0) {
                this.lastCollectData = option.rows[0]
              }
            }
          }
        }
      });
      return {
        lastCollectData: {},
        competitionOption,           // 竞品列表
        notCompetitionFlag: false
      }
    },
    watch: {
      /**
       *  @description: 是否刷新当前页面
       *  @author: 马晓娟
       *  @date: 2020/8/27 10:30
       */
      freshFlag(val) {
        if (val) {
          this.competitionOption.methods.reload()
        }
      },
      /**
       *  @description: 从父直接传入所有的竞品数据
       *  @author: 马晓娟
       *  @date: 2020/8/27 10:31
       */
      competitionAll (val) {
       if (val.length > 0) {
         this.competitionOption.list = val;
       }
      }
    },
    created () {
      if (this.queryId) {
        this.competitionOption.methods.reload()
      }
    },
    methods: {
      /**
       *  @description: 删除方法
       *  @author: 马晓娟
       *  @date: 2020/8/13 16:22
       */
      deleteFunc(item) {
        this.$emit('delete', item);
      },
      /**
       *  @description: 查看历史
       *  @author: 马晓娟
       *  @date: 2020/8/17 20:16
       */
      checkHistory() {
        this.$nav.push('/pages/terminal/terminal/history-competition-page', {
          accntId: this.queryId
        });
      },
      /**
       *  @description: 编辑方法
       *  @author: 马晓娟
       *  @date: 2020/8/13 16:22
       */
      editFunc(item) {
        this.$emit('edit', item);
      }
    }
  }
</script>

<style lang="scss">
  .dealer-competition {
    font-size: 28px;
    line-height: 28px;
    margin: 24px;
    .last-collect {
      background: #FFFFFF;
      border-radius: 8px;
      widows: 702px;
      height: 76px;
      @include flex-start-center;
      @include space-between;
      padding-right: 24px;
      padding-left: 24px;
      margin-bottom: 24px;

      .collect-content {
        .icon-time-circle {
          color: #8C8C8C;
          font-size: 28px;
        }
        .collect-text {
          color: #8C8C8C;
          font-size: 28px;
        }
        .last-time {
          color: #000000;
          font-size: 28px;
        }
      }
      .view-history {
        font-size: 28px;
        color: #2F69F8;
      }
    }
    .competition-card {
      background: #FFFFFF;
      border-radius: 16px;
      margin-bottom: 24px;
      /*deep*/ .link-item-icon{
      width: 0 !important;
    }
      .competition-card-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px dashed #DADEE9;
        padding-bottom: 31px;

        .name {
          font-size: 32px;
          color: #262626;
          letter-spacing: 0;
          line-height: 32px;
          font-weight: 500;
        }

        .operator {
          display: flex;
          justify-content: center;
          align-items: center;

          .operator-btn {
            color: #2F69F8;
            font-size: 28px;
            line-height: 28px;
            margin-right: 12px;
          }
        }

        .competition-head-info {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 60%;
          font-size: 24px;

          .label {
            color: #8C8C8C;
            margin-right: 10px;
          }

          .get-time {
            margin-right: 10px;
          }
        }

        .title {
          width: 40%;
          font-size: 32px;
          color: #262626;
          letter-spacing: 0;
          line-height: 32px;
          font-weight: 500;
          text-align: right;
        }
      }

      .competition-card-content {
        font-size: 28px;
        line-height: 28px;
        letter-spacing: 0;

        .product-info {
          display: flex;
          width: 100%;
          flex-direction: row;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 30px;

          .product-info-item {
            width: 33%;
            .product-info-data {
              color: #262626;
              text-align: center;
              line-height: 28px;
              padding-bottom: 16px;
            }

            .product-info-label {
              color: #8C8C8C;
              text-align: center;
              line-height: 28px;
              margin-bottom: 32px;
            }
          }
        }

        .product-image {
          display: flex;
          flex-direction: row;
          /*deep*/
          .lnk-img-item {
            width: 180px;
            height: 180px;
          }

        }
        .product-comments{
          margin-top: 32px;
          border-top: 2px dashed #DADEE9;
          font-size: 28px;
          color: #262626;
          letter-spacing: 0;
          line-height: 28px;
          padding: 32px 0 34px 0;
        }
      }
    }
  }
</style>
