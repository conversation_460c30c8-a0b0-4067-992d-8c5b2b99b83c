<template>
    <view class="dealer-basic-info">
        <view class="edit-text" @tap="goEditBasicInfo" v-if="editFlag && !disabledNewEdit && clientDetails.editApprovalStatus !== 'Submitted' && $utils.isNotEmpty(clientDetails)">编辑</view>
        <view class="edit-text"  v-else></view>
        <view class="card-content">
            <view class="card-rows" v-if="clientDetails.acctCode">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['acctCode']}">客户编码</view>
                    <view class="value">{{clientDetails.acctCode}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" v-if="clientDetails.acctCategory === 'qdlx-2'" :class="{'change-fielld': changeDetails['acctName']}">终端名称</view>
                    <view class="label" v-else :class="{'change-fielld': changeDetails['acctName']}">门头名称</view>
                    <view class="value">{{clientDetails.acctName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctType === 'Distributor'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['acctName']}">分销商名称</view>
                    <view class="value">{{clientDetails.acctName}}</view>
                </view>
            </view>
            <view class="card-rows">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['acctType,acctCategory,subAcctType']}">客户分类</view>
                    <view class="value">
                        <text v-if="clientDetails.acctType">{{clientDetails.acctType | lov('ACCT_TYPE')}}</text>
                        <text v-if="clientDetails.acctCategory">/{{clientDetails.acctCategory | lov('ACCNT_CATEGORY')}}</text>
                        <text v-if="clientDetails.subAcctType">/{{clientDetails.subAcctType | lov('SUB_ACCT_TYPE')}}</text>
                    </view>
                </view>
            </view>
            <view class="card-rows" v-if="isShiJiaZhuang">
                <view class="card-rows-content">
                    <view class="label">客户类型</view>
                    <view class="value">{{clientDetails.acctSort | lov('ACCT_SORT')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="isShiJiaZhuang">
                <view class="card-rows-content">
                    <view class="label">客户性质</view>
                  <view class="value">{{clientDetails.acctNature | lov('ACCT_NATURE')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="isShiJiaZhuang">
                <view class="card-rows-content">
                    <view class="label">考核客户</view>
                    <view class="value">{{clientDetails.accessAcctName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-2'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['kaSystemName']}">系统名称</view>
                    <view class="value">{{clientDetails.kaSystemName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.xAttr71">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['xAttr71']}">店号</view>
                    <view class="value">{{clientDetails.xAttr71}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory !== 'qdlx-2' && !$utils.isEmpty(clientDetails.simName)">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['simName']}">客户简称</view>
                    <view class="value">{{clientDetails.simName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="isGuojiao && !$utils.isEmpty(clientDetails.accntLogisticsCode)">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['accntLogisticsCode']}">客户喷码</view>
                    <view class="value">{{clientDetails.accntLogisticsCode}}</view>
                </view>
            </view>
            <view class="card-rows line" >
                <view class="card-rows-content">
                    <view class="label-address" :class="{'change-fielld': changeDetails['province,city,district,address,townName']}">门店地址</view>
                    <view class="value-address">{{clientDetails.province}}{{clientDetails.city}}{{clientDetails.district}}{{clientDetails.townName}}{{clientDetails.address}}</view>
                </view>
            </view>

            <!-- 考核组织 -->
            <view class="card-rows line"  v-if="clientDetails.acctType === 'Terminal' && showOrg">
                <view class="card-rows-content">
                    <view class="label-address" :class="{'change-fielld': changeDetails['province,city,district,address,townName']}">考核组织</view>
                    <view :class="['value-address', {'red':clientDetails.orgStatus === '0' }]">{{clientDetails.assessOrgName }}</view>
                </view>
            </view>

            <view class="card-rows line"  v-if="clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['chainStoreFlag']}">是否为连锁模式</view>
                    <view class="value">{{clientDetails.chainStoreFlag | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows line" v-if="clientDetails.isExclusiveShop">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['isExclusiveShop']}">是否泸州老窖官方形象店</view>
                    <view class="value">{{clientDetails.isExclusiveShop | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows line" v-if="clientDetails.isShareHolderShop">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['isShareHolderShop']}">是否专营公司股东门店</view>
                    <view class="value">{{clientDetails.isShareHolderShop | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows line" v-if="isJiaoLing && clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['channelMemberFlag']}">是否精英荟会员</view>
                    <view class="value">{{clientDetails.channelMemberFlag | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows line" v-if="clientDetails.ownStores">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ownStores']}">是否经销商自有门店</view>
                    <view class="value">{{clientDetails.ownStores | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <!-- <view class="card-rows line" v-if="showFortTerminal">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['starfireFlag']}">是否星火终端</view>
                    <view class="value">{{clientDetails.starfireFlag | lov('IS_FLAG')}}</view>
                </view>
            </view> -->
            <view class="card-rows" v-if="clientDetails.chainStoreFlag === 'Y'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['chainHeadStoreFlag']}">是否为连锁总店</view>
                    <view class="value">{{clientDetails.chainHeadStoreFlag | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows"  v-if="clientDetails.chainStoreFlag === 'Y' && clientDetails.chainHeadStoreFlag === 'N'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['chainHeadStoreName']}">连锁总店名称</view>
                    <view class="value">{{clientDetails.chainHeadStoreName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="isChuanDong">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['accntPartner']}">{{'是否合作客户'}}</view>
                    <view class="value">{{clientDetails.accntPartner | lov('ACCT_STATE')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['acctLevel']}">{{clientDetails.acctCategory === 'qdlx-4' ? '酒店档次' : '客户规划等级'}}</view>
                    <view class="value">{{clientDetails.acctLevel | lov('ACCT_LEVEL')}}</view>
                </view>
            </view>
            <view class="card-rows"  v-if="clientDetails.acctType === 'Terminal' && clientDetails.acctCategory !== 'qdlx-2'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['capacityLevel']}">容量级别</view>
                    <view class="value">{{clientDetails.capacityLevel| lov('CAPACITY_LEVEL')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['judgmentFlag']}">是否为品鉴基地</view>
                    <view class="value">{{clientDetails.judgmentFlag| lov('IS_FLAG')}}</view>
                </view>
            </view>
             <view class="card-rows"
                   v-if="clientDetails.trafficHighland && clientDetails.acctType === 'Terminal'
                         && clientDetails.acctCategory === 'qdlx-4'
                         && ['5161', '5902', '5903'].includes(clientDetails.mdmCompanyCode)">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['trafficHighland']}">是否流量高地</view>
                    <view class="value">{{clientDetails.trafficHighland | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['appreciationFlag']}">是否为活动场地</view>
                    <view class="value">{{clientDetails.appreciationFlag| lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="broadCompanyCode.indexOf(userCompanyCode) !== -1">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['joinFlag']}">合作状态</view>
                    <view class="value">{{clientDetails.joinFlag| lov('JOIN_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.judgmentFlag === 'Y' && clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['imageRoomNum']}">形象包间数</view>
                    <view class="value">{{clientDetails.imageRoomNum}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['roomTotalNum']}">包间总数</view>
                    <view class="value">{{clientDetails.roomTotalNum}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['businessStartTime']}">营业开始时间</view>
                    <view class="value">{{clientDetails.businessStartTime}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['businessEndTime']}">营业结束时间</view>
                    <view class="value">{{clientDetails.businessEndTime}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['storePhone']}">订餐号码/门店电话</view>
                    <view class="value">{{clientDetails.storePhone}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['customerUnitPrice']}">客单价</view>
                    <view class="value">{{clientDetails.customerUnitPrice}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['cuisineCategory']}">菜系</view>
                    <view class="value">{{clientDetails.cuisineCategory| lov('CUISINE_TYPE')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['isIntangibleCuisine']}">是否有非遗菜</view>
                    <view class="value">{{clientDetails.isIntangibleCuisine| lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['isScenicRestaurant']}">是否为景区餐饮店</view>
                    <view class="value">{{clientDetails.isScenicRestaurant| lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['waiterCount']}">服务员数量</view>
                    <view class="value">{{clientDetails.waiterCount}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['isAtmosphereStore']}">是否为氛围物料门店</view>
                    <view class="value">{{clientDetails.isAtmosphereStore| lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['isCoreStore']}">是否餐酒项目核心门店</view>
                    <view class="value">{{clientDetails.isCoreStore| lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctCategory === 'qdlx-4'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['hallTotalNum']}">大厅桌数</view>
                    <view class="value">{{clientDetails.hallTotalNum}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['xAttr50']}">店区位</view>
                    <view class="value">{{clientDetails.xAttr50 | lov('STORE_POSITION_TYPE')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['area']}">店面积</view>
                    <view class="value">{{clientDetails.area | lov('STORE_AREA')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.acctType === 'Terminal'">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['doorSigns']}">门头店招</view>
                    <view class="value">{{clientDetails.doorSigns| lov('DOOR_SIGNS')}}</view>
                </view>
            </view>
            <view class="card-rows">
                <view class="card-rows-content">
                    <view class="label" >创建人</view>
                    <view class="value">{{clientDetails.createdByName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="isGuojiao || positionType === 'SysAdmin'">
                <view class="card-rows-content">
                    <view class="label">终端数字认证</view>
                    <view class="value">{{clientDetails.terminalDigitization | lov('TERMINAL_DIGITIZATION')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="isYangShengOrYouXuan">
                <view class="card-rows-content">
                    <view class="label">审批状态</view>
                    <view class="value">{{clientDetails.auditStatus | lov('ACCT_OPEN_AUDIT_STATUS')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="editToApprovedFlag || positionType === 'SysAdmin'">
                <view class="card-rows-content">
                    <view class="label">编辑审批状态</view>
                    <view class="value">{{clientDetails.editApprovalStatus | lov('CONTACT_STATUS')}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="clientDetails.comments">
                <view class="card-rows-content">
                    <view class="label">备注</view>
                    <view class="value">{{clientDetails.comments}}</view>
                </view>
            </view>
        </view>
        <view class="card-content"
              style="margin-bottom: 100px"
              v-if="newFlag || positionType==='RegionSysAdmin'">
            <view class="card-title">
                <view class="sync-info-title">同步信息</view>
                <view class="sync-info-button">
                    <link-button :block="false" @tap="reSync" size="mini">重新同步</link-button>
                </view>
            </view>
            <view class="card-rows">
                <view class="card-rows-content">
                    <view class="label">同步状态</view>
                    <view class="value">{{clientDetails.synExternalSysFlag | lov('IS_FLAG')}}</view>
                </view>
            </view>
            <view class="card-rows">
                <view class="card-rows-content">
                    <view class="label">同步时间</view>
                    <view class="value">{{clientDetails.synTime | date('YYYY-MM-DD HH:mm:ss')}}</view>
                </view>
            </view>
            <view class="card-rows">
                <view class="card-rows-content">
                    <view class="label-address">失败原因</view>
                    <view class="value-address">{{clientDetails.synExternalSysInfo}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    export default {
        name: "dealer-basic-info",
        data(){
            return {
                positionType: '', // 当前用户职位类型
                broadCompanyCode: '', // 博大公司编码集
                userCompanyCode: '', // 当前用户所属品牌公司编码
                showOrg: false, // 考核组织展示标识
                // showFortTerminal: false // 是否星火终端展示标识
            }
        },
        props: {
            changeDetails: {
                type: Object,
                default: () => ({})
            },
            isJiaoLing: {
                type: Boolean,
                default: false
            },
            isGuojiao: {
                type: Boolean,
                default: false
            },
            isChuanDong: {
                type: Boolean,
                default: false
            },
            isShiJiaZhuang: {
                type: Boolean,
                default: false
            },
            newFlag: {
                type: Boolean,
                default: false
            },
            editToApprovedFlag: {
                type: Boolean,
                default: false
            },
            clientDetails: {
                type: Object,
                default: {}
            },
            editFlag: {
                type: Boolean,
                default: true
            },
            sellProductArr: {
                type: Array,
                default: []
            },
          disabledNewEdit:{
            type: Boolean,
            default: false
          },
            isYangShengOrYouXuan: {
                type: Boolean,
                default: false
            }

        },
        async created() {
            // 获取当前用户信息
            const userInfo = this.$taro.getStorageSync('token').result;
            this.positionType = userInfo.positionType;
            this.userCompanyCode = userInfo.coreOrganizationTile.brandCompanyCode;
            // 获取是否博大公司标识编码
            this.broadCompanyCode = await this.queryCfgProperty('getPurchaseSumForOrder');
            const curBrandCompanyCode = userInfo.coreOrganizationTile.brandCompanyCode;
            const res = await this.$utils.getCfgProperty('IS_CHECK_ORGANIZATION');
            const isBrandCompanyCode = res && res.length && res.includes(curBrandCompanyCode);
            this.showOrg = res.includes(this.clientDetails.mdmCompanyCode) && (!userInfo.coreOrganizationTile.l3Code) || isBrandCompanyCode;
        },
        // watch: {
        //     'clientDetails.mdmCompanyCode': {
        //         async handler(newVal, oldVal) {
        //             const brandCompanyCodeArr = await this.$utils.getCfgProperty('SPARK_TERMINAL');
        //             if (newVal) {
        //                 this.showFortTerminal = brandCompanyCodeArr.includes(newVal) && this.clientDetails.acctType === 'Terminal'
        //             }
        //         },
        //         immediate: true,
        //         deep: true // 表示开启深度监听
        //     }
        // },
        methods: {
            async goEditBasicInfo () {
                if(this.clientDetails.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                this.$emit("checkEdit",(success) => {
                    if (success) {
                        const url = this.editToApprovedFlag
                            ? '/pages/terminal2/edit-construction/edit-approve-page'
                            : '/pages/terminal2/new-construction/new-construction-page'
                        this.$nav.push(url, {
                            editFlag: 'edit',
                            activeNum: 0,
                            data: this.clientDetails,
                            accntId: this.clientDetails.id,
                            title: '基础信息',
                            editModule: 'basicInfo',
                            identifying: true,
                            sellProductFlag: this.sellProductArr.filter(item => item.status === 'Y').length === 0,
                        })
                    }
                });
            },
            /**
             * 重新同步
             * <AUTHOR>
             * @date 6/9/21
             */
            async reSync() {
                this.$utils.showLoading();
                try {
                    const data = await this.$http.post('action/link/datasyn/synAccntInfo', {id: this.clientDetails.id});
                    this.$utils.hideLoading();
                    if (data.success) {
                        if(data.result.startsWith('成功')) {
                            this.$message.success({message:'已重新同步，请查看同步状态',customFlag:true,});
                        } else {
                            this.$message.success({message:'已重新同步，请查看同步状态',customFlag:true,});
                        }
                        // 同步成功后实时更新同步信息
                        const acct = await this.getAcctInfoById(this.clientDetails.id);
                        if (Boolean(acct)) {
                            this.$set(this.clientDetails, 'synExternalSysFlag', acct.synExternalSysFlag);
                            this.$set(this.clientDetails, 'lastUpdated', acct.lastUpdated);
                            this.$set(this.clientDetails, 'synExternalSysInfo', acct.synExternalSysInfo);
                        }
                    } else {
                        this.$showError({message:'重新同步失败',customFlag:true});
                    }
                } catch(e) {
                    this.$utils.hideLoading();
                    this.$showError({message:'重新同步出错',customFlag:true});
                }
            },
            /**
             * 查询客户信息
             * <AUTHOR>
             * @date 6/10/21
             * @param id 客户id
             */
            async getAcctInfoById(id) {
                try {
                    const data = await this.$http.post('action/link/accnt/queryById', {
                        id: id,
                        // 是否需要外部系统失败原因字段
                        synExternalSysInfo: 'needSynContent'
                    });
                    return data.success ? data.result : null;
                } catch (e) {
                    return null;
                }
            },
            /**
             * 根据key值获取参数配置
             * <AUTHOR>
             * @date 8/11/21
             * @param key 参数配置健值
             */
            async queryCfgProperty(key) {
                const data = await this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                    filtersRaw: [
                        {id: 'key', property: 'key', value: key}
                    ]
                });
                if (data.success && data.rows && data.rows.length) {
                    return data.rows[0].value;
                } else {
                    return 'noMatch';
                }
            }
        }
    }
</script>

<style lang="scss">
    .dealer-basic-info {
        .change-fielld::after {
            content: '(变更)';
            color: red;
        }
        .card-content {
            background: #ffffff;
            border-radius: 16px;
            margin-bottom: 24px;
            margin-left: auto;
            margin-right: auto;
            width: 702px;
            padding-top: 8px;
            padding-bottom: 40px;
            .card-title {
                @include flex-start-center;
                @include space-between;
                border-bottom: 2px solid #F2F2F2;
                font-size: 28px;
                padding: 17px 24px;
                .sync-info-title {
                    color: #262626;
                }
            }
            .card-rows {
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                letter-spacing: 0;
                line-height: 28px;
                padding: 32px 24px 0 24px;
                .card-rows-content {
                    @include flex();
                    @include space-between();
                    .label {
                        color: #8C8C8C;
                    }
                    .value {
                        color: #262626;
                    }
                    .label-address  {
                        color: #8C8C8C;
                        line-height: 40px;
                    }
                    .value-address {
                        text-align: right;
                        width: 80%;
                        line-height: 40px;
                        color: #262626;
                        word-break: break-all;
                    }
                    .red{
                        color: red;
                    }
                }
            }
            .line {
                margin-top: -8px;
            }
        }
    }
</style>
