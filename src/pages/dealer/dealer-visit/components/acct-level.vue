<!--
客户等级
<AUTHOR>
@date 2024/5/18
-->
<template>
    <view class="dealer-customer-acct-level">
        <view class="title">客户等级</view>
        <!-- 可选品项 -->
        <lnk-taps v-model="tapActive"
                  v-if="tapOption.length"
                  padding
                  :taps="tapOption"
                  @switchTab="onTap"></lnk-taps>
        <!-- 客户等级卡片 -->
        <view class="level-content" v-if="acctLevelShow && tapOption.length">
            <!-- 头部时间 -->
            <view class="content-header">
                <view>活动时间：{{ acctLevelInfo.startDate | date('YYYY-MM-DD')}} - {{ acctLevelInfo.endDate | date('YYYY-MM-DD')}}</view>
            </view>
            <!-- 开瓶配置 -->
            <view class="content-body">
                <scroll-view class="scroll-wrap"
                             :scroll-x="true"
                             :enable-flex="true"
                             :scroll-with-animation="true"
                             :scroll-into-view="'process-item' + (acctLevelInfo.listMap.length - 1)">
                    <view class="process-wrap">
                        <view v-for="(item, index) in acctLevelInfo.listMap"
                              :key="index"
                              class="process-item"
                              :id="'process-item' + index">
                            <view class="line"></view>
                            <view class="dot" v-if="item.itemOrder === 0"></view>
                            <block v-else>
                                <view class="info-num">{{ item.cusScanQty }}瓶</view>
                                <view class="circle"
                                      :class="{'circle-active': item.rebateStatus !== 'NotAchieved'}">
                                    {{ item.itemLevel }}
                                </view>
                                <view class="info-status" v-if="item.rebateStatus"
                                      v-color="status[item.rebateStatus] ? status[item.rebateStatus].color : '#bdbdbd'">
                                    {{ status[item.rebateStatus] ? status[item.rebateStatus].value : '' }}
                                </view>
                            </block>
                        </view>
                    </view>
                </scroll-view>
            </view>
            <!-- 底部卡片 -->
            <view class="content-footer">
                <view class="footer-item">
                    <view class="item-label">达成等级</view>
                    <view class="item-content">{{ acctLevelInfo.achieveAcctLevel || '-' }}</view>
                </view>
                <view class="footer-item">
                    <view class="item-label">当前开瓶</view>
                    <view class="item-content">
                        <text>{{ acctLevelInfo.currentOpenScanNum || 0 }}</text>
                        <text class="item-unit">瓶</text>
                    </view>
                </view>
                <view class="footer-item">
                    <view class="item-label">已领取奖励</view>
                    <view class="item-content">
                        <text>
                            {{ acctLevelInfo.receiveReward && acctLevelInfo.receiveReward.value ? acctLevelInfo.receiveReward.value : '-'}}
                        </text>
                        <text class="item-unit">
                            {{ acctLevelInfo.receiveReward && acctLevelInfo.receiveReward.name ? acctLevelInfo.receiveReward.name : ''}}
                        </text>
                    </view>
                </view>
            </view>
        </view>
        <!-- 暂无数据 -->
        <view class="no-data" v-else>当前客户暂无开瓶配置，暂无数据</view>
    </view>
</template>

<script>
    import LnkTaps from '../../../core/lnk-taps/lnk-taps.vue';

    export default {
        name: 'dealer-customer-acct-level',
        props: {
            acctData: {
                type: Object,
                default: () => ({})
            },
        },
        components: {LnkTaps},
        data() {
            return {
                // 可选品项
                tapOption: [],
                // 当前选中品项
                tapActive: {},
                // 是否展示卡片
                acctLevelShow: false,
                // 客户等级卡片数据
                acctLevelInfo: {},
                // 状态
                status: {
                    WaitReceive: {
                        value: '可领取',
                        color: '#273D83'
                    },
                    Receive: {
                        value: '已领取',
                        color: '#666666'
                    },
                    Achieved: {
                        value: '已激活',
                        color: '#273D83'
                    },
                    NotAchieved: {
                        value: '未激活',
                        color: '#BDBDBD'
                    }
                },
            }
        },
        directives: {
            color: {
                inserted(el, binding) {
                    el.style.color = binding.value
                }
            }
        },
        async created() {
            await this.queryPort();
        },
        methods: {
            /**
             * 获取客户等级
             * <AUTHOR>
             * @date   2024/5/22 16:00
             */
            async queryAcctLevel() {
                try {
                    const {success, result} = await this.$http.post('/exportterminal/link/openRuleScanLevel/getCurrentLevelInfo', {
                        acctId: this.acctData.id,
                        conditionId: this.tapActive.id,
                        programId: this.tapActive.programId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            console.log(response);
                        }
                    });
                    if (success) {
                        if (result && result[this.tapActive.val]) {
                            this.acctLevelShow = true;
                            this.acctLevelInfo = result[this.tapActive.val];
                        } else {
                            this.acctLevelShow = false;
                        }
                    }
                } catch (e) {
                    console.log('获取客户等级失败' + e);
                }
            },
            /**
             * 获取品项及会员id
             * <AUTHOR>
             * @date   2024/5/22 15:05
             */
            async queryPort() {
                try {
                    const {success, rows} = await this.$http.post('action/link/terminalTag/queryHyItemPage', {
                        accntId: this.acctData.id,
                        companyId: this.acctData.companyId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            console.log(response);
                            this.$utils.hideLoading();
                        }
                    });
                    if (success) {
                        for (const item of rows) {
                            let name = await this.$lov.getNameByTypeAndVal('PROD_ITEMS', item.productItem);
                            this.$set(item, 'name', name);
                            this.$set(item, 'val', item.productItem);
                            this.$set(item, 'seq', item.id);
                        }
                        this.tapOption = rows;
                        this.tapActive = rows[0];
                        if (rows.length) {
                            await this.queryAcctLevel();
                        }
                    }
                } catch (e) {
                    console.log('获取品项错误：' + JSON.stringify(e.result));
                }
            },
            /**
             * 切换品项
             * <AUTHOR>
             * @date   2024/5/22 16:51
             */
            onTap() {
                this.queryAcctLevel();
            }
        }
    }
</script>

<style lang="scss">
    .dealer-customer-acct-level {
        margin: 0 14px 20px 14px;

        .lnk-tabs-container {
            margin-bottom: 24px;

            .lnk-tabs {
                position: static;
                border: none;
                z-index: 1 !important;

                .active {
                    color: #6051F6;
                    font-weight: bold;
                }

                .line {
                    background-color: #6051F6 !important;
                }
            }
        }

        .level-content {
            border-radius: 24px;
            overflow: hidden;

            .content-header {
                color: #273D83;
                font-size: 24px;
                padding: 14px 28px;
                background-image: linear-gradient(90deg, #C6D5FF 0%, #E4EFFF 100%);
            }

            .content-body {
                background: #F0F4FF;

                .scroll-wrap {
                    display: flex;
                    flex-direction: row;
                    height: 220px;
                    width: 100%;

                    .process-wrap {
                        display: flex;
                        padding-top: 32px;

                        .process-item {
                            position: relative;
                            width: 132px;
                            height: 100%;
                            display: flex;
                            flex-direction: column;
                            align-items: center;

                            &:first-child {
                                margin-left: -10px;
                            }

                            .line {
                                height: 2px;
                                background-color: #273D83;
                                position: absolute;
                                left: 88px;
                                top: 72px;
                                width: 70%;
                            }

                            .info-num {
                                font-size: 28px;
                                color: #666666;
                                line-height: 32px;
                            }

                            .dot {
                                position: absolute;
                                left: 88px;
                                top: 64px;
                                width: 16px;
                                height: 16px;
                                border-radius: 50%;
                                background-image: radial-gradient(circle at 50% 50%, #6051F6 0%, #6051F6 57%);
                                box-shadow: inset 0px -2px 6px 0px #92AEFD;
                            }

                            .circle {
                                margin: 16px 0;
                                width: 48px;
                                height: 48px;
                                font-size: 24px;
                                border-radius: 50%;
                                line-height: 48px;
                                text-align: center;
                                color: #a0a0a0;
                                background-image: radial-gradient(circle at 42% 35%, #f5f5f5 0%, #d3d3d3 57%);
                                box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.08);
                            }

                            .circle-active {
                                color: #fff;
                                background-image: radial-gradient(circle at 42% 35%, #92AEFD 0%, #6051F6 57%);
                                box-shadow: inset 0 -2px 6px 0 #92AEFD;
                            }

                            .info-status {
                                font-size: 24px;
                                color: #BDBDBD;
                                line-height: 36px;
                            }
                        }
                    }
                }

            }

            .content-footer {
                display: flex;
                justify-content: space-evenly;
                padding: 16px 0;
                background-image: linear-gradient(180deg, #E4EFFF 0%, #F0F4FF 100%);

                .footer-item {
                    display: flex;
                    flex-direction: column;
                    align-items: center;


                    .item-label {
                        font-size: 24px;
                        margin-bottom: 6px;
                    }

                    .item-content {
                        font-weight: bold;
                        font-size: 36px;

                        .item-unit {
                            font-size: 24px;
                        }
                    }
                }
            }
        }

        .no-data {
            margin-top: 20px;
            border-radius: 12px;
            background: #F8F8F8;
            padding: 36px 0;
            text-align: center;
            color: #666;
            font-size: 24px;
        }
    }
</style>
