<template>
    <link-page class="dealer-active-cost">
        <link-auto-list :option="activeCostList" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="active-cost-rows">
                    <view slot="note">
                        <view class="item-top">
                            <view class="item-left">{{data.startTime | date('YYYY-MM-DD')}}</view>
                            <view class="item-tag">
                                <view class="tag-content">{{data.status | lov('MC_STATUS')}}</view>
                            </view>
                        </view>
                        <view class="item-title">{{data.activityName}}</view>
                        <view class="item-middle">
                            <view class="middle-left">执行人 <text>{{data.executor}}</text></view>
                            <view class="middle-right">金额
                                <text class="amount" v-if="!(data.aproStatus === 'Feedback' || data.aproStatus === 'Refeedback' || data.aproStatus === 'RefeedWithdraw')&&(data.status === 'New' || data.status === 'Published' || data.status === 'Processing')">{{((data.cashApplyAmount+0) + (data.prodApplyAmount+0)) | cny}}</text>
                                <text class="amount" v-if="(data.aproStatus === 'Feedback' || data.aproStatus === 'Refeedback' || data.aproStatus === 'RefeedWithdraw')&&!(data.status === 'New' || data.status === 'Published' || data.status === 'Processing')">{{((data.cashFillAmount+0)+(data.prodFillAmount+0)) | cny}}</text>
                                <text class="amount" v-if="data.status === 'ActualAmount' || data.status === 'MaterialSup' || data.status === 'Closed' || data.status === 'Inactive'">{{((data.cashRealAmount+0)+(data.prodRealAmount+0)) | cny}}</text>
                            </view>
                        </view>
                        <view class="address">
                            <view class="iconfont icon-location"></view>
                            <view class="address-detail">{{data.province}}{{data.city}}{{data.district}}{{data.address}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-active-cost",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            // 活动费用
            const activeCostList = new this.AutoList(this, {
                module: 'action/link/marketAct',
                itemPath: '',
                loadOnStart: true,
                param: () => {
                    const param = {
                        oauth: 'ALL',
                        sort: 'created',
                        order: 'desc',
                        filtersRaw: [{id: 'beneficiaryId', property: 'beneficiaryId', value: `[${this.accntIdArr.toString()}]`, operator: 'in'}]
                    };
                    if (this.isDealer) {
                        // 经销商人员：1. 创建人=经销商人员；2. 创建人≠经销商人员，但根据执行案ID，关联获取执行案【费用垫付对象】，当【费用垫付对象】=经销商人员所属分销商/经销商；
                        param.dealerEmp = 'Y';
                    }
                    return param;
                },
                sortOptions: null,
                filterBar: {},
            });
            return {
                activeCostList
            }
        },
        props: {
            isDealer: {
                type: Boolean,
                default: false
            },
            accntIdArr: {
                type: Array,
                default: []
            }
        },
    }
</script>

<style lang="scss">
    /*deep*/.link-sticky-top:before {
                box-shadow: none!important;
    }
    /*deep*/.link-auto-list-top-bar {
                border-bottom: none;
            }
    .dealer-active-cost {
        margin-top: 24px;
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        .active-cost-rows {
            background: #FFFFFF;
            border-radius: 32px;
            padding: 40px 24px;
            width: 702px;
            margin: auto auto 24px auto;
            .item-top {
                @include flex-start-center;
                @include space-between;
                .item-left {
                    font-size: 28px;
                    color: #8C8C8C;
                }
                .item-tag {
                    height: 36px;
                    line-height: 36px;
                    background: #2F69F8;
                    box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                    transform: skewX(-30deg);
                    border-radius: 8px;
                    color: #ffffff;
                    text-align: center;
                    padding-left: 27px;
                    padding-right: 27px;
                    font-size: 20px;
                    margin-right: 8px;
                    .tag-content {
                        transform: skewX(30deg);
                    }
                }
            }
            .item-title {
                font-size: 32px;
                color: #262626;
                padding-top: 24px;
            }
            .item-middle {
                @include flex-start-center;
                @include space-between;
                padding-top: 26px;
                font-size: 28px;
                color: #8C8C8C;
                text {
                    color: #262626;
                }
                .amount {
                    color: #FF5A5A;
                }
            }
            .address {
                @include flex-start-center;
                padding-top: 24px;
                .icon-location{
                    color: #8C8C8C;
                    font-size: 32px;
                }
                .address-detail {
                    padding-left: 19px;
                    font-size: 28px;
                    color: #262626;
                }
            }

        }
    }
</style>
