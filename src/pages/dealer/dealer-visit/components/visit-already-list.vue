<template>
    <view class="dealer-visit-already-list">
        <view class="blank"></view>
        <link-auto-list :option="autoList" hideCreateButton :searchInputBinding="{props:{placeholder:'经销商名称/创建人'}}">
            <view slot="searchRight" class="search-container">
                <view class="only-today" @tap="viewTodayData" :style="style">仅看今日</view>
                <link-filter v-model="filterOption"/>
            </view>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="media-list-rows" :style="{'margin-top': index === 0 ? '75px':''}"  @tap="goToItem(data)">
                    <view slot="note" class="visit-list">
                        <view class="visit-content">
                            <view class="visit-top">
                                <view class="store-time">
                                    <view class="out-circle">
                                        <view class="dot-circle"></view>
                                    </view>
                                    <view class="visit-time">
                                        <view class="date">{{data.visitTime | date('YYYY-MM-DD') || data.created | date('YYYY-MM-DD')}}</view>
                                        <view class="week" v-if="data.visitTime">{{data.visitTime | week}}</view>
                                        <view class="week" v-else>{{data.created | week}}</view>
                                        <view class="week">{{data.createdName}}</view>
                                    </view>
                                </view>
                                <view class="item-tag" v-if="data.visitApplicationStatus" :style="data.colorBg">
                                    <view class="tag-content">{{data.visitApplicationStatus | lov('VISIT_STATUS')}}</view>
                                </view>
                            </view>
                            <view class="visit-start-time">
                                <view class="visit-duration">
                                    <view class="time" v-if="data.visitTime">{{data.visitTime | date('HH:mm')}}</view>
                                    <view class="time" v-else>{{data.created | date('HH:mm')}}</view>
                                    <view class="interim-left">···</view>
                                    <view class="duration">{{data.visitDuration}}</view>
                                    <view class="interim-right">···</view>
                                    <view class="time">{{data.visitEndTime | date('HH:mm')}}</view>
                                </view>
                                <view class="location" v-if="data.specialDescription !== undefined">
                                    <text class="iconfont icon-aim"></text> {{data.specialDescription}}
                                </view>
                            </view>
                            <view  class="visit-start-time">
                                <view class="visit-title">{{data.accntName}}</view>
                                <view class="location" v-if="data.checkOutSpecialDescription!== undefined">
                                    <text class="iconfont icon-aim"></text> {{data.checkOutSpecialDescription}}
                                </view>
                            </view>
                            <view class="visit-address">{{data.orieLocation}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    import {getFiltersRaw} from "link-taro-component";

    export default {
        name: "dealer-visit-already-list",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            const autoList = new this.AutoList(this, {
                module: 'action/link/accntVisit',
                param: () => {
                    const filtersRaw = [
                        {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: '[visited,closed]', operator: 'in'},
                        {id: 'visitType', property: 'visitType', value: 'dailySalesCall', operator: '='}
                    ];
                    getFiltersRaw(this.filterOption).forEach(item => {
                        item.value && filtersRaw.push(item);
                    });
                    // 特定公司的经销商人员只能看到自己职位创建的数据
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        oauth: this.oauth,
                        sort: 'created',
                        order: 'desc',
                        created: this.todayFlag ? this.$date.format(new Date(Date.now()), 'YYYY-MM-DD') : null,
                        filtersRaw
                    }
                },
                queryFields: 'visitTime,created,orieLocation,createdName,visitApplicationStatus,visitDuration,visitEndTime,specialDescription,accntName,accntProvince,accntCity,accntDistrict,accntAddr,checkOutSpecialDescription',
                sortOptions: null,
                searchFields: ['accntName', 'createdName'],
                hooks: {
                    afterLoad(data) {
                        data.rows.forEach(async item => {
                            if (this.$utils.isEmpty(item.visitDuration)){
                                let visitStartTime = this.$utils.isEmpty(item.visitTime) ? item.created : item.visitTime;
                                let duration = await this.$utils.getYMDHMS(Date.parse(new Date(visitStartTime)), Date.parse(new Date(item.visitEndTime)));
                                this.$set(item, 'visitDuration', duration);
                            }
                            switch (item.visitApplicationStatus) {
                                case 'visited':
                                    this.$set(item, 'colorBg', 'background: #2F69F8; box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);');
                                    break
                                case 'closed':
                                    this.$set(item, 'colorBg', 'background: #A1A7AF;box-shadow: 0 3px 4px 0 rgba(191,191,191,0.50);');
                                    break
                            }
                        })
                    }
                }
            });
            return {
                todayFlag: true,
                todayDate: null,
                style: 'background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;',
                autoList,
                filterOption: [
                    {label: '拜访开始时间', field: 'visitTime', type: 'date'},
                    {label: '拜访结束时间', field: 'visitEndTime', type: 'date'}
                ],
                copyISOptions: ''
            }
        },
        props: {
            isDealer: {
                type: Boolean,
                value: false
            },
            oauth: {
                type: String
            }
        },
        computed: {},
        watch: {
            filterOption (val) {
                if (val) {
                    this.filterList(getFiltersRaw(val))
                }
            }
        },
        created () {
            this.copyISOptions = this.$utils.deepcopy(this.autoList.option.param.filtersRaw)
        },
        methods: {
            async goToItem(data) {
                try {
                    const resp = await this.$http.post('action/link/accntVisit/queryById', {id: data.id});
                    if (!resp.success) {
                        this.$showError('查询拜访详情失败！' + resp.result);
                        return;
                    }
                    data = resp.result;
                } catch (e) {
                    this.$showError('查询拜访详情出错！');
                }
                this.$nav.push('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                    data: data,
                    source:'onlyView'
                })
            },
            /**
             * 筛选函数
             * <AUTHOR>
             * @date 2020-11-02
             * @param param
             */
            async filterList(param) {
                const that = this;
                await that.autoList.methods.reload();
            },
            /**
              * 只看今日
              * <AUTHOR>
              * @date 2020-09-08
            */
            viewTodayData () {
                this.todayFlag = !this.todayFlag;
                this.todayFlag ? this.style = 'background: #EDF3FF;color: #2F69F8;border: 1px solid #EDF3FF;' : this.style = 'border: 1px solid #E0E4EC;color: #8C8C8C;';
                this.todayFlag ? this.autoList.option.param.created = this.$date.format(new Date(Date.now()), 'YYYY-MM-DD') : this.autoList.option.param.created = null;
                this.autoList.methods.reload();
            }
        }
    }
</script>

<style lang="scss">
    .dealer-visit-already-list {
        /*deep*/.link-item {
                     padding: 0;
                }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-dropdown-content {
                    padding: 24px;
                }
        /*deep*/.link-search-input {
                    position: fixed;
                    margin-top: -2px;
                    width: 100%;
                    border-top: 2px solid #f2f2f2;
                }
        /*deep*/.link-auto-list-no-data {
                    margin-top: 160px!important;
                }
        /*deep*/.link-auto-list-loading-more {
                    padding-top: 200px;
                }
        .blank {
            width: 100%;
            height: 97px;
            background: #F2F2F2;
        }
        .search-container {
            @include flex-start-center;
            padding-left:24px;
            color: #8C8C8C;
            font-size: 28px;
            text-align: center;
            .only-today {
                border-radius: 26px;
                width: 144px;
                height: 52px;
                line-height: 52px;
                font-size: 28px;
                text-align: center;
                margin-right: 24px;
            }
        }
        .media-list-rows {
            background: #FFFFFF;
            border-radius: 32px;
            width: 702px;
            margin: 24px auto auto auto;
            .visit-list {
                padding: 24px 16px 24px 24px;
                .visit-content {
                    .visit-top {
                        @include flex-start-center;
                        @include space-between;
                        .store-time {
                            @include flex-start-center;
                            .out-circle {
                                width: 32px;
                                height: 32px;
                                border-radius: 50%;
                                background: #EFF3FF;
                                .dot-circle {
                                    width: 12px;
                                    height: 12px;
                                    margin: 10px auto auto auto;
                                    background: #2F69F8;
                                    border-radius: 50%;
                                }
                            }
                            .visit-time {
                                padding-left: 16px;
                                @include flex-start-center();
                                .date {
                                    font-family: PingFangSC-Semibold,serif;
                                    font-size: 32px;
                                    color: #262626;
                                }
                                .week{
                                    padding-left: 16px;
                                    font-size: 28px;
                                    color: #8C8C8C;
                                }
                            }
                        }
                        .item-tag {
                            height: 36px;
                            line-height: 36px;
                            text-align: center;
                            color: #ffffff;
                            background: #2F69F8;
                            box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                            border-radius: 8px;
                            padding-left: 24px;
                            padding-right: 24px;
                            font-size: 20px;
                            margin-right: 8px;
                            transform: skewX(-30deg);
                            .tag-content {
                                transform: skewX(30deg);
                            }
                        }
                    }
                    .visit-start-time {
                        padding-top: 24px;
                        @include flex-start-center;
                        @include space-between;
                        .visit-duration {
                            @include flex-start-center;
                            .time {
                                font-size: 28px;
                                color: #8C8C8C;
                                font-weight: bold;
                            }
                            .interim-left {
                                padding-left: 16px;
                                padding-right: 16px;
                                font-size: 32px;
                                font-weight: bold;
                                background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
                                -webkit-background-clip: text;
                                color:transparent;
                            }
                            .duration {
                                background: #959FB9;
                                border-radius: 18px;
                                /*width: 110px;*/
                                padding-right: 12px;
                                padding-left: 12px;
                                height: 36px;
                                line-height: 36px;
                                color: #ffffff;
                                font-size: 20px;
                                text-align: center;
                            }
                            .interim-right {
                                padding-left: 16px;
                                padding-right: 16px;
                                font-size: 32px;
                                font-weight: bold;
                                background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
                                -webkit-background-clip: text;
                                color:transparent;
                            }
                        }
                        .location {
                            color: #FF5A5A;
                            font-size: 24px;
                            .icon-aim {
                                font-size: 28px;
                            }
                        }
                    }
                    .visit-title {
                        font-family: PingFangSC-Semibold,serif;
                        font-size: 32px;
                        color: #262626;
                    }
                    .visit-address {
                        padding-top: 24px;
                        font-size: 24px;
                        color: #262626;
                    }
                }
            }
        }
    }
</style>
