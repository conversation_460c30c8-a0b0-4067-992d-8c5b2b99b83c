<!--
终端-收货地址
<AUTHOR>
@date 2023/10/09
-->
<template>
    <link-page class="dealer-address-list">
        <view @tap="addAddress('', 'NEW')" class="addAddr" v-show="!isShowAdd && !disabledNewEdit && clientDetails.editApprovalStatus !== 'Submitted'">添加</view>
        <view class="addAddr" @tap="goSellProduct" v-if="isShowAdd && !disabledNewEdit && clientDetails.editApprovalStatus !== 'Submitted'">编辑</view>
        <link-auto-list :option="addressOption" hideCreateButton>
            <template  slot-scope="{data,index}">
                <view class="address-container">
                    <view class="address-content">
                        <view class="address-head">
                            <view class="consignee-mobilePhone">{{data.consignee}} {{data.mobilePhone}}</view>
                            <view class="status-view" :style="{background: matchTypeColor[data.isEffective]}">
                                <view class="status" v-if="data.isEffective === 'Y'">有效</view>
                                <view class="status" v-else>无效</view>
                            </view>
                        </view>   
                        <view class="full-address">{{data.province}}{{data.city}}{{data.district}}{{data.townName}}{{data.addr}}</view>
                    </view>
                    <view class="iconfont icon-edit" v-show="!isShowAdd && !disabledNewEdit && clientDetails.editApprovalStatus !== 'Submitted'" @tap="addAddress(data, 'UPDATE')"></view>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-address-list",
        data () {
            const addressOption = new this.AutoList(this, {
                module: 'action/link/acctaddress',
                url: {
                    queryByExamplePage: 'action/link/acctaddress/queryAddressByAccountIdPage'
                },
                createPath: '/pages/terminal2/new-construction/new-construction-page',
                param: {
                    filtersRaw: [
                        {id: 'addrType', property: 'addrType', value: '[ShipAddr,DeliveryAddr]', operator: 'in'},
                        // {id: 'isEffective', property: 'isEffective', value: 'Y', operator: '='},
                    ],
                    // oauth: 'MY_POSTN',
                    acctId: this.acctId,
                    sort: 'created',
                    order: 'desc'
                },
                sortOptions: null,
                searchFields: null,
                hooks: {
                    beforeLoad(option) {
                        delete option.param.order;
                        delete option.param.sort;
                    },
                    async afterLoad (data) {
                        if (this.source === 'approval') {
                            const param = {
                                filtersRaw: [
                                    {id:"accntId",property:"accntId",value: this.acctId},
                                    {id:"changeObjType",property:"changeObjType",value:"AcctAddress"},
                                    {id:"flowFlag",property:"flowFlag",value:"Y",operator:"="}
                                ],
                                oauth: 'ALL'
                            }
                            const {rows} = await this.$http.post('action/link/accntHistory/queryByExamplePage', param);
                            if (rows.length) {
                                rows.forEach(item => {
                                    if (item.oldAccntJson) {
                                        data.rows.forEach(val => {
                                            if (JSON.parse(item.newAccntJson).id === val.id) {
                                                Object.assign(val, JSON.parse(item.newAccntJson));
                                            }
                                        })
                                    } else {
                                        data.rows.unshift(JSON.parse(item.newAccntJson));
                                    }
                                })
                            }
                        }
                        data.rows.forEach(item => {
                            this.$set(item, 'selectFlag', false);
                        });
                    }
                }
            });
            return {
                addressOption,
                shadow: true,
                matchTypeColor: { // 有效性状态显示颜色
                    Y: '#2F68F7', // 有效-蓝色
                    N: '#AAAAAA', // 无效-红色
                },
            }
        },
        props:{
            basicOption: {
                type: Object
            },
            // 终端id
            acctId:{
                type:String,
            },
            disabledNewEdit:{
                type: Boolean,
                default: false
            },
            // 终端状态
            clientDetails:{
                type:Object,
                default(){
                    return {}
                }
            },
            editToApprovedFlag: {
                type: Boolean,
                default: false
            },
            editFlag: {
                type: String,
                default: ''
            },
            // 是否从审批页面进入
            source: {
                type: String,
                default: ''
            }
        },
        computed:{
            isShowAdd() {
                return this.source === 'terminelTab'
            }
        },
        methods: {
            /**
              * 增加地址/编辑地址
              * <AUTHOR>
              * @date 2023-10-09
              * @param data 编辑时地址对象
              * @param flag 新建or编辑
            */
            addAddress (data, flag) {
                this.$nav.push('/pages/terminal/order/address-page', {
                    source: this.editFlag ? 'editTerminal' : 'terminalDetail',
                    addressData: data,
                    isEditFlag: flag,
                    acctId: this.acctId,
                    basicOption: this.basicOption
                })
            },
            goSellProduct () {
                if(this.clientDetails.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                this.$emit("checkEdit",(success) => {
                    if (success) {
                        const url = this.editToApprovedFlag
                            ? '/pages/terminal2/edit-construction/edit-approve-page'
                            : '/pages/terminal2/new-construction/new-construction-page'
                        this.$nav.push(url, {
                            editFlag: 'edit',
                            data: this.clientDetails,
                            accntId: this.clientDetails.id,
                            title: '收货地址',
                            editModule: 'addressList',
                        })
                    }
                });
            }
        }
    }
</script>

<style lang="scss">
    .dealer-address-list {
        .addAddr{
            float: right;
            padding: 28px;
            font-size: 28px;
            color:#2F69F8
        }
        .address-container {
            clear: both;
            margin-top: 20px;
            @include flex-start-center;
            background: #ffffff;
            border-bottom: 1px solid  #EEF3F5;
            border-radius: 20px;
            .address-content {
                margin-left: 50px;
                width: 80%;
                padding-top: 40px;

                .address-head { 
                    display: flex;
                    align-items: flex-start;
                    justify-content: space-between;

                    .consignee-mobilePhone {
                        font-size: 32px;
                        padding-bottom: 12px;
                        color: #41484D;
                    }

                    .status-view {
                        border-radius: 4px;
                        transform: skewX(-20deg);
                        margin-right: -10px;

                        .status {
                            font-size: 22px;
                            color: #FFFFFF;
                            letter-spacing: 2px;
                            text-align: center;
                            padding: 4px 40px;
                            transform: skewX(20deg);
                            max-height: 38px;
                        }
                    }
                }

                .full-address {
                    font-size: 30px;
                    color: #6B7378;
                    padding-bottom: 40px;
                }
            }
            .icon-edit {
                width: 10%;
                font-size: 40px;
                color: #9CA5A8;
                padding-bottom: 20px;
                padding-top: 20px;
            }
        }
        .bottom-sticky {
            .icon-plus {
                font-size: 34px;
                padding-right: 24px;
            }
            /*deep*/.link-button {
                width: 94%;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
                font-size: 30px;
            }
        }
    }
</style>
