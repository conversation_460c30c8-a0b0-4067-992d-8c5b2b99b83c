<template>
  <view class="dealer-visit-records">
    <view class="visit-head" v-if="positionChuanDong">
      <text class="basic num">总次数: {{terminalTag.visitCounts}}</text>
      <text class="basic time">总时长（分）: {{terminalTag.visitTotalTime}}</text>
    </view>
    <link-auto-list :option="visitRecordsOption" hideCreateButton>
      <template slot-scope="{data,index}">
        <item :arrow="false" :data="data" :key="index" class="visit-records-item" @tap="data.visitApplicationStatus==='visiting'?goToItem(data):goToItemView(data)">
          <view slot="note">
            <view class="visit-content">
              <view class="visit-top">
                <view class="store-time">
                  <view class="out-circle">
                    <view class="dot-circle"></view>
                  </view>
                  <view class="visit-time">
                    <view class="date">{{data.visitTime | date('YYYY-MM-DD')}}</view>
                    <view class="week">{{data.visitTime | week}}</view>
                    <view class="week">{{data.createdName}}</view>
                  </view>
                </view>
                <view class="item-tag" v-if="data.visitApplicationStatus">
                  <view class="tag-content">{{data.visitApplicationStatus | lov('VISIT_STATUS')}}</view>
                </view>
              </view>
              <view class="visit-start-time" :style="{'padding-bottom': $utils.isEmpty(data.comments) ? '0px' : '12px'}">
                <view class="visit-duration">
                  <view class="time">{{data.visitTime | date('HH:mm')}}</view>
                  <view class="interim-left">···</view>
                  <view class="duration" v-if="data.visitDuration">{{data.visitDuration}}</view>
                  <view class="interim-right" v-if="data.visitEndTime">···</view>
                  <view class="time" v-if="data.visitEndTime">{{data.visitEndTime | date('HH:mm')}}</view>
                  <view v-else class="end-desc">未结束</view>
                </view>
                <view class="location" >
                    <view v-if="data.specialDescription !== undefined"> <text class="iconfont icon-aim"></text>{{data.specialDescription}}</view>
                    <view v-if="data.checkOutSpecialDescription !== undefined"> <text class="iconfont icon-aim"></text>{{data.checkOutSpecialDescription}}</view>
                </view>
              </view>
              <view class="comments" v-if="data.comments"><text class="iconfont icon-beizhu"></text>{{data.comments}}</view>
            </view>
          </view>
        </item>
      </template>
    </link-auto-list>
  </view>
</template>

<script>
export default {
    name: "dealer-visit-recrods",
    props: {
        isDealer: {
            type: Boolean,
            default: false
        },
      queryId: {
        type: String,
        default: '',
        required: true
      },
      terminalTag: {
        type: Object,
        default: () => ({})
      },
      positionChuanDong: {
        type: Boolean,
        default: false
      }
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        const visitRecordsOption = new this.AutoList(this, {
            module: 'action/link/visit',
            param: () => {
                const filtersRaw = [];
                // 特定公司的经销商人员只能看到自己职位创建的数据
                if (this.isDealer) {
                    filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                }
                return {
                    page: 1,
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw,
                    rows: 5,
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    accntId: this.queryId,
                }
            },
            sortOptions: null,
            filterBar: {}
        });
        return {
            visitRecordsOption,
            coordinate: null,
            addressData: null,
        }
    },
    methods:{
        goto(data){
            if(data.visitApplicationStatus==='visiting'){
                this.goToItem(data)
            }else {
             this. goToItemView(data)
            }
        },
        /**
         * 查看详情
         * <AUTHOR>
         * @date 2020-08-31
         * @param data 详情信息
         */
        async goToItemView(data) {
            console.log('tesghu')
            try {
                const resp = await this.$http.post('action/link/visit/queryById', {id: data.id});
                if (!resp.success) {
                    this.$showError('查询拜访详情失败！' + resp.result);
                    return;
                }
                data = resp.result;
            } catch (e) {
                this.$showError('查询拜访详情出错！');
            }
            this.$nav.push('/pages/terminal/visit/visit-perform-page', {
                data: data,
                source:'onlyView'
            })
        },
        async goToItem(data) {
            if(data.acctStatus === 'N'){
                this.$showError('当前终端已被冻结，请生效后操作！');
                return
            }
            const accntId = data.accntId
            try {
                const resp = await this.$http.post('action/link/visit/queryById', {id: data.id});
                if (!resp.success) {
                    this.$showError('查询拜访详情失败！' + resp.result);
                    return;
                }
                data = resp.result;
            } catch (e) {
                this.$showError('查询拜访详情出错！');
            }
            if (this.$utils.isEmpty(this.coordinate)) {
                let userLocation = await this.$locations.openSetting();
                if (userLocation['scope.userLocation']) {
                    let coordinate = await this.$locations.getCurrentCoordinate();
                    this.$store.commit('coordinate/setCoordinate', coordinate);
                    await this.initData();
                }
            } else {
                if(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1612') {
                    const contactList = await this.getContactList(accntId) || []
                    const flag = contactList.some((item => {
                        return item.isEffective === 'Y'
                    }))
                    if(!flag) {
                        this.$dialog({
                            title: '提示',
                            content: '请完善联系人信息',
                            initial: true,
                            confirmText: '确认'
                        });
                        return
                    }
                }
                this.$nav.push('/pages/terminal/visit/visit-perform-page', {
                    data: data,
                    source:'viewDetail'
                })
            }
        },
        async initData() {
            this.coordinate = await this.$locations.getCurrentCoordinate();
            /*this.$aegis.report({
                msg: '拜访模块地址initData',
                ext1: JSON.stringify(this.coordinate),
                trace: 'log'
            });*/
            if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                let address =  await this.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '拜访记录');
                this.addressData = address['originalData'].result.addressComponent
            }
        },
        async getContactList(id) {
            if(!id) return []
            const {rows} = await this.$http.post('action/link/contacts/listByAcctId', {
                pageFlag: true,
                onlyCountFlag: false,
                filtersRaw: [],
                oauth: 'ALL',
                sort: 'id',
                order: 'desc',
                attr1: id,
            });
            return rows
        },
    },
    async created () {
        await this.initData();
    }
}
</script>

<style lang="scss">
  .dealer-visit-records {
    $font-size: 28px;
    $line-height: 28px;
    margin: 24px;
    /*deep*/.link-item-icon{
              width: 0 !important;
            }
    /*deep*/.link-sticky-top {
              box-shadow: none!important;
              z-index: 0!important;
            }
    .visit-head {
      padding: 0 20px 20px 20px;
      font-size: 32px;
      .basic {
        background-color: white;
        color: black;
        padding: 8px 36px 8px 16px;
        border-radius: 10px;
      }
      .num {
      }
      .time {
        margin-left: 20px;
      }
    }
    .visit-records-item {
      border-radius: 16px;
      background-color: white;
      margin-bottom: 24px;
      .first-line {
        width: 100%;
        display: flex;
        justify-content: space-between;
        flex-direction: row;
        margin-bottom: 32px;
        .date {
          font-size: 32px;
          color: #262626;
          line-height: 32px;
          display: flex;
          align-items: center;
          font-weight: 500;
          .out-circle {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: #EFF3FF;
            margin-right: 10px;
            .dot-circle {
              width: 12px;
              height: 12px;
              margin: 10px auto auto auto;
              background: #2F69F8;
              border-radius: 50%;
            }
          }
          .weekday {
            margin-left: 16px;
            font-size: $font-size;
            color: #8C8C8C;
            letter-spacing: 0;
            line-height: 32px;
            font-weight: 400;
          }
          .visit-name {
            margin-left: 10px;
            font-size: $font-size;
            color: #8C8C8C;
            letter-spacing: 0;
            text-align: right;
            line-height: $line-height;
            font-weight: 400;
          }
        }

        .visit-status {
          color: white;
          letter-spacing: 0;
          text-align: right;
          text-decoration: none;
          background: #2F69F8;
          box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
          height: 36px;
          transform: skew(-30deg, 0);
          display: flex;
          justify-content: center;
          align-items: center;
          border-radius: 6px;
          padding: 10px 16px;
          margin-right: 10px;
          .label {
            transform: skew(30deg, 0);
            font-size: 20px;
          }
        }
      }

      .second-line {
        margin-bottom: 32px;
        display: flex;
        justify-content: space-between;
        .time {
          display: flex;
          align-items: center;
          .time-item {
            font-size: $font-size;
            color: #8C8C8C;
            letter-spacing: 0;
            text-align: right;
            line-height: $line-height;
            font-weight: 500;
            margin-right: 14px;
          }
          .end-desc{
            font-size: 24px;
            color: #8C8C8C;
            letter-spacing: 0;
            line-height: 24px;
          }
          .interim-left {
            padding-left: 16px;
            padding-right: 16px;
            font-size: 32px;
            font-weight: bold;
            background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
            -webkit-background-clip: text;
            color:transparent;
          }
          .interim-right {
            padding-left: 16px;
            padding-right: 16px;
            font-size: 32px;
            font-weight: bold;
            background-image: linear-gradient(90deg, #BFBFBF 0%, rgba(191,191,191,0.20) 100%);
            -webkit-background-clip: text;
            color:transparent;
          }

          .time-period {
            background: #959FB9;
            border-radius: 18px;
            color: white;
            font-size: 20px;
            letter-spacing: 0;
            text-align: center;
            line-height: 20px;
            padding: 9px 18px;
            margin-right: 14px;
          }
        }

        .address {
          display: flex;
          align-items: center;
          font-size: 24px;
          color: #FF5A5A;
          letter-spacing: 0;
          .iconfont {
            font-size: 28px;
          }
        }
      }

      .visit-content {
        .visit-top {
          @include flex-start-center;
          @include space-between;
          .store-time {
            @include flex-start-center;
            .out-circle {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              background: #EFF3FF;
              .dot-circle {
                width: 12px;
                height: 12px;
                margin: 10px auto auto auto;
                background: #2F69F8;
                border-radius: 50%;
              }
            }
            .visit-time {
              padding-left: 16px;
              @include flex-start-center();
              .date {
                font-family: PingFangSC-Semibold,serif;
                font-size: 32px;
                color: #262626;
              }
              .week{
                padding-left: 16px;
                font-size: 28px;
                color: #8C8C8C;
              }
            }
          }
          .item-tag {
            height: 36px;
            line-height: 36px;
            text-align: center;
            color: #ffffff;
            background: #2F69F8;
            box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
            border-radius: 8px;
            padding-left: 24px;
            padding-right: 24px;
            font-size: 20px;
            margin-right: 8px;
            transform: skewX(-30deg);
            .tag-content {
              transform: skewX(30deg);
            }
          }
        }
        .visit-start-time {
          padding-top: 24px;
          @include flex-start-center;
          @include space-between;
          .visit-duration {
            @include flex-start-center;
            .time {
              font-size: 28px;
              color: #8C8C8C;
              font-weight: bold;
            }
            .interim-left {
              padding-left: 12px;
              padding-right: 12px;
              font-size: 32px;
              font-weight: bold;
              background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
              -webkit-background-clip: text;
              color:transparent;
            }
            .duration {
              background: #959FB9;
              border-radius: 18px;
              /*width: 110px;*/
              padding-right: 12px;
              padding-left: 12px;
              height: 36px;
              line-height: 36px;
              color: #ffffff;
              font-size: 20px;
              text-align: center;
            }
            .interim-right {
              padding-left: 12px;
              padding-right: 12px;
              font-size: 32px;
              font-weight: bold;
              background-image: linear-gradient(90deg, rgba(191,191,191,0.20) 0%, #BFBFBF 100%);
              -webkit-background-clip: text;
              color:transparent;
            }
          }
          .location {
            color: #FF5A5A;
            font-size: 24px;
            .icon-aim {
              font-size: 28px;
            }
          }
        }
        .comments {
          border-top: 2px dashed #DADEE9;
          font-size: $font-size;
          color: #262626;
          letter-spacing: 0;
          line-height: $line-height;
          padding: 32px 32px 0 0;
          .icon-beizhu {
            padding-right: 16px;
            color: #8C8C8C;
            font-size: 32px;
          }
        }
      }
    }
  }
</style>
