<!-- 按照时间维度查询组件 -->
<template>
  <view class="time-dimension">
    <view class="time-dimension__header">
      <view class="time-dimension__header--title">{{ title }}</view>
      <!-- 查询维度 日，周，月，年 -->
      <view class="time-dimension__header--dimension">
        <text
          class="time-dimension__header--text"
          :class="{'active': active === 'year'}"
          @tap="handlerClick('year')"
        >本年</text>
        <text
          class="time-dimension__header--text"
          :class="{'active': active === 'month'}"
          @tap="handlerClick('month')"
        >本月</text>
        <text
          class="time-dimension__header--text"
          :class="{'active': active === 'week'}"
          @tap="handlerClick('week')"
        >本周</text>
        <text
          class="time-dimension__header--text"
          :class="{'active': active === 'day'}"
          @tap="handlerClick('day')"
        >今日</text>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return {
      active: 'day'
    }
  },
  props: {
    title: {
      type: String,
      default: ''
    }
  },
  methods: {
    handlerClick(time) {
      this.active = time;
      this.$emit('change', time)
    }
  }
}
</script>

<style lang="scss">
.time-dimension {
  &__header {
    display: flex;
    justify-content: space-between;
    padding-right: 12px;
    align-items: center;
    &--title {
      font-family: PingFangSC-Medium;
      font-size: 32px;
      color: #333333;
      font-weight: 500;
    }
    &--dimension {
      background: #F2F2F2;
      border-radius: 18.5px;
      display: flex;
      align-items: center;
      .active {
        background-image: linear-gradient(90deg, #FE7759 0%, #F22232 100%);
        border-radius: 18.5px;
        font-family: PingFangSC-Regular;
        font-size: 24px;
        color: #FFFFFF;
        text-align: center;
        line-height: 20px;
        font-weight: 400;
      }
    }
    &--text {
      font-family: PingFangSC-Regular;
      font-size: 24px;
      color: #333333;
      text-align: center;
      line-height: 20px;
      font-weight: 400;
      padding: 12rpx 28rpx;
    }
  }
}

</style>