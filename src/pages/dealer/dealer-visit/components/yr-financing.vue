<!--
融资
<AUTHOR>
@date 240320
-->
<template>
    <link-page class="dealer-yr-financing">
        <link-auto-list :option="yrOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <view  @tap="gotoDetail(data)" class="yr-item">
                    <item :key="index" :data="data" :arrow="false">
                        <view slot="note">
                            <view class="media-list">
                                <view class="media-top">
                                    <!-- 编码 -->
                                    <view class="num-view" @longPress="copyActCode(data.financingCode)">
                                        <view class="num">{{data.financingCode}}</view>
                                    </view>
                                    <!-- 状态标识 -->
                                    <status-button v-if="data.orderStatus == '16'" :label="data.orderStatus| lov('YR_ORDER_STATUS')" type="green"></status-button>
                                    <status-button v-if="data.orderStatus == '15'" :label="data.orderStatus| lov('YR_ORDER_STATUS')" type="invalid"></status-button>
                                    <status-button v-if="data.orderStatus == '2F' || data.orderStatus == '2S'" :label="data.orderStatus| lov('YR_ORDER_STATUS')" type="normal"></status-button>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data2">
                                    <view class="title2">未还款金额(元)</view>
                                    <view class="val2">{{data.balance}}</view>
                                </view>
                                <view class="sum2">
                                    <view class="title2">融资金额(元)</view>
                                    <view class="val2">{{data.amount}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">融资日期</view>
                                    <view class="val">{{data.financingStartTime|date('YYYY-MM-DD')}}</view>
                                </view>
                                <view class="sum">
                                    <view class="title" v-if="data.orderStatus == '2F' || data.orderStatus == '2S'">到期日期</view>
                                    <view class="title" v-if="data.orderStatus == '15'">逾期日期</view>
                                    <view class="title" v-if="data.orderStatus == '16'">结清日期</view>
                                    <view class="val" v-if="data.orderStatus == '16'">{{data.finishTime|date('YYYY-MM-DD')}}</view>
                                    <view class="val" v-if="data.orderStatus == '15' || data.orderStatus == '2S' || data.orderStatus == '2F'">{{data.financingEndTime|date('YYYY-MM-DD')}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data">
                                    <view class="title">融资机构</view>
                                    <view class="val">{{data.bankName}}</view>
                                </view>
                            </view>
                            <view class="content-middle-line">
                                <view class="data1">
                                    <view class="title1">供货商名称</view>
                                    <view class="val1">{{data.dealerName}}</view>
                                </view>
                            </view>
                        </view>
                    </item>
                </view>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import StatusButton from './status-button';

export default {
    name: 'yr-financing',
    props: {
        index: Number,
        data: Object,
        yrOption: {
            type: Object
        },
    },
    components: {StatusButton},
    data() {
        return {
            activityTotal: '',
        }
    },
    methods: {
        /**
         * 复制活动编码
         *  <AUTHOR>
         * @date 240321
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
        /**
         * 跳转融资详情
         * <AUTHOR>
         * @date 240321
         */
         gotoDetail(data) {
            this.$nav.push('/pages/terminal/terminal/yr-financing-detail-page.vue', {
                data: data
            })
        },
    }
}
</script>

<style lang="scss">
@import "../../../../styles/list-card";
.dealer-yr-financing {
    .yr-item {
        background: #FFFFFF;
        width: 95%;
        margin: 24px auto;
        border-radius: 16px;
        overflow: hidden;
    }

    .media-list {
        @include media-list;

        .media-top {
            width: 100%;
            @include flex-start-center;
            @include space-between;
            height: 80px;
            line-height: 80px;

            .left-content {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                line-height: 32px;
                padding-top: 20px;

            }

            .right-content {
                font-family: PingFangSC-Semibold;
                font-size: 32px;
                color: #FF5A5A;
                letter-spacing: 0;
                text-align: right;
                line-height: 32px;
                padding-top: 20px;
            }

            .num-view {
                background: #A6B4C7;
                border-radius: 8px;
                line-height: 50px;

                .num {
                    font-size: 28px;
                    color: #FFFFFF;
                    letter-spacing: 0;
                    line-height: 40px;
                    padding: 2px 8px;
                }
            }

            .status-view {
                width: 120px;
                transform: skewX(-10deg);
                border-radius: 4px;
                background: #2F69F8;
                box-shadow: 0 6px 8px 0 rgba(47, 105, 248, 0.35);
                height: 36px;

                .status {
                    font-size: 20px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 36px;
                }
            }
        }
    }

    .content-middle {
        width: 100%;
        @include flex-start-center;
        @include space-between;
        height: 80px;
        line-height: 80px;

        .content {
            font-family: PingFangSC-Regular;
            font-size: 28px;
            color: #000000;
            letter-spacing: 0;
        }

        .name {
            font-family: PingFangSC-Semibold;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
        }
    }

    .content-middle-line {
        width: 100%;
        .data1 {
            width: 100%;
            float: left;

            .title1 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                width: 26%;
                float: left;

            }

            .val1 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }
        }

        .data {
            width: 53%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                width: 40%;
                float: left;

            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }

            .Submitted, .Feedback{
                color: #2F69F8;
            }

            .Approve, .FeedbackApro{
                color: #2EB3C2;
            }

            .Refused, .Refeedback{
                color: #FF5A5A;
            }

        }
        .data2 {
            width: 53%;
            float: left;

            .title2 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                width: 58%;
                float: left;

            }

            .val2 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }

            .Submitted, .Feedback{
                color: #2F69F8;
            }

            .Approve, .FeedbackApro{
                color: #2EB3C2;
            }

            .Refused, .Refeedback{
                color: #FF5A5A;
            }

        }

        .sum2 {
            width: 47%;
            float: left;

            .title2 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                width: 55%;
            }

            .val2 {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
                white-space:nowrap;
            }
        }
        .sum {
            width: 40%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                width: 50%;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
                white-space:nowrap;
            }
        }

        .sum-2 {
            width: 58%;
            float: left;

            .title {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 56px;
                float: left;
                margin-right: 24px;
            }

            .val {
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #000000;
                letter-spacing: 0;
                line-height: 56px;
            }
        }
    }
}
</style>

