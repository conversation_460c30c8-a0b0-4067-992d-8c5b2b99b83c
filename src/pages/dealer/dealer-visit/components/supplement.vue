<template>
    <view class="dealer-supplement">
        <link-sticky top :duration="durationOrder + 90">
            <view class="lnk-tabs-order">
                <view class="lnk-tabs-item" :class="{'active': tab.seq === supplementType.seq}" style="width: 50%"
                      v-for="(tab, index) in supplementTabs" :key="index" @tap="changeSupplementActive(tab, index)">
                    <view class="label-name-bg">
                        <view :class="tab.seq === supplementType.seq ? 'label-name-on' : 'label-name-off'">{{tab.name}}</view>
                    </view>
                </view>
            </view>
        </link-sticky>
        <view class="exit-text" v-if="!disabledNewEdit">
            <text @tap="goSupplement">补录</text>
        </view>
        <link-auto-list :option="supplementOption" class="supplementList">
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item" @tap="goSupplementItem(data)">
                    <view slot="note" class="list-item">
                        <view class="list-item-row1">
                            <view class="left">
                                <view class="lable-name" v-if="supplementType.val === 'quotaSupplement'">
                                    创建日期
                                </view>
                                <view class="lable-name" v-if="supplementType.val === 'scanSupplement'">
                                    扫码时间
                                </view>
                                <view class="text">
                                    {{ data.submitTime || data.scanTime | date('YYYY-MM') }}
                                </view>
                            </view>
                        </view>
                        <view class="list-item-row1" v-if="supplementType.val === 'quotaSupplement'">
                            <view class="left">
                                <view class="lable-name">
                                    申请人
                                </view>
                                <view class="text">
                                    {{ data.createdName }}
                                </view>
                            </view>
                        </view>
                        <view class="list-item-row1" v-if="supplementType.val === 'scanSupplement'">
                            <view class="left">
                                <view class="lable-name">
                                    {{ data.acctType === 'Terminal' ? '终端': '分销商' }}
                                </view>
                                <view class="deadLine"/>
                                <view class="text">
                                    {{ data.acctName }}
                                </view>
                            </view>
                        </view>
                        <view class="list-item-row1" v-if="supplementType.val === 'quotaSupplement'">
                            <view class="left">
                                <view class="lable-name">
                                    类型
                                </view>
                                <view class="text">
                                    {{ data.type | lov('QUOTA_TYPE') }}
                                </view>
                            </view>
                        </view>
                        <view class="list-item-row1" v-if="supplementType.val === 'scanSupplement'">
                            <view class="left">
                                <view class="lable-name">
                                    扫码总数
                                </view>
                                <view class="text">
                                    {{ data.scanTotal }}
                                </view>
                            </view>
                        </view>
                        <view class="list-item-row1" v-if="supplementType.val === 'quotaSupplement'">
                            <view class="left" style="height: auto;">
                                <view class="lable-name" style="min-width: 45px;">
                                    {{ data.acctType === 'Terminal' ? '终端': '分销商' }}
                                </view>
                                <view class="text" style="min-width: 100px;">
                                    {{ data.acctCode }}
                                </view>
                                <view class="deadLine"/>
                                <view class="text" style="line-height: 20px;">
                                    {{ data.acctName }}
                                </view>
                            </view>
                        </view>
                        <view class="list-item-row1" v-if="supplementType.val === 'scanSupplement'">
                            <view class="left">
                                <view class="lable-name">
                                    创建人
                                </view>
                                <view class="text">
                                    {{ data.scanCreator}}
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    export default {
        name: "dealer-supplement",
        data() {
            // 历史数据配额补录
            const supplementOption = new this.AutoList(this, {
                module: 'action/link/quota',
                loadOnStart: false,
                param: {},
                sortOptions: null,
                filterBar: {},
            });
            return {
                supplementOption,
                durationOrder: this.$device.isIphoneX ? 166 : 134,
                supplementTabs: [
                    {name: "配额补录", seq: "1", val: "quotaSupplement"},
                    {name: "扫码补录", seq: "2", val: "scanSupplement"}
                ],
                supplementType: {},
            }
        },
        props: {
            disabledNewEdit: {
                type: Boolean,
                default: false
            },
            accntIdArr: {
                type: Array,
                default: []
            },
            clientDetails: {
                type: Object,
                default: {}
            }
        },
        created() {
            this.supplementType = this.supplementTabs[0];
            this.changeSupplementActive();
        },
        methods: {
            /**
             * 历史数据补录切换按钮
             * <AUTHOR>
             * @data 2021-05-26
             * @param val 选中的值
             */
            async changeSupplementActive(val = {}) {
                if (this.supplementOption.loading) {
                    return;
                }
                val.val && (this.supplementType = val);
                const that = this;
                if (that.supplementType.val === 'quotaSupplement') {
                    that.supplementOption.option.module = 'action/link/quota';
                    that.supplementOption.option.sortField = 'submitTime';
                    that.supplementOption.option.sortDesc = 'desc';
                    that.supplementOption.option.param = {
                        filtersRaw: [
                            {id: 'enterFlag', property: 'enterFlag', value: 'Y'},
                            {id: 'acctId', property: 'acctId', value: `[${that.accntIdArr.toString()}]`, operator: 'in'}
                        ]
                    }
                }
                if (that.supplementType.val === 'scanSupplement') {
                    that.supplementOption.option.module = 'action/link/codeScan';
                    that.supplementOption.option.sortField = 'scanTime';
                    that.supplementOption.option.sortDesc = 'desc';
                    that.supplementOption.option.param = {
                        filtersRaw: [
                            {id: 'enterFlag', property: 'enterFlag', value: 'Y'},
                            {id: 'acctId', property: 'acctId', value: `[${that.accntIdArr.toString()}]`, operator: 'in'}
                        ]
                    }
                }
                this.supplementOption.methods.resetSort();
                await this.supplementOption.methods.reload();
            },
            /**
             * 历史数据补录后重载数据
             * <AUTHOR>
             * @data 2021-06-4
             * @param supplementType
             */
            async reloadQueryData(supplementType) {
                if (supplementType === 'quotaSupplement') {
                    this.supplementOption.option.sortField = 'submitTime';
                    this.supplementOption.option.sortDesc = 'desc';
                    this.supplementOption.option.module = 'action/link/quota';
                }
                if (supplementType === 'scanSupplement') {
                    this.supplementOption.option.sortField = 'scanTime';
                    this.supplementOption.option.sortDesc = 'desc';
                    this.supplementOption.option.module = 'action/link/codeScan';
                }
                this.supplementOption.methods.resetSort();
                await this.supplementOption.methods.reload();
            },
            // 补录数据
            goSupplement() {
                this.$nav.push('/pages/terminal2/supplement/quota-supplement-page', {
                    clientDetails: this.clientDetails,
                    supplementType: this.supplementType.val,
                    callback: () => {
                        this.reloadQueryData(this.supplementType.val);
                        // this.$emit('reloadQueryData', this.supplementType.val); //通知父组件重新查询配额或者扫码数据。
                    }
                })
            },
            // 查看详情
            goSupplementItem(data) {
                if (this.supplementType.val === 'quotaSupplement') {
                    this.$nav.push('/pages/terminal/quota/quota-detail-page', {
                        pageSource: data.type,
                        data: data,
                        source: 'list',
                        page: 'supplement'
                    })
                } else {
                    this.$nav.push('/pages/terminal/scan-code/scan-code-detail-page', {data})
                }
            },
        }
    }
</script>

<style lang="scss">
    .dealer-supplement {
        .exit-text {
            padding: 10px 30px 10px 0;
            text-align: right;
            color: #2F69F8;
            font-size: 28px;
        }

        .link-auto-list-top-bar{
            display: flex;
            .link-search-input{
                width: calc(100% - 120px);
                padding-bottom: 24px!important;
                padding-right: 0;
            }
            .link-auto-list-query-bar{
                width: 142px;
                padding-left: 0;
                .link-filter{
                    padding-left: 12px!important;
                    justify-content: flex-start!important;
                }
            }
        }
        .link-auto-list .link-auto-list-top-bar {
            border: none;
        }
        .perform-case-list-item {
            background: #FFFFFF;
            margin: 24px 0;
            border-radius: 16px;
            padding-right: 0;
        }

        .list-item {
            .list-item-row1 {
                width: 100%;
                @include flex-start-center;
                @include space-between;

                .left {
                    flex: 1;
                    @include flex-start-center;
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    letter-spacing: 0;
                    line-height: 60px;
                    height: 60px;

                    .deadLine {
                        width: 2px;
                        height: 24px;
                        border: 1px solid #eee;
                        margin: 0 14px;
                    }

                    .lable-name {
                        color: #8C8C8C;
                        margin-right: 10px;
                    }

                    .text {
                        color: #000;
                    }
                }

            }
        }
    }
</style>
