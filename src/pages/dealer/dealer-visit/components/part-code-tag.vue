<template>
  <view class="dealer-part-code-tag">
      <link-dialog ref="prodBottom" position="bottom" height="65vh" class="dialog-bottom" noPadding v-model="showProd" @hide="dialogHide">
          <view class="model-title">
              <view class="iconfont icon-close" @tap="dialogHide"></view>
              <view class="title">分品项</view>

          </view>
          <view class="dialog-content" style="height: calc(100% - 44px)">
              <scroll-view scroll-y="true" :style="{'height': 'calc(100% - 75px)'}">
                  <view class="new-tag-list">
                      <!-- <view>search</view> -->
                      <view>
                          <view v-for="(data,index) in list" :key="index+'list'">
                             <item :key="index" :data="data" :arrow="false" style="padding-top: 0;padding-bottom:0">
                                 <link-radio-group v-model="tempProdId">
                                     <item :arrow="false">
                                         <link-checkbox :val='data.partCode' slot="thumb" toggleOnClickItem @tap="tempProdInfo(data)"/>
                                     </item>
                                 </link-radio-group>
                                 <view class="list-item">
                                     {{data.partName}}
                                 </view>
                             </item>
                          </view>
                      </view>
                 </view>
              </scroll-view>
              <view class="link-dialog-foot-custom">
                  <link-button shadow @tap="chooseTag" label="确定" style="width:100vw"/>
              </view>
          </view>
      </link-dialog>
  </view>
</template>

<script>
  export default {
    name: "dealer-part-code-tag",
    props: {
      userInfo: {},
      showProd: false,
      orgId:'',
      list:{
          type:Array,
          default (){
              return []
          }
      }
    },
    data() {
        return {
            searchFlag: true,
            tempProdId: null, //选中的品项
            selectData: {},
        }
    },
      watch:{
          list(newVal, oldVal) {
              this.selectData = newVal[0]
              this.tempProdId = newVal[0].partCode
          }
      },
      methods: {
        tempProdInfo(val){
            this.selectData = val
        },
        // 选中品项
        chooseTag(){
             this.$emit('update:showProd', false);
             this.$emit('chose', this.selectData);
        },
        // 退出
        dialogHide(){
            this.$emit('update:showProd', false);
        }
    }

  }
</script>

<style lang="scss">
.dealer-part-code-tag{
    .link-dialog-foot-custom{
        width: auto !important;
    }
  .link-dialog-body{
    position: relative;
  }
  .link-auto-list .link-auto-list-top-bar{
    border:none;
  }
  .link-item .link-item-body-right{
    margin: 0 24px;
  }
  .link-radio-group{
    width: 70px;
    .link-item{
      padding:24px 24px 24px 0;
      .link-item-thumb{
        padding-right: 0;
      }
      .link-item-icon{
        display:none;
      }
    }
    .link-item-active{
      background-color: #f6f6f6;
    }
  }
  .list-item{
    flex: 1;
  }
  .link-radio-group .link-item:active,.link-item-active{
    background-color: #f6f6f6;
  }
  .link-auto-list-no-more{
    display: none;
  }
  .dialog-bottom{
    .dialog-content{
      padding: 0 20px;
      position: relative;
      //.link-button{
      //  position: absolute;
      //  bottom: 0
      //}
    }
    .model-title {
      .title {
        font-family: PingFangSC-Regular,serif;
        font-size: 32px;
        color: #262626;
        letter-spacing: 0;
        text-align: center;
        line-height: 96px;
        height: 96px;
        width: 90%;
        padding-left: 0!important;
        margin-right: 80px;
        margin-left: 10vw;
      }
      .icon-close {
        color: #BFBFBF;
        font-size: 48px;
        line-height: 96px;
        height: 96px;
        margin-right: 30px;
        float:right
      }
    }
  }
}
</style>
