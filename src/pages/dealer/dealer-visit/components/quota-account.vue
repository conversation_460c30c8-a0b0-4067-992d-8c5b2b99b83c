/**
* @createdBy  张丽娟
* @date  2020/10/16
* @description 配额
*/
<template>
    <view class="dealer-quota-account">
        <view class="edit-text" @tap="goHistoryQuota">历史配额</view>
        <link-auto-list :option="autoList" hideCreateButton>
            <template slot-scope="{data,index}">
                <view class="quota-single">
                    <view class="quota-content-row1">
                        {{data.startDate}} 至 {{data.endDate}}
                    </view>
                    <view class="quota-content-row2">
                        <view class="prod-name">
                            {{data.materialName}}
                        </view>
                        <view class="unit">
                            {{data.measureUnitName}}
                        </view>
                    </view>
                    <view class="quota-content-row3" :class="data.quotaOccupyAmt ? 'quota-content-row3-colum4': 'quota-content-row3-colum3'">
                        <view class="quota-content-row3-item">
                            <view class="number">
                                {{data.quotaTotalAmt}}
                            </view>
                            <view class="status">
                                额度
                            </view>
                        </view>
                        <view class="line"></view>
                        <view class="quota-content-row3-item">
                            <view class="number">
                                {{data.quotaUsedAmt}}
                            </view>
                            <view class="status">
                                已执行
                            </view>
                        </view>
                        <view class="line" v-if="data.quotaOccupyAmt || isGuojiao"></view>
                        <view class="quota-content-row3-item" v-if="data.quotaOccupyAmt || isGuojiao">
                            <view class="number">
                                {{data.quotaOccupyAmt}}
                            </view>
                            <view class="status">
                                占用中
                            </view>
                        </view>
                        <view class="line"></view>
                        <view class="quota-content-row3-item">
                            <view class="number">
                                {{data.quotaBalanceAmt}}
                            </view>
                            <view class="status">
                                余额
                            </view>
                        </view>
                    </view>

                </view>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    import Taro from "@tarojs/taro";

    export default {
        name: "dealer-quota-account",
        props: {
            acctId: {
                type: String,
                default: '',
                required: true
            },
            acctType:{
                type: String,
                default: '',
                required: true
            },
            editApprovalStatus: {
                type: String,
                default: ''
            }
        },
        data(){
            let userInfo = Taro.getStorageSync('token').result
            console.log(userInfo);
            let isGuojiao = userInfo.coreOrganizationTile.brandCompanyCode === '5600' ? true : false
            let isTerminal = this.acctType === 'Terminal' ? true : false
            return{
                autoList: new this.AutoList(this, {
                    url:{
                        queryByExamplePage: 'action/link/quotaItem/queryQuotaListForTerminal',
                    },
                    searchFields: null,
                    param: {
                        sort: 'created',
                        rows: 5,
                        acctId: this.acctId,
                        // headAcctType: isTerminal ? 'Terminal' : 'Distributor',
                        headAcctType: 'Terminal',
                        brandComCode: userInfo.coreOrganizationTile.brandCompanyCode
                    },
                    sortOptions: null,
                }),
                isGuojiao: isGuojiao,
                isTerminal: isTerminal
            }
        },
        methods:{
            /**
             * @createdBy  张丽娟
             * @date  2020/10/21
             * @methods goHistoryQuota
             * @para
             * @description 跳转到历史配额界面
             */
            goHistoryQuota(){
                this.$nav.push('/pages/terminal/quota/quota-list-page', {
                    data: {id: this.acctId},
                    pageFrom: 'agreement',
                    editApprovalStatus: this.editApprovalStatus
                })
            }
        }

    }
</script>

<style lang="scss">
    .dealer-quota-account{
        .quota-single{
            background: #fff;
            margin: 0 24px 24px;
            padding: 40px 0 ;
            border-radius: 16px;
            .quota-content-row1{
                font-family: PingFangSC-Regular;
                font-size: 28px;
                color: #8C8C8C;
                letter-spacing: 0;
                line-height: 28px;
                padding: 0 24px 24px;
            }
            .quota-content-row2{
                display: flex;
                justify-content: space-between;
                padding: 0 24px 24px;
                .prod-name{
                    font-family: PingFangSC-Semibold;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 32px;
                }
                .unit{
                    font-family: PingFangSC-Regular;
                    font-size: 28px;
                    color: #8C8C8C;
                    letter-spacing: 0;
                    line-height: 28px;
                }
            }
            .quota-content-row3-colum4{
                .quota-content-row3-item{
                    width: calc(25% - 2px);
                }
            }
            .quota-content-row3-colum3{
                .quota-content-row3-item{
                    width: 33.33%;
                }
            }
            .quota-content-row3{
                width: 100%;
                display: flex;
                .line{
                    width: 2px;
                    height: 50px;
                    background-image: linear-gradient(180deg, rgba(191,191,191,0.00) 0%, rgba(191,191,191,0.50) 52%, rgba(191,191,191,0.00) 100%);
                }
                .quota-content-row3-item{
                    .number{
                        font-family: PingFangSC-Regular;
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 28px;
                        margin-bottom: 12px;
                    }
                    .status{
                        font-family: PingFangSC-Regular;
                        font-size: 24px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        text-align: center;
                        line-height: 24px;
                    }
                }
                .quota-content-row3-item:last-child{
                    color: #FF5A5A;
                }
            }
        }
    }
</style>
