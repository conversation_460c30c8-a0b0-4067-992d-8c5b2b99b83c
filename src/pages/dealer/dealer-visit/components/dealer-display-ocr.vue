<template>
    <view class="dealer-display-ocr">
        <view class="display-ocr-wrap" v-if="false">
            <line-title title="AI识别附件"></line-title>
            <lnk-img-watermark v-if="$utils.isNotEmpty(headId)"
                            :parentId="headId"
                            :rootId="rootId"
                            source="agreeDetail"
                            moduleType="displayAI"
                            moduleName="陈列AI"
                            @initSuccess="initSuccess"
                            @imgUploadSuccess="imgUploadSuccess"
                            @imgDeleteSuccess="imgDeleteSuccess"
                            @canDel="canDel"
                            :delFlag="!disabled"
                            :newFlag="!disabled"
                            :album="false">
            </lnk-img-watermark>
        </view>
        <view class="display-ocr-wrap">
            <line-title title="陈列明细">
                <view class="right-wrap">
                    <text @tap="chooseProdItem" v-if="!disabled">手动填写</text>
                    <!-- <link-icon icon="icon-AIshibie" @tap="ocrDialogShow" v-if="!disabled"></link-icon> -->
                </view>
            </line-title>
            
            <view v-for="(item, index) in recognitionResult" :key="item.prodPartCode" class="ocr-item">
                <view class="row-info part-code-wrap">
                    <view class="part-code">{{item.prodPartCode | lov(isGuoJIao ? 'PROD_BUS_L_CLASS' : 'PROD_BUS_S_CLASS')}} | {{item.prodPartCode}}</view>
                    <view class="del-tip" v-if="!disabled">
                        <view v-if="item.source === 'AiOcr'" class="tip">AI识别结果不允许删除</view>    
                        <link-icon icon="icon-shanchu" v-else @tap="delRecItem(item, index)"/>
                    </view>
                </view>
                
                <view class="row-info">
                    <view class="row-left"><text class="red">*</text>陈列面（瓶）</view>
                    <link-number-keyboard v-model="item.displayNum" 
                                        placeholder="请填写陈列面（瓶）" 
                                        @input="changeDisplayNum(item)"
                                        :min="0" 
                                        v-if="!disabled"/>
                    <view v-else>{{item.displayNum}}</view>
                </view>
                <!-- <view class="row-info">
                    <view class="row-left">AI识别陈列面（瓶）</view>
                    <view>{{item.aiDisplayNum}}</view>
                </view> -->
            </view>
        </view>

        <!-- AI识别确认弹窗 -->
        <link-dialog ref="AIdialog" disabledHideOnClickMask>
            <view>
                <scroll-view :scroll-y="true" style="min-height: 150rpx; max-height: 490rpx;">
                    <view class="img-list">
                        <view v-for="(img, index) in imgList" :key="index" class="item-box-image">
                            <image class="item-image" 
                                :src="$image.getSignedUrl(img.smallurl)"
                                :data-src="img.smallurl">
                            </image>
                        </view>
                    </view>
                </scroll-view>
                <view class="ocr-tip">为了避免造成额外的消耗，请确认拍照清晰完整无误后，点击提交发起AI识别~</view>
            </view>
            
            <view class="btn-wrap" slot="foot">
                <link-button class="btn-item btn-left" @tap="cancel" shape="round" mode="stroke">取消</link-button>
                <link-button class="btn-item" @tap="recognition" shape="round">提交</link-button>
            </view>
            
        </link-dialog>

        <!-- 选择品项弹窗 -->
        <link-dialog ref="prodDialog" noPadding position="bottom" class="dialog-bottom"
                     height="70vh" borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">品项</view>
                <view class="iconfont icon-close1" @tap="$refs.prodDialog.hide()"></view>
            </view>
            <!-- <link-search-input v-model="searchText" @change="searchProd" placeholder="请搜索品项编码"/> -->
            <scroll-view :scroll-y="true" style="height: calc(100% - 120px)">
                <block v-if="prodList.length">
                    <view v-for="(data) in prodList" :key="data.prodId">
                        <link-checkbox-group v-model="chooseProd">
                            <item :arrow="false" :key="data.prodPartCode" :data="data"
                                  :content="data.prodPartName" 
                                  :title="data.prodPartCode">
                                <link-checkbox :val=data.prodPartCode slot="thumb" toggleOnClickItem/>
                            </item>
                        </link-checkbox-group>
                    </view>
                </block>
                <view class="no-data" v-else>暂无数据</view>
            </scroll-view>
            <view class="blank"></view>
            <link-sticky class="bottom-btn">
                <link-button block autoLoading @tap="confirmProd">确认</link-button>
            </link-sticky>
        </link-dialog>
    </view>
</template>

<script>
import LnkImgWatermark from '../../../core/lnk-img-watermark/lnk-img-watermark';
import LineTitle from './line-title.vue';

export default {
    components: {
        LineTitle,
        LnkImgWatermark
    },
    props: {
        recognitionResult: Array,
        disabled: Boolean,
        acctData: Object,
        rootId: String,
        headId: String
    },
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
        return {
            isGuoJIao: userInfo.coreOrganizationTile.brandCompanyCode === '5600',
            imgList: [],
            // 已识别过的图片id
            ocrImgIds: [],
            prodListForSearch: [],
            prodList: [],
            chooseProd: []
        }
    },
    methods: {
        /**
         * 修改陈列面数量
         * <AUTHOR>
         * @date    2024/12/31 17:39
         */
        changeDisplayNum(item) {
            this.$emit('changeDisplayNum', this.recognitionResult);
        },
        /**
         * 获取已识别过的图片
         * <AUTHOR>
         * @date    2024/12/31 17:39
         */
        async getOcrResult() {
            try {
                this.$utils.showLoading();
                const {success, imageIds, displayItems} = await this.$http.post('action/link/accntVisitDisplayItemResult/queryVisitItemResult', {
                    visitId: this.rootId
                });
                if (success) {
                    if (!this.recognitionResult.length) {
                        displayItems.forEach(item => {
                            item.row_status = 'NEW';
                            item.source = 'AiOcr';
                        });
                        this.recognitionResult = displayItems;
                    }
                    this.ocrImgIds = imageIds;
                }
            } catch (e) {
                console.log('e', e);
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 是否可以删除AI识别图片
         * <AUTHOR>
         * @date    2024/12/23 17:39
         */
        canDel(item, callback) {
            const ocrImgIds = this.ocrImgIds;
            callback({
                canDelFlag: !ocrImgIds.includes(item.id),
                delTip: '已识别的图片不允许删除'
            });
        },
        /**
         * 删除陈列明细
         * <AUTHOR>
         * @date    2024/12/23 17:39
         */
        async delRecItem(item, index) {
            await this.$dialog.confirm('确认要删除当前数据吗？');
            if (item.row_status === 'NEW') {
                this.recognitionResult.splice(index, 1);
                this.$message.success('删除成功！');
            } else {
                const {success, result} = await this.$http.post('action/link/accntVisitDisplayItem/deleteById', {
                    id: item.id
                });  
                if (success) {
                    this.recognitionResult.splice(index, 1);
                    this.$emit('changeDisplayNum', this.recognitionResult);
                    this.$message.success('删除成功！');
                }
            }
        },
        /**
         * 除
         * <AUTHOR>
         * @date    2024/12/23 17:39
         */
        divide(num1, num2) {
            if (this.$utils.isEmpty(num2)) {
                return '';
            }
            if (this.$utils.isEmpty(num1)) {
                num1 = 0;
            }
            const num1Digits = (num1.toString().split(".")[1] || "").length;
            const num2Digits = (num2.toString().split(".")[1] || "").length;
            const baseNum = Math.pow(10, Math.max(num1Digits, num2Digits));
            return (num1 * baseNum / num2 * baseNum) / baseNum / baseNum;  
        },
        /**
         * 确认选择品项
         * <AUTHOR>
         * @date    2024/12/23 17:39
         */
        async confirmProd() {
            console.log('this.chooseProd', this.chooseProd)
            if (!this.chooseProd.length) {
                this.$showError('请先选择品项！')
                return;
            }
            this.$refs.prodDialog.hide();
            this.chooseProd.forEach(item => {
                const index = this.recognitionResult.findIndex(data => data.prodPartCode === item);
                if (index === -1) {
                    this.$emit('confirmProd', item)
                }
            });
        },
        /**
         * 选择品项
         * <AUTHOR>            
         * @date    2024/12/23 17:39
         */
        async chooseProdItem() {
            try {
                const partList = this.recognitionResult.map(item => item.prodPartCode);
                const res = await this.$http.post('action/link/accntVisitDisplayItem/queryPartByAcctId', {
                    acctId: this.acctData.id,
                    partList: partList.length ? partList : []
                })
                if (res.success && res.rows) {
                    this.prodList = res.rows;
                } else {
                    this.prodList = [];
                }
                this.prodListForSearch = this.$utils.deepcopy(this.prodList);
                this.chooseProd = [];
                this.$refs.prodDialog.show();
            } catch (e) {
                console.log('e', e)
            }
        },
        /**
         * 取消识别
         * <AUTHOR>
         * @date    2024/12/23 17:39
         */
        cancel() {
            this.$refs.AIdialog.hide();
        },
        /**
         * 上传图片成功
         * <AUTHOR>
         * @date    2024/12/4 11:10
         */
        imgUploadSuccess(list) {
            this.imgList = list;
        },
        /**
         * 删除图片成功
         * <AUTHOR>
         * @date    2024/12/31 17:21
         */
        imgDeleteSuccess(list) {
            this.imgList = list;
        },
        /**
         * 获取已上传图片
         * <AUTHOR>
         * @date    2024/12/4 9:46
         */
        async initSuccess(data) {
            this.imgList = data; 
        },
        /**
         * 识别
         * <AUTHOR>
         * @date    2024/12/4 9:59
         */
        async recognition(source) {
            if (!this.imgList.length) {
                this.$showError('请先上传陈列图片！');
                return;
            }
            let images = [];
            let imagesId = [];
            this.imgList.forEach((item) => {
                images.push({
                    image: item.base64,
                    id: item.id
                });
                imagesId.push(item.id);
            });
            try {
                this.$utils.showLoading();
                const {success, rows} = await this.$http.post('action/link/accntVisitDisplayItem/aiIdentity', {
                    acctId: this.acctData.id,
                    visitId: this.rootId,
                    headId: this.headId,
                    images: images
                }, {
                    autoHandleError: source !== 'initSuccess',
                    handleFailed: (response) => {}
                });
                
                if (success) {
                    this.$utils.hideLoading();
                    this.$refs.AIdialog.hide();
                    // 存在则更新AI识别数量，否则push
                    rows.forEach((item) => {
                        const existIndex = this.recognitionResult.findIndex(val => val.prodPartCode === item.prodPartCode);
                        if (existIndex === -1) {
                            item.row_status = 'NEW';
                            item.source = 'AiOcr';
                            this.recognitionResult.push(item);
                        } else {
                            this.recognitionResult[existIndex].aiDisplayNum = item.aiDisplayNum;
                            this.recognitionResult[existIndex].source = 'AiOcr';
                        }
                    });
                    this.ocrImgIds = imagesId;
                }
            } catch (e) {
                console.log('e', e);
            } finally {
                this.$utils.hideLoading();
            }
        },
        /**
         * 获取图片
         * <AUTHOR>
         * @date    2024/11/19 16:38
         */
         ocrDialogShow() {
            if (!this.imgList.length) {
                this.$showError('请先上传陈列图片！');
                return;
            }
            this.$refs.AIdialog.show();
        }
    }
}
</script>

<style lang="scss">

.dealer-display-ocr {

    .display-ocr-wrap {
        margin: 20px;
        background: #fff;
        border-radius: 20px;
        padding-bottom: 20px;

        .line-title {
            margin-bottom: 24px;
        }

        .ocr-tip {
            margin: 20px;
        }

        .submit-btn {
            margin: 20px;
        }

        .right-wrap {
            display: flex;
            align-items: center;
            color: #1F74FF;
            font-size: 28px;

            .icon-AIshibie {
                color: #1F74FF;
                font-size: 56px;
                margin-left: 20px;
            }
        }

        .ocr-item {
            margin: 20px;
            border: 2px solid #A6BBC3;
            padding: 10px;
            border-radius: 8px;
        
            .red {
                color: red;
            }

            .row-info {
                display: flex;
                align-items: center;
                justify-content: space-between;
                margin-top: 20px;

                .row-left {
                    font-size: 28px;
                    color: #333;
                }

                .link-number-keyboard {
                    flex: 1;
                }
            }

            .part-code-wrap {
                margin-bottom: 28px;
                margin-top: 18px;

                .part-code {
                    background: #A6BBC3;
                    color: #fff;
                    font-size: 30px;
                    padding: 8px;
                    border-radius: 8px;
                }

                .del-tip {

                    .icon-shanchu {
                        font-size: 34px;
                        color: #aaa;
                        padding: 10px 0 10px 10px;
                    }

                    .tip {
                        font-size: 24px;
                        color: red;
                    }
                }
            }
        }   
    }

    .img-list {
        display: flex;
        flex-wrap: wrap;

        .item-box-image {
            position: relative;
            background: #fff;
            margin-bottom: 20px;
            width: 33.3%;
            height: 146px;

            .item-image {
                width: 146px;
                height: 146px;
                display: inline-block;
                object-fit: cover;
                border-radius: 16px;
            }
        }
    }

    .btn-wrap {
        display: flex;
        width: 100%;
        margin: 20px;

        .btn-left {
            margin-right: 20px;
        }

        .btn-item {
            flex: 1;
        }
    }

    .dialog-bottom {

        .model-title {
            display: flex;

            .title {
                font-size: 32px;
                color: #262626;
                text-align: center;
                line-height: 96px;
                height: 96px;
                flex: 1;
                margin-left: 76px;
            }

            .icon-close1 {
                color: #ddd;
                font-size: 48px;
                line-height: 96px;
                height: 96px;
                margin-right: 28px;
            }
        }

        .no-data {
            width: 100%;
            text-align: center;
            color: #999;
            margin: 150px 0;
        }
    }
}
</style>
