<template>
    <link-page class="dealer-consumer-activities">
        <link-auto-list :option="consumerActivities" hideCreateButton>
            <template slot-scope="{data,index}">
                <link-swipe-action class="perform-case-list-item">
                    <link-swipe-option  slot="option" @tap="deleteVerify(data)">
                        删除
                    </link-swipe-option>
                    <item :key="index" :data="data" :arrow="false" >
                        <view slot="note">
                            <view class="line">
                                <view class="code">{{ data.verifyPolicyName }}</view>
                                <view class="item-tag"  v-if="data.verifyPolicyStatus==1">
                                    <view class="tag-content" >{{data.verifyPolicyStatus| lov('VERIFY_POLICY_STATUS')}}</view>
                                </view>
                                <view class="item-tag" v-if="data.verifyPolicyStatus==2" style="background: #A1A7AF;box-shadow: 0 3px 4px 0 rgba(191,191,191,0.50);">
                                    <view class="tag-content"  >{{data.verifyPolicyStatus| lov('VERIFY_POLICY_STATUS')}}</view>
                                </view>

                            </view>
                            <view class="name">{{ data.prizeName }}</view>
                        </view>
                    </item>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: "dealer-consumer-activities",
    data () {
        const consumerActivities = new this.AutoList(this, {
            module: 'action/link/accntWriteoff',
            url: {
                queryByExamplePage: 'action/link/mvg/queryRightListPage'
            },
            loadOnStart: true,
            param: {
                mvgMapperName: 'accntVerifyPolicy',
                mvgSubsetId: this.accntId ,
                oauth: 'ALL'
            }
        })
        return {
            consumerActivities
        }
    },
    props: {
        accntId: {
            type: String,
            default: '',
            required: true
        }
    },
    methods:{
        /**
         * @createdBy  王雅琪
         * @date  2022/10/19
         * @methods deleteVerify
         * @param data
         * @description 删除关联的核销政策
         */
        async deleteVerify(data){
            const params = [{
                mvgMapperName: 'accntVerifyPolicy',
                mvgSubsetId: data.acctId,           //终端id字段
                mvgParentId: data.verifyId        //政策id字段
            }];
            try {
                this.$dialog({
                    title: '提示',
                    content: '是否确认取消绑定此活动？',
                    cancelButton: true,
                    onConfirm: async() => {
                        const res = await this.$http.post('action/link/mvg/batchDelete', params);
                        if(res.success) {
                            this.consumerActivities.methods.reload();
                        }
                    },
                })
            } catch (e) {
                this.$showError('取消绑定活动出错', e);
            }
        }
    }
}
</script>

<style lang="scss">
.dealer-consumer-activities {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular, serif;

    .perform-case-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
        width: auto;
        .line{
            width: 100%;
            display: flex;
            justify-content: space-between;
            .item-tag {
                min-width:48px;
                margin-left: 12px;
                height: 36px;
                line-height: 36px;
                text-align: center;
                color: #ffffff;
                background: #2F69F8;
                box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                border-radius: 8px;
                padding-left: 24px;
                padding-right: 24px;
                font-size: 20px;
                margin-right: 8px;
                transform: skewX(-30deg);
                .tag-content {
                    transform: skewX(30deg);
                }
            }
        }
    }
    .code {
        background: #A6B4C7;
        border-radius: 8px;
        font-size: 28px;
        color: #FFFFFF;
        letter-spacing: 0;
        line-height: 46px;
        display: inline-block;
        padding: 8px;
        margin-bottom: 16px;
    }
    .name {
        font-size: 34px;
        color: black;
    }
}
</style>
