<template>
    <link-page class="dealer-price">
        <view class="edit-text" @tap="goHistoryPrice">查看历史</view>
        <link-auto-list :option="priceList" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="price-list-rows">
                    <view slot="note">
                        <view class="media-list">
                            <view class="top-rows">
                                <view class="num-view">
                                    <view class="num">{{data.prodCode}}</view>
                                </view>
                                <view class="store-level">采集时间 <text class="date-name">{{data.created | date('YYYY-MM-DD')}} {{data.createdName}}</text></view>
                            </view>
                        </view>
                        <view class="media-list">
                            <view class="store-text">
                                <view class="content-middle">
                                    <view class="name">{{data.prodName}}</view>
                                    <view class="store-level">{{data.retailUnit | lov('PROD_UNIT')}}</view>
                                </view>
                                <view class="content-price">
                                    <view class="price-item">
                                        <view class="price">¥{{data.purchasePrice}}</view>
                                        <view class="price-type">采购价</view>
                                    </view>
                                    <view class="parting-line"></view>
                                    <view class="price-item">
                                        <view class="price">¥{{data.undercurrentPrice}}</view>
                                        <view class="price-type">暗流价</view>
                                    </view>
                                    <view class="parting-line"></view>
                                    <view class="price-item">
                                        <view class="price">¥{{data.finalPrice}}</view>
                                        <view class="price-type">消费者成交价</view>
                                    </view>
                                </view>
                                <view class="store-input">
                                    <view class="store-input-rows">
                                        <view class="label">供货货源</view>
                                        <view class="value">{{data.supplySource | lov('SUPPLY_SOURCE')}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-price",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            const priceList = new this.AutoList(this, {
                module: 'action/link/visitPrice',
                itemPath: '',
                param: () => {
                    const filtersRaw = [{id: 'visitStatus', property: 'visitStatus', value: 'Y', operator: '='}];
                    // 特定公司的经销商人员只能看到自己职位创建的数据
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        oauth: 'ALL',
                        sort: 'created',
                        order: 'desc',
                        accntId: this.acctId,
                        tabFlag: 'Y',
                        filtersRaw
                    }
                },
                searchFields: null,
                sortOptions: null,
                filterBar: {},
            });
            return {
                priceList,
            }
        },
        props: {
            isDealer: {
                type: Boolean,
                default: false
            },
            acctId: {
                type: String,
                default: '',
                required: true
            }
        },
        methods: {
            goHistoryPrice () {
                this.$nav.push('/pages/terminal/terminal/history-price-page', {
                    acctId: this.acctId
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../../styles/list-card";
    /*deep*/.link-sticky-top:before {
        box-shadow: none!important;
    }
    .dealer-price {
        font-family: PingFangSC-Regular,serif;
        /*deep*/.link-auto-list-top-bar {
                    border-bottom: none;
                }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-item {
                    padding: 0;
                }
        /*deep*/.link-item-active {
                    background: #ffffff;
                }
        /*deep*/.link-icon {
                    font-size: 28px;
                }
        /*deep*/.link-input-text-align-left {
                    text-align: right;
                }
        .price-list-rows {
            border-radius: 16px;
            margin: auto auto 24px auto;
            width: 702px;
            .media-list {
                @include media-list();
                font-size: 28px;
                .top-rows {
                    padding-left: 24px;
                    padding-right: 24px;
                    padding-top: 24px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    .date-name {
                        color: #262626;
                        font-size: 24px;
                    }
                    .store-level {
                        font-size: 24px;
                        color: #8C8C8C;
                    }
                }
                .store-text {
                    .content-middle {
                        padding: 24px;
                        margin: 0;
                    }
                }
                .content-price {
                    padding: 24px;
                    background: rgba(255,90,90,0.05);
                    @include flex-center-center;
                    @include space-around;
                    .parting-line {
                        width: 2px;
                        height: 64px;
                        background-image: linear-gradient(180deg, rgba(191,191,191,0.00) 0%, #BFBFBF 51%, rgba(191,191,191,0.00) 100%);
                    }
                    .price-item {
                        @include flex-center-center;
                        @include direction-column;
                        .price-type {
                            color: #8C8C8C;
                            font-size: 24px;
                        }
                        .price {
                            color: #FF5A5A;
                        }
                    }
                }
                .store-input {
                    padding: 24px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    .store-input-rows {
                        @include flex-start-center();
                        .label {
                            color: #8C8C8C;
                        }
                        .value {
                            padding-left: 8px;
                            color: #000000;
                        }
                    }
                }
            }
        }
    }
</style>
