<template>
  <view class="dealer-bc-grabPlate">
    <view class="bc-grabPlate-view">
      <view class="bc-grabPlate-view__container" v-if="dataList.prodList.length">
        <lnk-taps :taps="dataList.prodList" v-model="actionObj.dataBoardActive"
                  @switchTab="switchTab" padding
                  class="bc-grabPlate-view__taps"></lnk-taps>
      </view>
      <view class="bc-grabPlate-view__content" v-if="dataList.prodList.length">
        <ActivityGrade :acctData="acctData" :dataBoardActive="actionObj.dataBoardActive"/>
        <TurntableRevenue :acctData="acctData" :dataBoardActive="actionObj.dataBoardActive" />
        <BottleOpenSituation :acctData="acctData" :dataBoardActive="actionObj.dataBoardActive"/>
      </view>
    </view>
  </view>
</template>
<script>
import LnkTaps from "../../../core/lnk-taps/lnk-taps";
import ActivityGrade from './ActivityGrade.vue';
import TurntableRevenue from './TurntableRevenue.vue';
import BottleOpenSituation from './BottleOpenSituation.vue';

export default {
  name: 'dealer-bc-grab-plate',
  components: {
    LnkTaps,
    ActivityGrade,
    TurntableRevenue,
    BottleOpenSituation
  },
  data() {
    return {
      dataList: {
        prodList: []
      },
      actionObj: {
        dataBoardActive: {}
      },
      defaultObj: {
        acctId: this.acctData.id,
        attr1: 'QWQuery'
      }
    }
  },
  props: {
    acctData: {
        type: Object,
        default: () => ({})
    },
  },

  created() {
    this.getProdList();
  },

  methods: {
    async transLov(item) {
      const res = await this.$lov.getLovByType('PROD_ITEMS');
      return res && res.length ? res.filter((types) => types.val === item)[0].name : ''
    },
    async getProdList() {
      const { acctId, attr1 } = this.defaultObj;
      try {
        const res = await this.$http.post('/exportloyalty/loyalty/condition/queryMpHomeCondition', {
          acctId: acctId,
          attr1: attr1
        }, {autoHandleError: false});
        if (res && res.rows && res.rows.length) {
          const { rows } = res;
          const activeProdList = rows.filter(item => item.activeFlag === 'Y');
          // 使用 map 创建一个 Promise 数组
          const promises = activeProdList.map(({ id, productItems, programId }) => {
            return this.transLov(productItems).then(translatedName => ({
              name: translatedName,
              val: productItems,
              seq: id,
              programId: programId
            }));
          });

          // 等待所有 Promise 完成
          this.dataList.prodList = await Promise.all(promises);

          // 设置默认选中的项（如果有）
          this.actionObj.dataBoardActive = this.dataList.prodList[0] || {};
        }
      } catch (error) {
        console.log(error)
      }
    },
    switchTab(active, index) {
      this.actionObj.dataBoardActive = active;
    },
  }
}
</script>
<style lang="scss">
.dealer-bc-grabPlate{
  &-view {
    &__taps {
      .lnk-tabs {
        top: unset !important;
        position: relative !important;
          z-index: 1 !important;
      }
    }
    &__content {
      &--title {
        font-family: PingFangSC-Medium;
        font-size: 30px;
        color: #333333;
        font-weight: 600;
        padding: 12px;
      }
    }
  }
}
</style>
