<!--
终端年度开瓶等级
<AUTHOR>
@date 2024/10/10
-->
<template>
    <view class="dealer-customer-year-level">
        <view class="title">终端年度开瓶等级</view>
        <!-- 可选品项 -->
        <lnk-taps v-model="tapActive"
                  v-if="tapOption.length"
                  padding
                  :taps="tapOption"
                  @switchTab="onTap"></lnk-taps>
        <!-- 终端年度开瓶等级 -->
        <view class="level-content" v-if="acctLevelShow">
                  <view class='show-box'>
                      <view class="show-num">{{openNum || 0}}</view>
                      <view>年度开瓶数量</view>
                  </view>
                  <view class='show-box'>
                      <view class='show-num'>{{level ||'暂无等级'}}</view>
                      <view> 年度达成等级  </view>
                  </view>
         </view>
        <!-- 暂无数据 -->
        <view class="no-data" v-else>当前客户暂无开瓶配置，暂无数据</view>
    </view>
</template>

<script>
    import LnkTaps from '../../../core/lnk-taps/lnk-taps.vue';

    export default {
        name: 'dealer-customer-acct-level',
        props: {
            acctData: {
                type: Object,
                default: () => ({})
            },
        },
        components: {LnkTaps},
        data() {
            return {
                // 可选品项
                tapOption: [],
                // 当前选中品项
                tapActive: {},
                // 是否展示卡片
                acctLevelShow: false,
                // 客户等级卡片数据
                acctLevelInfo: {},
                openNum: 0,
                level: null,

                // 状态
                status: {
                    WaitReceive: {
                        value: '可领取',
                        color: '#273D83'
                    },
                    Receive: {
                        value: '已领取',
                        color: '#666666'
                    },
                    Achieved: {
                        value: '已激活',
                        color: '#273D83'
                    },
                    NotAchieved: {
                        value: '未激活',
                        color: '#BDBDBD'
                    }
                },
            }
        },
        directives: {
            color: {
                inserted(el, binding) {
                    el.style.color = binding.value
                }
            }
        },
        async created() {
            await this.queryPort();
        },
        methods: {
            /**
             * 获取客户等级
             * <AUTHOR>
             * @date   2024/5/22 16:00
             */
            async queryAcctLevel() {
                try {
                    const {success, result} = await this.$http.post('/exportterminal/link/openRuleScanLevel/getCurrentYearLevelInfoQW', {
                        acctId: this.acctData.id,
                        conditionId: this.tapActive.id
                    },{
                        autoHandleError: false,
                        handleFailed: (response) => {
                            console.log(response)
                        },
                    });
                    if (success&&result.length) {
                        this.acctLevelShow = true
                        this.openNum = result[0].openScanNum || 0.00
                        this.level =  result[0].yearAcctLevel
                    }else{
                        this.acctLevelShow = false
                    }
                } catch (e) {
                    console.log('获取客户等级失败' + e);
                }
            },
            /**
             * 获取品项及会员id
             * <AUTHOR>
             * @date   2024/5/22 15:05
             */
            async queryPort() {
                try {
                    const {success, rows} = await this.$http.post('action/link/terminalTag/queryHyItemPage', {
                        accntId: this.acctData.id,
                        companyId: this.acctData.companyId
                    }, {
                        autoHandleError: false,
                        handleFailed: (response) => {
                            console.log(response);
                            this.$utils.hideLoading();
                        }
                    });
                    if (success) {
                        for (const item of rows) {
                            let name = await this.$lov.getNameByTypeAndVal('PROD_ITEMS', item.productItem);
                            this.$set(item, 'name', name);
                            this.$set(item, 'val', item.productItem);
                            this.$set(item, 'seq', item.id);
                        }
                        this.tapOption = rows;
                        this.tapActive = rows[0];
                        if (rows.length) {
                            await this.queryAcctLevel();
                        }
                    }
                } catch (e) {
                    console.log('获取品项错误：' + JSON.stringify(e.result));
                }
            },
            /**
             * 切换品项
             * <AUTHOR>
             * @date   2024/5/22 16:51
             */
            onTap() {
                this.queryAcctLevel();
            }
        }
    }
</script>

<style lang="scss">
    .dealer-customer-year-level {
        margin: 0 14px 20px 14px;

        .lnk-tabs-container {
            margin-bottom: 24px;

            .lnk-tabs {
                position: static;
                border: none;
                z-index: 1 !important;

                .active {
                    color: #6051F6;
                    font-weight: bold;
                }

                .line {
                    background-color: #6051F6 !important;
                }
            }
        }

        .level-content {
            display: flex;
            justify-content: space-between;
            .show-box{
                width: 50%;
                text-align: center;
                font-size: 22px;
                color: #666666;
            }
            .show-num{
                font-size: 26px;
                color: #000000;
            }
        }

        .no-data {
            margin-top: 20px;
            border-radius: 12px;
            background: #F8F8F8;
            padding: 36px 0;
            text-align: center;
            color: #666;
            font-size: 24px;
        }
    }
</style>
