<template>
    <link-page class="dealer-prize-inventory">
        <link-auto-list :option="prizeInventory" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="perform-case-list-item">
                    <view slot="note">
                        <view class="media-list">
                            <view class="code">{{data.prizeCode}}</view>
                            <view class="num">
                                <text class="label">库存</text>
                                <text class="val">{{data.acctList[0].inventoryNum}}</text>
                            </view>
                        </view>
                        <view class="content-middle">
                            <view class="name">{{data.prizeName}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: "dealer-prize-inventory",
    data () {
        const prizeInventory = new this.AutoList(this, {
            module: 'action/link/terminalMember',
            url: {
                queryByExamplePage: 'action/link/terminalMember/queryWriteOffPrize'
            },
            loadOnStart: true,
            param: {
                acctCodeList: this.acctCodeList
            },
            hooks: {
                beforeLoad(option) {
                    delete option.param.page
                    delete option.param.rows
                    delete option.param.sort
                    delete option.param.order
                    delete option.param.filtersRaw
                }
            }
        })
        return {
            prizeInventory
        }
    },
    props: {
        acctCodeList: {
            type: Array,
            default: '',
            required: true
        }
    }
}
</script>

<style lang="scss">
.dealer-prize-inventory {
    background-color: #F2F2F2;
    font-family: PingFangSC-Regular,serif;
    .perform-case-list-item {
        background: #FFFFFF;
        margin: 24px;
        border-radius: 16px;
    }
    .media-list {
        display: flex;
        justify-content: space-between;
        height: 46px;
        .code {
            background: #A6B4C7;
            border-radius: 8px;
            font-size: 28px;
            color: #FFFFFF;
            letter-spacing: 0;
            line-height: 46px;
            padding: 2px 8px;
        }
        .num {
            line-height: 46px;
            .val {
                color: black;
            }
        }
    }
    .content-middle{
        width: 100%;
        @include flex-start-center;
        @include space-between;
        padding: 24px 0 16px 0;
        .name{
            font-family: PingFangSC-Semibold,serif;
            font-size: 32px;
            color: #262626;
            letter-spacing: 0;
            line-height: 32px;
        }
    }
}
</style>
