<!--
@created<PERSON><PERSON>  yangying
@date  2023/05/12
@description 协议详情
-->
<template>
    <view class="dealer-agree-item">
        <item :key="index" :data="data" :arrow="false" class="media-list-rows" @tap="goDetail(data)">
            <view slot="note" class="media-list">
                <image class="media-list-logo" :src="data.storeUrl" lazy-load="true"></image>
                <view class="store-content">
                    <view class="row-item">
                        <view class="num-name">
                            <text class="store-title" v-if="data.agrNumber">{{data.agrNumber}}</text>
                            <text class="terminal-type" v-if="data.agrNumber && data.agrName">—</text>
                            <text class="store-title" v-if="['display', 'approach', 'Cabinet', 'storeSign'].includes(data.agrType)">
                                {{data.agrName}}
                            </text>
                        </view>
                        <!-- 241226迭代：取消有效性展示 -->
                        <!-- <view class="status-view" :style="{background: matchTypeColor[data.isEffective]}" v-if="data.agrType==='display'">
                            <view class="status" v-if="data.isEffective === 'Y'">有效</view>
                            <view class="status" v-else>无效</view>
                        </view> -->
                    </view>
                    <view class="row-item">
                        <view class="label-wrap">
                            <!-- 协议类型 -->
                            <text class="label-item">{{data.agrType | lov('AGR_TYPE')}}</text>
                            <!-- 协议政策类型 -->
                            <text class="label-item" v-if="source === 'displayCheck' && data.displayType">
                                {{data.displayType | lov('EXHIBITION_TYPE')}}
                            </text>
                            <!-- 协议检查状态 -->
                            <text class="label-item" v-if="source === 'displayCheck' && data.examineStatus">
                                {{data.examineStatus | lov('EXAMINE_STATUS')}}
                            </text>
                            <!-- 协议状态 -->
                            <text class="label-item red-item">{{data.agrStatus | lov('AGR_STATUS')}}</text>
                            <!-- 是否必检 -->
                            <text class="label-item red-item" v-if="source === 'displayCheck' && data.mustCheckFlag === 'Y'">销售公司必检</text>
                            <text class="label-item red-item" v-if="source === 'displayCheck' && data.brandMustCheckFlag === 'Y'">品牌公司必检</text>
                        </view>
                    </view>
                    <view class="row-item">
                        <view class="item-left">
                            <text class="iconfont icon-hexinzhongduan"></text>
                            <text class="store-title" v-if="data.agrType === 'storeSign'">{{data.signBoard}}</text>
                            <text class="store-title" v-else>{{data.accntName}}</text>
                        </view>
                    </view>
                    <view class="row-item">
                        <view class="item-left">
                            <text class="iconfont icon-index"></text>
                            <text>{{data.startTime | date('YYYY/MM/DD')}}</text>
                            <text class="terminal-type">至</text>
                            <text>{{data.endTime | date('YYYY/MM/DD')}}</text>
                        </view>
                        <view class="item-right">
                            <view class="store-level">{{data.createdName}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </item>
    </view>
</template>

<script>
export default {
    name: 'dealer-agree-item',
    props: {
        data: Object,
        index: Number,
        source: String,
        readonlyFlag: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            matchTypeColor: { // 有效性状态显示颜色
                Y: '#2F68F7', // 有效-蓝色
                N: '#FF0000', // 无效-红色
            },
        }
    },
    methods: {
        /**
         * 跳转详情
         * <AUTHOR>
         * @date	2023/5/8 16:56
         */
        goDetail (data) {
            if (this.source === 'newActivity') {
                return;
            }
            const that = this
            // 不可编辑
            if (this.readonlyFlag || !['Revoked', 'Draft'].includes(data.agrStatus) || data.dataSource === 'lzljClub') {
                this.$nav.push('/pages/terminal/terminal/history-agreement-detail-page', {
                    data,
                    source: that.source,
                    readonlyFlag: true
                });
            } else {
                // 已撤回和草稿状态可编辑
                if (this.source === 'protocolList') {
                    this.$nav.push('/pages/terminal2/protocol-list/protocol-edit-page', {
                        data,
                        backEdit: data.agrStatus,
                        source: 'activity',
                        edit: true
                    });
                } else {
                    this.$nav.push('/pages/terminal2/protocol/protocol-edit-page.vue', {
                        data,
                        backEdit: data.agrStatus,
                        source: 'terminal'
                    });
                }
            }
        }
    }
}
</script>

<style lang="scss">
@import '../../../../styles/list-card';
.dealer-agree-item {
    .media-list-rows {
        background: #FFFFFF;
        border-radius: 24px;
        padding: 36px 24px;
    }

    .media-list {
        @include media-list();

        .media-list-logo {
            margin-right: 16px;
        }

        .store-content {
            .row-item {
                display: flex;
                justify-content: space-between;
                margin-bottom: 20px;
                color: #000;
                font-size: 28px;

                .num-name {
                    flex: 1;
                    overflow: hidden;
                    font-weight: 600;
                    margin-right: 22px;
                    white-space: nowrap;
                }

                .iconfont {
                    font-size: 30px;
                    color: #000;
                    margin-right: 8px;
                }

                .terminal-type {
                    margin: 0 8px;
                }
                .tag-item {
                    padding: 0 10px;
                    font-size: 24px;
                    font-weight: 400;
                    color: #3F66EF;
                    line-height: 36px;
                    background: #F0F5FF;
                    border-radius: 4px;
                    text-align: center;
                    margin-right: 16px;
                }

                .label-wrap {
                    display: flex;
                    flex-wrap: wrap;
                }

                .label-item {
                    padding: 0 10px;
                    font-size: 24px;
                    font-weight: 400;
                    color: #3F66EF;
                    line-height: 36px;
                    background: #F0F5FF;
                    border-radius: 4px;
                    text-align: center;
                    margin-right: 16px;
                    margin-top: 10px;
                }

                .red-item {
                    color: #FF461E;
                    background: #FFF1EB;
                }
            }

            .status-view {
                border-radius: 4px;
                transform: skewX(-30deg);
                margin-right: 10px;

                .status {
                    font-size: 22px;
                    color: #FFFFFF;
                    letter-spacing: 2px;
                    text-align: center;
                    padding: 4px 8px;
                    transform: skewX(30deg);
                    max-height: 38px;
                }
            }
        }
    }
}
</style>
