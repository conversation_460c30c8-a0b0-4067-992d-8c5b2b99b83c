<template>
    <view class="status-button-component">
        <view class= "status-button" :class="type" @tap="tap">
            <view class="label"><slot>{{label}}</slot></view>
        </view>
  </view>
</template>

<script>
  export default {
    name: "status-button",
    props: {
      label: {
        type: String,
        default: ''
      },
      type: {
        type: String,
        default: 'normal'
      }
    },
    methods: {
      /**
       *  @description: 点击方法
       *  @author: 马晓娟
       *  @date: 2020/8/31 19:29
       */
     tap () {
       this.$emit('tap');
     }
    }
  }
</script>

<style lang="scss">
.status-button-component{
    .status-button {
        min-width: 60px;
        color: white;
        letter-spacing: 0;
        text-align: right;
        text-decoration: none;
        height: 18px;
        transform: skew(-30deg, 0);
        display: flex;
        justify-content: center;
        align-items: center;
        border-radius: 6px;
        padding: 10px 16px;
        margin-right: 10px;
        .label {
            transform: skew(30deg, 0);
            font-size: 20px;
        }
    }
    .normal, .revoke, .new, .end, .approved, .AllEfficient, .New, .Pass{
        background: #2F69F8;
        box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
    }
    .warning, .running, .submitted, .Terminated, .Reviewing, .preapprove{
        background: #FF9C5A;
        box-shadow: 0 3px 4px 0 rgba(255,156,90,0.35);
    }
    .invalid, .error, .rejected, .Reject{
        background: #FF5A5A;
        box-shadow: 0 3px 4px 0 rgba(255,90,90,0.35);
    }
    .green,.PartEfficient{
        background: #2EB3C2;
        box-shadow: 0 3px 4px 0 rgba(46,179,194,0.35);
    }
    .other{
        background: #2FAEF8;
        box-shadow: 0 3px 4px 0 rgba(47,174,248,0.35);
    }
    .yellow{
        background: #FFB701;
        box-shadow: 0 3px 4px 0 rgba(255,183,1,0.35);
    }
    .gray, .Closed{
        background: #A6B4C7;
    }
}
</style>
