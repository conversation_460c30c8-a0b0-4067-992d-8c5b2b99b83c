<!--
@created<PERSON><PERSON>  yangying
@date  2023/04/26
@description 陈列协议扫码详情列表
-->
<template>
    <view class="dealer-display-scan-code">
        <link-auto-list :option="scanRecordOption">
            <template v-slot="{data, index}">
                <!-- 不可删除 -->
                <display-scan-code-item v-if="disabled" :data="data" :index="index"/>
                <!-- 可删除 -->
                <link-swipe-action v-else>
                    <link-swipe-option slot="option" @tap="deleteItem(data, index)">删除</link-swipe-option>
                    <display-scan-code-item :data="data" :index="index"/>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
import DisplayScanCodeItem from './display-scan-code-item'
export default {
    name: 'dealer-display-scan-code',
    props: {
        disabled: Boolean,
        headId: String,
        source: String
    },
    components: {DisplayScanCodeItem},
    data() {
        const scanRecordOption = new this.AutoList(this, {
            module: 'action/link/headquarterScanRecord',
            param: {
                attr2: 'levelBox', // 统计标识
                filtersRaw: [
                    {id: 'headId', property: 'headId', value: this.headId}
                ]
            }
        })
        return {
            scanRecordOption,
            displayScan: null, // 陈列总数
        };
    },
    methods: {
        /**
         * 删除扫码记录
         * <AUTHOR>
         * @date	2023/4/26 15:45
         */
        async deleteItem(data, index) {
            this.scanRecordOption.list.splice(index, 1);
            const url = this.source === 'display'
                ? 'action/link/headquarterScanRecord/deleteVisitScanById'
                : 'action/link/headquarterScanRecord/deleteAgreeScanById';
            try {
                const {success, result} = await this.$http.post(url, {
                    id: data.id
                });
                if (success) {
                    this.$emit('updateDisScan');
                    this.$message.success('删除成功！');
                }
            } catch (e) {
                this.$showError('删除错误！');
            }
        }
    }
}
</script>

<style lang="scss">
.dealer-display-scan-code {
    margin-top: 24px;

    .link-auto-list-wrapper {
        margin: 0 24px;
    }
    .link-swipe-action {
        width: 100%;
        border-radius: 24px;
    }
    .link-auto-list-no-more {
        display: none !important;
    }
}
</style>
