<template>
  <view class="line-title">
    <view class="line">
      <view class="line-top"></view>
      <view class="line-bottom"></view>
    </view>
    <view class="title-info">
      <view class="stair-title">{{title}}</view>
      <view class="edit" @tap="tap()" v-if="typeof buttonName === 'string' && buttonName">{{buttonName}}</view>
      <view v-if="typeof buttonName === 'object'"  class="edit">
          <text class="edit-text" v-for="(item, index) in buttonName" @tap="tap(item.val)" :key="index">{{item.name}}</text>
      </view>
      <view class="tips" v-if="tips">
        <link-icon icon="icon-warning-circle"/>
        <view>{{tips}}</view>
      </view>
      <view class="slot-info">
          <slot></slot>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    name: "line-title",
    props: {
      title: {
        type: String,
        default: ''
      },
      buttonName: {
        type: [String, Array],
        default: ''
      },
      tips: {
        type: String,
        default: ''
      }
    },
    methods: {
      /**
       *  @description: 点击方法
       *  @author: 马晓娟
       *  @date: 2020/9/8 16:10
       */
      tap (val) {
        this.$emit('tap', val)
      }
    }

  }
</script>

<style lang="scss">
  .line-title{
    margin-left: 24px;
    padding: 24px 24px 0 0;
    @include flex-start-center;

    .line {
      .line-top {
        width: 8px;
        height: 16px;
        background: #3FE0E2;
      }

      .line-bottom {
        width: 8px;
        height: 16px;
        background: #2F69F8;
      }
    }
    .title-info{
      display: flex;
      justify-content: space-between;
      width: 100%;
      align-items: center;
    }
    .stair-title {
      margin-left: 16px;
      font-family: PingFangSC-Semibold, serif;
      font-size: 32px;
      color: #262626;
      letter-spacing: 1px;
      line-height: 32px;
      white-space: nowrap;
      min-width: 40%;
      width: auto;
    }
    .edit {
      font-family: PingFangSC-Regular;
      font-size: 28px;
      color: #2F69F8;
      letter-spacing: 0;
      line-height: 80px;
      text-align: right;
      min-width: 40%;
      height: 80px;
      white-space: nowrap;
      width: auto;
        .edit-text{
            padding-left: 10px;
        }
    }
    .tips {
        @include flex-end-center;
        flex: 1;
        font-size: 24px;
        color: #555;
        margin-right: 10px;

        .icon-warning-circle {
            color: #bbb;
        }
    }
  }
</style>
