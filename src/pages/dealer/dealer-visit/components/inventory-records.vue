<!--
库存盘点-盘点记录
<AUTHOR> 2024-9-3
-->
<template>
    <link-page class="dealer-inventory-records">
        <link-auto-list :option="inventoryList" :searchInputBinding="{props:{placeholder: '盘点编号'}}">
            <link-filter-group slot="filterGroup">
                <link-filter-item label="创建时间" :param="{sort:{field:'created',desc:true}}"/>
                <link-filter-item label="最新更新" :param="{sort:{field:'lastUpdated',desc:true}}"/>
            </link-filter-group>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="inventory-list" @tap="goToDetail(data)">
                    <view slot="note" class="inventory-item">
                        <view class="row-1">
                            <view class="row-1__left">
                                <view class="top-content">{{data.brandName}}</view>
                            </view>
                            <view class="row-1__right" >
                                <status-button :type="getStatusClassColor(data.approvalStatus)" :label="data.approvalStatus| lov('QUOTA_APPROVAL_STATUS')"></status-button>
                            </view>
                        </view>
                        <view class="inventory-content">
                            <view class="inventory-type">盘点编号：</view>
                            <view class="inventory-name">{{data.tradeCode}}</view>
                        </view>
                        <view class="inventory-content">
                            <view class="inventory-type">盘点周期：</view>
                            <view class="inventory-name">{{data.description}}</view>
                        </view>
                        <view class="inventory-content">
                            <view class="inventory-type">盘点时间：</view>
                            <view class="inventory-name">{{data.created}}</view>
                        </view>
                        <view class="inventory-content">
                            <view class="inventory-type">录入来源：</view>
                            <view class="inventory-name">{{data.dataSource | lov('INVENTORY_RECORD_SOURCE')}}</view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>

    </link-page>
</template>

<script>
import StatusButton from "./status-button";

export default {
    name: "dealer-inventory-records",
    components: {StatusButton},
    props: {
        clientDetails:{
            type: Object,
            default: {}
        }
    },
    data () {
        const inventoryList = new this.AutoList(this, {
            module: 'action/link/headquarterActivity',
            url: {
                queryByExamplePage: 'dealer/link/checkStock/queryDealerCheckStockHistory',
            },
            param: {
                dealerCode: this.clientDetails.acctCode
            },
            searchFields: ['tradeCode'],
            sortField: 'created',
            sortDesc: 'desc',
            sortOptions: null,
            filterOption:[
                {label: '盘点公司', field: 'brandName', type: 'text'},
                {label: '盘点时间', field: 'created', type: 'date'},
                {label: '审批状态', field: 'approvalStatus', type: 'lov',lov: 'QUOTA_APPROVAL_STATUS'}
            ]
        });

        return {
            showFlag:false, // 控制弹窗显示
            brandList:[], //获取品牌信息
            chooseItem: {}, //选择的品牌信息
            inventoryList,
        }
    },
    methods: {
        getStatusClassColor (status) {
            let flag = 'normal'
            if(status==='AUDITSUCC'){
                flag = 'normal'
            } else if(status==='AUDITING') {
                flag = 'running'
            } else if(status==='AUDITFAIL'){
                flag = 'rejected'
            }
            return flag
        },
        /**
         * 复制活动编码
         *  <AUTHOR>
         *  @date 2023-03-28
         */
        copyActCode(text) {
            wx.setClipboardData({data: text});
        },
        reFresh () {
            this.inventoryList.methods.reload();
        },
        goToDetail(data) {
            data.dealerCode = this.clientDetails.acctCode
            data.dealerName = this.clientDetails.acctName
            data.accntId = this.clientDetails.id
            this.$nav.push('/pages/qdhy/inventory/inventory-detail-page.vue', {data: data,dealerType:this.clientDetails.acctType, accntId: data.accntId})
        }
    },
}
</script>

<style lang="scss">
.dealer-inventory-records {
    .inventory-list{
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
        .inventory-item {
            border-radius: 10px;
            background: white;
            .row-1 {
                display: flex;
                justify-content: space-between;

                &__left {
                    text-align: left;

                    .top-content {
                        font-size: 28px;
                        background: #2f69f8;;
                        padding: 8px 16px;
                        color: white;
                        border-radius: 8px;
                    }
                }

                &__right {
                    width: 30%;
                    display: flex;
                    justify-content: flex-end;
                }
            }
            .inventory-top {
                display: flex;
                .top-content {
                    font-size: 28px;
                    background: rgb(182,194,209);
                    padding: 8px 16px;
                    &:not(:first-child) {
                        margin: 0 24px 0 24px;
                    }
                }
            }
            .inventory-content {
                @include flex;
                align-items: center;
                margin-top: 20px;
                width: calc(100% - 24px);
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                .inventory-type {
                    color: #8C8C8C;
                    //min-width: 50px;
                }
                .inventory-name {
                    font-size: 24px;
                    color: #000000;
                    letter-spacing: 0;
                    padding-left: 8px;
                    width: calc(100% - 50px);
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                }
            }
        }
    }
}
</style>
