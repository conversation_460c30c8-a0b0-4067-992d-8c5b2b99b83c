<template>
    <view class="dealer-order">
        <link-sticky top :duration="durationOrder + 90">
            <view class="lnk-tabs-orders">
                <view class="lnk-tabs-box" v-if="orderOptions.length">
                    <view class="lnk-tabs-items" :class="{'active': tab.seq === orderType.seq}"
                      v-for="(tab, index) in orderOptions" :key="index" @tap="changeOrderActive(tab, index)">
                        <view class="label-name-bg">
                            <view :class="tab.seq === orderType.seq ? 'label-name-on' : 'label-name-off'">{{tab.name}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </link-sticky>
        <link-auto-list :option="orderData" class="order-list" hideCreateButton>
            <template slot-scope="{data,index}">
                <link-card>
                    <view @tap="goOrderItem(data)">
                        <view class="top-all">
                            <view class="right-tag" :style="data.tagBgColor"></view>
                            <view class="top-container">
                                <view class="code">
                                    <text v-if="orderType.val === 'NcOutStockOrder'">{{data.businessNumber}}</text>
                                    <text v-if="orderType.val === 'NcOutStockOrder' && data.businessNumber && data.businessType"> | </text>
                                    <text v-else>{{data.orderNo}}</text>
                                    <text v-if="orderType.val !== 'NcOutStockOrder' && data.orderNo && data.orderType"> | </text>
                                    <text class="sales-ticket" v-if="orderType.val === 'NcOutStockOrder'">{{data.businessType | lov('OUT_WAREHOUSING_TYPE')}}</text>
                                    <text class="sales-ticket" v-else>{{data.orderType | lov('ORDER_TYPE')}}</text>
                                </view>
                                <view class="item-tag" :style="data.tagColor">
                                    <view class="tag-content" v-if="orderType.val === 'NcOutStockOrder'">
                                        <text v-if="data.billStatus === 'add' || data.billStatus === 'upd'">审批通过</text>
                                    </view>
                                    <view class="tag-content">{{data.orderStatus | lov('EST_ORDER_STATUS')}}</view>
                                </view>
                            </view>
                        </view>
                        <view class="time-buyer">
                            <view class="order-time">
                                <text class="label" v-if="orderType.val === 'NcOutStockOrder'">创建日期 </text>
                                <text class="label" v-else>申请提交时间 </text>
                                <text class="value" v-if="orderType.val === 'NcOutStockOrder'">{{data.businessDate | date('YYYY-MM-DD HH:mm')}}</text>
                                <text class="value" v-else>{{data.orderDate | date('YYYY-MM-DD HH:mm')}}</text>
                            </view>
                            <view class="order-time">
                                <text class="label">制单人  </text>
                                <text class="value" v-if="orderType.val === 'NcOutStockOrder'">{{data.fstName}}</text>
                                <text class="value" v-else>{{data.creatorName}}</text>
                            </view>
                        </view>
                        <view class="order-warehouse"  v-if="orderType.val === 'NcOutStockOrder'">
                            <text class="label">仓库 </text>
                            <text class="value">{{data.warehouse}}</text>
                        </view>
                        <view class="order-bottom">
                            <view class="order-num">
                                <text class="label">总数量 </text>
                                <text class="value" v-if="orderType.val === 'NcOutStockOrder'">{{data.prodTotal}}  <text class="unit">瓶</text></text>
                                <text class="value" v-else-if="['DisplayCashCouponOrder', 'CashCouponOrder', 'RetailerRewardOrder'].includes(orderType.val)">
                                    {{data.pcSum}}件{{data.botSum}}瓶
                                </text>
                                <text class="value" v-else>{{data.orderTotalQty}} <text class="unit" v-if="orderType.val === 'estimateorder'">件</text><text class="unit" v-if="orderType.val === 'SalesOrder'">瓶</text></text>
                            </view>
                            <view class="order-num" v-if="data.orderType === 'SalesOrder'">
                                <text class="label">总金额 </text>
                                <text class="value">{{data.actualOrderAmount}}</text>
                            </view>
                        </view>
                    </view>
                </link-card>
            </template>
        </link-auto-list>
    </view>
</template>

<script>
    export default {
        name: "dealer-order",
        data () {
            // 用户信息
            const userInfo = this.$taro.getStorageSync('token').result;
            // 国窖公司
            const isGuojiao = userInfo.coreOrganizationTile.brandCompanyCode === '5600';
            // 蓉城怀旧公司
            const isRongCheng = userInfo.coreOrganizationTile.brandCompanyCode === '5910';
            const orderData = new this.AutoList(this, {
                module: 'action/link/saleorder',
                loadOnStart: false,
                param: {
                    oauth: 'ALL',
                    sort: 'created',
                    order: 'desc',
                    attr4: 'Y',
                    partOauthFlag: '', //分品项-安全性
                    filtersRaw: [
                        {id: 'orderKind', property: 'orderKind', value: '[TerminalOrder, DistributorOrder]', operator: 'in'}
                    ]
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    afterLoad: (data) => {
                        if(data.rows.length !== 0) {
                            data.rows.forEach(item => {
                                if (this.orderType.val === 'NcOutStockOrder') {
                                    switch (item.billStatus) {
                                        case '签字出库':
                                            this.$set(item, 'tagColor','background: #2EB3C2;box-shadow: 0 3px 4px 0 rgba(46,179,194,0.35);');
                                            this.$set(item, 'tagBgColor','background: #2EB3C2;');
                                            break;
                                        case '已关闭':
                                            this.$set(item, 'tagColor','background: #A1A7AF;box-shadow: 0 3px 4px 0 rgba(191,191,191,0.50);');
                                            this.$set(item, 'tagBgColor','background: #A1A7AF;');
                                            break;
                                        case 'add':
                                        case 'upd':
                                            this.$set(item, 'tagColor','background: #2EB3C2;box-shadow: 0 3px 4px 0 rgba(46,179,194,0.35);');
                                            this.$set(item, 'tagBgColor','background: #2EB3C2;');
                                            break;
                                    }
                                } else {
                                    switch (item.orderStatus) {
                                        case 'Submitted':
                                            this.$set(item, 'tagColor','background: #2F69F8;box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);');
                                            this.$set(item, 'tagBgColor','background: #2F69F8;');
                                            break;
                                        case 'Approved':
                                        case 'AllDelivered':
                                            this.$set(item, 'tagColor','background: #2EB3C2;box-shadow: 0 3px 4px 0 rgba(46,179,194,0.35);');
                                            this.$set(item, 'tagBgColor','background: #2EB3C2;');
                                            break;
                                        case 'Rejected':
                                            this.$set(item, 'tagColor','background: #FF5A5A;box-shadow: 0 3px 4px 0 rgba(255,90,90,0.35);');
                                            this.$set(item, 'tagBgColor','background: #FF5A5A;');
                                            break;
                                        case 'PartDelivered':
                                            this.$set(item, 'tagColor','background: #2FAEF8;box-shadow: 0 3px 4px 0 rgba(47,174,248,0.35);');
                                            this.$set(item, 'tagBgColor','background: #2FAEF8;');
                                            break;
                                        case 'Cancelled':
                                            this.$set(item, 'tagColor','background: #A1A7AF;box-shadow: 0 3px 4px 0 rgba(191,191,191,0.50);');
                                            this.$set(item, 'tagBgColor','background: #A1A7AF;');
                                            break;
                                    }
                                }
                            })
                        }
                    }
                }
            });
            return {
                userInfo,
                isGuojiao,
                orderOptions: [],
                durationOrder: this.$device.isIphoneX ? 166 : 134,
                orderData,
                orderType: {},
                // 是否是分品项公司
                isPartPort: false
            }
        },
        props: {
            isDealer: {
                type: Boolean,
                default: false
            },
            acctType: {
                type: String,
                default: '',
                required: true
            },
            accntIdArr: {
                type: Array,
                default: []
            }
        },
        async created () {
            await this.getOrderTaps()
            this.prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
            this.isPartPort = this.prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
            this.queryOrderRows(this.orderType);
        },
        methods: {
            /**
             * 获取订单tab
             * <AUTHOR>
             * @date    2024/11/4 16:59
             */
            async getOrderTaps() {
                // 订单tab
                let orderOptions = [
                    {name: '预订单', seq: 1, val: 'estimateorder'},
                    {name: 'NC订单', seq: 2, val: 'SalesOrder'},
                    {name: 'NC出库单', seq: 3, val: 'NcOutStockOrder'},
                    {name: '融资预订单', seq: 6, val: 'FinancingOrder'},
                ];
                let rebateOrderTap = await this.$utils.getCfgProperty('REBATE_ORDER_COM');
                rebateOrderTap = JSON.parse(rebateOrderTap);
                let flag = false;
                let orderType = [];
                for (let key in rebateOrderTap) {
                    let tapItem = {};
                    switch (key) {
                        case 'CashCouponOrder':
                            tapItem = {name: '常规现金券预订单', seq: 4, val: 'CashCouponOrder'};
                            break;
                        case 'RewardHOrder':
                            tapItem = {name: '现金券预订单（红60）', seq: 4, val: 'CashCouponOrder'};
                            break;
                        case 'DisplayCashCouponOrder':
                            tapItem = {name: '陈列现金券预订单', seq: 5, val: 'DisplayCashCouponOrder'};
                            break;
                        case 'RetailerRewardOrder':
                            tapItem = {name: '战略零售商激励预订单', seq: 6, val: 'RetailerRewardOrder'};
                            break;
                    }
                    if (this.userInfo.positionType === 'SysAdmin' || rebateOrderTap[key].indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1) {
                        flag = true;
                        key === 'DiscountCouponOrder'
                            ? orderOptions.push({name: '抵扣券预订单', seq: 5, val: 'DiscountCouponOrder'})
                            : orderType.push(tapItem) ;
                    }
                }
                if (flag) orderOptions.push({name: '现金券预订单', seq: 4, val: 'CashCouponOrder'});
                orderOptions.sort((a, b) => a.seq - b.seq);
                this.orderType = orderOptions[0];
                this.orderOptions = orderOptions;
            },
            /**
             * 请求订单数据
             * <AUTHOR>
             * @date 2020-10-26
             * @param param
             */
            queryOrderRows (param = this.orderType) {
                const filtersRaw = [];
                // 特定公司的经销商人员只能看到自己职位创建的数据
                if (this.isDealer) {
                    filtersRaw.push({id: 'postnId', property: 'postnId', value: this.userInfo.postnId});
                }
                if (this.$utils.isNotEmpty(param.val)) {
                    filtersRaw.push({id: 'orderType', property: 'orderType', value: `[${[param.val]}, TravelOrder, DisplayOrder]`, operator: 'in'});
                }
                if (this.$utils.isNotEmpty(this.accntIdArr)) {
                    filtersRaw.push({id: 'acctId', property: 'acctId', value:`[${this.accntIdArr.toString()}]`, operator: 'in'});
                }
                this.orderData.option.param.partOauthFlag  = this.isPartPort ? 'Y' : '';
                this.orderData.option.param.filtersRaw  = [...filtersRaw, this.orderData.option.param.filtersRaw];
                this.orderData.methods.reload();
            },
            /**
             * 订单切换按钮
             * <AUTHOR>
             * @date 2020-09-17
             * @param val 选中的值
             */
            async changeOrderActive(val) {
                if (this.orderData.loading) {
                    return;
                }
                this.orderData.list = []
                const that = this;
                this.orderType = val;
                const filtersRaw = [{id: 'acctId', property: 'acctId', value:`[${that.accntIdArr.toString()}]`, operator: 'in'}];
                // 特定公司的经销商人员只能看到自己职位创建的数据
                if (this.isDealer) {
                    filtersRaw.push({id: 'postnId', property: 'postnId', value: this.userInfo.postnId})
                }
                that.orderData.option.module = 'action/link/saleorder';
                switch (this.orderType.val) {
                    case 'estimateorder':   // 预订单
                        that.orderData.option.param.attr4 = 'Y'
                        that.orderData.option.param.filtersRaw = [
                            ...filtersRaw,
                            {id: 'orderKind', property: 'orderKind', value: '[TerminalOrder, DistributorOrder]', operator: 'in'},
                            {id: 'orderType', property: 'orderType', value: `[${[val.val]}, TravelOrder, DisplayOrder]`, operator: 'in'},
                        ];
                        that.orderData.option.param.partOauthFlag = this.isPartPort ? 'Y' : ''; //分品项-安全性
                        break;
                    case 'SalesOrder':   // NC订单
                        that.orderData.option.param.filtersRaw = [
                            ...filtersRaw,
                            {id: 'orderKind', property: 'orderKind', value: '[TerminalOrder, DistributorOrder]', operator: 'in'},
                            {id: 'orderType', property: 'orderType', value: val.val, operator: '='},
                            {id: 'sourceSystem', property: 'sourceSystem', value: 'NC', operator: '='},
                        ];
                        that.orderData.option.param.partOauthFlag = this.isPartPort ? 'Y' : ''; //分品项-安全性
                        break;
                    case 'NcOutStockOrder':  // nc出库单
                        that.orderData.option.module = 'action/link/ordeInoutHead';
                        that.orderData.option.param.filtersRaw = [
                            ...filtersRaw,
                            {id: 'businessType', property: 'businessType', value: '[Salesout, Salesin]', operator: 'in'},
                            {id: 'objectType', property: 'objectType', value: 'DealerOrder', operator: '='},
                        ];
                        that.orderData.option.param.partOauthFlag = this.isPartPort ? 'Y' : ''; //分品项-安全性
                        break;
                    case 'CashCouponOrder':   // 现金券预订单
                        that.orderData.option.param.attr4 = 'Y'
                        that.orderData.option.param.filtersRaw = [
                            ...filtersRaw,
                            {id: 'orderKind', property: 'orderKind', value: '[TerminalOrder, DistributorOrder]', operator: 'in'},
                            {id: 'orderType', property: 'orderType', operator: 'in',
                                value: `[${[val.val]}, DisplayCashCouponOrder, RetailerRewardOrder, RewardHOrder]`},
                        ];
                        that.orderData.option.param.partOauthFlag = this.isPartPort ? 'Y' : ''; //分品项-安全性
                        break;
                    // 融资预订单
                    case 'FinancingOrder':
                        that.orderData.option.param.attr4 = 'Y'
                        that.orderData.option.param.filtersRaw = [
                            ...filtersRaw,
                            {id: 'orderKind', property: 'orderKind', value: '[TerminalOrder, DistributorOrder]', operator: 'in'},
                            {id: 'orderType', property: 'orderType', value: val.val, operator: '='},
                        ];
                        break;
                    // 抵扣券预订单
                    case 'DiscountCouponOrder':
                        that.orderData.option.param.attr4 = 'Y';
                        that.orderData.option.param.filtersRaw = [
                            ...filtersRaw,
                            {id: 'orderKind', property: 'orderKind', value: '[DistributorOrder]', operator: 'in'},
                            {id: 'orderType', property: 'orderType', value: val.val, operator: '='},
                        ];
                        that.orderData.option.param.partOauthFlag = this.isPartPort ? 'Y' : ''; //分品项-安全性
                        break;

                }
                await that.orderData.methods.reload();
            },
            /**
             * 跳转订单详情
             * <AUTHOR>
             * @date 2020-10-15
             * @param data
             */
            goOrderItem (data) {
                if (['CashCouponOrder', 'DisplayCashCouponOrder', 'RetailerRewardOrder', 'DiscountCouponOrder'].includes(this.orderType.val)){
                    this.$nav.push('/pages/terminal/order/order-cash-page', {
                        orderId: data.id,
                        pageTitle: `${this.orderType.name}详情`,
                        orderType: this.orderType.val
                    })
                }else if(this.orderType.val === 'FinancingOrder'){
                    this.$nav.push('/pages/terminal2/order/finance-order-detail-page', {
                        orderId: data.id,
                        pageTitle: `${this.orderType.name}详情`,
                        orderType: this.orderType.val
                    })
                }else{
                    this.$nav.push('/pages/terminal/order/order-item-page', {
                        orderId: data.id,
                        pageTitle: `${this.orderType.name}详情`,
                        orderType: this.orderType.val
                    })
                }
            },
        }
    }
</script>

<style lang="scss">
    /*deep*/.link-sticky-top:before {
        box-shadow: none!important;
    }
    .dealer-order {
        margin-top: 24px;
        .lnk-tabs-orders{
            display: flex;
            justify-content: space-between;
            width: 92vw;
            padding: 0 4vw;
            background-color: #f2f2f2;
            font-size: 28px;
            height: 50px;
            overflow: hidden;
            .lnk-tabs-box{
                width: 100%;
                height: 70px;
                display: flex;
                overflow-x: scroll;
                .lnk-tabs-items{
                    display: inline-block;
                    margin: 0 10px;
                    min-width: 36%;
                    .label-name-bg{
                        text-align: center;
                        .label-name-on{
                            border-radius: 8px;
                            padding: 8px 0;
                            background-color: #2F69F8;
                            color: #fff;
                        }
                        .label-name-off{
                            color:#8C8C8C;
                            border-radius: 8px;
                            padding: 8px 0;
                        }
                    }
                }

            }
            .lnk-tabs-items{
                margin: 0 20px;
            }

         }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-auto-list-top-bar {
                    border-bottom: none;
                }
        .order-list {
            .link-card {
                padding:0 24px 0 0;
                margin: 24px;

                /*deep*/.link-card-content {
                    padding: 40px 24px;
                }
                .identify {
                    position: absolute;
                    width: 12px;
                    height: 58px;
                    background: #2F69F8;
                    border-radius: 0 0 0 100px;
                    right: 0;
                    top: 0;
                }
                .top-all {
                    .right-tag {
                        position: absolute;
                        transform: scaleX(-1);
                        background: #2F69F8;
                        border-top-left-radius: 100px;
                        border-bottom-right-radius: 100px;
                        width: 12px;
                        height: 58px;
                        margin-right: 24px;
                        margin-top: -40px;
                        right: 0;
                    }
                    .top-container {
                        position: relative;
                        @include flex-start-center;
                        @include space-between;
                        .code {
                            max-width: 78%;
                            white-space: nowrap;
                            background: #A6B4C7;
                            border-radius: 8px;
                            font-size: 28px;
                            color: #FFFFFF;
                            padding: 6px 12px;
                            .sales-ticket {
                                font-size: 24px;
                            }
                        }
                        .item-tag {
                            min-width: 120px;
                            height: 36px;
                            line-height: 36px;
                            text-align: center;
                            color: #ffffff;
                            background: #2F69F8;
                            box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                            border-radius: 8px;
                            padding-left: 27px;
                            padding-right: 27px;
                            font-size: 20px;
                            margin-right: 8px;
                            transform: skewX(-30deg);
                            .tag-content {
                                transform: skewX(30deg);
                            }
                        }
                    }
                }
                .time-buyer {
                    margin-top: 12px;
                    @include flex-start-center;
                    @include space-between;
                    .order-time {
                        font-size: 28px;
                        .label{
                            color: #8C8C8C;
                        }
                        .value {
                            color: #000000;
                        }
                    }
                }
                .order-title {
                    margin-top: 12px;
                    font-size: 32px;
                    color: #262626;
                }
                .order-warehouse {
                    font-size: 28px;
                    padding-top: 12px;
                    .label {
                        color: #8C8C8C;
                    }
                    .value {
                        color: #000000;
                    }
                }
                .order-bottom {
                    margin-top: 12px;
                    @include flex-start-center;
                    @include space-between;
                    .order-num {
                        font-size: 28px;
                        .label {
                            color: #8C8C8C;
                        }
                        .value {
                            color: #000000;
                        }
                    }
                }
            }
        }
    }
</style>
