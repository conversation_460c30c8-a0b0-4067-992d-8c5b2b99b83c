<template>
    <link-page class="dealer-inventory">
        <view class="edit-text" @tap="goHistoryInventory">查看历史</view>
        <link-auto-list :option="priceList" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="inventory-list-rows">
                    <view slot="note">
                        <view class="media-list">
                            <view class="top-rows">
                                <view class="num-view">
                                    <view class="num">{{data.prodCode}}</view>
                                </view>
                                <view class="store-level">采集时间 <text class="date-name">{{data.created | date('YYYY-MM-DD')}} {{data.createdName}}</text></view>
                            </view>
                        </view>
                        <view class="media-list">
                            <view class="store-text">
                                <view class="content-middle">
                                    <view class="name">{{data.prodName}}</view>
                                </view>
                                <view class="store-input">
                                    <view class="store-input-rows">
                                        <view class="value">{{data.qty1 || 0}}</view>
                                        <view class="label">{{data.saleUnit | lov('PROD_UNIT') || '件'}}</view>
                                        <view class="value">{{data.qty2 || 0}}</view>
                                        <view class="label">{{data.retailUnit | lov('PROD_UNIT') || '瓶'}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-inventory",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            const priceList = new this.AutoList(this, {
                module: 'action/link/visitInventory',
                itemPath: '',
                loadOnStart: false,
                param: () => {
                    const filtersRaw = [{id: 'visitStatus', property: 'visitStatus', value: 'Y', operator: '='}];
                    // 特定公司的经销商人员只能看到自己职位创建的数据
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        pageFlag: true,
                        onlyCountFlag: false,
                        filtersRaw,
                        oauth: 'ALL',
                        sort: 'created',
                        order: 'desc',
                        accntId: this.acctId,
                        tabFlag: 'Y',
                        partOauthFlag: this.isPartPort ? 'Y':'', //分品项-安全性
                    }
                },
                sortOptions: null,
                filterBar: {},
            });
            return {
                userInfo,
                priceList,
                // 是否是分品项公司
                isPartPort: false
            }
        },
        props: {
            isDealer: {
                type: Boolean,
                default: false
            },
            acctId: {
                type: String,
                default: '',
                required: true
            }
        },
        async created() {
            const prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
            this.isPartPort = prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
            this.priceList.methods.reload();
        },
        methods: {
            goHistoryInventory () {
                this.$nav.push('/pages/terminal/terminal/history-inventory-page', {
                    acctId: this.acctId
                })
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../../styles/list-card";
    /*deep*/.link-sticky-top:before {
        box-shadow: none!important;
    }
    .dealer-inventory {
        font-family: PingFangSC-Regular,serif;
        /*deep*/.link-auto-list-top-bar {
                    border-bottom: none;
                }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-item {
                    padding: 24px;
                }
        /*deep*/.link-item-active {
                    background: #ffffff;
                }
        /*deep*/.link-icon {
                    font-size: 28px;
                }
        /*deep*/.link-input-text-align-left {
                    text-align: right;
                }
        .inventory-list-rows {
            border-radius: 16px;
            margin: auto auto 24px auto;
            width: 702px;
            .media-list {
                @include media-list();
                font-size: 28px;
                .top-rows {
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    .date-name {
                        color: #262626;
                        font-size: 24px;
                    }
                    .store-level {
                        font-size: 24px;
                        color: #8C8C8C;
                    }
                }
                .store-input {
                    padding-top: 16px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    .store-input-rows {
                        @include flex-start-center();
                        .label {
                            color: #8C8C8C;
                            padding-left: 8px;
                            padding-right: 8px;
                        }
                        .value {
                            color: #000000;
                        }
                    }
                }
            }
        }
    }
</style>
