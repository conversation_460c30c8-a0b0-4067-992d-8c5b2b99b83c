<!-- bc抢盘展示信息 -->
<template>
  <view class="activity-grade-info">
    <!-- 达成等级 -->
    <view class="activity-grade-info__item">
      <view class="activity-grade-info__item--label">达成等级</view>
      <view class="activity-grade-info__item--num">{{ achievingLevel }}</view>
    </view>
    <!-- 当前进货 -->
    <view class="activity-grade-info__item">
      <view class="activity-grade-info__item--label">当前进货</view>
      <view class="activity-grade-info__item--num">{{ currentInScanNum }}</view>
    </view>
    <!-- 当前开瓶 -->
    <view class="activity-grade-info__item">
      <view class="activity-grade-info__item--label">当前开瓶</view>
      <view class="activity-grade-info__item--num">
        <text>{{ currentOpenScanNum }}</text>
        <text style="font-size:12px">{{ currentOpenScanNum ? '瓶' : '' }}</text>
      </view>
    </view>
    <!-- 已领取奖金 -->
    <view class="activity-grade-info__item">
      <view class="activity-grade-info__item--label">已领取奖励</view>
      <view class="activity-grade-info__item--num">
        <text>{{ receiveReward.value }}</text>
        <text style="font-size:12px" v-if="receiveReward.value !== '-'">{{ receiveReward.name }}</text>
      </view>
    </view>
  </view>
</template>
<script>
export default {
  data() {
    return{}
  },
  props: {
    info: {
      type: Object,
      default: () => ({}),
    },
    acctData: {
      type: Object,
      default: () => ({}),
    }
  },
  computed: {
    // 达成等级
    achievingLevel() {
      const { achieveMemberLevel, achieveAcctLevel, memberDimension } = this.info;
      if (memberDimension && memberDimension === 'Y') {
        return achieveMemberLevel || '-'
      }
      return achieveAcctLevel || '-'
    },
    // 当前进货
    currentInScanNum() {
      const { currentInScanNum } = this.info;
      if (currentInScanNum && currentInScanNum.indexOf('.') >= 0) {
        return currentInScanNum.substring(0, currentInScanNum.indexOf('.') + 3)
      }
      return currentInScanNum || '-'
    },
    // 当前开瓶
    currentOpenScanNum() {
      const { currentOpenScanNum } = this.info;
      return currentOpenScanNum || '-'
    },
    // 已领取奖励
    receiveReward() {
      const { receiveReward } = this.info;
      if (receiveReward && typeof receiveReward === 'object' && Object.keys(receiveReward).length) {
        const { value, name } = receiveReward;
        return {
          value: value || '-',
          name: name || '-'
        }
      }
      return {
        value: '-',
        name: '-'
      }
    }
  }
}
</script>
