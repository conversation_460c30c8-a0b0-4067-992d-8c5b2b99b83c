<!--
 * @Description: 特曲年度配额明细列表
 * @Author: 邓佳柳
 * @Date: 2025-02-11
-->
<template>
	<view class="tequ-year-quota-list">
		<view class="picker-box" v-if="flagParam&&flagParam.showFilter === 'Y'">
			<view class="picker-item">
				<picker @change="yearChange" :value="yearIndex" range-key="name" :range="yearList">
					<view>所属年月: {{ curMonth }}</view>
				</picker>
			</view>
		</view>
		<link-auto-list :option="teQuOptions" hideCreateButton>
			<template slot-scope="{data, index}">
				<item :key="index" :data="data" :arrow="false" class="list-rows"  @tap="goDetails(data)">
					<view slot="note" class="tequ-list">
						<slot>
							<view class="title-row">
								<view class="prod">
									<text class="content-title">{{data.prodPartCode | lov('PROD_BUS_S_CLASS')}}</text>
								</view>
								<view>{{'所属年月:'}} {{ data.month || curMonth }}</view>
							</view>
						</slot>
						<quota-detail-card
							title="计划内配额明细"
							:showField="flagParam && flagParam.detail ? details : tequPLanned"
							:bg-color="colorIn"
							:data="data"
							@click="toApplyList($event, data)" />
					</view>
				</item>
				<!-- 已失效，未认证不可申请-->
				<view class="bottom-btn" v-if="showBtn && accntDetails.acctStage == 'ykf' && accntDetails.acctStatus === 'Y'">
					<link-button class="sure-btn plain" mode="stroke" autoLoading :throttleTimer="10" @tap="edit('AuthorizedQuota', data)">授权配额申请</link-button>
				</view>
			</template>
		</link-auto-list>
	</view>
</template>
<script>
	import quotaDetailCard from '@/pages/terminal2/annual-quota-apply/components/quota-detail-card.vue';
	import {DateService} from 'link-taro-component';

	export default {
		name: 'tequ-year-quota-list',
		components: {quotaDetailCard},
		props: {
			id: {
				//终端id
				type: String,
				require: true
			},
			fiscalYear: {
				//财年
				type: [String, Number],
				require: false
			},
			month:{
				//所属月份
				type: String,
				require: false
			},
			showBtn: {
				//配额申请按钮显示
				type: Boolean,
				require: false,
				default: false
			},
			flagParam: {
				type: Object,
				require: false,
				default: () => {}
			},
			prodPartCode:{
				// 品项
				type: String,
				require: false
			}
		},
		data() {
			const teQuOptions = new this.AutoList(this, {
				url: {
					queryByExamplePage: 'action/link/tequQuotaDetail/queryTequQuotaDetail'
				},
				param: () => {
					return {
						acctId: this.id,
						fiscalYear: this.fiscalYear,
						prodPartCode:this.prodPartCode
					};
				},
				sortOptions: null,
                enableSortField:false,
				filterBar: {},
				hooks:{
					beforeLoad(option){
						if(this.$utils.isNotEmpty(this.fiscalYear)){
							if(this.$utils.isEmpty(this.month)){
								option.param.month = this.curMonth 
							}else{
								option.param.month = this.month 
							}
						}
						
					}
				}
			});
			return {
                teQuOptions,
				colorIn: '#eff9ff',
				colorOut: '#fdf4f5',
				tequPLanned: [
					{label: '年度配额计划', key: 'yearlyPlannedTarget'},
					{label: '月度配额计划', key: 'monthlyPlannedTarget'},
					{
						label: '月度授权配额',
						key: 'monthlyPlannedAuthorized',
						url: '/pages/terminal2/annual-quota-apply/annual-quota-list-page.vue',
						type: 'month',
						quotaType: 'AuthorizedQuota'
					},
					{label: '年度计划内下单', key: 'yearlyPlannedOrder'},
					{label: '月度计划内下单', key: 'monthlyPlannedOrder'},	
					{label: '月度剩余配额', key: 'monthlyPlannedRemaining'},
				],
				details: [
					{label: '年度配额计划', key: 'yearlyPlannedTarget'},
					{
						label: '年度授权配额',
						key: 'yearlyPlannedAuthorized',
						url: '/pages/terminal2/annual-quota-apply/annual-quota-list-page.vue',
						type: 'year',
						quotaType: 'AuthorizedQuota'
					},
					{label: '调整配额', key: 'yearlyPlannedAdjusted'},
					{label: '计划内下单', key: 'yearlyPlannedOrder'},
					{label: '计划内占用', key: 'yearlyPlannedOccupied'},
					{label: '计划内已执行', key: 'yearlyPlannedExecuted'},
					{label: '月度配额计划', key: 'monthlyPlannedTarget'},
					{
						label: '月度授权配额',
						key: 'monthlyPlannedAuthorized',
						url: '/pages/terminal2/annual-quota-apply/annual-quota-list-page.vue',
						type: 'month',
						quotaType: 'AuthorizedQuota'
					},
					{label: '月度调整配额', key: 'monthlyPlannedAdjusted'},
					{label: '月度剩余配额', key: 'monthlyPlannedRemaining'},
					{label: '月度计划内下单', key: 'monthlyPlannedOrder'},
					{label: '月度计划内占用', key: 'monthlyPlannedOccupied'},
					{label: '月度计划内已执行', key: 'monthlyPlannedExecuted'}
				],
				accntDetails: {},
				yearIndex:0,
				yearList:[],
				curMonth:''
			};
		},
		watch: {},
		computed: {},
		created() {
			// 当前终端的客户信息
			if(this.showBtn) { this.getTerInfo() };
			// 初始化月份选项 11月-当前月
			this.calcMinYear()
			// 初始化当前月
			this.curMonth = DateService.format(new Date(), 'YYYYMM');
			if(this.month){
				this.curMonth = this.month
			}
			this.yearIndex = this.yearList.findIndex(item=> item.val == this.curMonth)
		},
		mounted() {},
		methods: {
			calcMinYear() {
				let year = new Date().getFullYear();
				let month = new Date().getMonth() + 1;
				this.yearList = [];
				if(month <= 10){
					this.yearList.push({name: `${year-1}${11}`, val: `${year-1}${11}`});
					this.yearList.push({name: `${year-1}${12}`, val:`${year-1}${12}`});
					for (let i = 1; i <= month; i++) {
						let m = i < 10 ? '0'+i : i
						this.yearList.push({name: `${year}${m}`, val: `${year}${m}`});
					
					}
				}else{
					for (let i = 11; i <= month; i++) {
						this.yearList.push({name: `${year}${m}`, val: `${year}${m}`});
					
					}
				}
				
				
			},
			yearChange(e){
				this.yearIndex = Number(e.detail.value);
				this.curMonth = this.yearList[this.yearIndex].val
				this.teQuOptions.methods.reload()
			},
			/**
			 * @description: 特曲配额-查看申请列表
			 * @author: 邓佳柳
			 * @Date: 2025-02-10
			 * @param {*} data
			 */
			toApplyList(item, data) {
				// 编辑状态不可查看
				if (!!this.flagParam && this.flagParam.isEditFlag) return;
				// 指定不查看标识
				if (!!this.flagParam && this.flagParam.isCheckList === 'N') return;

				let {url, type, quotaType} = item;
				// type year/month
				let filtersRaw = [{id:'fiscalYear',property:'fiscalYear',value: data.fiscalYear, operator:'='},]
                if(type === 'month'){
                    filtersRaw.push({id:'month',property:'month',value: `${data.month || this.curMonth}`, operator:'='},)
                }
				this.$nav.push(url, {
					url: 'action/link/annualQuotaDetails/queryByExamplePage',
                    sortField:'created',
					param: {
						filtersRaw:[
							{id:'acctId',property:'acctId',value: this.id, operator:'='},
                            {id:'prodPartCode',property:'prodPartCode',value: data.prodPartCode, operator:'='},
                       		{id:'quotaType',property:'quotaType',value: quotaType, operator:'='},
							...filtersRaw
						]
					},
					data: {...item, isTequ: true,label:'申请授权配额数量'},
					pageTitle: `${item.label}明细`,
					cellClick: (data2) => {
						// 列表点击查看详情
						this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-tequ-page', {
							data: {...data2},
							isCheckList: 'N' // 详情是否继续查看列表
						});
					}
				});
			},
			/**
			 * @description: 特曲配额申请
			 * @author: 邓佳柳
			 * @Date: 2025-02-11
			 * @param {*} quotaType 申请类型
			 * @param {*} data 当前财年配额统计数据
			 */
			edit(quotaType, data) {
				if (!!this.accntDetails.fourColorLabel && this.accntDetails.fourColorLabel.indexOf('Black') > -1) {
					this.$dialog({
						title: '提示',
						content: '黑标签客户不允许发起年度配额申请！',
						onConfirm: () => {}
					});
					return;
				}
				this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-tequ-page', {
					data: {
						...data,
						quotaType: quotaType,
						orgCode:this.accntDetails.orgCode,
						parentType:'SalesArea'
					},
					operate:'NEW',
					type:'authorized'
				});
			},
			getTerInfo() {
				this.$http
					.post('action/link/accnt/queryById', {
						id: this.id
					})
					.then((data) => {
						if (data.success) {
							this.accntDetails = data.result;
						}
					});
			},
			goDetails(data){
				if(this.flagParam.goDetail === 'Y'){
					if(this.$utils.isEmpty(data.month)){
						data.month = this.curMonth
					}
					this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-tequ-page', {
					data: {
						...data,
					},
					detail:'Y'})
				}
				
			}
		}
	};
</script>
<style lang="scss">
	.tequ-year-quota-list {
		.bottom-btn {
			display: flex;
			justify-content: space-around;
			align-items: center;
			margin: 40px;
			.sure-btn {
				width: 42%;
				border-radius: 40px;
				border: 0;
				&.plain {
					background-color: rgba(47, 105, 247, 0.1);
				}
			}
		}
		.picker-box{
			.picker-item{
				border-radius: 30px;
				border: 2px solid #DDDDDD;
				padding: 6px 24px;
				margin-right: 16px;
				width: 200px;
			}
			
			display: flex;
			justify-content: flex-end;
			align-items: center;
			font-size: 24px;
            color: #212223;
            line-height: 44px;
			margin-bottom: 20px;
		}
		.list-rows {
			background: #ffffff;
			border-radius: 32px;
			padding: 24px;
			width: 702px;
			margin: auto auto 24px auto;
			.tequ-list {
				color: #595959;
				font-size: 26px;
				line-height: 50px;
				.label {
					font-size: 25rpx;
					line-height: 30px;
					padding: 12px 0;
				}
			}
			.prod{
                color: #2F69F8;
				.content-title{
					padding: 1px 20px;
					border: 1px solid #C5D7FD;
					background: rgba(239, 243, 254, .5);
				}
            }
			.title-row {
				@include flex-start-center;
				@include space-between;
				color: #000;
			}
		}
	}
</style>
