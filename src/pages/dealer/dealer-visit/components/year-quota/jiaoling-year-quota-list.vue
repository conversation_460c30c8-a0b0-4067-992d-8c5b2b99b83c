<!--
 * @Description: 窖龄年度配额明细表
 * @Author: 邓佳柳
 * @Date: 2025-03-19
-->
<template>
	<view class="jiaoling-year-quota-list">
		<link-auto-list :option="jiaolingOptions" hideCreateButton>
			<template slot-scope="{data, index}">
				<item :key="index" :data="data" :arrow="false" class="list-rows" @tap.stop="toDetail(data)">
					<view slot="note" class="list-content">
						<slot :data="data">
							<view :class="['item-tag', data.approvalStatus]">
								<view class="tag-content">{{ data.approvalStatus | lov('ANNUAL_QUOTA_STATUS') }}</view>
							</view>
							<view class="title-box">
								<view class="title-content"> 年度配额申请（{{ data.quotaCode }}） </view>
							</view>
							<view class="prod-box">
								<text class="prod-content">{{ data.prodPartCode | lov('PROD_BUS_M_CLASS') }}</text>
							</view>
							<view class="row-content">
								<view>年度配额总额：{{ data.yearlyTotal }}</view>
								<view>所属财年：{{ data.fiscalYear }}</view>
							</view>
						</slot>
						<quota-detail-card title="计划内配额明细" :showField="planned" :bg-color="colorIn" :data="data" @click="toApplyList($event, data)" />
						<quota-detail-card title="计划外配额明细" :showField="unplanned" :bg-color="colorOut" :data="data" @click="toApplyList($event, data)" />
					</view>
				</item>
			</template>
		</link-auto-list>
	</view>
</template>
<script>
	import quotaDetailCard from '@/pages/terminal2/annual-quota-apply/components/quota-detail-card.vue';
	export default {
		name: 'jiaoling-year-quota-list',
		components: {quotaDetailCard},
		props: {
			id: {
				//终端id
				type: String,
				require: true
			},
			fiscalYear: {
				//财年
				type: [String, Number],
				require: false
			},
			prodPartCode: {
				//品项
				type: String,
				require: false
			},
			flagParam: {
				type: Object,
				require: false,
				default: () => {}
			}
		},
		data() {
			const jiaolingOptions = new this.AutoList(this, {
				url: {
					queryByExamplePage: '/action/link/jiaolingQuotaDetail/queryJiaolingQuotaDetail'
				},
				param: () => {
					return {
						acctId: this.id,
						fiscalYear: this.fiscalYear,
						prodPartCode:this.prodPartCode
					};
				},
				sortOptions: null,
				enableSortField: false,
				filterBar: {}
			});
			return {
				jiaolingOptions,
				colorIn: '#eff9ff',
				colorOut: '#fdf4f5',
				planned: [
					{label: '计划内总额', key: 'plannedTotal'},
					{label: '计划内余额', key: 'plannedRemaining'},
					{label: '配额计划', key: 'quotaPlan'},
					{label: '调整配额', key: 'quotaAdjust', url: '/pages/terminal2/annual-quota-apply/annual-quota-list-page.vue', quotaType: 'QuotaAdjustApplication'},
					{label: '未执行扣减', key: 'unexecutedDeduction'},
					{label: '计划内下单', key: 'plannedOrder'},
					{label: '计划内占用', key: 'plannedOccupied'},
					{label: '计划内已执行', key: 'plannedExecuted'}
				],
				unplanned: [
					{label: '计划外配额', key: 'unplannedQuota', url: '/pages/terminal2/annual-quota-apply/annual-quota-list-page.vue', quotaType: 'UnplannedQuotaApplication'},
					{label: '计划外余额', key: 'unplannedRemaining'},
					{label: '计划外下单', key: 'unplannedOrder'},
					{label: '计划外占用', key: 'unplannedOccupied'},
					{label: '计划外已执行', key: 'unplannedExecuted'}
				]
			};
		},
		created() {},
		methods: {
			/**
			 * @description: 窖龄配额-查看申请列表
			 * @author: 邓佳柳
			 * @param {*} data
			 */
			toApplyList(item, data) {
				// 编辑状态不可查看
				if (!!this.flagParam && this.flagParam.isEditFlag) return;
				// 指定不查看标识
				if (!!this.flagParam && this.flagParam.isCheckList === 'N') return;

				let {url, quotaType} = item;

				this.$nav.push(url, {
					url: 'action/link/annualQuotaDetails/queryByExamplePage',
					sortField: 'created',
					param: {
						filtersRaw: [
							{id: 'acctCode', property: 'acctCode', value: data.acctCode, operator: '='},
							{id: 'prodPartCode', property: 'prodPartCode', value: data.prodPartCode, operator: '='},
							{id: 'fiscalYear', property: 'fiscalYear', value: data.fiscalYear, operator: '='},
							{id: 'quotaType', property: 'quotaType', value: quotaType, operator: '='}
						]
					},
					data: {...item, isjiaoling: true},
					pageTitle: `${item.label}申请明细`,
					cellClick: (data2) => {
						// 列表点击查看详情
						let operate = '';
						if (['Rejected', 'Revoke'].includes(data2.approvalStatus)) {
							operate = 'UPDATE';
						}

						this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-jiaoling-page', {
							data: {...data2},
							operate,
							isCheckList: 'N' // 详情是否继续查看列表
						});
					}
				});
			},
			toDetail(data) {
				if (!!this.flagParam && this.flagParam.goDetail === 'Y'){
					let operate = ['Rejected', 'Revoke'].includes(data.approvalStatus) ? 'UPDATE' : '';
					this.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-jiaoling-page', {
						data,
						operate
					});
				}
			}
		}
	};
</script>
<style lang="scss">
	.jiaoling-year-quota-list {
		.list-rows {
			background: #ffffff;
			border-radius: 32px;
			padding: 24px;
			width: 702px;
			margin: auto auto 24px auto;
			.list-content {
				position: relative;
				color: #595959;
				font-size: 26px;
				line-height: 50px;
				.item-tag {
					position: absolute;
					right: 0;
					top: 0;
					width: 80px;
					height: 36px;
					line-height: 36px;
					display: inline-block;
					text-align: center;
					color: #ffffff;
					background: #2f69f8;
					box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
					border-radius: 8px;
					padding-left: 27px;
					padding-right: 27px;
					font-size: 20px;
					margin-right: 8px;
					transform: skewX(30deg);
					flex: 1;
					.tag-content {
						transform: skewX(-30deg);
					}
					&.Submitted {
						background: #2f69f8;
						box-shadow: 0 3px 4px 0 rgba(47, 105, 248, 0.35);
					}
					&.Approved {
						background: #2eb3c2;
						box-shadow: 0 3px 4px 0 rgba(46, 179, 194, 0.35);
					}
					&.Rejected {
						background: #ff5a5a;
						box-shadow: 0 3px 4px 0 rgba(255, 90, 90, 0.35);
					}
					&.Revoke {
						background: #a1a7af;
						box-shadow: 0 3px 4px 0 rgba(191, 191, 191, 0.5);
					}
				}
				.title-box {
					position: relative;

					.title-content {
						font-weight: bold;
					}
				}
				.prod-box {
					color: #2f69f8;
					.prod-content {
						padding: 1px 20px;
						border: 1px solid #c5d7fd;
						background: rgba(239, 243, 254, 0.5);
					}
				}
				.row-content {
					@include flex-start-center;
					@include space-between;
				}
			}
		}
	}
</style>
