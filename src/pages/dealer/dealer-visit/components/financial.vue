<template>
    <view class="dealer-financial">
        <view class="edit-change"  v-if="editFlag && financialOption.editApprovalStatus !== 'Submitted'">
            <view class="multi-acct" @tap="changeAuditStatus">
                一户多开：<text>{{financialOption.multiAcctFlag | lov('IS_FLAG')}} </text>
                <text class="change" v-if="financialOption.multiAcctFlag === 'N' && !disabledNewEdit">变更</text></view>
            <view class="edit" v-if="financialOption.multiAcctFlag === 'Y'&& !disabledNewEdit" @tap="goFinancial">添加子户头</view>
        </view>
        <view class="edit-change" v-else/>
        <view class="card-content" v-for="(item, index) in financialArray" :key="index">
            <view class="invoice-check-status">
                <view class="primary-account" v-if="item.multiAcctMainFlag === 'Y'">主户头</view>
                <view class="child-account" v-if="item.multiAcctMainFlag === 'N' && financialArray.length !== 1 && !approveFlag">子户头{{index}}</view>
                <view class="child-account" v-if="item.multiAcctMainFlag === 'N' && approveFlag">子户头</view>
                <view class="audit-status" v-if="editFlag && financialOption.editApprovalStatus !== 'Submitted'" @tap="item.auditStatus !== 'underReview' && item.acctStatus === 'Y' ? editFinancial(item) : blankFun(item)">
                    <link-icon icon="icon-team team" v-if="item.auditStatus === 'underReview'"/>
                    <link-icon icon="icon-jinggao jinggao" v-if="item.auditStatus === 'failed'"/>
                    <view :class="(item.auditStatus === 'failed' || item.auditStatus === 'failed2') ? 'jinggao' : 'team'">{{item.auditStatus | lov('ACCT_OPEN_AUDIT_STATUS')}}</view>
                    <view class="line" v-if="item.auditStatus !== 'underReview' && item.auditStatus !== 'underReview2' && item.acctStatus === 'Y' && !disabledNewEdit"></view>
                    <view class="line" v-if="!disabledNewEdit && item.auditStatus === 'underReview2' && item.acctStatus === 'Y' && item.acctStage ==='ykf'"></view>
                    <view class="revise" v-if="!disabledNewEdit && item.auditStatus === 'underReview2' && item.acctStatus === 'Y' && item.acctStage ==='ykf'">查看进度</view>
                    <view class="revise" v-if="item.auditStatus !== 'underReview' && item.auditStatus !== 'underReview2' && item.acctStatus === 'Y' && item.acctStage !=='ykf' && !disabledNewEdit">修改</view>
                    <view class="revise" v-if="item.auditStatus !== 'underReview' && item.auditStatus !== 'underReview2' && item.acctStatus === 'Y' && item.acctStage ==='ykf' && !disabledNewEdit">变更</view>
                </view>
            </view>
            <view class="invoice-logo">
                <view class="invoice-title">{{item.credentType | lov('INVOICE')}}</view>
                <view class="outer-circle"><view class="inside-circle"></view></view>
            </view>
            <view class="card-rows" v-if="item.billTitle">
                <view class="card-rows-content">
                    <view class="label">{{item.invoiceCategory === 'indivisual' ? '姓名' : '营业执照名称'}}</view>
                    <view class="value">{{item.billTitle}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.creditNo">
                <view class="card-rows-content">
                    <view class="label">{{item.invoiceCategory === 'indivisual' ? '身份证号' : '统一社会信用代码'}}</view>
                    <view class="value">{{item.creditNo}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.ncAccntCode">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ncAccntCode']}">NC客户编码</view>
                    <view class="value">{{item.ncAccntCode}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.registrationDate">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ncAccntCode']}">注册日期</view>
                    <view class="value">{{item.registrationDate}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.period">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ncAccntCode']}">经营期限</view>
                    <view class="value">{{item.period}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.person">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ncAccntCode']}">法人姓名</view>
                    <view class="value">{{item.person}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.business">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ncAccntCode']}">经营业务范围</view>
                    <view class="value">{{item.business}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.capital">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ncAccntCode']}">注册资本</view>
                    <view class="value">{{item.capital}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.accountBankName">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['accountBankName']}">开户行名称</view>
                    <view class="value">{{item.accountBankName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.mainBillAccntId">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['mainBillAccntId']}">银行账户</view>
                    <view class="value">{{item.mainBillAccntId}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.billPhone">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['billPhone']}">开票电话</view>
                    <view class="value">{{item.billPhone}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.billProvince || item.billCity || item.billDistrict || item.billAddr">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['billProvince,billCity,billDistrict,billAddr']}">公司注册地址</view>
                    <view class="value">{{item.billProvince}}{{item.billCity}}{{item.billDistrict}}{{item.billAddr}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.taxpayerNumber">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['taxpayerNumber']}">联行号</view>
                    <view class="value">{{item.taxpayerNumber}}</view>
                </view>
            </view>
            <view class="division" v-if="item.receiveBillProvince &&  item.receiveBillCity && item.receiveBillDistrict && item.receiveBillAddr && item.consignee && item.mobilePhone && item.email">
                <view class="left-circle"></view>
                <view class="line"></view>
                <view class="right-circle"></view>
            </view>
            <view class="card-rows" v-if="item.receiveBillContact">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['receiveBillContact']}">收票人姓名</view>
                    <view class="value">{{item.receiveBillContact}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.receiveBillPhone">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['receiveBillPhone']}">收票人联系方式</view>
                    <view class="value">{{item.receiveBillPhone}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.receiveBillProvince &&  item.receiveBillCity && item.receiveBillDistrict && item.receiveBillAddr">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['receiveBillProvince,receiveBillCity,receiveBillDistrict,receiveBillAddr']}">收票人邮寄地址</view>
                    <view class="value">{{item.receiveBillProvince}}{{item.receiveBillCity}}{{item.receiveBillDistrict}}{{item.receiveBillAddr}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.fixedName">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['fixedName']}">电票接收人姓名</view>
                    <view class="value">{{item.fixedName}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.receiveBillEmail">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['receiveBillEmail']}">电票邮箱</view>
                    <view class="value">{{item.receiveBillEmail}}</view>
                </view>
            </view>
            <view class="card-rows" v-if="item.ticketingPhone">
                <view class="card-rows-content">
                    <view class="label" :class="{'change-fielld': changeDetails['ticketingPhone']}">电票联系电话</view>
                    <view class="value">{{item.ticketingPhone}}</view>
                </view>
            </view>
            <view class="division">
                <view class="left-circle"></view>
                <view class="line"></view>
                <view class="right-circle"></view>
            </view>
            <view class="business-license">
                <view class="business-label">{{item.invoiceCategory === 'indivisual' ? `身份证照片`:'营业执照照片'}}</view>
                <view class="business-image-container" v-if="item.invoiceCategory === 'indivisual'">
                    <view class="business-image" v-for="(val, key) in item.iDCardArr" :key="key" @tap="preImage(val)">
                        <image :src="val.imgUrl"></image>
                    </view>
                </view>
                <view class="business-image-container" v-else>
                    <view class="business-image" v-for="(val, key) in item.billTitleImgArr" :key="key" @tap="preImage(val)">
                        <image :src="val.imgUrl"></image>
                    </view>
                </view>
                <view v-if="isShiJiaZhuang">
                    <view v-for="(val, key) in shiJiaZhuangType">
                        <view class="business-label" v-if="item[key + 'Arr']">{{val}}</view>
                        <view class="business-image-container" v-if="item[key + 'Arr']">
                            <view class="business-image" v-for="(val, key) in item[key + 'Arr']" :key="key" @tap="preImage(val)">
                                <image :src="val.imgUrl"></image>
                            </view>
                        </view>
                    </view>
                </view>
            </view>
            <view class="division" v-if="financialArray.length > 0 && (item.auditStatus === 'underReview' || item.auditStatus === 'failed') && !approveFlag">
                <view class="left-circle"></view>
                <view class="line"></view>
                <view class="right-circle"></view>
            </view>
            <!-- @make edit by 谭少奇 审批附件调整历史数据-->
            <view class="business-license" v-if="item.annexArr && item.annexArr.length">
                <view class="card-rows">
                    <view class="card-rows-content">
                        <view class="label">附件</view>
                    </view>
                </view>
                <view class="business-image-container">
                    <view class="business-image" v-for="(val, key) in item.annexArr" :key="key" @tap="preImage(val)">
                        <image :src="val.imgUrl"></image>
                    </view>
                </view>
            </view>
            <view class="card-rows" v-if="financialArray.length > 0 && (item.auditStatus === 'underReview' || item.auditStatus === 'underReview2') && !approveFlag">
                <view class="card-rows-content">
                    <view class="label">当前审批人</view>
                    <view class="value" v-if="approvePhotoArr[index] && approvePhotoArr[index].flowNodePsnName">{{approvePhotoArr[index].flowNodePsnName}}</view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
    import LnkImg from '../../../core/lnk-img/lnk-img';

    export default {
        name: "dealer-financial",
        props: {
            shiJiaZhuangType: {
                type: Object,
                default: () => ({})
            },
            isShiJiaZhuang: {
                type: Boolean,
                default: false
            },
            financialArray: {
                type: Array,
                default: {}
            },
            sellProductArr: {
                type: Array,
                default: []
            },
            financialOption: {
                type: Object,
                default: {}
            },
            historyDeatils: {
                type: Object,
                default: () => ({})
            },
            changeDetails: {
                type: Object,
                default: () => ({})
            },
            editFlag: {
                type: Boolean,
                default: true
            },
            approveFlag: {
                type: Boolean,
                default: false
            },
          disabledNewEdit:{
            type: Boolean,
            default: false
          },
          isDinghao:{
            type: Boolean,
            default: false
          },
            isYangShengOrYouXuan: {
                type: Boolean,
                default: false
            },
            isChuanDong: {
                type: Boolean,
                default: false
            },
            isCheckShiJiaZhuang: {
                type: Boolean,
                default: false
            },
            isChuanDongOrXingJi: {
                type: Boolean,
                default: false
            }
        },
        data(){
            // 用户信息
            const userInfo = this.$taro.getStorageSync('token').result;
            const contactList = [];//终端维护的联系人
            return{
                contactList,
                userInfo,
                // 窖龄标志
                isJiaoLing: userInfo.coreOrganizationTile.brandCompanyCode === '5151',
                approvePhotoArr: []
            }
        },
        components: {
            LnkImg
        },
        async created() {
            await this.queryApprovePhoto()
            await this.queryContactsList();
        },
        mounted() {
            this.$bus.$on("initFinancialVueContactList",async () => {
                await this.queryContactsList();
            });
            this.$bus.$on("initQueryApprovePhoto",async () => {
                await this.queryApprovePhoto();
            });
        },
        methods: {
            /**
             * 查询开户附件(由于最开始附件没挂在终端下，所以单独用一个数组存储信息）
             * <AUTHOR>
             * @date 2021-12-16
             */
            async queryApprovePhoto() {
                if(this.approveFlag) return
                try{
                    this.approvePhotoArr = []
                    for(let i = 0; i < this.financialArray.length; i++) {
                        //只查询开户审核中，开户审核不通过，变更审核中这3个状态下的信息
                        if(
                            this.financialArray[i].auditStatus !== 'underReview' &&
                            this.financialArray[i].auditStatus !== 'failed' &&
                            this.financialArray[i].auditStatus !== 'underReview2'
                        ) {
                            this.approvePhotoArr.push({})
                            continue
                        }
                        //查询审批id
                        const {rows} = await this.$http.post('action/link/accnt/getFlowHeadId', {id: this.financialArray[i].id})
                        if(!rows) {
                            this.approvePhotoArr.push({})
                            continue
                        }
                        //查询附件信息和审批人
                        const data = await this.$http.post('action/link/flow/v2/queryById', {id: rows});
                        const arr =  data.result && data.result.flowAttachmentList || []
                        arr.forEach((item)=>{
                            item.smallurl = item.smallUrl
                        })
                        this.approvePhotoArr.push({
                            approveUrl: arr,
                            flowNodePsnName: data.result && data.result.flowNodePsnName
                        })
                    }
                    return '获取附件结束'
                }catch (e){
                    console.error('获取审批附件出错',e)
                }
            },
            /**
             * 查询终端维护的联系人
             * <AUTHOR>
             * @date 2021-06-22
             */
            async queryContactsList () {
                if(this.approveFlag) return
                // 联系人
                const data = await this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: this.financialOption.id,
                });
                this.contactList = data.rows;
            },
            /**
              * 空函数
              * <AUTHOR>
              * @date 2020-11-11
              * @param param
            */
            blankFun (param) {
                console.log('blankFun')
            },
            async queryApprovalData(data) {
                return await this.$http.post('action/link/accntFinance/queryCurrentFinanceByAcctId', {acctId: data.id})
            },
            /**
             * 查看审批进度
             * <AUTHOR>
             * @date 2021-01-15
             * @param param
             */
            async goApprovalProgress(data) {
                if (data.auditStatus === 'underReview2' && data.acctStatus === 'Y' && data.acctStage === 'ykf') {
                    let approvalData = await this.queryApprovalData(data);
                    console.log(approvalData)
                }
            },
            /**
             * 跳转财务编辑页面
             * <AUTHOR>
             * @date 2020-10-22
             * @param data 财务对象
             */
            async editFinancial(data) {
                if(this.financialOption.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                if (data.auditStatus === 'underReview2') {
                    this.$http.post('action/link/accntFinance/queryCurrentFinanceByAcctId', {acctId: data.id}).then(value => {
                        this.$nav.push('/pages/terminal/terminal/terminal-examine-approve/terminal-examine-approve-page', {
                            source: 'terminalDetails',
                            financialId: value.result.id,
                            acctId: value.result.acctId,
                            approvalId: value.result.flowHeadId,
                            approvalType: 'ChangeAccount',
                        })
                    })
                    return
                }
                // 基础信息/所售产品/联系人校验
                if (!this.preCheck()) {
                    return;
                }
                this.$nav.push('/pages/terminal2/edit-construction/edit-financial-page', {
                    data: data,
                    editFlag: 'edit',
                    mainAcctData: this.financialOption,
                    isShiJiaZhuang: this.isShiJiaZhuang,
                    shiJiaZhuangType: this.shiJiaZhuangType
                })
            },
            /**
              * 变更提示弹框
              * <AUTHOR>
              * @date 2020-09-21
            */
            async changeAuditStatus () {
                if(this.financialOption.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                // 基础信息/所售产品/联系人校验
                if (!this.preCheck()) {
                    return;
                }
                // 一户多开状态不变更提示
                if (this.financialOption.multiAcctFlag !== 'N') return
                this.$dialog({
                    title: '变更提示',
                    content: `请确认是否变更改终端为一户多开（该操作只能执行一次）！`,
                    cancelButton: true,
                    dialogProps: {
                        borderRadius: false,            // 无圆角
                        verticalFootButton: true,       // foot 按钮纵向排列
                    },
                    confirmText: '确认，并添加子户头',
                    onConfirm: () => {
                        this.updatePrimaryAccount()
                    },
                    onCancel: () => {}
                })
            },
            /**
             * 更新主户头财务信息
             * <AUTHOR>
             * @date 2020-09-22
             * @param param
             */
            updatePrimaryAccount () {
                this.goFinancial(true)
            },
            /**
             * 校验基础数据
             * <AUTHOR>
             * @date	2024/3/13 16:51
             */
            checkBasic() {
                try {
                    let msg = '';
                    const data = this.financialOption;
                    let acctType = data.acctType;
                    let acctCategoryVal = data.acctCategory;
                    let subAcctTypeVal = data.subAcctType;
                    if (this.$utils.isEmpty(data.acctType)) {
                        msg = '未维护客户分类，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && (this.$utils.isEmpty(data.acctType) || this.$utils.isEmpty(data.acctCategory) || this.$utils.isEmpty(data.subAcctType))) {
                        msg = '未维护客户分类，';
                        return msg;
                    }
                    if (this.isCheckShiJiaZhuang) {
                        if (this.$utils.isEmpty(data.acctSort)) {
                            msg = '未维护客户类型，';
                            return msg;
                        }
                        if (this.$utils.isEmpty(data.acctNature)) {
                            msg = '未维护客户性质，';
                            return msg;
                        }
                    }
                    if (acctCategoryVal === 'qdlx-2' && this.$utils.isEmpty(data.kaSystemName)) {
                        msg = '未维护系统名称，';
                        return msg;
                    }
                    if (acctCategoryVal === 'qdlx-2' && subAcctTypeVal !== 'LianSuoBianLi' && this.$utils.isEmpty(data.xAttr71)) {
                        msg = '未维护店号，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.acctName)) {
                        msg = '未维护门头名称，';
                        return msg;
                    }
                    if (acctType === 'Distributor' && this.$utils.isEmpty(data.acctName)) {
                        msg = '未维护分销商名称，';
                        return msg;
                    }
                    if (acctCategoryVal !== 'GroupBuy' && this.$utils.isEmpty(data.province) && this.$utils.isEmpty(data.city) && this.$utils.isEmpty(data.district)) {
                        msg = '未维护所在地区，';
                        return msg;
                    }
                    if (acctCategoryVal !== 'GroupBuy' && this.$utils.isEmpty(data.address)) {
                        msg = '未维护门店地址详细地址，';
                        return false;
                    }
                    if (acctType === 'Terminal' && this.storeHeadLength === 0 ) {
                        msg = '未维护门头照片，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.isExclusiveShop)){
                        msg = '未维护是否泸州老窖官方形象店，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.isShareHolderShop)){
                        msg = '未维护是否专营公司股东门店,';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.chainStoreFlag)) {
                        msg = '未维护是否为连锁模式，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.doorSigns)) {
                        msg = '未维护门头店招，';
                        return msg;
                    }
                    if (acctType === 'Terminal'&& data.doorSigns==='JingPin' && this.$utils.isEmpty(data.competitiveGoodsType)){
                        msg = '未维护竞品类型，';
                        return msg;
                    }
                    if (this.isJiaoLing) {
                        if(acctType === 'Terminal' && this.$utils.isEmpty(data.channelMemberFlag)) {
                            msg = '未维护是否为精英荟会员,'
                            return msg;
                        }
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.ownStores)) {
                        msg = '未维护是否经销商自有门店，';
                        return msg;
                    }
                    if (data.chainStoreFlag === 'Y' && data.chainHeadStoreFlag === 'N' && this.$utils.isEmpty(data.chainHeadStoreName)) {
                        msg = '未维护连锁总店名称，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.acctLevel)) {
                        if(!this.isChuanDongOrXingJi || data.accntPartner === 'cooperation') {
                            msg = '未维护客户规划等级，';
                            return msg;
                        }
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.capacityLevel)) {
                        if(!this.isChuanDongOrXingJi || data.accntPartner === 'cooperation') {
                            msg = '未维护容量级别,';
                            return msg;
                        }
                    }
                    if (data.judgmentFlag === 'Y' && this.$utils.isEmpty(data.imageRoomNum)) {
                        msg ='未维护形象包间数，';
                        return msg;
                    }
                    if (acctCategoryVal === 'qdlx-4' && this.$utils.isEmpty(data.roomTotalNum)) {
                        msg = '未维护包间总数，';
                        return msg;
                    }
                    if (acctCategoryVal === 'qdlx-4' && this.$utils.isEmpty(data.hallTotalNum)) {
                        msg = '未维护大厅总数，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.xAttr50)) {
                        msg = '未维护店区位，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.area)) {
                        msg = '未维护店面积，';
                        return msg;
                    }
                    if (this.$utils.isNotEmpty(data.deliveryProvince) && this.$utils.isNotEmpty(data.deliveryCity) && this.$utils.isNotEmpty(data.deliveryDistrict) && this.$utils.isEmpty(data.deliveryDetailAddr)) {
                        msg = '未维护收货地址详细地址，';
                        return msg;
                    }
                    return msg;
                } catch (e) {
                    console.log('e', e);
                }
            },
            /**
             * 前置校验基础信息/所售产品/联系人
             * <AUTHOR>
             * @date	2024/3/13 14:29
             */
            preCheck() {
                // 校验基础信息
                const basicMsg = this.checkBasic();
                if (basicMsg) {
                    this.$dialog({
                        title: '提示',
                        content: '您' + basicMsg + '是否前往维护再进行认证',
                        cancelButton: true,
                        onConfirm: () => {
                            this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                data: this.financialOption,
                                editFlag: 'edit'
                            });
                        }
                    });
                    return false;
                }
                if (this.sellProductArr.length === 0) {
                    this.$dialog({
                        title: '提示',
                        content: '您尚未维护该终端的所售产品，是否前往维护再进行认证',
                        cancelButton: true,
                        onConfirm: () => {
                            this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                data: this.financialOption,
                                editFlag: 'edit',
                                // 展示下标为 1的所售产品页签
                                index: 1
                            });
                        }
                    });
                    return false;
                }
                // 所售产品校验
                if (this.isChuanDong && this.financialOption.accntPartner !== 'cooperation') {
                    this.$dialog({
                        title: '提示',
                        content: '未合作客户不可添加有效的所售产品，是否前往维护再进行认证',
                        cancelButton: true,
                        initial: true,
                        onConfirm: () => {
                            this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                data: this.financialOption,
                                editFlag: 'edit'
                            });
                        }
                    });
                    return false;
                }
                // 终端联系人校验
                let contactInfoFlag = false;//是否需要弹框校验联系人列表有数据，或维护的联系人数据是否完整。
                let msg = "您尚未维护该终端的联系人，";
                if (this.contactList.length === 0) {
                    contactInfoFlag = true;
                } else if (this.contactList.length === 1 && this.contactList[0].isEffective !== 'Y'){
                    contactInfoFlag = true;
                    msg = "您尚未维护该终端有效的联系人，"
                } else {
                    //lzlj-002-3346校验手机号
                    contactInfoFlag = false;
                    const reg = /^[1][3456789][0-9]{9}$/;
                    const b = this.contactList.filter((item) => !reg.test(item.mobilePhone) && item.isEffective === 'Y')
                    if(b.length > 0) {
                        contactInfoFlag = true;
                        msg = "联系人号码格式不正确!"
                    }
                    const a = this.contactList.filter((item1) => item1.isEffective === 'Y' && (this.$utils.isEmpty(item1.contactsName) || this.$utils.isEmpty(item1.mobilePhone) || this.$utils.isEmpty(item1.contactsType)));
                    if(!this.$utils.isEmpty(a)){
                        contactInfoFlag = true;
                        msg = "您维护的联系人数据：姓名、联系电话、角色职务未完善，"
                    }
                }
                if (contactInfoFlag) {
                    this.$dialog({
                        title: '提示',
                        content: msg + '是否前往维护再进行认证',
                        cancelButton: true,
                        confirmText: '确认',
                        onConfirm: () => {
                            this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                data: this.financialOption,
                                editFlag: 'edit',
                                // 展示下标为 2的联系人页签
                                index: 2
                            });
                        }
                    });
                    return false;
                }
                return true;
            },
            /**
             * 跳转编辑财务信息
             * <AUTHOR>
             * @date 2020-09-21
             * @param updateFlag 一户多开参数multiAcctFlag是否需要调整
             */
            async goFinancial(updateMultiAcctFlag = false) {
                if(this.financialOption.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                // 基础信息/所售产品/联系人校验
                if (!this.preCheck()) {
                    return;
                }
                this.$nav.push('/pages/terminal2/edit-construction/edit-financial-page', {
                    editFlag: 'add',
                    data: this.financialOption,
                    title: '添加子户头',
                    isShiJiaZhuang: this.isShiJiaZhuang,
                    shiJiaZhuangType: this.shiJiaZhuangType,
                    updateMultiAcctFlag
                })
            },
            /**
              * 预览图片
              * <AUTHOR>
              * @date 2020-09-21
              * @param data 预览图片对象
            */
            async preImage(data) {
                let imgUrl = this.$image.getSignedUrl(data.attachmentPath);
                const inOptions = {
                    current: imgUrl,
                    urls: [imgUrl]
                };
                this.$image.previewImages(inOptions)
            }
        }
    }
</script>

<style lang="scss">
    .dealer-financial {
        .change-fielld::after {
            content: '(变更)';
            color: red;
        }

        .edit-change {
            @include flex;
            font-size: 28px;
            padding-bottom: 24px;
            padding-top: 32px;
            .multi-acct {
                padding-left: 24px;
                text-align: left;
                width: 50%;
                color: #262626;
                .change {
                    color: #2F69F8;
                }
            }
            .edit {
                padding-right: 24px;
                width: 50%;
                text-align: right;
                color: #2F69F8;
            }
        }
        .card-content {
            background: #ffffff;
            border-radius: 16px;
            margin: auto;
            width: 702px;
            padding-top: 8px;
            padding-bottom: 40px;
            box-shadow: 0 7px 42px 0 rgba(126,141,162,0.08);
            margin-bottom: 24px!important;
            .invoice-check-status {
                @include flex-start-center;
                @include space-between;
                padding-top: 12px;
                position: absolute;
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                letter-spacing: 0;
                text-align: right;
                line-height: 28px;
                width: 702px;
                padding-right: 28px;
                .primary-account {
                    font-size: 24px;
                    color: #FFFFFF;
                    background: #FF5A5A;
                    border-radius: 4px 0 16px 4px;
                    padding: 8px 20px;
                }
                .child-account {
                    font-size: 24px;
                    color: #FFFFFF;
                    background: #2F69F8;
                    border-radius: 4px 0 16px 4px;
                    padding: 8px 20px;
                }
                .audit-status {
                    @include flex-start-center;
                    .team {
                        color: #2EB3C2;
                    }
                    .jinggao {
                        color: #FF5A5A;
                    }
                    .line {
                        width: 2px;
                        height: 28px;
                        margin-left: 16px;
                        margin-right: 16px;
                        background: #EEEEEE;
                    }
                    .revise {
                        color: #2F69F8;
                    }
                }
            }
            .invoice-logo {
                .invoice-title {
                    position: absolute;
                    font-family: PingFangSC-Semibold,serif;
                    font-size: 32px;
                    color: #FF8B8B;
                    letter-spacing: 2px;
                    text-align: center;
                    line-height: 32px;
                    width: 702px;
                    padding-top: 34px;
                }
                .outer-circle {
                    margin: 40px auto;
                    width: 192px;
                    height: 98px;
                    border-radius: 50% / 50%;
                    border: 6px solid #FFBDBD;
                    .inside-circle {
                        margin: 10px auto auto auto;
                        width: 168px;
                        height: 72px;
                        border-radius: 50% / 50%;
                        border: 2px solid #FFBDBD;
                    }
                }
            }
            .division {
                @include flex();
                width: 702px;
                padding-top: 20px;
                padding-bottom: 10px;
                .left-circle {
                    position: absolute;
                    width: 30px;
                    height: 30px;
                    background: #F2F2F2;
                    border-radius: 50%;
                    left: 0;
                    margin-left: 15px;
                }
                .line {
                    border: 2px #F2F2F2 dashed;
                    width: 100%;
                    margin-top: 15px;
                }
                .right-circle {
                    position: absolute;
                    width: 30px;
                    height: 30px;
                    background: #F2F2F2;
                    border-radius: 50%;
                    right: 0;
                    margin-right: 15px;
                }
            }
            .business-license {
                .business-label {
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding: 32px 0 24px 24px;
                    color: #8C8C8C;
                }
                .business-image-container {
                    @include flex();
                    @include wrap();
                    .business-image {
                        width: 202px;
                        height: 196px;
                        border-radius: 16px;
                        overflow: hidden;
                        margin-left: 24px;
                        margin-bottom: 24px;
                        image {
                            width: 100%;
                            height: 100%;
                        }
                    }
                }
            }
            .card-rows {
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                letter-spacing: 0;
                line-height: 28px;
                padding: 32px 24px 0 24px;
                .card-rows-content {
                    @include flex();
                    @include space-between();
                    .label {
                        color: #8C8C8C;
                    }
                    .value {
                        color: #262626;
                    }
                }
            }
        }
    }
</style>
