<template>
    <view class="dealer-freeze-dialog">
        <link-page ref="approveUpload">
            <link-dialog ref="dialog" disabledHideOnClickMask v-model="uploadDialog">
                <view slot="head">
                    上传审核附件点击上传按钮，不需要则直接点击完成按钮
                </view>
                <scroll-view scroll-y="true" :style="{'height': '40vh'}">
                    <view style="margin-bottom: 10px;">
                        <view class="agreement-upload-content" >
                            <lnk-img parentId="openAccountFlow"
                                     moduleType="accountList"
                                     :delFlag="true"
                                     :camera="true"
                                     :album="true"
                                     :pathKeyArray="pathKeyArray"
                                     :noGetImgKeyList="Boolean(true)"
                                     :drawWatermarkCancleFlag="true"
                                     :newFlag="true"></lnk-img>
                        </view>
                    </view>
                </scroll-view>
                <link-button slot="foot" @tap="uploadSuccess(pathKeyArray)">完成</link-button>
            </link-dialog>
        </link-page>
    </view>
</template>

<script>
import LnkImg from '../../../core/lnk-img/lnk-img';
export default {
    name: "dealer-approve-upload-dialog",
    components: {
        LnkImg
    },
    data(){
        const closeAccountOption = new this.AutoList(this,{
            module: 'action/link/accnt',
            loadOnStart: true,
            param: {
                filtersRaw: [
                    {id: 'acctType', property: 'acctType', value: '[Terminal, Distributor]', operator: 'in'},
                    {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                ],
                multiAcctMainId: this.multiAcctMainId,
                oauth: this.detailsOauth
            },
            beforeLoad(option){
                option.param.sort = 'created';
                option.param.order = 'asc';
            },
            renderFunc: (h, {data, index}) => {
                return (
                    <item key={index} data={data} arrow="false"
                          style="background:#fff;padding: 14px 0px 14px 12px;position: relative;">
                        <view style="display: flex;align-items: center;width: 100%;border-bottom: #f2f2f2">
                            <link-checkbox val={data.id} toggleOnClickItem slot="note"
                                           style="margin-right: 10px;"/>
                            <view
                                style="display: flex;flex-direction: column;justify-content: space-between;align-items: flex-start;overflow: hidden;">
                                <view style="margin-bottom: 10px;">
                                    <view style="white-space: nowrap;border: 0.5px solid #2F69F8;border-radius: 4px;font-size: 10px;padding-left: 9px;padding-right: 9px;line-height: 20px;height: 20px;color: #2F69F8;margin-right: 5px">
                                        {data.id === this.multiAcctMainId ? '主户头': '子户头'}
                                    </view>
                                </view>
                                <view style="width:100%;display:flex;">
                                    <view style="color: #8c8c8c;height: 20px;line-height: 20px;margin-right: 10px"> {'营业执照名称: '}</view>
                                    <view
                                        style="font-size: 16px;color: #262626;letter-spacing: 0;line-height: 20px;text-overflow:ellipsis;-webkit-line-clamp: 2;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;word-break: break-all;word-wrap: break-word;white-space: normal">
                                        {data.billTitle}
                                    </view>
                                </view>
                                <view style="width:100%;display:flex">
                                    <view style="color: #8c8c8c;height: 20px;line-height: 20px;margin-right: 10px"> {'纳税人识别号: '}</view>
                                    <view
                                        style="font-size: 16px;color: #262626;letter-spacing: 0;line-height: 20px;text-overflow:ellipsis;-webkit-line-clamp: 2;-webkit-box-orient: vertical;display: -webkit-box;overflow: hidden;word-break: break-all;word-wrap: break-word;white-space: normal">
                                        {data.creditNo}
                                    </view>
                                </view>
                                <view
                                    style="width: 100%;height: 1px;background: #f2f2f2;position: absolute;bottom: 0;left: 0; right: 0;margin: 0 12px;"></view>
                            </view>
                        </view>
                    </item>
                )
            },
        });
        return{
            pathKeyArray: [],
            clickFlag:true,
            uploadDialog:false,
            closeAccountOption,
            batchFreezeList:[]
        }
    },
    props:{
        detailsOauth:{
            type: String,
            default: ''
        },
        clientDetails:{
            type: Object,
            default: {}
        },
        multiAcctMainId:{
            type: String,
            default: ''
        }
    },
    methods:{
        async terminalFailure(){
            const that=this
            console.log(this.clientDetails.multiAcctFlag)
            if (this.clientDetails.multiAcctFlag === 'Y') {
                const list = await this.$object(this.closeAccountOption, {
                    multiple: true,
                    pageTitle: "请选择需要冻结的客户",
                    showInDialog: true,
                    beforeConfirm: async (rows) => {
                        if(this.closeAccountOption.list.length === 1 && rows.length === 1&& rows[0].id === this.multiAcctMainId ){
                            return
                        }
                        //筛选主户头数据
                        const list1 = rows.filter(x => x.id === this.multiAcctMainId);
                        if(list1.length === 1 && rows.length < this.closeAccountOption.list.length ){
                            // this.$taro.showToast({title: '请将子户头也进行勾选', icon: 'none', duration: 3000});
                            this.$dialog({
                                title: '提示',
                                content: '请将子户头也进行勾选!',
                                cancelButton: false,
                                initial: true,
                                onConfirm: () => {},
                                onCancel: () => {}
                            });
                            return Promise.reject()
                        }
                    },
                })
                // @edit by 谭少奇 2023/09/04移除终端冻结上传附件弹窗
                this.batchFreezeList = list
                if(this.batchFreezeList.length > 0) {
                    await this.closeAccount(this.batchFreezeList, [])
                    this.batchFreezeList = []
                } else {
                    await this.terminalApprovalFailure([])
                }
            }else{
                setTimeout(function () {
                    that.$dialog({
                        title: '提示',
                        content: '请确认是否将该终端失效？',
                        cancelButton: true,
                        initial: true,
                        onConfirm: () => {
                            that.terminalApprovalFailure([])
                        },
                        onCancel: () => {}
                    });
                }, 200);
            }
        },
        uploadSuccess(flowAttachmentList) {
            if(!this.clickFlag) return
            this.clickFlag = false
            if(this.batchFreezeList.length > 0) {
            this.closeAccount(this.batchFreezeList, flowAttachmentList)
                this.batchFreezeList = []
            } else {
                this.terminalApprovalFailure(flowAttachmentList)
            }
            this.uploadDialog = false
            this.$emit('uploadFreezeSuccess')
        },
        /**
          * @createdBy  张丽娟
          * @date  2021/3/15
          * @methods
          * @para
          * @description
          */
        async closeAccount(list, flowAttachmentList){
            const ids = flowAttachmentList.map(item => ({attachmentId: item.id}))
            list.forEach(item => {
                item.flowAttachmentList = ids
            })
            this.$utils.showLoading('提交中...');
            const data = await this.$http.post('action/link/accnt/batchCloseAccount', list, {
                handleFailed: (error)=>{
            this.$utils.hideLoading()
            this.clickFlag = true;
                }
            });
            if(data.success){
                this.$utils.hideLoading()
                this.clickFlag = true;
                if(data.result === 'closed'){
                    this.$taro.showToast({title: '该客户已冻结成功', icon: 'none', duration: 2000});
                }else{
                    this.$taro.showToast({title: '客户冻结申请已提交，请及时审批', icon: 'none', duration: 2000});
                }
            }
            },
        /**
         * 终端失效审批
         * <AUTHOR>
         * @date 2020-10-21
         */
        async terminalApprovalFailure(list) {
            const that = this;
            const ids = list.map(item => ({attachmentId: item.id}))
            const cloneDetails = JSON.parse(JSON.stringify(that.clientDetails))
            cloneDetails.flowAttachmentList = ids
            const data = await this.$http.post('action/link/accnt/closeAccount', cloneDetails, {
                handleFailed: (error)=>{
                    this.clickFlag = true;
                    that.$utils.hideLoading()
                }
            });
            that.$utils.hideLoading();
            this.clickFlag = true;
            if (data.result === 'approvalFlow') {
                this.$taro.showToast({title: '客户冻结申请已提交，请及时审批', icon: 'none', duration: 2000});
            } else {
                this.$taro.showToast({title: '该客户已冻结成功', icon: 'none', duration: 2000});
            }
            },
    }
}
</script>

<style scoped lang="scss">
.dealer-freeze-dialog{
    .attachmentBox{
        .attachmentTitle{
            color: #8C8C8C;
            margin-bottom: 10px;
        }
    }
    .agreement-upload-content{
        display: flex;
        justify-content: center;
        .lnk-img{
            width: 95%;
            max-width: 432px
        }
        .lnk-img-item{
            width: 45%!important;
            max-width: 206px;
            &:nth-child(odd){
                margin-right: 20px;
                margin-left: 0px;
            }
            &:nth-child(even){
                margin-right: 0px;
                margin-left: 0px;
            }
        }
    }

}
</style>
