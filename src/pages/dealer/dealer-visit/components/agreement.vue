<template>
    <link-page class="dealer-agreement">
        <view class="edit-text" @tap="goHistoryAgreement">历史协议</view>
        <link-auto-list :option="agreementOptions" hideCreateButton>
            <template slot-scope="{data,index}">
                <link-swipe-action>
                    <link-swipe-option slot="option" @tap="invalid(data, index)" v-if="data.agrType==='display'">失效</link-swipe-option>
                    <agree-item :data="data" :index="index" :readonlyFlag="readonlyFlag"/>
                </link-swipe-action>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
    import AgreeItem from './agree-item';
    import {ComponentUtils} from 'link-taro-component';

    export default {
        name: "dealer-agreement",
        components: {AgreeItem},
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            // 协议数据
            let nowDate = this.$date.format(new Date(), 'YYYY-MM-DD');
            const agreementOptions = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/agreement/queryFieldsByExamplePage'
                },
                param: () => {
                    const filtersRaw = [
                        {id: 'agrCategory', property: 'agrCategory', value: 'protocol', operator: '='},
                        {id: 'agrStatus', property: 'agrStatus', value: 'normal', operator: '='},
                        {id: "endTime", property: "endTime", value: nowDate, operator: ">="},
                        {id: "startTime", property: "startTime", value: nowDate, operator: "<="},
                        {id: 'isEffective', property: 'isEffective', value: 'N', operator: '<>'},
                        {id: "agrType", property: "agrType", value: '[approach, display]', operator: "NOT IN"}
                    ];
                    if (this.multiAcctMainIds.length > 2) {
                        filtersRaw.push({id: "accntId", property: "accntId", value: this.multiAcctMainIds, operator: "IN"})
                    } else {
                        filtersRaw.push({id: "accntId", property: "accntId", value: '[' + this.acctId + ']', operator: "IN"})
                    }
                    // 特定公司的经销商人员只能看到自己职位创建的数据
                    if (this.isDealer) {
                        filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                    }
                    return {
                        oauth: 'ALL',
                        sort: 'created',
                        stayFields: "id,accntId,agrType,agrName,signBoard,startTime,endTime,createdName,agrPicKey,isEffective,agrStatus,agrNumber,accntName,postnId,dataSource",
                        order: 'desc',
                        filtersRaw
                    }
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    async afterLoad (data) {
                        //lzlj-002-3026进场协议置前，陈列协议按时间排序且不筛选开始时间了
                        const approachParams = {
                            stayFields: "id,accntId,agrType,agrName,signBoard,startTime,endTime,createdName,agrPicKey,isEffective,agrStatus,agrNumber,accntName",
                            filtersRaw: [
                                {id: 'agrCategory', property: 'agrCategory', value: 'protocol', operator: '='},
                                {id: 'agrStatus', property: 'agrStatus', value: 'normal', operator: '='},
                                {id: "endTime", property: "endTime", value: nowDate, operator: ">="},
                                {id: "startTime", property: "startTime", value: nowDate, operator: "<="},
                                {id: "accntId", property: "accntId", value: this.multiAcctMainIds, operator: "IN"},
                                {id: 'isEffective', property: 'isEffective', value: 'N', operator: '<>'},
                                {id: "agrType", property: "agrType", value: 'approach', operator: "="},
                            ]
                        }
                        const displayParams = {
                            stayFields: "id,accntId,agrType,agrName,signBoard,startTime,endTime,createdName,agrPicKey,isEffective,agrStatus,agrNumber,accntName,postnId,dataSource",
                            sort: 'startTime',
                            order: 'desc',
                            filtersRaw: [
                                {id: 'agrCategory', property: 'agrCategory', value: 'protocol', operator: '='},
                                {id: 'agrStatus', property: 'agrStatus', value: '[normal, Signed, Inbound]', operator: 'IN'},
                                // {id: "endTime", property: "endTime", value: nowDate, operator: ">="},
                                {id: "accntId", property: "accntId", value: this.multiAcctMainIds, operator: "IN"},
                                // {id: 'isEffective', property: 'isEffective', value: 'N', operator: '<>'},
                                {id: "agrType", property: "agrType", value: 'display', operator: "="},
                            ]
                        }
                        // 特定公司的经销商人员只能看到自己职位创建的数据
                        if (this.isDealer) {
                            approachParams.filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                            displayParams.filtersRaw.push({id: 'postnId', property: 'postnId', value: userInfo.postnId});
                        }
                        const res = await Promise.all([
                            this.$http.post('action/link/agreement/queryFieldsByExamplePage', approachParams),
                            this.$http.post('action/link/agreement/queryFieldsByExamplePage', displayParams)
                        ])
                        data.rows.unshift(...res[1].rows)
                        data.rows.unshift(...res[0].rows)
                        for (const item of data.rows) {
                            if (!this.$utils.isEmpty(item.agrPicKey)) {
                                let urlData = await this.$image.getSignedUrl(item.agrPicKey);
                                this.$set(item, 'storeUrl', urlData);
                            } else {
                                this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                            }
                        }
                    },
                }
            });
            return {
                agreementOptions,
                matchTypeColor: { // 有效性状态显示颜色
                    Y: '#2F68F7', // 有效-蓝色
                    N: '#FF0000', // 无效-红色
                },
                userInfo
            }
        },
        props: {
            acctId: {
                type: String,
                default: ''
            },
            multiAcctMainIds: {
                type: String,
                default: ''
            },
            readonlyFlag: {
                type: Boolean,
                default: false
            },
            isDealer: {
                type: Boolean,
                default: false
            }
        },
        methods:{
            /**
             * 营业执照校验弹框
             * <AUTHOR>
             * @date   2024/6/6 14:53
             * @param tip 弹窗提示
             */
             showInfoDialog(tip) {
                const dfd = ComponentUtils.dfd();
                this.$dialog({
                    title: '提示',
                    content: () => {
                        return (
                            <view style="width:100%;font-size: 26rpx;">
                                <view>{tip}</view>
                            </view>
                        )
                    },
                    slots: {
                        foot: (h, renderParam) => {
                            return (
                                <view style="width:100%;display:flex;border-top: 2rpx solid #E5E5E5;">
                                    <link-button label="取消"
                                                 style="flex: 1;border-radius:0;"
                                                 disabled={Boolean(this.buttonTime)}
                                                 onTap={() => {renderParam.close(); dfd.reject()}}/>
                                    <link-button label="确认"
                                                 mode="stroke"
                                                 style="flex: 1;border-radius:0;border:none;"
                                                 disabled={Boolean(this.buttonTime)}
                                                 onTap={() => {dfd.resolve(); renderParam.close()}}/>
                                </view>
                            );
                        }
                    }
                });
                return dfd.promise;
            },
            /**
             * 失效协议
             * <AUTHOR>
             * @date	2023/4/25 16:43
             */
            async invalid(data, index) {
                if (this.readonlyFlag) {
                    return;
                }
                await this.showInfoDialog('失效后不可逆，是否继续失效协议');
                await this.showInfoDialog('失效后不可恢复且该协议无法继续发放返利，是否仍要失效');
                const param = this.$utils.deepcopy(data);
                try {
                    param.isEffective = 'N';
                    param.agrStatus = 'invalid';
                    const {success, result} = await this.$http.post('action/link/agreement/update', {
                        ...param,
                        updateFields: 'isEffective,agrStatus',
                        attr3: 'Invalid'
                    });
                    if (success) {
                        this.agreementOptions.list.splice(index, 1);
                        this.$message.success('失效协议成功！');
                    } else {
                        this.$showError('失效协议失败！');
                    }
                } catch (e) {
                    console.log(e);
                    // this.$showError('失效协议错误！');
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/10/21
             * @methods goHistoryAgreement
             * @para
             * @description 跳转到历史协议界面
             */
            goHistoryAgreement() {
                this.$nav.push('/pages/terminal/terminal/history-agreement-page', {
                    acctId: this.acctId,
                    multiAcctMainIds: this.multiAcctMainIds,
                    readonlyFlag: this.readonlyFlag
                })
            }
        }
    }
</script>

<style lang="scss">
    @import '../../../../styles/list-card';
    .dealer-agreement {
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }

        .link-swipe-action {
            width: 100%;
            margin-bottom: 24px;
        }
    }
</style>
