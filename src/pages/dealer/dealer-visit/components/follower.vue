/**
 * 客户经营品项-跟进人
 * <AUTHOR>
 * @date 2023/6/9 12:17 下午
 */
<template>
    <link-page class="dealer-follower">
        <link-auto-list :option="followerOption" hideCreateButton>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="follower-item">
                    <view slot="note" class="follower-item-note">
                        <view class="header-image">
                            <view class="status" v-if="data.empStatus === 'QUIT'">离职</view>
                            <image :src="$imageAssets.femaleImage" v-if="data.gender === 'FEMALE'"></image>
                            <image :src="$imageAssets.maleImage" v-else></image>
                        </view>
                        <view class="follower-info">
                            <view class="item-top">
                                <view class="left">
                                    <view class="name">{{data.fstName}}</view>
                                    <view class="phone">{{data.mobilePhone}}</view>
                                </view>
                                <view class="right" v-if="data.mainPostn === 'Y'">
                                    <view class="main-flag">主要</view>
                                </view>
                            </view>
                            <view class="item-inner">
                                <text class="inner-tag">{{data.postnName}}</text>
                            </view>
                            <view class="item-middle">
                                <view class="info-item">
                                    <view class="label">人员类型</view>
                                    <view class="value">{{data.empType | lov('STAFF_TYPE')}}</view>
                                </view>
                                <view class="info-item">
                                    <view class="label">负责品项</view>
                                    <view class="value">
                                        <view class="value-item" v-for="(item, index) in data.parts" :key="index">
                                            <text class="tag-item main" v-if="item.mainFlag === 'Y'">{{item.prodPartCode | lov('PROD_BUS_M_CLASS')}}</text>
                                            <text class="tag-item sub" v-else>{{item.prodPartCode | lov('PROD_BUS_M_CLASS')}}</text>
                                        </view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
export default {
    name: 'dealer-follower',
    props: {
        acctId: {
            type: String,
            default: '',
            required: true
        }
    },
    data() {
        const followerOption = new this.AutoList(this, {
            module: 'action/link/accntProdPart',
            url: {
                queryByExamplePage: '/action/link/accntProdPart/queryAcctProdPartPage'
            },
            param: {
                acctId: this.acctId,
                oauth: 'ALL'
            }
        })
        return {
            followerOption
        }
    },
    mounted() {
        this.$bus.$on('refreshFollower', () => {
            this.followerOption.methods.reload()
        })
    }
}
</script>

<style lang="scss">
.dealer-follower {
    .follower-item {
        margin: 24px 0;
        border-radius: 16px;
        .follower-item-note {
            @include flex-start-start;
            .header-image {
                width: 100px;
                height: 100px;
                border-radius: 50%;
                position: relative;
                .status {
                    text-align: center;
                    color: #ffffff;
                    position: absolute;
                    background: rgba(0,0,0,0.50);
                    width: 100%;
                    height: 100%;
                    line-height: 100px;
                    border-radius: 50%;
                }
                image {
                    width: 100%;
                    height: 100%;
                }
            }
            .follower-info {
                margin-left: 24px;
                flex: 1;
                .item-top {
                    @include flex-start-center;
                    @include space-between;
                    .left {
                        @include flex-start-center;
                        .name {
                            font-size: 32px;
                            color: #262626;
                            margin-right: 24px;
                        }
                        .phone {
                            font-size: 28px;
                            color: #2F69F8;
                        }
                    }
                    .right {
                        .main-flag {
                            padding: 0 24px;
                            font-size: 22px;
                            font-weight: 400;
                            color: #3F66EF;
                            line-height: 36px;
                            background: #F0F5FF;
                            border-radius: 4px;
                            text-align: center;
                        }
                    }
                }
                .item-inner {
                    margin: 8px 0;
                    line-height: 44px;
                    .inner-tag {
                        padding: 0 10px;
                        font-size: 24px;
                        color: #3F66EF;
                        background: #F0F5FF;
                        border-radius: 4px;
                        text-align: center;
                    }
                }
                .item-middle {
                    .info-item {
                        @include flex-start-start;
                        width: 100%;
                        font-size: 28px;
                        margin-bottom: 8px;
                        line-height: 44px;

                        .label {
                            color: #999999;
                            width: 132px;
                        }

                        .value {
                            color: #333333;
                            flex: 1;

                            @include flex-start-start;
                            @include wrap;
                            .value-item {
                                margin-right: 8px;
                                margin-bottom: 8px;
                                .tag-item {
                                    padding: 0 10px;
                                    line-height: 36px;
                                    border-radius: 4px;
                                    text-align: center;
                                    &.main {
                                        color: #3F66EF;
                                        background: #F0F5FF;
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    }
}
</style>
