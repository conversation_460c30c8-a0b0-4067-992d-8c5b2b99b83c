<template>
    <link-page class="dealer-list-new-page">
        <link-auto-list :option="terminalListOption" :hideCreateButton="true"
                        :searchInputBinding="{props:{placeholder:'终端名称/客户编码/省市区(县)/详细地址'}}">
<!--            <view slot="searchRight" class="search-container"  @tap="goMapList" v-if="searchRightMap">-->
<!--                <link-icon icon="icon-ditu"/>-->
<!--                <view>地图</view>-->
<!--            </view>-->
            <link-filter-group slot="filterGroup">
                <link-filter-item label="创建时间" :param="{sort:{field:'created',desc:true}}"/>
                <link-filter-item label="最新更新" :param="{sort:{field:'lastUpdated',desc:true}}"/>
            </link-filter-group>

            <template slot="other" v-if="!hideCreateButton && !isDinghao">
              <link-fab-button  @tap="goCreateTerminal"/>
            </template>
            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="terminal-list-item" @tap="(pageParam.source === 'advanceOrder' || pageParam.source === 'quickEntryVisitTerminal') ? goBack(data) : goToItem(data)">
                    <view class="terminal-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                                <view class="store-content">
                                    <view class="store-content-top" v-if="data.acctType">
                                        <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                        <view class="store-title" v-if="data.acctType === 'Terminal'">{{data.acctName}}</view>
                                        <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                        <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.acctName || data.billTitle}}</view>
                                        <!--已认证-->
                                        <view class="store-level" v-if="data.acctStage === 'ykf'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                                        <!--未认证-->
                                        <view class="store-level" v-if="data.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                                        <!--已失效-->
                                        <view class="store-level" v-if="data.acctStage === 'ysx'"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                                        <!--潜客-->
                                        <view class="store-level" v-if="data.acctStage === 'dkf'"><image :src="$imageAssets.storeStatusPotentialImage"></image></view>
                                    </view>
                                    <view class="store-content-middle">
                                      <view class="left">
                                        <view class="store-type" v-if="data.acctType">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                        <view class="store-type" v-if="data.acctCategory">{{data.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                                        <view class="store-type" v-if="data.acctLevel !== undefined || data.capacityLevel !== undefined">
                                          <text v-if="data.acctLevel !== undefined">{{data.acctLevel | lov('ACCT_LEVEL')}}</text>
                                          <text v-if="data.capacityLevel !== undefined"> | {{data.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                                        </view>
                                      </view>
                                      <view class="right">
                                        <view class="store-type store-type-button" v-if="isDinghao" @tap="rowClaimComfirm($event,data)">{{'认领'}}</view>
                                      </view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">编码</view>
                                        <view class="terminal-name">{{data.acctCode}}</view>
                                    </view>
                                    <view class="store-content-representative" v-if="(pageParam.source === 'advanceOrder' || pageParam.source === 'quotaTerminal'|| pageParam.source === 'quickEntryVisitTerminal') && !$utils.isEmpty(data.billTitle)">
                                        <view class="terminal-type" v-if="data.multiAcctMainFlag === 'Y'">主户头</view>
                                        <view class="terminal-type" v-if="data.multiAcctMainFlag === 'N'">子户头</view>
                                        <view class="terminal-name">{{data.billTitle}}</view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">业代</view>
                                        <view class="terminal-name">{{data.salesManListString}}</view>
                                    </view>
                                    <view class="store-content-address">
                                        <view class="store-address">{{data.province}}{{data.city}}{{data.district}}{{data.address}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>

    export default {
        name: "dealer-list-new-page",
        data () {
            // 获取客户一级分类值列表和客户二级分类值列表
            Promise.all([
                this.$lov.getLovByType('ACCT_TYPE'),
                this.$lov.getLovByType('ACCNT_CATEGORY'),
            ]).then(([ACCT_TYPE, ACCNT_CATEGORY]) => {
                let acctTypeExcludeLov = ACCT_TYPE.filter(item => item.val !== 'Terminal' && item.val !== 'Distributor').map(value => value.val);
                let excludeLov = ACCNT_CATEGORY.filter(item => item.parentType === 'ACCT_TYPE' && item.parentVal !== 'Terminal' && item.parentVal !== 'Distributor').map(val => val.val);
                let arrOption = [
                    {label: '客户一级分类', field: 'acctType', type: 'lov',lov: 'ACCT_TYPE',lovOption: {excludeLovs: acctTypeExcludeLov}},
                    {label: '客户二级分类', field: 'acctCategory', type: 'lov',lov: 'ACCNT_CATEGORY',lovOption: {parentType: 'ACCT_TYPE', excludeLovs: excludeLov}},
                ];
                this.terminalListOption.option.filterOption = this.terminalListOption.option.filterOption.concat(arrOption)
            });
            let mdmCompanyCode = this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode;        // 品牌公司代码
           let filtersRaw = [
            {id: 'acctType', property: 'acctType', value: '[Dealer]', operator: 'in'},
            {id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '='},
            {id: 'receivedAcctFlag', property: 'receivedAcctFlag', value: 'Y', operator: '<>'},
            {id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '='},
            {id: 'relatedAcctId', property: 'relatedAcctId', value: '', operator: 'IS NULL'},
          ]
            // 终端列表
            const terminalListOption = new this.AutoList(this, {
                module: 'action/link/accnt',
                url : {
                    queryByExamplePage :'action/link/accnt/queryAcctByPerDataPage',
                },
                sortField: ' ',
                param: {
                    filtersRaw: filtersRaw,
                    oauth: 'ALL',
                    multiAcctMainId: null,
                    notReceivedAcct: 'Y'
                },
                sortOptions: null,
                searchFields: ['id', 'acctName', 'createdByName', 'acctCode', 'province', 'city', 'district', 'address', 'addrDetailAddr'],
                filterOption: [
                    {label: '客户阶段', field: 'acctStage', type: 'lov',lov: 'ACCT_STATUS'},
                    {label: '客户规划等级', field: 'acctLevel', type: 'lov',lov: 'ACCT_LEVEL',lovOption: {parentType: 'BRAND_COM_NAME', parentVal: mdmCompanyCode}},
                    {label: '客户容量级别', field: 'capacityLevel', type: 'lov',lov: 'CAPACITY_LEVEL'}
                ],
                hooks: {
                    beforeLoad (options) {
                        if (this.$utils.isEmpty(options.param.sort.trim())) {
                            delete options.param.order;
                            delete options.param.sort;
                        }
                    },
                    afterLoad (data) {
                        data.rows.map(async (item) => {
                            let saleManName = '';
                            if (!this.$utils.isEmpty(item.storePicPreKey)) {
                                let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                                this.$set(item, 'storeUrl', urlData);
                            } else {
                                this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                            }
                        })
                    }
                }
            });
            return {
                hideCreateButton: false,
                searchRightMap: null,
                terminalListOption,
                claimStatus: this.pageParam.claimStatus,
                isDinghao: false
            }
        },
        async created() {
            this.$bus.$on('terminalListRefresh', async () => {
                await this.terminalListOption.methods.reload();
            })
          if(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1210'){
            this.isDinghao = true
          }
          this.hideCreateButton = this.pageParam.pageFlag === 'visitListPage' || this.pageParam.source === 'quotaTerminal' || this.pageParam.source === 'advanceOrder' || this.pageParam.source === 'stockTaking' || this.pageParam.source === 'quickEntryVisitTerminal' || this.$utils.isPostnOauth() === 'MULTI_ORG';
          this.searchRightMap = this.pageParam.source !== 'quotaTerminal' && this.pageParam.source !== 'advanceOrder' && this.pageParam.source !== 'stockTaking' && this.pageParam.source !== 'quickEntryVisitTerminal';
        },
        methods: {
            /**
              * 跳转创建终端界面
              * <AUTHOR>
              * @date 12/16/20
            */
            goCreateTerminal () {
                if(this.bottomClaimButton){
                  this.$taro.setNavigationBarTitle({title: '国窖待认领终端列表'});
                  this.rowClaimButton = true
                   this.bottomClaimButton = false
                }else{
                  this.$nav.push('/pages/terminal2/new-construction/new-construction-page', {
                    sellProductFlag: true
                  })
                }
            },
            /**
             * 门头照片预览
             * <AUTHOR>
             * @date 2020-09-22
             * @param param
             */
            async previewStoreUrl(param) {
                if (!this.$utils.isEmpty(param.storePicKey)) {
                    let imgUrl =  this.$image.getSignedUrl(param.storePicKey);
                    const inOptions = {
                        current: imgUrl,
                        urls: [imgUrl]
                    };
                    this.$image.previewImages(inOptions)
                } else {
                    const inOptions = {
                        current: param.storeUrl,
                        urls: [param.storeUrl]
                    };
                    this.$image.previewImages(inOptions)
                }
            },
            /**
              * 监控返回函数
              * <AUTHOR>
              * @date 2020-09-15
              * @param param
            */
            onBack (param) {
                if (this.$utils.isEmpty(param)) {
                    return
                }
                if (param.refreshFlag) {
                    this.terminalListOption.methods.reload();
                }
            },
            /**
              * 返回上一页
              * <AUTHOR>
              * @date 2020-10-19
              * @param data 返回携带终端数据
            */
            goBack (data) {
                let param = {
                    terminalFlag: true,
                    terminalDetail: data,
                };
                this.$nav.back(param)
            },
            /**
             * 查看详情
             * <AUTHOR>
             * @date 2020-08-31
             * @param data 详情信息
             */
            async goToItem(data) {
                if(this.pageParam.source === 'quotaTerminal') {
                    const param = {source: 'terminalList', data:data}
                    this.$nav.back(param)
                    return
                }
                if (this.pageParam.source === 'stockTaking') {
                    this.$nav.push('/pages/terminal/inventory-list/inventory-list-page', {data: data})
                    return
                }
                if (this.pageParam.pageFlag === 'visitListPage') {
                    this.$nav.redirect('/pages/terminal/visit/visit-perform-page', {data: data, createdVisitOriginal: 'terminalMapToTerminalList'})
                } else {
                    this.$nav.push('/pages/terminal/terminal/client-details-page', {data: data, fromNewPage: true})
                }
            },
            /**
              * 切换地图模式
              * <AUTHOR>
              * @date 2020-09-01
            */
            goMapList () {
                if (this.pageParam.pageFlag === 'visitListPage') {
                    // this.$nav.back();
                    this.$nav.redirect('/pages/terminal/terminal/terminal-map-list-page', {
                        source: 'terminalList',
                        pageFlag: 'visitListPage',
                    })
                } else {
                    this.$nav.push('/pages/terminal/terminal/terminal-map-list-page', {
                        source: 'terminalList'
                    })
                }
            },
          /**
           * @createdBy  张丽娟
           * @date  2021/3/15
           * @methods rowClaimComfirm
           * @para
           * @description 认领终端
           */
            async rowClaimComfirm(e,data) {
              e.stopPropagation();
              const that = this
              if (data.editApprovalStatus === 'Submitted') {
                  this.$showError('该终端正在编辑审批中，无法认领！');
                  return;
              }
              that.$dialog({
                title: '提示',
                content: (h) => {
                  return [
                    <view style="width:100%">{'请确认是否认领该终端?'}</view>
                  ]
                },
                cancelButton: true,
                onConfirm: async () => {
                  await that.confirmClain(data)
                  that.terminalListOption.methods.reload()
                },
                onCancel: () => {
                }
              })
            },
          /**
           * @createdBy  张丽娟
           * @date  2021/3/18
           * @methods confirmClain
           * @para
           * @description 确认认领
           */
          async confirmClain(data){
            try {
              let userInfo = this.$taro.getStorageSync('token').result;
              this.$utils.showLoading('提交中')
              const {result, success} = await this.$http.post('action/link/accnt/receivedByDingHao', { id: data.id,postnId: userInfo.postnId, orgId:userInfo.orgId})
              if (!success) {
                this.$nav.error(`认领失败！` + result)
              } else {
                this.$utils.showAlert('认领成功')
              }
            } catch (e) {
              this.$nav.error(`认领失败！`)
              throw e
            }finally {
              this.$utils.hideLoading()
            }
          }
        },
     }
</script>

<style lang="scss">

.dealer-list-new-page {
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    /*deep*/.link-item {
                padding: 0;
            }
    /*deep*/.link-item-icon {
                width: 0;
                padding-left: 0;
            }
    /*deep*/.link-dropdown-content {
                padding: 24px;
            }
     .terminal-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;
                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }
                .store-content {
                    width: 80%;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }
                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                  .store-content-middle {
                    display: flex;
                    justify-content: space-between;
                    .left ,.right{
                      @include flex-start-center;
                      margin-top: 20px;
                      margin-left: 24px;
                      .store-type {
                        white-space: nowrap;
                        border: 1px solid #2F69F8;
                        border-radius: 8px;
                        font-size: 20px;
                        padding-left: 25px;
                        padding-right: 25px;
                        line-height: 40px;
                        height: 40px;
                        color: #2F69F8;
                        margin-right: 10px;
                      }
                      .store-type-button{
                        background: #2F69F8!important;
                        color: #fff!important;
                      }
                    }
                  }
                    .store-content-representative {
                        @include flex;
                        margin-left: 24px;
                        margin-top: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        .terminal-type {
                            color: #8C8C8C;
                            min-width: 50px;

                            }
                            .terminal-name {
                                font-family: PingFangSC-Regular,serif;
                                font-size: 24px;
                                color: #000000;
                                letter-spacing: 0;
                                padding-left: 8px;
                                width: calc(100% - 50px);
                                overflow: hidden;
                                white-space: nowrap;
                                text-overflow: ellipsis;
                            }
                        }
                        .store-content-address {
                            margin-left: 24px;
                            margin-top: 18px;
                            font-family: PingFangSC-Regular,serif;
                            font-size: 24px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                        }
                    }
                }
            }
        }
    }
</style>
