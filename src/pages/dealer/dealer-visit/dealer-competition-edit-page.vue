<template>
  <link-page class="dealer-competition-edit-page">
    <view :class="lastTimeCollect.created !== undefined && lastTimeCollect.createdName !== undefined ? 'last-time-collect' : 'last-collect-client'"
          v-if="lastTimeCollectFlag && !notCompetitionFlag">
      <view class="collect-content" v-if="lastTimeCollect.created !== undefined && lastTimeCollect.createdName !== undefined">
        <view class="iconfont icon-time-circle"></view>
        <view class="last-collect-text">上次采集</view>
        <view class="last-time">{{lastTimeCollect.created | date('YYYY-MM-DD HH:mm')}} {{lastTimeCollect.createdName}}</view>
      </view>
      <view class="add-product" @tap="goAddCompetition" v-if="pageParam.sourcePage!=='onlyView'">添加竞品</view>
    </view>
    <view class="last-collect-client" v-if="!lastTimeCollectFlag && !notCompetitionFlag">
      <view class="add-product" @tap="goAddCompetition" v-if="pageParam.sourcePage!=='onlyView'">添加竞品</view>
    </view>
    <link-auto-list :option="competitionOption" hideCreateButton>
      <template slot-scope="{data}">
        <item :key="data.id" :data="data" :arrow="false" class="competition-card" v-if="data.existFlag !== 'N'">
          <view slot="note">
            <view class="competition-card-head">
              <view class="name">
                {{data['competitorBrand']}}<text v-if="data.competitorBrand && data.competitorSeries">/</text>{{data['competitorSeries']}}
              </view>
              <view class="operator">
                <view class="operator-btn" @tap="editFunc(data)" v-if="pageParam.sourcePage!=='onlyView'">编辑</view>
                <view class="operator-btn" @tap="deleteFunc(data)" v-if="pageParam.sourcePage!=='onlyView'">删除</view>
              </view>
            </view>
            <view class="competition-card-content">
              <view class="product-info">
                <view class="product-info-item" v-if="data.inventoryNum">
                  <view class="product-info-data">{{data['inventoryNum']}}</view>
                  <view class="product-info-label">库存数量(件)</view>
                </view>
                <view class="product-info-item" v-if="data.mainTransPrice">
                  <view class="product-info-data">￥{{data['mainTransPrice']}}</view>
                  <view class="product-info-label">主流成交价</view>
                </view>
                <view class="product-info-item" v-if="data.purchasePrice">
                  <view class="product-info-data">￥{{data['purchasePrice']}}</view>
                  <view class="product-info-label">采购价</view>
                </view>
                <view class="product-info-item" v-if="data.displayWay">
                  <view class="product-info-data">{{data['displayWay'] | lov('DISPLAY_WAY')}}</view>
                  <view class="product-info-label">陈列方式</view>
                </view>
                <view class="product-info-item" v-if="data.displayArea">
                  <view class="product-info-data">{{data['displayArea']}}</view>
                  <view class="product-info-label">陈列面</view>
                </view>
                <view class="product-info-item" v-if="data.duitouFlag">
                  <view class="product-info-data">{{data['duitouFlag'] | lov('IS_FLAG')}}</view>
                  <view class="product-info-label">是否堆头</view>
                </view>
                <view class="product-info-item" v-if="data.duitouFlag">
                  <view class="product-info-data">{{data['startSellNum']}}</view>
                  <view class="product-info-label">本月动销件数</view>
                </view>
                <view class="product-info-item" v-if="commaFlag && data.promotionMaterial">
                  <view class="product-info-data-material">
                    <view class="material-item" v-for="(val, key) in data.promotionMaterial" :key="key">
                      {{val | lov('PROMOTION_MATERIAL')}}<text v-if="(key + 1) < data.promotionMaterial.length">,</text>
                    </view>
                  </view>
                  <view class="product-info-label-material">促销物料</view>
                </view>
              </view>
              <view class="product-image">
                <lnk-img :parentId="data.id"
                         :pathKeyArray="data.attachmentList"
                         :moduleType="'competition'"
                         :delFlag="false"
                         :newFlag="false"
                ></lnk-img>
              </view>
              <view class="product-comments" v-if="data && data['comments']">
                <view class="icon-beizhu iconfont"></view>
                <view class="comments">{{data['comments']}}</view>
              </view>
            </view>
          </view>
        </item>
      </template>
    </link-auto-list>
    <link-sticky class="bottom-sticky" v-if="pageParam.sourcePage!=='onlyView'">
      <view class="sticky">
        <view class="subsidiary" @tap="changeSubsidiary">
          <view class="icon-yiwanchengbuzhou iconfont icon-font" v-if="notCompetitionFlag"></view>
          <view class="icon-circle-outline iconfont icon-font" v-else></view>
          <view>无竞品</view>
        </view>
        <link-button block size="large" @tap="submit" :shadow="shadow">提交</link-button>
      </view>
    </link-sticky>
  </link-page>
</template>

<script>
  import lnkImg from "../../core/lnk-img/lnk-img";

  export default {
    name: "dealer-competition-prod-edit",
    components: {lnkImg},
    data() {
        const userInfo = this.$taro.getStorageSync('token').result;
      const competitionOption = new this.AutoList(this, {
        module: 'action/link/accntVisitCompetitor',
        url:{
          queryByExamplePage:'action/link/accntVisitCompetitor/record'
        },
        param: () => {
            let filtersRaw = [];
            if (this.isDealer) filtersRaw = [{id: 'postnId', property: 'postnId', value: userInfo.postnId}];
            return {
                accntId: this.pageParam.data.id,
                visitId: this.pageParam.visitId,
                filtersRaw,
                oauth: 'MY_POSTN',
                sort: 'created',
                order: 'desc'
            }
        },
        loadOnStart: false,
        sortOptions: null,
        filterBar: {},
        hooks: {
          async afterLoad(option) {
            if (option.rows.length !== 0) {
              this.competitionDefault = option.rows;
              this.lastTimeCollect = option.rows[0];
              this.lastTimeCollectFlag = true;
            }
            if (option.rows.length === 1) {
              this.notCompetitionFlag = option.rows[0].existFlag === 'N';
            }
            for (let index in option.rows) {
              let item = option.rows[index]
              if (!this.$utils.isEmpty(item.promotionMaterial)) {
                item.promotionMaterial = item.promotionMaterial.split(',');
              }
              //如果拜访id不同，表示数据是上次采集的，则需要重新赋值一些字段，并添加入store
              if(item.visitId !== this.pageParam.visitId) {
                  const obj = {
                      accntId: this.pageParam.data.id,
                      attachmentList: item.attachmentList,   //图片
                      competitorBrand: item.competitorBrand,
                      competitorBrandId: item.competitorBrandId,
                      competitorSeries: item.competitorSeries,
                      competitorSeriesId: item.competitorSeriesId,
                      createdBy: this.pageParam.data.createdBy,
                      displayArea: item.displayArea, //陈列面
                      displayWay: item.displayWay,  //陈列方式
                      duitouFlag: item.duitouFlag, //是否堆头
                      existFlag: "Y",
                      id: await this.$newId(),
                      inventoryNum: item.inventoryNum, //当前库存
                      mainTransPrice: item.mainTransPrice, //主流成交价
                      orgId: this.$taro.getStorageSync('token').result.orgId,
                      postnId: this.$taro.getStorageSync('token').result.postnId,
                      promotionMaterial: item.promotionMaterial, //促销物料
                      purchasePrice: item.purchasePrice, //采购价
                      row_status: "NEW",
                      startSellNum: item.startSellNum, //本月动销件数
                      visitId: this.pageParam.visitId,
                      visitStatus: "Y"
                  }
                  let storeArr = this.$store.getters['prodCollect/getProdCollect'] || [];
                  this.$store.commit('prodCollect/setProdCollect', storeArr.concat(obj));
                  this.$set(option.rows, index, obj)
              }
            }
          }
        }
      });
      return {
          userInfo, // 用户信息
          isDealer: false, // 是否走特定公司的经销商人员安全性
        commaFlag: true,
        competitionOption,
        notCompetitionFlag: false,
        shadow: true,
        lastTimeCollect: {},
        lastTimeCollectFlag: false,
        clientDetails: {},      // 用户信息
        competitionAll: [],
        visitRecord: '',  // 拜访记录数据
        competitionDefault: []
      }
    },
    async created() {
        this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
        await this.competitionOption.methods.reload();
      this.$store.commit('prodCollect/setProdCollect', '');
      this.clientDetails = this.pageParam.data;
      this.visitRecord = await this.queryVisitRecord();       // 获取拜访记录数据
    },
    onShow () {},
    computed: {},
    methods: {
      /**
       * 监控返回数据
       * <AUTHOR>
       * @date 2020-09-30
       * @param param
       */
      async onBack(param) {
        const that = this;
        if (that.$utils.isEmpty(param)) {
          return
        }
        if (param.addCompetitionFlag) {
          await that.handleStoreData()
        }
      },
      /**
        * 处理添加竞品返回的数据
        * <AUTHOR>
        * @date 2020-09-30
      */
      async handleStoreData () {
        const that = this;
        let addCompetition = that.$store.getters['prodCollect/getProdCollect'];
        // 当拜访下午竞品时，直接push
        if (that.competitionOption.list.length === 0) {
          that.competitionOption.list.push(...addCompetition);
          return;
        }
        // 当拜访下存在拜访时，判断拜访数据是否已存在
        let listIndex = that.competitionOption.list.map(item => item.id);
        addCompetition.forEach(item => {
          if (listIndex.includes(item.id)) {
            let index = that.competitionOption.list.findIndex(v => v.id === item.id);
            that.competitionOption.list.splice(index, 1);
            that.competitionOption.list.push(item);
          } else {
            that.competitionOption.list.push(item);
          }
        });
        //页面显示数据进行去重
        let map = new Map();
        for (let item of that.competitionOption.list) {
          if(!(item.competitorSeries)) item.competitorSeries = '';
          map.set(item.competitorBrand + item.competitorSeries, item);
        }
        that.competitionOption.list =  [...map.values()];
      },
      /**
        * 是否勾选无竞品
        * <AUTHOR>
        * @date 2020-09-29
      */
      changeSubsidiary () {
        const that = this;
        that.notCompetitionFlag = !that.notCompetitionFlag;
        let addCompetition = that.$store.getters['prodCollect/getProdCollect'];
        let temCompetition = [];
        let competitionIndex = [];
        if (!this.$utils.isEmpty(addCompetition)) {
          competitionIndex = addCompetition.map(item => item.id);
        }
        // 缓存为空，赋值是查询数据
        if (this.$utils.isEmpty(addCompetition)) {
            temCompetition = that.competitionDefault
        }
        // 查询数据为空，赋值为缓存数据
        if (this.$utils.isEmpty(this.competitionDefault)) {
            temCompetition = addCompetition
        }
        // 两个不为空，合并去重
        if (!this.$utils.isEmpty(this.competitionDefault) && !this.$utils.isEmpty(addCompetition)) {
          temCompetition = this.competitionDefault.concat(addCompetition);
          // es6数组去重方法
          // Array.from()方法就是将一个类数组对象或者可遍历对象转换成一个真正的数组。
          // new Set() 对象允许你存储任何类型的唯一值，无论是原始值或者是对象引用。
          temCompetition = Array.from(new Set(temCompetition));
        }
        that.notCompetitionFlag ? that.competitionOption.list = [] : that.competitionOption.list = temCompetition;
      },
      /**
       * 跳转添加竞品页面
       * <AUTHOR>
       * @date 2020-09-28
       * @param param
       */
      async goAddCompetition() {
        this.$nav.push('/pages/dealer/dealer-visit/competition-prod-collection/competition-prod-edit-page', {
          rowStatus: 'NEW',
          terminalData: this.pageParam.data,
          visitId: this.pageParam.visitId,
          id: await this.$newId()
        })
      },
      /**
        * 编辑
        * <AUTHOR>
        * @date 2020-10-10
        * @param data 编辑数据
      */
      editFunc(data) {
        this.$nav.push('/pages/dealer/dealer-visit/competition-prod-collection/competition-prod-edit-page', {
          rowStatus: 'UPDATE',
          terminalData: this.pageParam.data,
          visitId: this.pageParam.visitId,
          editData: this.$utils.deepcopy(data),
          editFlag: true
        })
      },
      /**
        * 删除当前数据
        * <AUTHOR>
        * @date 2020-10-10
        * @param data
      */
      deleteFunc (data) {
        const that = this;
        that.$dialog({
          title: '提示',
          content: '确认要删除该条竞品记录吗？',
          cancelButton: true,
          onConfirm: () => {
            if (data.row_status === 'NEW') {
              let listIndex = that.competitionOption.list.findIndex(item => item.id === data.id);
              let storeIndex = that.$store.getters['prodCollect/getProdCollect'].findIndex(item => item.id === data.id);
              that.competitionOption.list.splice(listIndex, 1);
              that.$store.getters['prodCollect/getProdCollect'].splice(storeIndex, 1);
            } else {
              that.deleteCompetitionRecord(data)
            }
          },
          onCancel: () => {}
        })
      },
      /**
        * 请求后端删除竞品记录
        * <AUTHOR>
        * @date 2020-10-13
        * @param data 删除数据
      */
      deleteCompetitionRecord (data) {
        const that = this;
        that.$http.post('action/link/accntVisitCompetitor/deleteById', {
          id: data.id
        }).then(data => {
          if (data.success) {
            that.competitionOption.list.splice(data, 1);
            if (that.competitionOption.list.length === 0) {
              that.$bus.$emit('updateVisitStatus')
            }
          }
        })
      },
      /**
       * 提交竞品采集
       *  <AUTHOR>
       *  @date 2020/8/17
       */
      async submit() {
        const that = this;
        that.commaFlag = false;
        let addCompetition = [];
        // 未勾选有无竞品标志【false】,插入竞品数据
        if (!that.notCompetitionFlag) {
          addCompetition = that.$store.getters['prodCollect/getProdCollect'];
          if (addCompetition.length !== 0) {
            addCompetition.forEach(item => {
              if(typeof(item.promotionMaterial) === 'object'){
                item.promotionMaterial = item.promotionMaterial.join(",");
              }
              delete item.attachmentList;
            })
          }
        }
        let existFlagItem = that.competitionDefault.filter(item => item.existFlag === 'N');
        // 勾选有无竞品标志【true】,且存在无竞品记录数据时，直接返回不作任何数据操作
        if (that.notCompetitionFlag && existFlagItem.length !== 0) {
          let param = {refreshFlag: false};
          that.$nav.back(param);
          return
        }
        // 勾选有无竞品标志【true】,且不存在无竞品记录数据时，调用接口生成无竞品记录数据
        if (that.notCompetitionFlag && existFlagItem.length === 0) {
          let option = {
            accntId: that.pageParam.data.id,
            visitId: that.pageParam.visitId,
            createdBy: that.pageParam.data.createdBy,
            postnId: that.$taro.getStorageSync('token').result.postnId,
            orgId: that.$taro.getStorageSync('token').result.orgId,
            existFlag: 'N',
            row_status: 'NEW',
            visitStatus: 'Y'
          };
          addCompetition.push(option);
        }
        that.visitRecord.competitorCollectStatus = 'Y';
        if (that.$utils.isEmpty(addCompetition)) {
          that.$message['warn']('请添加竞品数据');
          return
        }
        that.$utils.showLoading();
        that.$http.post('action/link/accntVisitCompetitor/batchUpsert', addCompetition, {
          handleFailed (error) {
            that.$utils.hideLoading();
            that.commaFlag = true;
          }
        }).then(async data => {
          if (data.success) {
            that.updateCompetitorCollectStatus(that.visitRecord);
            that.$store.commit('prodCollect/setProdCollect', '');                     // 竞品采集成功，清除本地store的竞品数据
          }
        });
      },
      /**
       * 更新竞品采集状态到拜访记录记录
       * <AUTHOR>
       * @date 2020-10-10
       * @param data 拜访记录更新数据
       */
      updateCompetitorCollectStatus (data) {
        const that = this;
        that.$http.post('action/link/accntVisit/update', data).then(data => {
          if (data.success) {
            let param = {
              refreshFlag: true,
              visitRecordData: data.newRow
            };
            setTimeout(async function () {await that.$nav.back(param);}, 1500);
          }
        })
      },
      /**
        * 获取拜访记录数据
        * <AUTHOR>
        * @date 2020-10-10
        * @param param
      */
      queryVisitRecord () {
        return new Promise(resolve => {
          this.$http.post('action/link/accntVisit/queryById', {
            id: this.pageParam.visitId
          }).then(data => {
            if (data.success) {
              resolve(data.result)
            }
          })
        });
      },
    }
  }
</script>

<style lang="scss">
  .dealer-competition-edit-page {
    font-size: 28px;
    line-height: 28px;
    .competition-content {
      margin: 24px;
    }
    .last-time-collect {
      margin-bottom: 24px;
      @include flex-start-center();
      @include space-between();
      height: 76px;
      background: #ffffff;
      text-align: center;
      padding-left: 24px;
      padding-right: 24px;
      .collect-content {
        @include flex-start-center();
        .icon-time-circle {
          font-size: 28px;
          color: #8C8C8C;
        }
        .last-collect-text {
          font-size: 28px;
          color: #8C8C8C;
          padding-left: 8px;
        }
        .last-time {
          font-size: 28px;
          color: #000;
          padding-left: 8px;
        }
      }
      .add-product {
        font-size: 28px;
        color: #2F69F8;
        letter-spacing: 0;
        text-align: right;
      }
    }
    .last-collect-client {
      margin-bottom: 24px;
      @include flex-end-center();
      height: 76px;
      background: #ffffff;
      text-align: center;
      padding-left: 24px;
      padding-right: 24px;
      .add-product {
        font-size: 28px;
        color: #2F69F8;
        letter-spacing: 0;
        text-align: right;
      }
    }
    .competition-card {
      background: #FFFFFF;
      border-radius: 16px;
      width: 702px;
      margin: auto auto 24px auto;
      /*deep*/ .link-item-icon{
                  width: 0 !important;
                }
      .competition-card-head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 2px dashed #DADEE9;
        padding-bottom: 31px;

        .name {
          font-size: 32px;
          color: #262626;
          letter-spacing: 0;
          line-height: 32px;
          font-weight: 500;
        }

        .operator {
          display: flex;
          justify-content: center;
          align-items: center;

          .operator-btn {
            color: #2F69F8;
            font-size: 28px;
            line-height: 28px;
            margin-left: 32px;
          }
        }

        .competition-head-info {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 60%;
          font-size: 24px;

          .label {
            color: #8C8C8C;
            margin-right: 10px;
          }

          .get-time {
            margin-right: 10px;
          }
        }

        .title {
          width: 40%;
          font-size: 32px;
          color: #262626;
          letter-spacing: 0;
          line-height: 32px;
          font-weight: 500;
          text-align: right;
        }
      }

      .competition-card-content {
        font-size: 28px;
        line-height: 28px;
        letter-spacing: 0;

        .product-info {
          display: flex;
          width: 100%;
          flex-direction: row;
          align-items: center;
          flex-wrap: wrap;
          margin-top: 30px;

          .product-info-item {
            width: 33%;
            .product-info-data {
              color: #262626;
              text-align: center;
              line-height: 28px;
              padding-bottom: 16px;
            }
            .product-info-data-material {
              color: #262626;
              text-align: center;
              line-height: 28px;
              padding-bottom: 16px;
              @include flex-center-center;
              @include wrap;
              .material-item {
                line-height: 36px;
              }
            }
            .product-info-label {
              color: #8C8C8C;
              text-align: center;
              line-height: 28px;
              margin-bottom: 32px;
            }
            .product-info-label-material {
              color: #8C8C8C;
              text-align: center;
              line-height: 28px;
              margin-bottom: 32px;
              margin-top: 16px;
            }
          }
        }

        .product-image {
          display: flex;
          flex-direction: row;
          margin-bottom: 24px;
          margin-top: -16px;
          /*deep*/
          .lnk-img-item {
            width: 180px;
            height: 180px;
          }

        }
        .product-comments{
          @include flex-start-center;
          border-top: 2px dashed #DADEE9;
          font-size: 28px;
          color: #262626;
          letter-spacing: 0;
          padding: 32px 0 10px 0;
          .icon-beizhu {
            margin-right: 16px;
            color: #8C8C8C;
            font-size: 32px;
          }
          .comments {
            line-height: 40px;
          }
        }
      }
    }
    .all-select {
      height: 96px;
      padding-left: 24px;
      @include flex-start-center();
      width: 50%;
      font-size: 28px;
      color: #595959;
      letter-spacing: 0;
      line-height: 28px;
      .iconfont {
        font-size: 40px;
        color: #BFBFBF;
      }
      .icon-yiwanchengbuzhou {
        color: $color-primary;
      }
      .all-select-text {
        padding-left: 16px;
      }
    }
    .bottom-sticky {
      .sticky {
        width: 100%;
        .subsidiary {
          padding-top: 24px;
          margin-left: 24px;
          color: #595959;
          @include flex-start-center;
          .icon-font {
            font-size: 36px;
            margin-right: 16px;
          }
          .icon-yiwanchengbuzhou {
            color: #2F69F8;
          }
        }
        /*deep*/.link-button {
                  width: 94%;
                  height: 96px;
                  margin-right: 24px;
                  margin-left: 24px;
                }
      }
    }
  }
</style>
