<!--
 * @Description: 终端详情
-->

<template>
    <link-page class="client-details-page">
        <!--顶部背景-->
        <view class="top-container">
            <navigation-bar :backVisible="true"
                            :navBarAllHeight="fixTop"
                            :backgroundImg="$imageAssets.homeMenuBgImage"
                            :title="navigationBarTitle"
                            :titleColor="navigationBarTitleColor"
                            :navBackgroundColor="navBackgroundColor">
                <view class="top-content">
                    <view class="store-image" @tap="previewStoreUrl(clientDetails)">
                        <image :src="clientDetails.storeUrl || $imageAssets.terminalDefaultImage"></image>
                    </view>
                    <view class="store-content-cover">
                        <view class="store-content-code">{{clientDetails.acctCode}}</view>
                        <view class="title-level-code">
                            <view class="store-content-top">
                                <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                                <view class="store-title" v-if="clientDetails.acctType === 'Terminal'">{{clientDetails.acctName}}</view>
                                <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                                <view class="store-title" v-if="clientDetails.acctType === 'Distributor'">{{clientDetails.acctName || clientDetails.billTitle}}</view>
                                <!--已认证-->
                                <view class="store-level" v-if="clientDetails.acctStage === 'ykf'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                                <!--未认证-->
                                <view class="store-level" v-if="clientDetails.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                                <!--已失效-->
                                <view class="store-level" v-if="clientDetails.acctStage === 'ysx' && !claimFlag"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                                <!--潜客-->
                                <view class="store-level" v-if="clientDetails.acctStage === 'dkf' && !isYangShengOrYouXuan && !claimFlag"><image :src="$imageAssets.storeStatusPotentialImage"></image></view>
                            </view>
                            <view class="qr-code" @tap="showQrCode" v-if="showQrCodeFlag && !claimFlag">
                                <image :src="$imageAssets.qrCodeImage"></image>
                            </view>
                        </view>
                        <view class="store-content-middle">
                            <!-- @edit by 邓佳柳 2024/11/28 四色标签 -->
                            <color-tag :value="clientDetails.fourColorLabel" v-if="clientDetails.fourColorLabel" />
                            <view class="store-type" v-if="clientDetails.financingFlag">贷 | {{clientDetails.financingFlag | lov('YR_FINANCING_FLAG')}}</view>
                            <view class="store-type" v-if="typeList.includes(clientDetails.acctType)">{{clientDetails.acctType | lov('ACCT_TYPE')}}</view>
                            <view class="store-type" v-if="categoryLst.includes(clientDetails.acctCategory)">{{clientDetails.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                            <view class="store-type" v-if="sublist.includes(clientDetails.subAcctType)">{{clientDetails.subAcctType | lov('SUB_ACCT_TYPE')}}</view>
                            <!-- 战略零售商标签 -->
                            <view class="store-type" v-if="clientDetails.strategicFlag && clientDetails.strategicFlag === 'Y'">{{clientDetails.strategicFlag | lov('STRATEGIC_TAG')}}</view>
                            <view class="store-type" v-if="levelList.includes(clientDetails.acctLevel) || caplist.includes(clientDetails.capacityLevel)">
                                <text v-if="levelList.includes(clientDetails.acctLevel)">{{clientDetails.acctLevel | lov('ACCT_LEVEL')}}</text>
                                <text  v-if="levelList.includes(clientDetails.acctLevel) && caplist.includes(clientDetails.capacityLevel)"> | </text>
                                <text v-if="caplist.includes(clientDetails.capacityLevel)">{{clientDetails.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                            </view>
                            <view class="store-type" v-if="clientDetails.accntPartner && isChuanDong && !claimFlag">{{clientDetails.accntPartner | lov('ACCT_STATE')}}</view>
                        </view>
                    </view>
                </view>
            </navigation-bar>
        </view>
        <view class="content">
            <!--title-->
            <view class="top-blank" v-if="tapsFix"
                  :style="{'height': duration + 'px','line-height': duration+ 'px', 'padding-top':statusBarHeight +'rpx'}">
                {{clientDetails.acctName}}
            </view>
            <!--状态栏-->
            <link-sticky top :duration="this.$device.isIphoneX ? duration * 2 - 12 + this.$device.systemInfo.statusBarHeight : duration * 2 + 16">
                <view class="tap-container">
                    <view class="lnk-tabs">
                        <view class="lnk-tabs-item" :class="{'active': tab.values.code === tapsActive.values.code}"
                              v-for="(tab, index) in templateData" :key="index" @tap="switchTab(tab, 'status')">
                            <view class="label-name" style="min-width: 67px;">
                                {{tab.base.label}}
                            </view>
                            <view class="line"  v-if="tab.values.code === tapsActive.values.code"></view>
                        </view>
                    </view>
                    <link-dropdown class="dropdown" v-model="showDropdownFlag">
                        <view class="iconfont dropdown-icon" :class="showDropdownFlag ?'icon-close':'icon-appstore'"></view>
                        <view slot="dropdown" class="dropdown-container">
                            <view v-for="(item, index) in templateData" :key="index" class="menu-item" @tap="switchTab(item, 'dropdown')">
                                <view class="iconfont menu-icon" :class="item.values.iconPath"></view>
                                <view class="menu-name">{{item.base.label}}</view>
                            </view>
                        </view>
                    </link-dropdown>
                </view>
            </link-sticky>
            <!--360模板无权限-->
            <lnk-no-auth v-if="templateDataFlag"></lnk-no-auth>
            <view v-if="tapsActive.values.code === 'customerLabel'" class="customer-label-view">
                <customer-label
                    class="content"
                    :acctData="clientDetails"
                    :terminalTag="clientDetails.terminalTag"
                    :showDropdownFlag="showDropdownFlag"
                    :isChuanDong="isChuanDong"
                    @setChange='setChange'
                    :show-all='canReadAll'
                />
            </view>
            <!--基础信息-->
            <view v-if="tapsActive.values.code === 'basicInfo'">
                <basic-info :client-details="clientDetails"
                            :newFlag="pageParam.newFlag"
                            :sellProductArr="sellProductOption.list"
                            :account-img-array="accountImgArray"
                            :isYangShengOrYouXuan="isNewYangShengOrYouXuan"
                            :isShiJiaZhuang="isNewShiJiaZhuang"
                            :isJiaoLing="isNewJiaoLing"
                            :isGuojiao="isNewGuojiao"
                            :isChuanDong="isChuanDong"
                            :editToApprovedFlag="editToApprovedFlag"
                            :disabledNewEdit="isDinghao || editInfoFlag || parentTerminal || claimFlag"
                            @checkEdit="checkEdit"></basic-info>
            </view>
            <!--跟进人-->
            <view v-if="tapsActive.values.code === 'follower'">
                <follower :acctId="clientDetails.id"></follower>
            </view>
            <!--财务信息-->
            <view v-if="tapsActive.values.code === 'financial'" class="financial-components">
                <lnk-no-auth label="尚未认证" :authFlag="false" attestation="前往认证"
                             @goAttestation="goAttestation"
                             :disabledNewEdit="isDinghao || parentTerminal"
                             v-if="$utils.isEmpty(clientDetails.credentType)&&$utils.isEmpty(clientDetails.billTitle)&&$utils.isEmpty(clientDetails.creditNo)" ></lnk-no-auth>
                <financial :financial-array="financialArr" :financial-option="clientDetails" @updateSuccess="updateSuccess"  :isChuanDong="isChuanDong"
                           :sellProductArr="sellProductOption.list" :disabledNewEdit="isDinghao || parentTerminal || claimFlag" :shiJiaZhuangType="shiJiaZhuangType"
                           :isYangShengOrYouXuan="isYangShengOrYouXuan" :isShiJiaZhuang="isNewShiJiaZhuang" :isCheckShiJiaZhuang="isCheckShiJiaZhuang"
                           :isChuanDongOrXingJi="isChuanDongOrXingJi" ref="financial" v-else></financial>
            </view>
            <!--联系人-->
            <view v-if="tapsActive.values.code === 'contacts'" class="financial-components">
                <contacts ref="contacts"
                          :attr1="pageParam.data.id"
                          :terminalData="clientDetails"
                          :editToApprovedFlag="editToApprovedFlag"
                          :editContactFlag="editContactFlag"
                          :claimFlag="claimFlag"
                          :showScanCodeFlag="clientDetails.showAllScanCodeButtonFlag"
                          :disabledNewEdit="isDinghao || editInfoFlag || parentTerminal"
                          @checkEdit="checkEdit"></contacts>
            </view>
            <!--融资-->
            <view v-if="tapsActive.values.code === 'yrFinancing'" class="financial-components">
                <yr-financing ref="yrFinancing"
                          :attr1="pageParam.data.id"
                          :yrOption="yrOption"></yr-financing>
            </view>
            <!-- 四色标签 -->
            <view v-if="tapsActive.values.code === 'fourColorLable'">
                <four-color-label :id="this.pageParam.data.acctCode"></four-color-label>
            </view>
            <!--所售产品-->
            <view v-show="tapsActive.values.code === 'sellProduct' && !showDropdownFlag">
                <sell-product :sellProductOption="sellProductOption"
                              :acctData="clientDetails"
                              :editToApprovedFlag="editToApprovedFlag"
                              :isDinghao="isDinghao"
                              :isChuanDong="isChuanDong"
                              :disabledNewEdit="editInfoFlag || parentTerminal"
                              @checkEdit="checkEdit"></sell-product>
            </view>
            <!-- 标签明细 -->
             <view v-if="tapsActive.values.code === 'labelDetails'">
                <label-details :acctData="clientDetails"></label-details>
             </view>
            <!--资金账户-->
            <view v-if="tapsActive.values.code === 'capitalAccount' && !showDropdownFlag">
                <capital-account :financialArr="financialArr" :subsidiaryRange="subsidiaryRange" :subsidiaryNcCode="subsidiaryNcCode"></capital-account>
            </view>
            <!--协议-->
            <view v-if="tapsActive.values.code === 'agreement' && !showDropdownFlag">
                <agreement ref="agreement" :acctId="clientDetails.id" :multiAcctMainIds="multiAcctMainIds" :readonlyFlag="parentTerminal" :isDealer="isDealer"></agreement>
            </view>
            <!--年度配额协议-->
            <view v-if="tapsActive.values.code === 'yearAgreement' && !showDropdownFlag">
                <year-agreement ref="yearAgreement" :editApprovalStatus="clientDetails.editApprovalStatus" :acctId="clientDetails.id"
                                :multiAcctMainIds="multiAcctMainIds" :isGuojiao='isGuojiao' :mdmCompanyCode='clientDetails.mdmCompanyCode'
                                 :acctType='clientDetails.acctType' :isDealer="isDealer" :allowPostn="allowPostn"></year-agreement>
            </view>
            <!-- 配额-->
            <view v-if="tapsActive.values.code === 'quota' && !showDropdownFlag">
                <quota-account :acctId="clientDetails.id" :acctType="clientDetails.acctType" :editApprovalStatus="clientDetails.editApprovalStatus"></quota-account>
            </view>
            <!--订单 -->
            <view v-if="tapsActive.values.code === 'order' && !showDropdownFlag">
                <order ref="order" :accntIdArr="accntIdArr" :isDealer="isDealer" :acctType="clientDetails.acctType" ></order>
            </view>
            <!-- 收货地址 -->
            <view v-if="tapsActive.values.code === 'address-list'">
                <address-list
                    ref="addressRef"
                    source="terminelTab"
                    :editToApprovedFlag="editToApprovedFlag"
                    :acctId="pageParam.data.id"
                    :clientDetails='clientDetails'
                    :disabledNewEdit="isDinghao || editInfoFlag || parentTerminal || claimFlag"
                    @checkEdit="checkEdit">
                </address-list>
            </view>
            <!-- 商超动销-->
            <view v-if="tapsActive.values.code === 'shang-chao-sell'" style="padding-top: 12px">
                <shang-chao-sell :acctCode="clientDetails.acctCode"></shang-chao-sell>
            </view>
            <!--出入库-->
            <view v-if="tapsActive.values.code === 'scanCode'">
                <in-out-storage :acctId="multiAcctMainIds" 
                                :acctType="clientDetails.acctType" 
                                :acctCode="clientDetails.acctCode" 
                                :isPartPort="isPartPort"
                                :isDealer="isDealer"/>
            </view>
            <!--消费者活动-->
            <view v-if="tapsActive.values.code === 'consumerActivities'">
                <consumer-activities :accntId="clientDetails.id" ref="consumerActive"></consumer-activities>
            </view>
            <!--奖品库存-->
            <view v-if="tapsActive.values.code === 'prizeInventory'">
                <prize-inventory :acctCodeList="[clientDetails.acctCode]"></prize-inventory>
            </view>
            <!--核销奖品信息-->
            <view v-if="tapsActive.values.code === 'writeOffPrize'">
                <write-off-prize :acctId="clientDetails.id"></write-off-prize>
            </view>
            <!--拜访记录-->
            <view v-if="tapsActive.values.code === 'visitRecords'">
                <visit-records :queryId="clientDetails.id" :terminalTag="clientDetails.terminalTag" :isDealer="isDealer"
                               :positionChuanDong="positionChuanDong"></visit-records>
            </view>
            <!--库存-->
            <view v-if="tapsActive.values.code === 'inventory'">
                <inventory :acctId="clientDetails.id" :isDealer="isDealer"></inventory>
            </view>
            <!--库存盘点-->
            <view v-if="tapsActive.values.code === 'inventoryRecords'">
                <inventory-records :clientDetails="clientDetails" ref="inventoryRef"></inventory-records>
            </view>
            <!--价格-->
            <view v-if="tapsActive.values.code === 'price'">
                <price :acctId="clientDetails.id" :isDealer="isDealer"></price>
            </view>
            <!--陈列记录-->
            <view v-if="tapsActive.values.code === 'displayRecords'">
                <display-records tab-flag="Y" :queryId="clientDetails.id" :showHead="true" :isDealer="isDealer"></display-records>
            </view>
            <!--竞品-->
            <view v-if="tapsActive.values.code === 'competition'">
                <competition :queryId="clientDetails.id" tab-flag="Y" :isDealer="isDealer"></competition>
            </view>
            <!--活动费用-->
            <view  v-if="tapsActive.values.code === 'activeCost'">
                <active-cost ref="activeCost" :accntIdArr="accntIdArr" :isDealer="isDealer"></active-cost>
            </view>
            <!--消费者-->
            <view  v-if="tapsActive.values.code === 'consumer'">
                <consumer :pageParam="pageParam" :isDealer="isDealer"></consumer>
            </view>
            <!-- 历史数据补录 -->
            <view v-if="tapsActive.values.code === 'supplement' && !showDropdownFlag">
                <supplement ref="supplement" :accntIdArr="accntIdArr" :client-details="clientDetails" :disabledNewEdit="parentTerminal"></supplement>
            </view>
            <!-- 终端兑付 -->
            <view v-if="tapsActive.values.code === 'terminalCash'">
                <terminal-cash :acctData="clientDetails"/>
            </view>
        </view>
        <link-fab-button v-if="claimFlag" @tap="claimShow">
            {{'认领'}}
        </link-fab-button>
        <!--悬浮按钮-->
        <link-fab-group v-model="showFabButtonFlag"
                        v-if="!parentTerminal && !isDinghao && !['customerLabel', 'terminalCash'].includes(tapsActive.values.code) && !claimFlag">
            <link-fab-item icon="icon-zhengce" label="活动绑定" @tap-icon="()=>onTapFabItem('writeOffPolicy')" v-if="showWriteOffPolicy && showFabButtonFlag"/>
            <link-fab-item icon="icon-index" label="拜访" @tap-icon="()=>onTapFabItem('visitPerform')"/>
            <link-fab-item icon="icon-bank" label="库存采集" @tap-icon="()=>onTapFabItem('inventoryCollect')"/>
            <link-fab-item icon="icon-file-image" v-if="(clientDetails.acctType === 'Terminal' ||clientDetails.acctType === 'Distributor') && editApprovalFlag " label="门头协议" @tap-icon="()=>onTapFabItem('agreementCreated')"/>
            <link-fab-item icon="icon-file-image"
                           v-if="clientDetails.acctType === 'Terminal' && broadCompanyCode.indexOf(userInfo.coreOrganizationTile.brandCompanyCode) !== -1"
                           label="包量协议"
                           @tap-icon="() => onTapFabItem('commitmentAgreement')"/>
            <link-fab-item icon="icon-file-image" label="陈列协议" @tap-icon="()=>onTapFabItem('displayAgreementCreated')"/>
            <link-fab-item icon="icon-file-image" v-if="showApproach" label="进场协议" @tap-icon="()=>onTapFabItem('approachAndDisplay')"/>
            <link-fab-item icon="icon-file-image" v-if="isGuojiao" label="酒柜协议" @tap-icon="()=>onTapFabItem('cabinetAgreementCreated')"/>
            <link-fab-item icon="icon-sliders" label="普通配额" @tap-icon="()=>onTapFabItem('quotaApply')" v-if="editApprovalFlag && !(clientDetails.acctType === 'Terminal')"/>
            <link-fab-item icon="icon-file-image" v-if="quotaSubmit" label="年度配额申请" @tap-icon="() => onTapFabItem('yearAgreement')"/>
            <!-- <link-fab-item icon="icon-sliders" label="发票换配额" @tap-icon="()=>onTapFabItem('InvoiceExchange')" v-if="isGuojiao && editApprovalFlag"/> -->
            <link-fab-item icon="icon-file-text" label="预订单" @tap-icon="()=>onTapFabItem('advanceOrder')" v-if="clientDetails.acctStage === 'ykf' && editApprovalFlag && allowPostn"/>
            <link-fab-item icon="icon-file-text" label="现金券预订单" @tap-icon="()=>onTapFabItem('cashCoupons')" v-if="(isRongCheng || isGuojiao) && editApprovalFlag && clientDetails.acctStage === 'ykf'"/>
            <link-fab-item icon="icon-file-text" label="抵扣券预订单" @tap-icon="()=>onTapFabItem('discountCouponOrder')" v-if="isGuojiao && editApprovalFlag && clientDetails.acctStage === 'ykf' && clientDetails.acctType === 'Distributor'"/>
            <link-fab-item icon="icon-hexinzhongduan" label="终端分配" @tap-icon="()=>onTapFabItem('terminalAllot')" v-if="showTerminalAllot || chuanDongFlag"/>
            <link-fab-item icon="icon-warning-circle" label="冻结" @tap-icon="()=>onTapFabItem('terminalFailure')" v-if="clientDetails.acctStatus==='Y'"/>
            <link-fab-item v-if="isNeiQing && clientDetails.acctStatus==='N'" icon="icon-warning-circle" label="生效" @tap-icon="()=>onTapFabItem('terminalEffect')"/>
            <link-fab-item icon="icon-zhongzhi" label="主子户头同步" @tap-icon="()=>onTapFabItem('acctSync')"/>
            <link-fab-item icon="icon-kucunpandian" label="库存盘点" v-if="clientDetails.acctType === 'Distributor'" @tap-icon="()=>dealerNav()"></link-fab-item>
        </link-fab-group>
        <!--二维码弹窗-->
        <view v-if="qrCodeFlag" class="qr-code-container" :style="{'height':$device.systemInfo.windowHeight + 'px','z-index': zIndex}">
            <view v-if="qrCodeLoseFlag" class="lose-qrCode" @tap="setQrCode" :style="`width:${qrWidth}px; height:${qrHeight}px`">
                <image :src="$imageAssets.loseEfficacyImage"></image>
            </view>
            <link-qr-code v-if="!qrCodeLoseFlag" :text="qrCodeUrl" :width="`${qrWidth}px`" :height="`${qrHeight}px`"/>
            <view class="tips-text">
                {{`${changeQrCode?'用以荐酒员扫码变更门店，有效时间2分钟，超时请刷新' : '用以荐酒员注册扫码，有效时间2分钟，超时请刷新'}`}}
            </view>
            <view class="change-store" @tap="changeStore">
                <text class="iconfont icon-qiehuan"></text>
                <text class="change-store-text">{{`${changeQrCode ? '荐酒员注册' : '变更门店'}`}}</text></view>
            <view class="iconfont icon-close-circle" @tap="closeQrCode"></view>
        </view>
         <!-- @edit by 谭少奇 2023/09/04移除终端冻结上传附件弹窗 -->
        <freeze-dialog :multiAcctMainId="pageParam.data.id" :clientDetails="clientDetails" :detailsOauth="detailsOauth" ref="freezeClick"/>
        <!--同步信息提示-->
        <link-dialog ref="confirmDialog" title="提示" :initial="true">
            <view slot="head">
                提示
            </view>
            <view style="padding: 10px;">
                当前终端正在同步主户头信息，请稍等！
            </view>
            <link-button slot="foot" @tap="$refs.confirmDialog.hide()">关闭</link-button>
        </link-dialog>
        <choose-brand-dialog :showFlag="chooseBrandFlag" :clientDetails="clientDetails" @close="close"></choose-brand-dialog>
    </link-page>
</template>

<script lang="jsx">
    import TerminalCash from '../../terminal2/terminal-cash/components/terminal-cash.vue';
    import lnkTaps from '../../core/lnk-taps/lnk-taps'
    import navigationBar from 'link-taro-component'
    import customerLabel from './components/customer-label'
    import basicInfo from './components/basic-info'
    import yrFinancing from './components/yr-financing'
    import financial from './components/financial'
    import sellProduct from './components/sell-product'
    import agreement from './components/agreement'
    import yearAgreement from "./components/year-agreement";
    import visitRecords from "./components/visit-recrods";
    import DisplayRecords from "./components/display-records";
    import Competition from "./components/competition";
    import capitalAccount from "./components/capital-account"
    import quotaAccount from "./components/quota-account"
    import activeCost from "./components/active-cost"
    import consumer from "./components/consumer"
    import price from "./components/price"
    import inventory from "./components/inventory"
    import inventoryRecords from "./components/inventory-records"
    import order from "./components/order"
    import shangChaoSell from "./components/shang-chao-sell"
    import contacts from "./components/contacts"
    import lnkNoAuth from "../../core/lnk-no-auth/lnk-no-auth"
    import Taro from "@tarojs/taro";
    import supplement from "./components/supplement";
    import prizeInventory from "./components/prize-inventory";
    import writeOffPrize from "./components/write-off-prize";
    import consumerActivities from "./components/consumer-activities";
    import {ComponentUtils} from "link-taro-component";
    import freezeDialog from './components/approve-upload-dialog';
    import verifyVisit from "../../terminal/verify-visit";
    import clientDetails from '../../terminal/mixins/client-details';
    import follower from './components/follower';
    import InOutStorage from '../../terminal2/scan-code/component/in-out-storage';
    import addressList from "./components/address-list";
    import ChooseBrandDialog from "../../qdhy/inventory/components/choose-brand-dialog";
    import FourColorLabel from './components/four-color-label.vue'
	import ColorTag from '../../terminal2/components/ColorTag.vue';
    import LabelDetails from './components/label-details.vue'

    export default {
        name: "client-details-page",
        mixins: [verifyVisit(), clientDetails()],
        components: {
            TerminalCash,
            ChooseBrandDialog,
            consumerActivities,   //消费者活动
            writeOffPrize,        //核销奖品信息
            prizeInventory,       //奖品库存
            Competition,          // 竞品
            DisplayRecords,       // 陈列记录
            visitRecords,         // 拜访记录
            navigationBar,        // 页面top背景栏
            lnkTaps,              // 状态tap键
            customerLabel,        //客户标签
            basicInfo,            // 基础信息
            yrFinancing,          // 融资
            financial,            // 财务信息
            sellProduct,          // 所售产品
            agreement,            // 协议
            yearAgreement,        // 年度配额协议
            activeCost,           // 活动费用
            consumer,             // 消费者
            inventory,            // 库存
            inventoryRecords,     //库存盘点记录
            price,                // 价格
            capitalAccount,       // 资金账户
            quotaAccount,         // 配额账户
            order,                // 订单
            shangChaoSell,        //商超动销
            contacts,             // 联系人
            lnkNoAuth,            // 无权限
            supplement,           // 历史数据补录
            freezeDialog,
            InOutStorage,         // 出入库
            follower,              // 跟进人
            addressList,          //收货地址
            FourColorLabel,        // 四色标签子页签
            ColorTag,               // 四色标签tag组件
            LabelDetails           // 标签明细
        },
        data() {
            const userInfo = this.$taro.getStorageSync('token').result;
            // 蓉城怀旧公司
            const isRongCheng = userInfo.coreOrganizationTile.brandCompanyCode === '5910';
            let detailsOauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();
            if (detailsOauth === 'MY_ORG') {
                detailsOauth = 'MULTI_ORG'
            }
            if(this.$taro.getStorageSync('token').result.positionType === 'CityManager') {
                detailsOauth = 'MULTI_ORG'
            }
            // 融资对象
            const yrOption = new this.AutoList(this, {
                // module: 'action/link/financingTag',
                url: {
                    queryByExamplePage: 'action/link/financingTag/queryFinancingInfo'
                },
                loadOnStart: ['Y', 'N'].includes(this.pageParam.data.financingTerminalStatus),
                param: {
                    customerCode: this.pageParam.data.acctCode,
                    customerCreditCode: this.pageParam.data.creditNo,
                    companyCode: this.pageParam.data.mdmCompanyCode,
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    beforeLoad(option){
                        delete option.param.filtersRaw
                        delete option.param.sort
                        delete option.param.order
                        option.param.pageFlag = false
                        option.param.rows = 999
                    }
                }
            });
            // 所售产品对象
            const sellProductOption = new this.AutoList(this, {
                module: 'action/link/saleCategory',
                loadOnStart: false,
                param: () => {
                    return {
                        pageFlag: true,
                        onlyCountFlag: false,
                        partOauthFlag: this.prodPartFlag ? 'Y': '', //分品项-安全性
                        oauth: 'ALL',
                        sort: 'created',
                        order: 'desc',
                        accntId: this.pageParam.data.id
                    }
                },
                sortOptions: null,
                filterBar: {},
                hooks: {
                    beforeLoad(option){
                        delete option.param.sort
                        delete option.param.order
                    },
                    afterLoad(data) {
                        data.rows.forEach(item => {
                            if(!this.subsidiaryRange.includes(item.supplierName) && item.supplierManageMode === 'subsidiary') {
                                this.subsidiaryRange.push(item.supplierName);
                                this.subsidiaryNcCode.push(item.supplierNcCode);
                            }
                        });
                    }
                }
            });
            return {
                isRongCheng,
                chooseBrandFlag: false,
                hisForm: {},
                canReadAll: true,
                claimFlag: this.pageParam.claimFlag ? true : false,
                typeList: [],
                categoryLst: [],
                sublist: [],
                levelList: [],
                caplist:[],
                detailsOauth,
                acctIds:[],
                showFabButtonFlag: false,
                sellProductOption,
                yrOption,
                licenseHeadId: [],
                financialArr: [],
                financialAttachment: [],                                                                                              // 财务数据附件信息
                showDropdownFlag: false,
                tapsFix: false,
                tapsActive: {values: {code: ''}},
                fixTop: 200,
                topHeight: 0,
                clientDetails: this.pageParam.data,
                duration: this.$device.isIphoneX ? 88 : 64,
                statusBarHeight: this.$device.systemInfo.statusBarHeight,
                navigationBarTitle: '客户详情',
                navigationBarTitleColor: '#ffffff',
                navBackgroundColor: 'transparent',
                templateDataFlag: false,
                qrCodeUrl:'',
                timer: null,                                                                                        // 计时器
                qrWidth: 220,
                qrHeight: 220,
                qrCodeFlag: false,
                qrCodeLoseFlag: false,                                                                                  // 二维码
                changeQrCode: false,                                                                                    // 变更门店二维码控制参数
                zIndex: ComponentUtils.nextIndex() + 1,                                                                 // 二维码弹窗层级数
                terminalL6Id: '',
                showTerminalAllot: null,
                mustInputArr: [],                   // 拜访采集必填项目
                accntIdArr: [],                     // 主户头和子户头accntId数组，用于订单和活动费用列表查询
                multiAcctMainIds: null,               //主户头及子户头id（字符串）
                ncAccntCodeArr: [],
                subsidiaryRange: [],
                subsidiaryNcCode: [],
                accountImgArray: [],                 // 审批附件只需要再审批详情界面显示，终端详情界面不显示
                formData: {},
                contactList: [],//终端维护的联系人
                userInfo,
                isDealer: false, // 是否走特定公司的经销商人员安全性
                isNeiQing: userInfo.positionType === 'InternalStaff',
                shiJiaZhuangType: {
                    customerInformation: '客户资料申请表扫描件',
                    copyBusinessLicense: '营业执照复印件',
                    openingPermit: '开户许可证',
                    invoicingData: '开票资料',
                    copyBankCard: '个人银行卡复印件'
                },
            }
        },
        computed:{
            // 年度配额申请入口权限
            quotaSubmit(){
                let { acctType, acctStage, acctStatus} = this.clientDetails
                const typeIn = ['Terminal', 'Distributor'].includes(acctType)
                if(this.isJiaoLing){
                    return typeIn && acctStage === 'ykf' && acctStatus === 'Y'
                }else if(this.isGuojiao){
                    return typeIn && this.clientDetails.acctStage === 'ykf' && this.editApprovalFlag
                }else if(this.isTeQu){
                    let { acctType, acctStage, acctStatus} = this.clientDetails
                    return acctType === 'Terminal' && acctStage === 'ykf' && acctStatus === 'Y' && this.allowPostn
                }
                return false
            },
            // 来源页面是否是整合终端详情
            parentTerminal() {
                return this.pageParam.pageFrom && this.pageParam.pageFrom === 'parentTerminal';
            },
            editApprovalFlag() {
                const flag = this.clientDetails.editApprovalStatus !== 'Submitted'
                return flag
            },
            chuanDongFlag() {
                const arr = ['Salesman', 'CustServiceSpecialist', 'SalesTeamLeader']
                return this.positionChuanDong && arr.includes(this.userInfo.positionType)
            },
            editInfoFlag() {
                return this.financialArr.some((item) => {
                    return ['underReview', 'underReview2'].includes(item.auditStatus)
                })
            },
        },
        /**
         * 监听页面滚动函数
         * <AUTHOR>
         * @date 2020-08-05
         * @param e 距离顶部距离
         */
        onPageScroll(e) {
            this.tapsFix = e.scrollTop >= this.fixTop - this.duration;
            this.showDropdownFlag = false
        },
        async created () {
            this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
            if (this.isYangShengOrYouXuan && this.userInfo.positionType === 'SalesAreaManager') {
                this.detailsOauth = 'MULTI_POSTN'
            }
            this.showTerminalAllot = this.userInfo.positionType !== 'Salesman';
            this.getTopContentHeight();                                                                     // 获取.top-content高度
            this.fetchmultiAcctMainIds();
            if (this.$utils.isEmpty(this.$store.getters['configTemplate/getTerminalVisitPerformTemp'])) {
                this.$configTemplate.terminalVisitPerformTemp();
            }
            this.$bus.$on('deleteSellProdBack', async () => {
                await this.sellProductOption.methods.reload();
            });
            await this.queryContactsList();
            await this.getHis();
            this.getTypeArray();
        },
        destroyed () {clearTimeout(this.timer);},
        async mounted() {
            this.$bus.$on("initFinancialVueContactList",async () => {
                this.$refs.contacts && this.$refs.contacts.contactsOptions.methods.reload();
                //更新终端基础信息
                await this.updateSuccess(this.clientDetails.id, false)
                await this.queryContactsList();
            });
            this.$bus.$on('reloadAgreement', async () => {
                await this.$refs.agreement && this.$refs.agreement.agreementOptions.methods.reload();
            });
            this.$bus.$on('reloadChildAccount', async () => {
                await this.queryChildAccount();
            });
        },
        methods: {
            //跳转盘点经销商
            dealerNav() {
                this.chooseBrandFlag = true
            },
            close() {
                this.chooseBrandFlag = false
            },
            toEdit(){
               this.onTapFabItem('census', true)
            },
            // 获取普查检查记录
            async getHis(){
                const url = 'action/link/terminalCensusCheck/queryByExamplePage';
                const param = {
                    filtersRaw: [{id: 'acctId', property: 'acctId', value: this.pageParam.data.id, operator: '='}]
                }
                const {success, rows} = await this.$http.post(url, param)
                if(success){
                    const newData = rows.find(i=>{
                        return i.acctId === this.pageParam.data.id
                    })
                    this.hisForm = newData || {id: null}
                }
            },
            async getTypeArray() {
                const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE','ACCT_LEVEL','CAPACITY_LEVEL']);
                list[0].forEach(item => {
                    this.typeList.push(item.val)
                });
                list[1].forEach(item => {
                    this.categoryLst.push(item.val)
                });
                list[2].forEach(item => {
                    this.sublist.push(item.val)
                });
                list[3].forEach(item => {
                    this.levelList.push(item.val)
                });
                list[4].forEach(item => {
                    this.caplist.push(item.val)
                });
            },
            /**
             * 调整品项进销存数据
             * <AUTHOR>
             * @date 2023/01/23
             */
            setChange(val){
                if(!val.cumulativeSalesLevel){
                    val.cumulativeSalesLevel = ''
                }
                this.clientDetails.terminalTag = {
                    ...this.clientDetails.terminalTag,
                    ...val
                }
            },
            /**
             * 查询终端维护的联系人
             * <AUTHOR>
             * @date 2021-06-22
             */
            async queryContactsList () {
                // 联系人
                const data = await this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: this.clientDetails.id,
                });
                this.contactList = data.rows;
            },
            /**
             * 复制终端跳转
             * <AUTHOR>
             * @date 2023-12-22
             */
            claimShow(){
                this.$dialog({
                    title: '提示',
                    content: h => (
                        <view style="padding: 36rpx; font-size:30rpx">请确认是否复制该终端？</view>
                    ),
                    cancelButton: true,
                    confirmText: '是',
                    cancelText: '否',
                    onConfirm:async () => {
                        const {result} = await this.$http.post('action/link/accnt/queryCopyAccountById', {id: this.clientDetails.id})
                        this.$nav.push('/pages/terminal2/new-construction/new-construction-page', {
                            basicDatail: result, //基本信息
                            sellProductFlag: true,
                            claimFlag: true,//认领跳转
                            listOauth: this.pageParam.listOauth
                        })
                    },
                    onCancel: async () => {}
                });
            },
            /**
             * 关闭二维码
             * <AUTHOR>
             * @date 2020-10-13
             */
            closeQrCode () {
                this.qrCodeFlag = !this.qrCodeFlag;
                this.qrCodeLoseFlag = false;
                clearTimeout(this.timer);
            },
            /**
             * 请求二维码图片
             * <AUTHOR>
             * @date 2020-10-19
             */
            qrCodeCreated () {
                const that = this;
                return new Promise(async resolve => {
                    let url = this.changeQrCode ? 'action/link/youma/changPromoterQrCode' : 'action/link/youma/registerQrCode';
                    const data = await that.$http.post(url, {
                        userId: that.userInfo.id,
                        acctId: that.clientDetails.id,
                        taxNo: that.clientDetails.creditNo
                    });
                    if(data.success) {
                        clearTimeout(this.timer);
                        resolve(data.rows.regUrl);
                    }
                });
            },
            /**
             * 显示注册二维码弹窗
             * <AUTHOR>
             * @date 2020-10-13
             */
            async showQrCode() {
                const that = this;
                if(that.clientDetails.acctStatus === 'N'){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                that.qrCodeUrl = await that.qrCodeCreated();
                this.qrCodeFlag = !this.qrCodeFlag;
                this.changeQrCode = false;
                that.setTimeoutFlash()
            },
            /**
             * 二维码间隔函数
             * <AUTHOR>
             * @date 2020-10-13
             */
            setTimeoutFlash () {
                const that = this;
                this.timer = setTimeout(function(){
                    that.qrCodeLoseFlag = true;
                }, 120000)
            },
            /**
             * 变更按钮
             * <AUTHOR>
             * @date 12/7/20
             */
            async changeStore() {
                const that = this;
                if (that.qrCodeLoseFlag) {
                    that.qrCodeLoseFlag = false;
                }
                that.changeQrCode = !that.changeQrCode;
                that.qrCodeUrl = await that.qrCodeCreated();
                that.setTimeoutFlash();
            },
            /**
             * 重新生成二维码
             * <AUTHOR>
             * @date 2020-10-13
             */
            async setQrCode() {
                this.qrCodeUrl = await this.qrCodeCreated();
                this.qrCodeLoseFlag = false;
                this.setTimeoutFlash();
            },
            /**
             * 快捷入口
             * <AUTHOR>
             * @date 2020-09-27
             * @param tag
             */
            async onTapFabItem(tag, fromEdit = false) {
                const that = this;
                if(that.clientDetails.acctStatus === 'N' && !['terminalAllot', 'terminalEffect'].includes(tag)){
                    this.$showError('当前客户已被冻结，请生效后再进行操作!');
                    return;
                }
                // 预订单、现金券预订单、抵扣券预订单、普通配额、年度配额申请、发票换配额：国窖公司黑名单客户按钮限制
                if (['advanceOrder', 'cashCoupons', 'discountCouponOrder', 'quotaApply', 'yearAgreement', 'InvoiceExchange'].includes(tag)
                    && this.clientDetails.mdmCompanyCode === '5600' && ['PartBlack', 'Black'].includes(this.clientDetails.fourColorLabel)) {
                    this.$showError('当前终端为黑名单终端，请联系管理员！');
                    return;
                }
                // 预订单：特曲公司黑名单客户按钮限制
                if (['advanceOrder'].includes(tag) && this.clientDetails.mdmCompanyCode === '5137' && ['PartBlack', 'Black'].includes(this.clientDetails.fourColorLabel)) {
                    this.$showError('当前终端为黑名单终端，请联系管理员！');
                    return;
                }
                switch (tag) {
                    // add by 谭少奇 2024/04/26 新增终端普查
                    case 'census':
                        that.$nav.push('/pages/terminal2/parent-terminal/census-content-page', {
                            data: that.clientDetails,
                            fromEdit,
                            editData: this.hisForm,
                        });
                        break;
                    case 'visitPerform':
                        this.verifyVisit();
                        break;
                    case 'inventoryCollect':
                        that.$nav.push('/pages/terminal/visit/inventory-acquisition-page', {
                            data: that.clientDetails,
                            isEditFlag: true,
                            originFlag: 'clientDetails'
                        });
                        break;
                    case 'quotaApply':
                        that.quotaApplyCheck('Common');
                        break;
                    case 'InvoiceExchange':
                        that.quotaApplyCheck('InvoiceExchange');
                        break;
                    case 'advanceOrder':
                        that.$nav.push('/pages/terminal/order/advance-order-page', {
                            source: 'quickEntryVisitTerminal',
                            data: this.clientDetails
                        });
                        break;
                    case 'cashCoupons':
                        // lj2024-3659：国窖-贷款逾期终端限制下单现金券预订单
                        if (this.clientDetails.financingFlag === 'Overdue' && this.clientDetails.mdmCompanyCode === '5600') {
                            this.$showError('存在逾期未还贷款，暂停现金券预订单下单，请提醒客户及时还款！');
                        } else {
                            that.$nav.push('/pages/terminal/order/cash-coupons-order-page', {
                                orderType: 'CashCouponOrder',
                                source: 'quickEntryVisitTerminal',
                                data: this.clientDetails
                            });
                        }
                        break;
                    // 抵扣券预订单
                    case 'discountCouponOrder':
                        that.$nav.push('/pages/terminal/order/cash-coupons-order-page', {
                            orderType: 'DiscountCouponOrder',
                            source: 'quickEntryVisitTerminal',
                            data: this.clientDetails
                        });
                        break;
                    case 'terminalAllot':
                        that.$nav.push('/pages/terminal/terminal/terminal-allocation-page', {
                            data: this.pageParam.data,
                            chuanDongFlag: this.chuanDongFlag,
                            terminalL6Id: this.terminalL6Id,
                            prodPartFlag: this.prodPartFlag
                        });
                        break;
                    case 'terminalFailure':
                        await this.$refs.freezeClick.terminalFailure();
                        break;
                    case 'terminalEffect':
                        if(this.clientDetails.acctStatus === 'Y') {
                            this.$showError('该客户状态为有效')
                            return
                        }
                        that.$dialog({
                            title: '提示',
                            content: '请确认是否将该终端生效？',
                            cancelButton: true,
                            initial: true,
                            onConfirm: () => {
                                this.$utils.showLoading({});
                                this.effectAccount()
                            },
                            onCancel: () => {}
                        });
                        break
                    case 'agreementCreated':
                        that.$nav.push('/pages/terminal/visit/door-collect-page', {
                            data: that.clientDetails,
                            isEditFlag: true
                        });
                        break;
                    case 'commitmentAgreement':
                        that.$nav.push('/pages/terminal/visit/commitment-protocol-page', {
                            data: that.clientDetails,
                            isEditFlag: true
                        });
                        break;
                    case 'displayAgreementCreated':
                        that.$nav.push('/pages/terminal2/protocol/protocol-edit-page.vue', {
                            data: that.clientDetails,
                            source: 'terminal'
                        });
                        break;
                    case 'cabinetAgreementCreated':
                        that.$nav.push('/pages/terminal/visit/displays-protocol-page.vue', {
                            data: that.clientDetails,
                            agrType: 'Cabinet',
                            pageFrom: 'terminal'
                        });
                        break;
                    case 'approachAndDisplay':
                        that.$nav.push('/pages/terminal/visit/displays-protocol-page', {
                            data: that.clientDetails,
                            multiAcctMainIds: that.multiAcctMainIds,
                            approachAndDisplay: true,
                            agrType: 'approach',
                            isEditFlag: true
                        });
                        break;
                    case 'yearAgreement':
                        if(that.isGuojiao && that.clientDetails.acctType === 'Terminal'){
                            that.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-apply-page', {
                                data: that.clientDetails,
                                isEditFlag: false,
                                type: 'new'
                            });
                        }else if(that.isTeQu){
                            let {id:acctId, acctName, acctCode, orgCode } = this.clientDetails
                            that.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-tequ-page', {
                                data: {acctId, acctName, acctCode, orgCode, quotaType:'AuthorizedQuota'},
                                operate: 'NEW',
                                type:'authorized',
                            });
                        }else if(that.isJiaoLing && that.clientDetails.acctType === 'Terminal'){
                            that.$nav.push('/pages/terminal2/annual-quota-apply/annual-quota-jiaoling-page', {
                                data: that.clientDetails,
                                operate: 'NEW',
                                type:'new'
                            });
                        }else{
                            that.$nav.push('/pages/terminal/protocol/new-year-protocol-page', {
                                data: that.clientDetails,
                                isEditFlag: true
                            });
                        }
                        break;
                    case 'writeOffPolicy':
                        if(that.clientDetails.synExternalSysFlag === 'N') {
                            this.$showError('该终端信息未同步成功，请重新同步');
                            return
                        }
                        if(!that.clientDetails.longitude || !that.clientDetails.latitude) {
                            this.$showError('该终端经纬度信息为空，请重新定位终端地址');
                            return
                        }
                        this.$nav.push('/pages/terminal2/terminal/act-binding-page.vue', {
                            acctData: this.clientDetails,
                            reloadAct: () => {
                                this.$refs.consumerActive && (this.$refs.consumerActive.consumerActivities.methods.reload());
                            }
                        });
                        break;
                    case 'acctSync':
                        await this.syncAcctInfo();
                        break;
                }
            },
            /**
             * 同步主子户头
             * <AUTHOR>
             * @date	2024/3/20 11:43
             */
            async syncAcctInfo() {
                this.$refs.confirmDialog.show();
                try {
                    const {success, result} = await this.$http.post('action/link/accnt/syncMainAccount', {
                        id: this.clientDetails.id
                    });
                    if (!success) {
                        this.$showError(result);
                    } else {
                        this.$message.success('主子户头同步成功！');
                    }
                } catch (e) {

                } finally {
                    this.$refs.confirmDialog.hide();
                }
            },
            async effectAccount() {
                try {
                    this.$utils.showLoading('提交中...');
                    const data = await this.$http.post('action/link/accnt/openAccount', {id: this.clientDetails.id}, {
                        handleFailed: (error)=>{
                            this.$utils.hideLoading()
                        }
                    });
                    if (data.success){
                        this.$utils.hideLoading()
                        this.$taro.showToast({title: '该客户已生效', icon: 'none', duration: 2000});
                    } else {
                        this.$nav.error(`操作失败:${data.message}`);
                    }
                } catch(e) {
                    this.$nav.error(`操作失败:${e}`);
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/9/25
             * @description 跳转到新建界面;国窖需要校验，校验通过-跳转，校验不通过-不跳转，特曲，直接跳转界面
             */
            async quotaApplyCheck(param){
                let formData = {
                    acctName: this.clientDetails.acctName,
                    id: this.clientDetails.id,
                    acctCode: this.clientDetails.acctCode,
                    acctCategory: this.clientDetails.acctCategory,
                    acctLevel: this.clientDetails.acctLevel,
                    acctType: this.clientDetails.acctType,
                    salesmanCityId: this.clientDetails.salesmanCityId,
                    salesmanAreaId: this.clientDetails.salesmanAreaId,
                    multiAcctFlag: this.clientDetails.multiAcctFlag,
                    creditNo: this.clientDetails.creditNo
                };
                if (param === 'InvoiceExchange') {
                    const text = await this.checkData('发票换配额');
                    if (this.isGuojiao && !text) {
                        this.$utils.showAlert('当前未开放发票换配额', {
                            icon: 'none'
                        })
                    }else{
                        this.$nav.push('/pages/terminal/quota/new-quota-page', {pageSource: 'InvoiceExchange',init: 'terminal',clientDetails: formData})
                    }
                }else{
                    const text = await this.checkData('普通配额');
                    if (this.isGuojiao && !text) {
                        this.$utils.showAlert('当前未开放配额申请', {
                            icon: 'none'
                        })
                    }else{
                        this.$nav.push('/pages/terminal/quota/new-quota-page', {pageSource: 'Common',init: 'terminal',clientDetails: formData})
                    }
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/9/25
             * @methods checkData
             * @description 校验配置表
             */
            async checkData(value){
                let nowDate = this.$date.format(new Date(), 'YYYY-MM-DD');
                //   当前用户的品牌公司代码为“5600时”，点击列表新建按钮弹出的“普通配额”选项时校验：当前用户的
                // 【组织片区ID】在配置表中是否存在当天处于【申请开始时间】与【申请结束之间】的，且【配额类型】
                //   包含“普通配额”的记录，有则弹出新建页面，无则弹窗提示：当前未开放配额申请
                const param ={
                    filtersRaw:
                        [
                            {id: "orgId", property: "orgId", value: this.userInfo.coreOrganizationTile.l5Id, operator: "="},
                            {id: "quotaType", property: "quotaType", value: value, operator: "like"},
                            {id: "applyStartDate", property: "applyStartDate", value: nowDate, operator: "<="},
                            {id: "applyEndDate", property: "applyEndDate", value: nowDate, operator: ">="}]
                };
                const data = await this.$http.post('action/link/quotaConfig/queryByExamplePage',param );
                if (!data.success) {
                    this.$utils.showAlert('查询配置表出错！', {icon: 'none'});
                    return false
                }
                return data.rows.length !== 0;
            },
            /**
             * 门头照片预览
             * <AUTHOR>
             * @date 2020-09-22
             * @param param
             */
            async previewStoreUrl(param) {
                const that = this;
                if (!that.$utils.isEmpty(param.storePicKey) && param.acctType === 'Terminal') {
                    let imgUrl = await this.$image.getSignedUrl(param.storePicKey);
                    const inOptions = {
                        current: imgUrl,
                        urls: [imgUrl]
                    };
                    that.$image.previewImages(inOptions)
                } else {
                    const inOptions = {
                        current: that.$imageAssets.terminalDefaultImage,
                        urls: [that.$imageAssets.terminalDefaultImage]
                    };
                    that.$image.previewImages(inOptions)
                }
            },
            /**
             * 查询子户头的数据
             * <AUTHOR>
             * @date 2020-09-22
             */
            queryChildAccount () {
                const that = this;
                this.accntIdArr = [];
                const url = this.pageParam.fromNewPage
                    ? 'action/link/accnt/queryAcctByPerDataPage'
                    : 'action/link/accnt/queryByExamplePage'
                const oauth =  this.pageParam.fromNewPage
                    ? 'ALL'
                    : this.detailsOauth
                this.$http.post(url, {
                    filtersRaw: [
                        {id: 'acctType', property: 'acctType', value: '[Terminal, Distributor]', operator: 'in'},
                        {id: 'multiAcctMainId', property: 'multiAcctMainId', value: this.clientDetails.id, operator: '='},
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                    ],
                    oauth,
                }).then(data => {
                    if (data.success) {
                        that.licenseHeadId = [];
                        data.rows.forEach(item => {
                            if(this.isDinghao && this.pageParam.claimList){
                              that.licenseHeadId.push(item.relatedAcctId);
                            }else{
                              that.licenseHeadId.push(item.id);
                            }
                            that.accntIdArr.push(item.id);
                            that.ncAccntCodeArr.push(item.ncAccntCode);
                            that.$set(item, 'billTitleImgArr', []);
                            that.$set(item, 'iDCardArr', []);
                        });
                      if(this.isDinghao && this.pageParam.claimList){
                        that.licenseHeadId.push(that.clientDetails.relatedAcctId);
                      }else{
                        that.licenseHeadId.push(that.clientDetails.id);
                      }
                        that.financialArr = [that.clientDetails, ...data.rows];
                        that.getImgKeyList();
                        this.$nextTick(() => {
                            this.$bus.$emit("initQueryApprovePhoto");//通知财务组件重新查询开户附件
                        })
                        that.accntIdArr.push(this.clientDetails.id);
                        that.ncAccntCodeArr.push(this.clientDetails.ncAccntCode);
                    }
                })
            },
            /**
             * 财务更新成功之后，更新基本信息
             * <AUTHOR>
             * @date 2020-09-22
             */
            async updateSuccess (id, queryChildFlag = true) {
                if(this.$utils.isEmpty(id)){
                    return;
                }
                const that = this;
                await this.$http.post('action/link/accnt/queryById', {
                    id: id,
                    // 是否需要外部系统失败原因字段
                    synExternalSysInfo: 'needSynContent'
                }).then(async data => {
                    if (data.success) {
                        if (!that.$utils.isEmpty(data.result.storePicPreKey) && data.result.acctType === 'Terminal') {
                            let imgUrl = await that.$image.getSignedUrl(data.result.storePicPreKey);
                            that.$set(data.result, 'storeUrl', imgUrl);
                        }
                        that.$set(data.result, 'billTitleImgArr', []);
                        that.$set(data.result, 'iDCardArr', []);
                        that.clientDetails = data.result;
                        if(queryChildFlag) that.queryChildAccount();
                    }
                })
            },
            /**
             * 获取更新图片后的新内容
             * <AUTHOR>
             * @date 2023/09/20
             * @params {Array} oldArr 原数据
             *  @params {Object} newObj 请求数据
             */
            getNewArr(oldArr,newObj){
                let newArr = []
                let inArr = oldArr.find(item=>{
                    return item.id == newObj.id
                })
                if(inArr){
                    newArr = oldArr
                }else{
                    newArr = oldArr.concat([newObj])
                }
                return newArr
            },
            /**
             * 获取图片营业执照照片
             * <AUTHOR>
             * @date 2020-07-09
             * @editby 谭少奇
             * @editDate 2023/09/20
             * 数据更新防止图片重复显示处理
             */
            async getImgKeyList () {
                const that = this;
                // 避免全量查询附件
                if (this.$utils.isEmpty(that.licenseHeadId)) {
                    return;
                }
                let moduleTypeArr = '[IDCardFront, IDCardBack, billTitlePhoto, T_INFO_FINANCIAL_ATTACHMENT]'
                if (this.isShiJiaZhuang) moduleTypeArr = '[IDCardFront, IDCardBack, billTitlePhoto, T_INFO_FINANCIAL_ATTACHMENT, customerInformation, copyBusinessLicense,openingPermit,invoicingData,copyBankCard]'
                let filtersRaw = [
                    {id: 'moduleType', property: 'moduleType', value: moduleTypeArr, operator: 'in'},
                    {id: 'headId', property: 'headId', value: `[${that.licenseHeadId}]`, operator: 'in'}
                ];
                that.$http.post('action/link/attachment/queryByExamplePage', {
                    filtersRaw: filtersRaw,
                    uploadType: 'cos',
                    pageFlag: false,
                    sort: 'created',
                    order: 'desc',
                    queryFields: 'id,uploadType,attachmentPath,moduleType,headId,dataSource,created,smallurl'
                }).then((data) => {
                    if (data.success) {
                        that.financialAttachment =  data.rows;
                        that.financialArr.forEach(i=>{
                            i.annexArr = []
                        })
                        data.rows.forEach(async item => {
                            if (item.moduleType === 'IDCardBack') {
                                let imgUrl = await this.$image.getSignedUrl(item.smallurl);
                                let opt = {
                                    imgUrl: imgUrl,
                                    ...item
                                };
                              let index = null
                              if(that.isDinghao && this.pageParam.claimList){
                                 index = that.financialArr.findIndex(val => val.relatedAcctId === item.headId);
                              }else{
                                 index = that.financialArr.findIndex(val => val.id === item.headId);
                              }
                              that.financialArr[index].iDCardArr = that.getNewArr(that.financialArr[index].iDCardArr,opt)
                            }
                            if (item.moduleType === 'IDCardFront') {
                                let imgUrl = await this.$image.getSignedUrl(item.smallurl);
                                let opt = {
                                    imgUrl: imgUrl,
                                    ...item
                                };
                              let index = null
                              if(that.isDinghao && this.pageParam.claimList){
                                index = that.financialArr.findIndex(val => val.relatedAcctId === item.headId);
                              }else{
                               index = that.financialArr.findIndex(val => val.id === item.headId);
                              }
                              that.financialArr[index].iDCardArr = that.getNewArr(that.financialArr[index].iDCardArr,opt)
                                // that.financialArr[index].iDCardArr = that.financialArr[index].iDCardArr.concat([opt]);
                            }
                            if (item.moduleType === 'billTitlePhoto')  {
                                let imgUrl = await this.$image.getSignedUrl(item.smallurl);
                                let opt = {
                                    imgUrl: imgUrl,
                                    ...item
                                };
                              let index = null
                              if(that.isDinghao && this.pageParam.claimList){
                                index = that.financialArr.findIndex(val => val.relatedAcctId === item.headId);
                              }else{
                                index = that.financialArr.findIndex(val => val.id === item.headId);
                              }
                              that.financialArr[index].billTitleImgArr = that.getNewArr(that.financialArr[index].billTitleImgArr,opt)
                            }
                            if (item.moduleType === 'T_INFO_FINANCIAL_ATTACHMENT')  {
                                let imgUrl = await this.$image.getSignedUrl(item.smallurl);
                                let opt = {
                                    imgUrl: imgUrl,
                                    ...item
                                };
                              let index = null
                              if(that.isDinghao && this.pageParam.claimList){
                                index = that.financialArr.findIndex(val => val.relatedAcctId === item.headId);
                              }else{
                                index = that.financialArr.findIndex(val => val.id === item.headId);
                              }
                                that.financialArr[index].annexArr = that.financialArr[index].annexArr.concat([opt]);
                            }
                            if(Object.keys(this.shiJiaZhuangType).includes(item.moduleType)) {
                                let imgUrl = await this.$image.getSignedUrl(item.smallurl);
                                let opt = {
                                    imgUrl: imgUrl,
                                    ...item
                                };
                                const index = that.financialArr.findIndex(val => val.id === item.headId);
                                const arrType = item.moduleType + 'Arr'
                                if(!that.financialArr[index][arrType]) that.$set(that.financialArr[index], arrType, [])
                                that.financialArr[index][arrType].push(opt)
                            }
                        });
                    }
                })
            },
            /**
             * 校验基础数据
             * <AUTHOR>
             * @date	2024/3/13 16:51
             */
            checkBasic() {
                try {
                    let msg = '';
                    const data = this.clientDetails;
                    let acctType = data.acctType;
                    let acctCategoryVal = data.acctCategory;
                    let subAcctTypeVal = data.subAcctType;
                    if (this.$utils.isEmpty(data.acctType)) {
                        msg = '未维护客户分类，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && (this.$utils.isEmpty(data.acctType) || this.$utils.isEmpty(data.acctCategory) || this.$utils.isEmpty(data.subAcctType))) {
                        msg = '未维护客户分类，';
                        return msg;
                    }
                    if (this.isCheckShiJiaZhuang) {
                        if (this.$utils.isEmpty(data.acctSort)) {
                            msg = '未维护客户类型，';
                            return msg;
                        }
                        if (this.$utils.isEmpty(data.acctNature)) {
                            msg = '未维护客户性质，';
                            return msg;
                        }
                    }
                    if (acctCategoryVal === 'qdlx-2' && this.$utils.isEmpty(data.kaSystemName)) {
                        msg = '未维护系统名称，';
                        return msg;
                    }
                    if (acctCategoryVal === 'qdlx-2' && subAcctTypeVal !== 'LianSuoBianLi' && this.$utils.isEmpty(data.xAttr71)) {
                        msg = '未维护店号，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.acctName)) {
                        msg = '未维护门头名称，';
                        return msg;
                    }
                    if (acctType === 'Distributor' && this.$utils.isEmpty(data.acctName)) {
                        msg = '未维护分销商名称，';
                        return msg;
                    }
                    if (acctCategoryVal !== 'GroupBuy' && this.$utils.isEmpty(data.province) && this.$utils.isEmpty(data.city) && this.$utils.isEmpty(data.district)) {
                        msg = '未维护所在地区，';
                        return msg;
                    }
                    if (acctCategoryVal !== 'GroupBuy' && this.$utils.isEmpty(data.address)) {
                        msg = '未维护门店地址详细地址，';
                        return false;
                    }
                    if (acctType === 'Terminal' && this.storeHeadLength === 0 ) {
                        msg = '未维护门头照片，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.isExclusiveShop)){
                        msg = '未维护是否泸州老窖官方形象店，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.isShareHolderShop)){
                        msg = '未维护是否专营公司股东门店,';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.chainStoreFlag)) {
                        msg = '未维护是否为连锁模式，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.doorSigns)) {
                        msg = '未维护门头店招，';
                        return msg;
                    }
                    if (acctType === 'Terminal'&& data.doorSigns==='JingPin' && this.$utils.isEmpty(data.competitiveGoodsType)){
                        msg = '未维护竞品类型，';
                        return msg;
                    }
                    if (this.isJiaoLing) {
                        if(acctType === 'Terminal' && this.$utils.isEmpty(data.channelMemberFlag)) {
                            msg = '未维护是否为精英荟会员,'
                            return msg;
                        }
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.ownStores)) {
                        msg = '未维护是否经销商自有门店，';
                        return msg;
                    }
                    if (data.chainStoreFlag === 'Y' && data.chainHeadStoreFlag === 'N' && this.$utils.isEmpty(data.chainHeadStoreName)) {
                        msg = '未维护连锁总店名称，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.acctLevel)) {
                        if(!this.isChuanDongOrXingJi || data.accntPartner === 'cooperation') {
                            msg = '未维护客户规划等级，';
                            return msg;
                        }
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.capacityLevel)) {
                        if(!this.isChuanDongOrXingJi || data.accntPartner === 'cooperation') {
                            msg = '未维护容量级别,';
                            return msg;
                        }
                    }
                    if (data.judgmentFlag === 'Y' && this.$utils.isEmpty(data.imageRoomNum)) {
                        msg ='未维护形象包间数，';
                        return msg;
                    }
                    if (acctCategoryVal === 'qdlx-4' && this.$utils.isEmpty(data.roomTotalNum)) {
                        msg = '未维护包间总数，';
                        return msg;
                    }
                    if (acctCategoryVal === 'qdlx-4' && this.$utils.isEmpty(data.hallTotalNum)) {
                        msg = '未维护大厅总数，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.xAttr50)) {
                        msg = '未维护店区位，';
                        return msg;
                    }
                    if (acctType === 'Terminal' && this.$utils.isEmpty(data.area)) {
                        msg = '未维护店面积，';
                        return msg;
                    }
                    if (this.$utils.isNotEmpty(data.deliveryProvince) && this.$utils.isNotEmpty(data.deliveryCity) && this.$utils.isNotEmpty(data.deliveryDistrict) && this.$utils.isEmpty(data.deliveryDetailAddr)) {
                        msg = '未维护收货地址详细地址，';
                        return msg;
                    }
                    return msg;
                } catch (e) {
                    console.log('e', e);
                }
            },
            /**
             * 财务信息前往认证
             * <AUTHOR>
             * @date 2020-09-21
             * @param param
             */
            goAttestation () {
                // 鼎昊公司和整合终端页面来源不可认证
                if(this.isDinghao || this.parentTerminal){
                    return;
                }
                // 校验基础信息
                const basicMsg = this.checkBasic();
                if (basicMsg) {
                    this.$dialog({
                        title: '提示',
                        content: '您' + basicMsg + '是否前往维护再进行认证',
                        cancelButton: true,
                        onConfirm: () => {
                            this.checkEdit((success) => {
                                if (success) {
                                    this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                        data: this.clientDetails,
                                        editFlag: 'edit'
                                    });
                                }
                            });
                        }
                    });
                    return false;
                }
                // 所售产品校验
                if (this.sellProductOption.list.filter(item => item.status === 'Y').length === 0) {
                    this.$dialog({
                        title: '提示',
                        content: '您尚未维护该终端的所售产品，是否前往维护再进行认证',
                        cancelButton: true,
                        onConfirm: () => {
                            this.checkEdit((success) => {
                                if (success) {
                                    this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                        data: this.clientDetails,
                                        editFlag: 'edit',
                                        // 展示下标为 1的所售产品页签
                                        index: 1
                                    });
                                }
                            });
                        }
                    });
                    return;
                }
                if (this.isChuanDong && this.clientDetails.accntPartner !== 'cooperation') {
                    this.$dialog({
                        title: '提示',
                        content: '未合作客户不可添加有效的所售产品，是否前往维护再进行认证',
                        cancelButton: true,
                        initial: true,
                        onConfirm: () => {
                            this.checkEdit((success) => {
                                if (success) {
                                    this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                        data: this.clientDetails,
                                        editFlag: 'edit'
                                    });
                                }
                            });
                        }
                    });
                    return;
                }
                // 终端联系人校验
                let contactInfoFlag = false;//是否需要弹框校验联系人列表有数据，或维护的联系人数据是否完整。
                let msg = "您尚未维护该终端的联系人，";
                if (this.contactList.length === 0) {
                    contactInfoFlag = true;
                } else if (this.contactList.length === 1 && this.contactList[0].isEffective !== 'Y'){
                    contactInfoFlag = true;
                    msg = "您尚未维护该终端有效的联系人，"
                } else {
                    //lzlj-002-3346校验手机号
                    contactInfoFlag = false;
                    const reg = /^[1][3456789][0-9]{9}$/;
                    const b = this.contactList.filter((item) => !reg.test(item.mobilePhone) && item.isEffective === 'Y')
                    if(b.length > 0) {
                        contactInfoFlag = true;
                        msg = "联系人号码格式不正确!"
                    }
                    const a = this.contactList.filter((item1) => item1.isEffective === 'Y' && (this.$utils.isEmpty(item1.contactsName) || this.$utils.isEmpty(item1.mobilePhone) || this.$utils.isEmpty(item1.contactsType)));
                    if(!this.$utils.isEmpty(a)){
                        contactInfoFlag = true;
                        msg = "您维护的联系人数据：姓名、联系电话、角色职务未完善，"
                    }
                }
                if (contactInfoFlag) {
                    this.$dialog({
                        title: '提示',
                        content: msg + '是否前往维护再进行认证',
                        cancelButton: true,
                        confirmText: '确认',
                        onConfirm: () => {
                            this.checkEdit((success) => {
                                if (success) {
                                    this.$nav.push('/pages/terminal2/edit-construction/edit-approve-page', {
                                        data: this.clientDetails,
                                        editFlag: 'edit',
                                        // 展示下标为 2的联系人页签
                                        index: 2
                                    });
                                }
                            });
                        }
                    });
                    return;
                }
                this.$nav.push('/pages/terminal2/new-construction/new-construction-page', {
                    data: this.clientDetails,
                    editFlag: 'edit',
                    attestation: 'attestation',
                    activeNum: 3,
                    accntId: this.clientDetails.id,
                    title: '财务信息',
                    sellProductFlag: this.sellProductOption.list.length === 0,
                    financialAttachment: this.financialAttachment,
                    onlyAddSubAccount: true
                });
            },
            /**
             * 监控页面返回参数
             * <AUTHOR>
             * @date 2020-08-31
             * @param param
             */
            async onBack(param) {
                if (param) {
                    if(param.refreshFlags){
                        this.getHis()
                    }
                    if(param.quota){
                        this.$refs.yearAgreement && await this.$refs.yearAgreement.guoJiaoOptions.methods.reload();
                    }
                    if(param.jiaolingQuota){
                        this.$refs.yearAgreement && this.$refs.yearAgreement.$refs.jiaoling && this.$refs.yearAgreement.$refs.jiaoling.jiaolingOptions.methods.reload();
                    }
                    // 更新所售产品列表
                    if (param.refreshSellFlag) {
                        await this.sellProductOption.methods.reload();
                        if (!this.$utils.isEmpty(param.acctData)) {
                            if (!this.$utils.isEmpty(param.acctData.storePicPreKey)) {
                                let urlData = this.$image.getSignedUrl(param.acctData.storePicPreKey);
                                this.$set(param.acctData, 'storeUrl', urlData);
                            } else {
                                this.$set(param.acctData, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                            }
                            this.clientDetails = param.acctData;
                        }
                    }
                    // 刷新地址列表
                    if(param.addressFlag){
                        this.$refs.addressRef && this.$refs.addressRef.addressOption.methods.reload()
                    }
                    // 更新联系人列表
                    if (param.refreshContacts) {
                        this.$refs.contacts && await this.$refs.contacts.contactsOptions.methods.reload();
                    }
                    // 更新融资列表
                    if (param.refreshYrFinancing) {
                        this.$refs.yrFinancing && await this.$refs.yrFinancing.yrOption.methods.reload();
                    }
                    // 刷新基础和财务信息
                    if (param.refreshFlag) {
                        this.updateSuccess(this.clientDetails.id)
                    }
                    // 订单列表刷新
                    if (param.orderListRefresh) {
                        if (!this.$utils.isEmpty(this.$refs.order.orderData)) {
                            await this.$refs.order.orderData.methods.reload();
                        }
                    }
                    // 更新协议列表
                    if (param.refreshAgreement) {
                        this.$refs.agreement && await this.$refs.agreement.agreementOptions.methods.reload();
                        this.updateSuccess(this.clientDetails.id)
                    }
                    // 更新协议列表
                    if (param.refreshYearAgreement) {
                        this.$refs.yearAgreement && await this.$refs.yearAgreement.agreementOptions.methods.reload();
                    }
                    if(param.isProdConfirm) {
                        //重新查询一下是因为首次添加子公司产品会更改财务信息状态
                        this.updateSuccess(this.clientDetails.id)
                        //手动修改这2个固定的字段，由于其他信息并未做改变，且编辑页面内没用到，故不等查询结束直接跳转
                        const financialInfo = JSON.parse(JSON.stringify(this.financialArr[0]))
                        financialInfo.auditStatus = 'new'
                        financialInfo.acctStage = 'xk'
                        this.$nav.push('/pages/terminal2/edit-construction/edit-financial-page', {
                            data: financialInfo,
                            editFlag: 'edit',
                            mainAcctData: this.clientDetails
                        })
                    }
                    if (param.refreshFlag ==='refresh') {
                        //保存后刷新
                        this.$refs.inventoryRef.reFresh()
                    }
                }
                this.showFabButtonFlag = false;
            },
            /**
             * 获取.top-content高度
             * <AUTHOR>
             * @date 2020-08-05
             */
            getTopContentHeight () {
                const that = this
                setTimeout(function () {
                    const query = wx.createSelectorQuery();
                    query.select('.top-content').boundingClientRect((ret) => {
                        // edit by 谭少奇 2023/11/28 修复阿里云平台部分用户打开高度异常报错
                        if(ret){
                            that.topHeight = ret.height + ret.top
                        }else{
                            that.topHeight = 154
                        }
                    }).exec()
                }, 200)
            },
            /**
             * 导航栏切换
             * <AUTHOR>
             * @date 2020-04-02
             */
            async switchTab(val, flag) {
                switch (flag) {
                    case 'status':
                        this.tapsActive = val;
                        break;
                    case 'dropdown':
                        this.tapsActive = val;
                        this.showDropdownFlag = false;
                        break;
                }
            },
            /**
             * 获取基础信息模板信息
             * <AUTHOR>
             * @date 2020-08-04
             * @param param
             */
            get360Template () {
              return new Promise(async (resolve) => {
                  const data = await this.$utils.getQwMpTemplate('TerminalAcct360');
                  if (data.success) {
                      let resultOpt = JSON.parse(data.result);
                      let res = JSON.parse(resultOpt.conf)
                      const userInfo = Taro.getStorageSync('token').result
                      let positionArr = [
                          'SalesSupervisor',         // 业务主管
                          'Salesman',                // 业务代表
                          'VipManager',              // VIP经理
                      ];
                      let flag = positionArr.includes(userInfo.positionType) && userInfo.coreOrganizationTile.l5Id === '96900004282531112';
                      //lzlj-002-2846成都国窖业代、主管、VIP经理看不到资金账户
                      if(this.claimFlag) { //复制终端详情360展示
                          resolve(res.filter((item) => ['basicInfo','contacts','financial','address-list'].includes(item.values.code)));
                      } else if (flag) {
                          resolve(res.filter((item) => item.base.label !== '资金账户'))
                      } else {
                          resolve(res)
                      }
                  } else {
                      this.templateDataFlag = true;
                  }
                });
            },
            /**
             * @createdBy  张丽娟
             * @date  2020/11/20
             * @methods fetchmultiAcctMainIds
             * @para
             * @description 返回住户头及子户头数组字符串
             */
            async fetchmultiAcctMainIds(){
                const url = this.pageParam.fromNewPage
                    ? 'action/link/accnt/queryAcctByPerDataPage'
                    : 'action/link/accnt/queryByExamplePage'
                const oauth =  this.pageParam.fromNewPage
                    ? 'ALL'
                    : this.detailsOauth
                const param = {
                    multiAcctMainId: this.clientDetails.id,
                    oauth,
                    pageFlag: false
                }
                const data = await this.$http.post(url, param);
                if (!data.success) {
                    this.$utils.showAlert('查询配置表出错！', {icon: 'none'});
                    return false
                }
                let ids = data.rows.map((item)=>{
                    return item.id
                })
                if(ids.length>0){
                    this.multiAcctMainIds = '['+ ids.join(',')+']';
                    this.acctIds=data.rows.map((item)=>{
                        return item.id -0
                    });
                }else{
                    this.multiAcctMainIds = '['+ this.clientDetails.id+']';
                    this.acctIds=[this.clientDetails.id-0];
                }
            },
        }
    }
</script>

<style lang="scss">
    .client-details-page {
        /*deep*/.link-fab-button {
        background: linear-gradient(128deg, rgba(47, 140, 248, 0.8) 12%, rgba(47, 105, 248, 0.8) 70%);
    }
        .top-container {
            .comp-navbar {
                width: 100vw;
                .placeholder-bar{
                    background-color: transparent;
                    width: 100%;
                    display: -webkit-box;
                    display: -ms-flexbox;
                    display: flex;
                    -webkit-box-pack: start;
                    -ms-flex-pack: start;
                    justify-content: flex-start;
                    -webkit-box-align: center;
                    -ms-flex-align: center;
                    align-items: center;
                    .icon-left {
                        width: 10%;
                        font-size: 34px;
                        color: #FFFFFF;
                        padding-left: 24px;
                    }
                    .navigator-back {
                        width: 46px;
                        height: 46px;
                        padding-left: 24px;
                    }
                    .bar-title {
                        width: 82%;
                        font-size: 34px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                        text-align: center;
                    }
                }
            }
            .top-content {
                @include flex;
                .store-image {
                    margin-left: $margin-normal;
                    margin-top: 32px;
                    width: 128px;
                    height: 128px;
                    border-radius: 16px;
                    overflow: hidden;
                    box-shadow: 0 7px 49px 0 rgba(20,28,51,0.39);
                    image {
                        width: 100%;
                        height: 100%;
                    }
                }
                .store-content-cover {
                    width: 80%;
                    .store-content-code {
                        margin-left: 24px;
                        color: #fff;
                        font-size: 28px;
                        line-height: 28px;
                    }
                    .title-level-code {
                        @include flex-start-center;
                        @include space-between;
                        width: 100%;
                        .store-content-top {
                            @include flex-start-center;
                            @include space-between;
                            margin-left: 24px;
                            .store-title {
                                font-family: PingFangSC-Semibold,serif;
                                font-size: 32px;
                                color: #ffffff;
                                letter-spacing: 0;
                                line-height: 40px;
                                max-width: 370px;
                            }
                            .store-level {
                                width: 120px;
                                height: 44px;
                                margin-left: 12px;
                                image {
                                    width: 100%;
                                    height: 100%;
                                }
                            }
                        }
                        .qr-code {
                            width: 80px;
                            height: 44px;
                            padding-right: 24px;
                            padding-left: 12px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                    .store-content-middle {
                        @include flex-start-center;
                        height: 60px;
                        line-height: 60px;
                        margin-left: 24px;
                        flex-wrap: wrap;
                        .store-type {
                            border: 1px solid #ffffff;
                            border-radius: 8px;
                            font-size: 20px;
                            padding-left: 18px;
                            padding-right: 18px;
                            line-height: 36px;
                            color: #ffffff;
                            margin-right: 10px;
                            margin-top: 10px;
                        }
                    }
                }
            }
        }
        .content {
            .top-blank {
                width: 100%;
                background: $color-primary;
                position: fixed;
                top: 0;
                font-family: PingFangSC-Semibold,serif;
                font-size: 34px;
                color: #FFFFFF;
                letter-spacing: 0;
                text-align: center;
                z-index: 9999;
            }
            .tap-container {
                width: 100%;
                display: flex;
                height: 92px;
                overflow: hidden;
                .lnk-tabs::-webkit-scrollbar {
                    display:none
                }
                .lnk-tabs {
                    overflow-x: scroll;
                    white-space: nowrap;
                    border-top: 1px solid #f2f2f2;
                    display: flex;
                    background-color: #fff;
                    color: #595959;
                    width: 670px;
                    z-index: 9999;
                    &.marginTop {
                        margin-top: 80px;
                    }
                    .active {
                        color: $color-primary;
                    }
                    .lnk-tabs-item {
                        height: 92px;
                        line-height: 92px;
                        text-align: center;
                        .label-name {
                            width: 100%;
                            font-size: 28px;
                            margin-left: 10px;
                        }
                        .line {
                            height: 8px;
                            width: 56px;
                            border-radius: 16px 16px 0 0;
                            background-color: $color-primary;
                            box-shadow: 0 3px 8px 0 rgba(47,105,248,0.63);
                            margin: -12px auto auto auto;
                        }
                    }
                }
                .dropdown {
                    right: 0;
                    position: relative;
                    width: 92px;
                    height: 92px;
                    background: #FFFFFF;
                    box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16) !important;
                    .dropdown-icon {
                        line-height: 92px;
                        text-align: center;
                        font-size: 36px;
                        color: #595959;
                    }
                    /*deep*/.link-dropdown-reference {
                                box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                            }
                    /*deep*/.link-dropdown-content {
                                border-radius: 0 0 32px 32px;
                            }
                    /*deep*/.link-dropdown {
                                box-shadow: -3px 0 28px 0 rgba(7,44,105,0.16)!important;
                            }
                    .dropdown-container {
                        @include flex-start-center();
                        @include wrap();
                        border-radius: 0 0 32px 32px;
                        padding-bottom: 28px;
                        .menu-item {
                            width: 25%;
                            @include flex-center-center();
                            @include direction-column();
                            .menu-icon {
                                color: $color-primary;
                                font-size: 48px;
                                padding-top: 28px;
                                padding-bottom: 30px;
                            }
                            .menu-name {
                                font-family: PingFangSC-Regular,serif;
                                font-size: 28px;
                                color: #595959;
                                letter-spacing: 0;
                                text-align: center;
                                line-height: 28px;
                                padding-bottom: 28px;
                            }
                        }
                    }
                }
            }
            .customer-label-view {
                .content {
                    width: 702px;
                    margin: 24px auto;
                    overflow: hidden;
                }
            }
            .lnk-tabs-order {
                white-space: nowrap;
                border-top: 1px solid #f2f2f2;
                display: flex;
                background-color: #F2F2F2;
                color: #595959;
                width: 100vw;
                z-index: 9999;
                padding-left: 24px;
                padding-right: 24px;
                &.marginTop {
                    margin-top: 94px;
                }
                .active {
                    color: #595959;
                }
                .lnk-tabs-item {
                    margin-top: 24px;
                    display: inline-block;
                    text-align: center;
                    .label-name-bg {
                        @include flex-center-center();
                        .label-name-on {
                            background: $color-primary;
                            color: #fff;
                            border-radius: 8px;
                            padding-top: 8px;
                            padding-bottom: 8px;
                            width: 95%;
                            font-size: 28px;
                        }
                        .label-name-off {
                            color: #8C8C8C;
                            border-radius: 8px;
                            padding-top: 8px;
                            padding-bottom: 8px;
                            width: 95%;
                            font-size: 28px;
                        }
                    }
                }
            }
            .edit-text {
                padding-bottom: 32px;
                padding-top: 32px;
                text-align: right;
                padding-right: 24px;
                font-family: PingFangSC-Regular,serif;
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                line-height: 28px;
            }

            .financial-components {
                padding-bottom: 68px;
            }
            .capital-loading {
                .loading {
                    font-size: 40px;
                    margin-top: 24px;
                    width: 100%;
                    text-align: center;
                    /*deep*/.link-loading-tag {
                    margin: auto;
                }
                }
                .capital-loading-text {
                    margin-top: 10px;
                    font-size: 24px;
                    color: #999;
                    width: 100%;
                    text-align: center;
                }
            }
            .subsidiary-picker {
                background: #ffffff;
                border-radius: 8px;
                width: 702px;
                margin: 24px auto auto auto;
                padding-top: 24px;
                padding-bottom: 24px;
                text-align: center;
                .picker-text {
                    font-size: 28px;
                    color: #000000;
                    font-weight: bold;
                    display: inline-block;
                }
                .icon-down {
                    position: absolute;
                    padding-top: 10px;
                    font-size: 28px;
                    color: #2F69F8;
                    display: inline-block;
                }
            }
        }
        .qr-code-container {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255,255,255,0.90);
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .lose-qrCode {
                image {
                    width: 100%;
                    height: 100%;
                }
            }

            .change-store {
                padding-top: 24px;
                text-align: center;
                font-size: 28px;
                padding-bottom: 60px;
                color: #595959;
                white-space: nowrap;
                .icon-qiehuan {
                    font-size: 28px;
                    color: #2F69F8;
                }
                .change-store-text {
                    color: #2F69F8;
                    border-radius: 8px;
                    padding-left: 12px;

                }
            }
            .tips-text {
                padding-top: 24px;
                text-align: center;
                font-size: 28px;
                color: #595959;
            }
            .icon-close-circle {
                width: 100%;
                text-align: center;
                font-size: 44px;
                color: #595959;
                padding-top: 60px;
            }
        }

        .link-fab-group {
            bottom: 45px !important;

            .link-fab-item-wrapper-list .link-fab-item-wrapper {
                margin-top: 10PX !important;
            }

            .link-fab-item-wrapper-list {
                max-height: calc(100vh - 350px) !important;
                flex-wrap: wrap-reverse !important;
                left: unset !important;
                align-items: flex-start;

                .link-fab-item-label {
                    position: static !important;
                    margin: 0 24px !important;
                }
            }

            /* prettier-ignore */
            $margin: 16PX;
            /* prettier-ignore */
            $itemSize: 44PX;
            &:not(.link-fab-group-show) {
                &,& .link-fab-item {
                    pointer-events: none !important;
                }
                .link-fab-item-wrapper {
                    /*兼容20个按钮的样式*/
                    @for $i from 11 through 20 {
                        &.link-fab-item-wrapper-#{$i} {
                            transform: translate3d(0, ($itemSize+$margin)*$i, 0);
                            opacity: 0;
                            pointer-events: none;

                            &, & .link-fab-item-label, & .link-fab-item-icon {
                                transition-delay: (0.02s*$i);
                            }
                        }
                    }
                }
            }
        }
    }
</style>
