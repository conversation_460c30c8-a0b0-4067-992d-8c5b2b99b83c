<template>
    <link-page class="dealer-map-list-page">
        <!--搜索-->
        <link-search-input class="search-input" @change="searchStoreName" placeholder="经销商编码/经销商名称/实时定位地址">
            <view class="search-container" @tap="backList">
                <link-icon class="icon" icon="icon-unorderedlist"/>
                <view>列表</view>
            </view>
        </link-search-input>
        <view class="tips-content" style="font-size: 12px;margin-left: 12px;">
            <link-icon icon="icon-icon-tip"/> 此处仅展示存在实时定位地址的经销商
        </view>
        <!--地图-->
        <map id="map"
             :longitude="longitude"
             :latitude="latitude"
             :scale="scale"
             show-location="true"
             show-compass="true"
             show-scale="true"
             class="map-content"
             :style="{'height': mapHeight + 'px'}"
             :setting="setting"
             @controltap="controltap"
             :markers="markers"
             @markertap="markerTap">
            <cover-view :style="{'margin-top': mapHeight - (pageParam.source === 'parentTerminalList' ? 120 : 60) +'px' }">
				<cover-view marker-id="2" class="location-aim-container2" v-if="pageParam.source === 'parentTerminalList'">
				    <!-- <link-fab-button :bottom='500'  @tap="toAddCensus"/> -->
					<cover-view class="location-aim" marker-id="1" @tap="toAddCensus">
					    +
					</cover-view>
				</cover-view>
                <cover-view class="location-aim-container"
                            @tap="backCurrentPosition">
                    <cover-view class="location-aim" marker-id="1">
                        <cover-image class="aim-image" :src="$imageAssets.locationAimImage"></cover-image>
                    </cover-view>
                </cover-view>

            </cover-view>
        </map>
        <!--经销商列表-->
        <scroll-view class="store-list-container"
                     scroll-y="true"
                     id="list-store"
                     v-show="!storeDetailsFlag"
                     @scrolltolower="scrollToLower"
                     :style="{'height': storeListTotalHeight + 'px'}">
            <view v-for="(data, index) in storeList" :key="index">
                <view class="media-list" :key="index" :data="data"  @tap="goStoreDetails(data, index)">
                    <image class="media-list-logo" :src="data.storeUrl" lazy-load="true"></image>
                    <view class="store-content">
                        <view class="store-content-top">
                            <!--【客户一级分类】为“终端Terminal”的时候显示storeSigns字段-->
                            <view class="store-title" v-if="data.acctType === 'Dealer'">{{data.acctName || data.accountName}}</view>
                            <!--【客户一级分类】为“分销商Distributor”时展示billTitle字段-->
                            <!-- <view class="store-title" v-if="data.acctType === 'Distributor'">{{data.acctName || data.billTitle}}</view> -->
                            <!--已认证-->
                            <view class="store-level" v-if="data.acctStage === 'ykf'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                            <!--未认证-->
                            <view class="store-level" v-if="data.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                            <!--已失效-->
                            <view class="store-level" v-if="data.acctStage === 'ysx'"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                        
                        </view>
                        <view class="store-content-address">
                            <view class="store-address"><text v-if="data.distance">{{data.distance}}米 | </text>{{data.orieLocation}}</view>
                        </view>
                    </view>
                </view>
            </view>
        </scroll-view>
        <!--终端详情-->
        <view class="store-details" v-show="storeDetailsFlag">
            <view class="icon-back" @tap="backListMap">
                <view class="iconfont icon-left"></view>
            </view>
            <view class="store-item">
                <image class="media-list-logo" :src="storeOptions.storeUrl"></image>
                <view class="store-content">
                    <view class="store-content-top">
                        <view class="store-title">{{storeOptions.acctName}}</view>
                        <!--已认证-->
                        <view class="store-level" v-if="storeOptions.acctStage === 'ykf'"><image :src="$imageAssets.storeStatusVerifiedImage"></image></view>
                        <!--未认证-->
                        <view class="store-level" v-if="storeOptions.acctStage === 'xk'"><image :src="$imageAssets.storeStatusUnverifiedImage"></image></view>
                        <!--已失效-->
                        <view class="store-level" v-if="storeOptions.acctStage === 'ysx'"><image :src="$imageAssets.storeStatusInvalidationImage"></image></view>
                     
                    </view>
                    <view class="store-content-middle">
                        <view class="store-type" v-if="typeList.includes(storeOptions.acctType)">{{storeOptions.acctType | lov('ACCT_TYPE')}}</view>
                        <view class="store-type" style="width: 85px;" v-if="categoryLst.includes(storeOptions.acctCategory)">{{storeOptions.acctCategory | lov('ACCNT_CATEGORY')}}</view>
                        <view class="store-type-sub" v-if="sublist.includes(storeOptions.subAcctType)">{{storeOptions.subAcctType | lov('SUB_ACCT_TYPE')}}</view>
                        <view class="store-type-detail" v-if="levelList.includes(storeOptions.acctLevel) || caplist.includes(storeOptions.capacityLevel)">
                            <text v-if="levelList.includes(storeOptions.acctLevel)"> {{storeOptions.acctLevel | lov('ACCT_LEVEL')}}</text>
                            <text v-if="levelList.includes(storeOptions.acctLevel) && caplist.includes(storeOptions.capacityLevel)"> | </text>
                            <text v-if="caplist.includes(storeOptions.capacityLevel)">{{storeOptions.capacityLevel | lov('CAPACITY_LEVEL')}}</text>
                        </view>
                        <view class="store-type" v-if="storeOptions.coreTerminalFlag === 'Y'">核心</view>
                        <view class="store-type" v-if="storeOptions.sampleTerminalFlag === 'Y'">样本</view>
                    </view>
                    <view class="store-content-address">
                        <view class="store-address"><text>{{storeOptions.distance}}米 | </text>{{storeOptions.orieLocation}}</view>
                    </view>
                </view>
            </view>
            <view class="button">
                <link-button class="button-item" label="路线" icon="icon-luxian" mode="stroke" size="normal" @tap="navGpsLine"/>
                <link-button class="button-item" label="拜访" v-if="pageParam.source !== 'parentTerminalList'"  mode="fill" size="normal" @tap="goVisitPerform(storeOptions)"/>
                <link-button class="button-item" label="查看详情"  mode="fill" size="normal" @tap="goTerminalDetails(storeOptions)"/>
            </view>
        </view>
    </link-page>
</template>

<script>
import PAGES_PATH from '../../../constants/pagesPath';
    export default {
        name: "dealer-map-list-page",
        data() {
            let oauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();
            if (oauth === 'MY_ORG') {
                oauth = 'MULTI_ORG'
            }
            return {
                typeList: [],
                categoryLst: [],
                sublist: [],
                levelList: [],
                caplist:[],
                radius: null, //打卡半径
                coordinate: null,                   // 定位坐标
                listOauth: oauth,
                oauth,
                checkOutOver:false, //超距离离店
                pageNum: 1,
                pageLimit: 20,
                selectIndex: null,
                scale: 17,
                animation: 'display: none;',
                longitude: 0,
                latitude: 0,
                mapHeight: 0,
                nextPageFlag: true,
                searchInputHeight: 0,
                tipsHeight:0,
                storeListTotalHeight: 0,
                page: 1,
                addressData: {},
                addressDataFull: '',
                storeDetailsFlag: false,
                setting: {
                    enable3D: false,
                    enableTraffic: false,
                },
                storeOptions: {},
                markers: [],
                hideCreateButton: false,
                mdmCompanyCodeArr: [],
                temMarker: [],
                storeList: [],                                  // 终端列表
                countMapHeightTimer: null,                      // 计算终端列表容器media-list的view元素高度计时器
                storeDetailsTimer: null, 
            }
        },
        async created () {
            if (this.$utils.isPostnOauth === 'MY_ORG') this.hideCreateButton = false;
            this.getSearchInput();
            await this.getAddress();
            await this.getAccntList();
            if (this.$utils.isEmpty(this.$store.getters['dealerConfigTemplate/getDealerVisitPerformTemp'])) {
                this.$configTemplate.terminalVisitPerformTemp();
            }
            const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
            if(this.$taro.getStorageSync('token').result.coreOrganizationTile.l5Id === chuanDongOrgId) {
                //川东打卡范围缩小至100m
                this.radius = 100
            } else {
                //其他公司-走参数配置
                this.radius = await this.getFirmParam();
            }
            this.getTypeArray();
        },
        destroyed () {
            clearTimeout(this.countMapHeightTimer);
            clearTimeout(this.storeDetailsTimer);
        },
        methods: {
            // 新增普查
            async toAddCensus(){
                this.$nav.push('/pages/dealer/dealer-visit/census-dealer-add-page', {
                    sellProductFlag: true
                })
            },
            async getTypeArray() {
                const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE','ACCT_LEVEL','CAPACITY_LEVEL']);
                list[0].forEach(item => {
                    this.typeList.push(item.val)
                });
                list[1].forEach(item => {
                    this.categoryLst.push(item.val)
                });
                list[2].forEach(item => {
                    this.sublist.push(item.val)
                });
                list[3].forEach(item => {
                    this.levelList.push(item.val)
                });
                list[4].forEach(item => {
                    this.caplist.push(item.val)
                });
            },
            /**
             * 获取系统允许打卡半径范围
             * <AUTHOR> @date 2023-11-28
             */
            getFirmParam () {
                return new Promise(resolve => {
                    if (this.$taro.getStorageSync('radius')) {
                        resolve(this.$taro.getStorageSync('radius'));
                    } else {
                        this.$http.post('action/link/cfgProperty/queryByExamplePage', {
                            key: 'signInLimitDistance'
                        }).then(data => {
                            if (data.success) {
                                this.$taro.setStorageSync('radius', Number(data.rows[0].value));
                                resolve(Number(data.rows[0].value));
                            }
                        })
                    }
                })
            },
            /**
             * 按照门店名称搜索
             * <AUTHOR>
             * @date 2020-11-17
             */
            searchStoreName (val) {
                this.storeDetailsFlag = false;
                this.pageNum = 1;
                this.nextPageFlag = true;
                this.getAccntList(val);
            },
            /**
             * @description: 获取终端列表和对应的地图点位
             * @param {string} param 根据终端名称搜索
             * @Author: <EMAIL>
             * @Date: 2024-07-22 16:17:46
             */
            async getAccntList (param) {
                let that = this
                if (!this.nextPageFlag) return;
                //终端查询接口
                let url = 'export/link/es/simpleaccnt/queryByExamplePage'
                // }
                let filtersRaw = [];
                filtersRaw.push(
                    {id: 'acctType', property: 'acctType', value: '[Dealer]', operator: 'in'},
                    {id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '='},
                    // {id: 'distance', property: 'distance', value: '500', operator: '<='},
                )
                if (param) filtersRaw.push({id: 'acctName', property: 'acctName', value: param, operator: 'LIKE'});


                let data = await that.$httpForm.post(url, {
                    filtersRaw:JSON.stringify(filtersRaw),
                    queryFields: 'id,source,acctType,companyId,acctCode,orieLocation,acctName,billTitle,acctStage,acctCategory,subAcctType,acctLevel,capacityLevel,multiAcctMainFlag,province,city,district,address,storePicKey,storePicPreKey,coreTerminalFlag,sampleTerminalFlag,distance,longitude,latitude',
                    rows: this.pageLimit,
                    page: this.pageNum,
                    longitude: this.longitude,
                    latitude: this.latitude,
                    oauth: this.oauth,
                })

                if (!data.success) {
                    this.$taro.showToast({ title: '服务器报错了',icon:'none',duration: 2000})
                    return
                }
                if (data.total > this.pageLimit * this.pageNum) {
                    this.pageNum++
                } else {
                    this.nextPageFlag = false
                }
                if(this.pageNum == 1){
                    this.storeList = []
                    this.markers = []
                }

                var counter = 0;
                data.rows.forEach(async (item)=>{
                    //处理图片
                    if (!this.$utils.isEmpty(item.storePicPreKey)) {
                        let urlData = await this.$image.getSignedUrl(item.storePicPreKey);
                        this.$set(item, 'storeUrl', urlData);
                    } else {
                        this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage)
                    }
                    // 核心和非核心判断逻辑：
                    // 客户规划等级值为【belowA】且存在父值列类型【BRAND_COM_NAME】，独立源代码为【5600】——国窖/[5137]——特曲 这两个里面为非核心；
                    // 客户规划等级值非【belowA】且存在父值列类型【BRAND_COM_NAME】，独立源代码为【5600】——国窖/[5137]——特曲 这两个里面为核心
                    this.mdmCompanyCodeArr = []
                    let param = {type: 'ACCT_LEVEL', val: item.acctLevel, returnType: 'parentVal'};
                    let parentVal = await this.$lov.getLovByParentVal(param);
                    if (!this.mdmCompanyCodeArr.includes(parentVal)) {
                        this.mdmCompanyCodeArr.push(parentVal)
                    }
                    this.$set(item, 'parentVal', parentVal);

                    // //异步回调中
                    counter++;
                    //  处理等forEach中异步全部执行完毕再 走下一步操作
                    if(counter === data.rows.length){
                        this.storeList = this.storeList.concat(data.rows);
                        let markers = data.rows.map(item=>({
                                iconPath: that.mdmCompanyCodeArr.includes(item.parentVal) && item.acctLevel === 'belowA' ? '/static/images/map-icon/not-core.png'  : '/static/images/map-icon/core.png',
                                id: Number(item.id),
                                latitude: Number(item.latitude),
                                longitude: Number(item.longitude),
                                width: 28,
                                height: 34,
                                label: {
                                    content: item.acctName,
                                    padding: 0,
                                    color: '#262626',
                                    display: 'ALWAYS',
                                    textAlign: 'center',
                                    anchorX: -(item.acctName ? (item.acctName.length * 12) : 0) / 2
                                }
                        }))
                        this.markers = this.markers.concat(markers)
                        this.countMapHeightTimer = setTimeout(function () {
                            that.countMapHeight()
                        }, 200)
                    }
                })

            },
            /**
             * 查询拜访中列表是否含有当前人拜访中的记录
             * <AUTHOR>
             * @date 2020-10-15
             */
            queryUserVisit () {
                const that = this;
                return new Promise(resolve => {
                    that.$http.post('export/link/accntVisit/queryByExamplePage', {
                        filtersRaw: [
                            {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: 'visiting', operator: '='},
                            {id: 'visitType', property: 'visitType', value: 'dailySalesCall', operator: '='},
                            {id: 'createdBy', property: 'createdBy', value: that.$taro.getStorageSync('token').result.id, operator: '='},
                        ],
                        oauth: 'MY_POSTN'
                    }).then(data => {
                        if (data.success) {
                            that.$store.commit('visitRecord/setVisitRecord', data.rows[0]);
                            resolve(data.rows[0]);
                        }
                    })
                });
            },
            /**
             * 校验拜访采集必输模块
             * <AUTHOR>
             * @date 2020-11-18
             * @param visitId 拜访id
             * @param accntId 终端id
             */
            async verifyMustCollect (visitId, accntId) {
                const that = this;
                let templateVisitData = this.$store.getters['dealerConfigTemplate/getDealerVisitPerformTemp']
                that.mustInputArr = templateVisitData.filter(item => item.base.require === true);
                let verifyPass = [];
                let i = 0;
                return new Promise(resolve => {
                    templateVisitData.forEach(item => {
                        if (item.base.require) {
                            that.$http.post(item.values.queryUrl, {
                                visitId: visitId,
                                accntId: accntId
                            }).then(data => {
                                i++;
                                if (data.rows.length !== 0) {
                                    item.verifyMustFlag = true;
                                    verifyPass.push(item);
                                    if (i === that.mustInputArr.length) {
                                        resolve(verifyPass)
                                    }
                                } else {
                                    if (i === that.mustInputArr.length) {
                                        resolve(verifyPass)
                                    }
                                }
                            })
                        }
                    });
                })
            },
            //超距弹窗
            checkOutDialog(visitId, visitTime, accntId){
                if(this.checkOutOver){
                    this.$dialog({
                        title: '提示',
                        content: '您已超距离离店是否强制结束拜访',
                        cancelButton: true,
                        initial: true,
                        cancelText: '取消',
                        confirmText: '确定',
                        onConfirm:() => {
                            this.forceClose(visitId, visitTime, accntId)
                        },
                        onCancel: () => {
                        }
                    });
                }else{
                    this.forceClose(visitId, visitTime, accntId)
                }
            },
            /**
             * 强制关闭当前拜访
             * <AUTHOR>
             * @date 2020-11-18
             * @param visitId 拜访
             * @param visitTime 拜访开始时间
             * @param visitTime 拜访开始时间
             * @param terminalData 拜访开始时间
             */
            async forceClose(visitId, visitTime, terminalData) {
                const that = this;
                let dateTime = new Date(await that.$utils.getTimestamp());
                let visitEndTime = this.$date.format(dateTime, 'YYYY-MM-DD HH:mm:ss');                                                                 // 打卡时间转换格式
                let visitDuration = await this.$utils.getYMDHMS(Date.parse(new Date(visitTime.replace(/-/g, '/'))), Date.parse(new Date(visitEndTime.replace(/-/g, '/'))));    // 计算拜访时长
                this.$http.post('action/link/accntVisit/closeVisit', {
                    id: visitId,
                    visitDuration: visitDuration,
                    visitEndTime: visitEndTime
                }, {
                    handleFailed(error) {
                    }
                }).then(async data => {
                    if (data.success) {
                        let opt = {
                            input: {id: visitId, visitDuration: visitDuration, visitEndTime: visitEndTime},
                            output: data
                        };
                        this.updatePunchCardRecord('forceClose');
                        that.$bus.$emit('visitCreatedSuccess');
                        let userBrandCompanyCode = that.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode;
                        let param = {type: 'ACCT_LEVEL', val: terminalData.acctLevel, returnType: 'parentVal'};
                        let parentVal = await that.$lov.getLovByParentVal(param);
                        that.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                            data: terminalData,
                            noStoreCoreFlag: userBrandCompanyCode === parentVal && terminalData.acctLevel === 'belowA',
                            source: 'terminalMap',
                            createdVisitOriginal: 'terminalMapList'
                        });
                    }
                })
            },
            /**
             * 插入离店记录
             * <AUTHOR>
             * @date 2020-09-06
             */
            async updatePunchCardRecord(flag, terminalData) {
                const that = this;
                let visitRecord = that.$store.getters['visitRecord/getVisitRecord'];
                let insetOptions = {
                    signInType: 'checkOut',
                    province: that.addressData.province,
                    city: that.addressData.city,
                    district: that.addressData.district,
                    address: `${that.addressData.street}${that.addressData.street_number}`,
                    longitude: that.longitude,
                    latitude: that.latitude,
                    signInStatus: 'normal',
                    acctId: visitRecord.accntId,
                    headId: visitRecord.id,
                    visitStatus: flag === 'forceClose' ? 'N' : 'Y'
                };
                that.$http.post('action/link/accntSignInDetails/insert', insetOptions).then((data) => {
                    if (data.success && flag === 'closeVisit') {
                        that.updateVisitStatus(terminalData) // 更新当前拜访中记录
                    }
                })
            },
            /**
             * 更新列表当前列表状态
             * <AUTHOR>
             * @date 2020-09-01
             */
            async updateVisitStatus(terminalData) {
                const that = this;
                let visitRecord = that.$store.getters['visitRecord/getVisitRecord'];
                let timestamp = await this.$utils.getTimestamp();                                                                                                             // 服务器获取当前打卡结束时间
                let visitEndTime = this.$date.format(new Date(timestamp), 'YYYY-MM-DD HH:mm:ss');                                                                             // 打卡时间转换格式
                let visitStartTime = that.$utils.isEmpty(visitRecord.visitTime) ? visitRecord.created.replace(/-/g, '/') : visitRecord.visitTime.replace(/-/g, '/');          // 拜访时间不存在时用创建时间
                let visitDuration = await this.$utils.getYMDHMS(Date.parse(new Date(visitStartTime)), Date.parse(new Date(visitEndTime.replace(/-/g, '/'))));                 // 计算拜访时长
                visitRecord.visitApplicationStatus = 'visited';                                                                                                               // 修改拜访状态
                visitRecord.visitEndTime = visitEndTime;                                                                                                                      // 拜访结束时间
                visitRecord.visitDuration = visitDuration;                                                                                                                    // 拜访时长
                that.$http.post('action/link/accntVisit/update', visitRecord).then(async (data) => {
                    if (data.success) {
                        that.$bus.$emit('visitCreatedSuccess');                                                                                                               // 广播事件用于刷新拜访列表
                        let userBrandCompanyCode = that.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode;
                        let param = {type: 'ACCT_LEVEL', val: terminalData.acctLevel, returnType: 'parentVal'};
                        let parentVal = await that.$lov.getLovByParentVal(param);
                        that.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                            data: terminalData,
                            noStoreCoreFlag: userBrandCompanyCode === parentVal && terminalData.acctLevel === 'belowA',
                            source: 'terminalMap',
                            createdVisitOriginal: 'terminalMapList'
                        });
                    }
                });
            },
            /**
             * 校验当前用户的拜访中记录数据
             * <AUTHOR>
             * @date 2020-11-18
             * @param data 终端详情
             */
            async verifyVisit(data) {
                const that = this;
                // 根据用户当前的品牌公司代码 === 父值列表类型的独立源代码的
                let queryUserVisit = await that.queryUserVisit();
                let userBrandCompanyCode = that.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode;
                let param = {type: 'ACCT_LEVEL', val: data.acctLevel, returnType: 'parentVal'};
                let parentVal = await this.$lov.getLovByParentVal(param);
                if (that.$utils.isEmpty(queryUserVisit)) {
                    that.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                        data: data,
                        noStoreCoreFlag: userBrandCompanyCode === parentVal && data.acctLevel === 'belowA',
                        source: 'terminalMap',
                        createdVisitOriginal: 'terminalMapList'
                });
                    return
                }else{
                    //计算距离
                    let distance = await this.$locations.getDistance(Number(that.coordinate.latitude), Number(that.coordinate.longitude), Number(queryUserVisit.latitude), Number(queryUserVisit.longitude));
                    this.checkOutOver= distance* 1000 > this.radius;
                }
                let verifyPassArr = await that.verifyMustCollect(queryUserVisit.id, queryUserVisit.accntId);        // 拜访执行必输采集校验
                wx.showModal({
                    title: '提示',
                    content: '您有未完成的拜访，是否要结束上次拜访？',
                    cancelColor: '#2F69F8',
                    confirmColor: '#2F69F8',
                    confirmText: '结束拜访',
                    success (res) {
                        if (res.confirm) {
                            if (verifyPassArr.length !== that.mustInputArr.length) {
                                wx.showModal({
                                    title: '提示',
                                    content: '上次拜访有必填项尚未填写，您可以强制关闭或者继续执行上次拜访',
                                    cancelText: '强制关闭',
                                    cancelColor: '#2F69F8',
                                    confirmColor: '#2F69F8',
                                    confirmText: '继续执行',
                                    success (res) {
                                        if (res.confirm) {
                                            if(queryUserVisit.acctStatus==='N'){
                                                that.$showError('当前终端已被冻结，请生效后操作！');
                                                return;
                                            }
                                            that.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                                                data: queryUserVisit,
                                                source:'viewDetail'
                                            })
                                        } else if (res.cancel) {
                                            let visitStart = that.$utils.isEmpty(queryUserVisit.visitTime) ? queryUserVisit.created : queryUserVisit.visitTime;
                                            // that.forceClose(queryUserVisit.id, visitStart, data)
                                            that.checkOutDialog(queryUserVisit.id, visitStart, data)
                                        }
                                    }
                                })
                            } else {
                                that.updatePunchCardRecord('closeVisit', data);     // 结束拜访插入离店记录
                            }
                        } else if (res.cancel) {
                        }
                    }
                });
            },
            /**
             * 查看详情
             * <AUTHOR>
             * @date 2020-08-27
             * @param data
             */
            goTerminalDetails (data) {
                // if(this.pageParam.source === 'parentTerminalList'){
                //     this.$nav.push('/pages/dealer/dealer-visit/parent-dealer-detail-page.vue', {
                //         oauth: this.listOauth,
                //         data
                //     });
                // }else{
                //     this.$nav.push('/pages/dealer/dealer-visit/dealer-client-details-page', {
                //         data: data,
                //         newFlag: this.pageParam.newFlag,
                //         listOauth: this.listOauth
                //     })
                // }
                data.tempUpdateStatusFlag = true;
                 this.$nav.push(PAGES_PATH.DEALER_DETAILS_PAGE, {data:data})

            },
            /**
             * 获取终端联系人
             * <AUTHOR>
             * @date 2022-11-4
             * @param data 终端信息
             */
            async getContactList(data) {
                const {rows} = await this.$http.post('action/link/contacts/listByAcctId', {
                    pageFlag: true,
                    onlyCountFlag: false,
                    filtersRaw: [],
                    oauth: 'ALL',
                    sort: 'id',
                    order: 'desc',
                    attr1: data.id,
                });
                return rows
            },
            /**
             * 拜访执行
             * <AUTHOR>
             * @date 2020-08-27
             * @param data
             */
            async goVisitPerform (data) {
                if(this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '1612') {
                    const contactList = await this.getContactList(data) || []
                    const flag = contactList.some((item => {
                        return item.isEffective === 'Y'
                    }))
                    if(!flag) {
                        this.$dialog({
                            title: '提示',
                            content: '请完善联系人信息',
                            initial: true,
                            confirmText: '确认'
                        });
                        return
                    }
                }
                const that = this;
                if (that.pageParam.source === 'terminalList') {
                    this.verifyVisit(data)
                } else {
                    that.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-perform-page', {
                        data: data,
                        noStoreCoreFlag: that.mdmCompanyCodeArr.includes(data.parentVal) && data.acctLevel === 'belowA',
                        pageFlag: that.pageParam.pageFlag,
                        createdVisitOriginal: that.$utils.isEmpty(that.pageParam.createdVisitOriginal) ? 'terminalMapList' : that.pageParam.createdVisitOriginal
                    });
                }
            },
            /**
             * 计算高度
             * <AUTHOR>
             * @date 2020-08-04
             */
            countMapHeight () {
                const that = this;
                if (this.searchInputHeight === 0) this.getSearchInput();
                if (this.tipsHeight === 0) this.getTipsHeight();
                const query = wx.createSelectorQuery();
                query.select('.media-list').boundingClientRect((ret) => {
                    if (that.storeList.length < 4) {
                        that.mapHeight = that.$device.systemInfo.safeArea.height - ret.height * that.storeList.length - 70 - that.searchInputHeight - that.tipsHeight;
                        that.storeListTotalHeight = ret.height  * that.storeList.length;
                    } else if (that.$device.systemInfo.windowHeight < 680){
                        that.mapHeight = that.$device.systemInfo.safeArea.height - ret.height * 3 - 70 - that.searchInputHeight - that.tipsHeight;
                        that.storeListTotalHeight = ret.height  * 3;
                    } else {
                        that.mapHeight = that.$device.systemInfo.safeArea.height - ret.height * 3 - 70 - that.searchInputHeight - that.tipsHeight;
                        that.storeListTotalHeight = ret.height  * 3;
                    }

                }).exec()
            },
            /**
             * 详情返回列表
             * <AUTHOR>
             * @date 2020-08-04
             */
            async backListMap() {
                const that = this
                this.storeDetailsFlag = false;
                that.markers = that.temMarker;
                that.markers[that.selectIndex].width = 28;
                that.markers[that.selectIndex].height = 34;
                that.countMapHeightTimer = setTimeout(function () {
                    that.countMapHeight()
                }, 200);
                await this.getAddress()
            },
            /**
             * 获取终端详情块高度
             * <AUTHOR>
             * @date 2020-08-19
             */
            getStoreDetailsHeight () {
                const that = this
                if (that.storeDetailsFlag) {
                    that.storeDetailsTimer = setTimeout(function () {
                        const query = wx.createSelectorQuery();
                        query.select('.store-details').boundingClientRect((ret) => {
                            that.mapHeight = that.$device.systemInfo.windowHeight - ret.height - that.searchInputHeight- that.tipsHeight - (that.$device.systemInfo.statusBarHeight * 2);
                        }).exec()
                    }, 200)
                }
            },
            /**
             * 获取搜索框高度
             * <AUTHOR>
             * @date 2020-08-19
             */
            getSearchInput () {
                const that = this;
                setTimeout(function () {
                    const query = wx.createSelectorQuery();
                    query.select('.search-input').boundingClientRect((ret) => {
                        that.searchInputHeight = ret.height
                    }).exec()
                }, 200)
            },
            getTipsHeight () {
                const that = this;
                setTimeout(function () {
                    const query = wx.createSelectorQuery();
                    query.select('.tips-content').boundingClientRect((ret) => {
                        that.tipsHeight = ret.height
                    }).exec()
                }, 200)
            },
            /**
             * 门店详情
             * <AUTHOR>
             * @date 2020-08-03
             * @param data
             * @param index
             */
            goStoreDetails (data, index) {
                const that = this;
                that.storeDetailsFlag = true;
                that.storeOptions = data;
                that.getStoreDetailsHeight();
                that.longitude = Number(data.longitude);
                that.latitude = Number(data.latitude);
                that.temMarker = that.$utils.deepcopy(that.markers);
                that.markers[index].width = 42;
                that.markers[index].height = 51;
                that.markers = [that.markers[index]];
                that.selectIndex = index;
            },
            /**
             * 导航
             * <AUTHOR>
             * @date 2020-08-04
             */
            navGpsLine () {
                const that = this;
                wx.openLocation({
                    latitude: Number(that.storeOptions.latitude),
                    longitude: Number(that.storeOptions.longitude),
                    scale: 16,
                    name: that.storeOptions.acctName,
                    address: `${that.storeOptions.province}${that.storeOptions.city}${that.storeOptions.district}${that.storeOptions.address}`
                })
            },
            /**
             * 回到当前定位位置
             * <AUTHOR>
             * @date 2020-08-03
             */
            async backCurrentPosition() {
                await this.getAddress();
                this.scale = 17;
            },
            /**
             * 触底函数
             * <AUTHOR>
             * @date 2020-08-03
             * @param e
             */
            scrollToLower (e) {
                this.getAccntList()
            },
            /**
             * 返回门店列表
             * <AUTHOR>
             * @date 2020-08-02
             * @param param
             */
            backList () {
                if (this.pageParam.pageFlag === 'visitListPage') {
                    this.$nav.redirect('/pages/dealer/dealer-visit/es-dealer-list-page', {pageFlag: 'visitListPage'})
                } else {
                    this.$nav.back()
                }

            },
            /**
             * 获取当前位置的地址信息
             * <AUTHOR>
             * @date 2020-07-14
             */
            async getAddress () {
                let coordinate = await this.$locations.getCurrentCoordinate();
                if (!this.$utils.isEmpty(coordinate.latitude) && !this.$utils.isEmpty(coordinate.longitude)) {
                    this.longitude = Number(coordinate.longitude);
                    this.latitude = Number(coordinate.latitude);
                    this.coordinate = coordinate;
                    let address =  await this.$locations.reverseTMapGeocoder(this.latitude, this.longitude, '终端地图页面');
                    this.addressData = address['originalData'].result;
                    this.addressDataFull = address['originalData'].result.formatted_address
                }
            },
            async changeMarker (key) {
                const that = this
                that.markers.forEach((item, index) => {
                    if(index !== key) {
                        that.$set(item, 'width', 28)
                        that.$set(item, 'height', 32)
                    } else {
                        that.$set(item, 'width', 42)
                        that.$set(item, 'height', 51)
                    }
                })
            },
            async markerTap(e) {
                const that = this
                let opt = this.storeList.filter(item => Number(item.id) === e.markerId);
                that.storeOptions = opt[0];
                that.storeDetailsFlag = true;
                that.getStoreDetailsHeight();
                that.selectIndex = that.markers.findIndex(item => Number(item.id) === e.markerId);
                await that.changeMarker(that.selectIndex)
                that.longitude = Number(opt[0].longitude);
                that.latitude = Number(opt[0].latitude);
            },
            controltap(e) {}
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .dealer-map-list-page {
        background-color: #ffffff;
        .search-container {
            padding-left:24px;
            color: #8C8C8C;
            font-size: 28px;
            text-align: center;
        }
        .map-content {
            width: 100%;
            .location-aim-container {
                @include flex-end-end;
                margin-right: 24px;
                margin-top: 16px;
                .location-aim {
                    width: 72px;
                    height: 72px;
                    border-radius: 50%;
                    background: #FFFFFF;
                    box-shadow: 0 5px 9px 0 rgba(11,33,85,0.14);
                    text-align: center;
                    .aim-image {
                        margin: 11px auto;
                        width: 50px;
                        height: 50px;
                    }
                }
            }
			.location-aim-container2 {
				@include flex-end-end;
			    margin-right: 24px;
			    margin-top: 16px;
			    .location-aim {
			        width: 100px;
			        height: 100px;
			        border-radius: 50%;
					background: #2f69f8;
					color:white;
					font-size: 60px;
					line-height: 100px;
					text-align: center;

			    }
			}
        }
        .store-list-container {
            background: #ffffff;
            border-radius: 32px 32px 0 0;
            .media-list {
                @include flex;
                border-bottom: 1px solid #F2F2F2;
                .media-list-logo {
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                    margin: 32px 0 32px 24px;
                }
                .store-content {
                    width: 80%;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        margin-top: 56px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 32px;
                        }
                        .store-level {
                            margin-right: 24px;
                            width: 145px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }
                    .store-content-address {
                        margin-left: 24px;
                        margin-top: 20px;
                        font-family: PingFangSC-Regular,serif;
                        font-size: 24px;
                        color: #8C8C8C;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
            }
        }
        .store-details {
            background: #FFFFFF;
            .store-item{
                .store-content-middle{
                    flex-wrap: wrap;
                    .store-type-sub{
                        min-width: 115px;
                        height: 40px;
                        text-align: center;
                        border: 1px solid #2F69F8;
                        border-radius: 8px;
                        font-size: 20px;
                        line-height: 40px;
                        color: #2F69F8;
                        margin-right: 10px;
                    }
                }
            }
            .icon-back {
                @include flex-start-center;
                width: 100%;
                padding-left: 24px;
                padding-bottom: 24px;
                padding-top: 24px;
                .icon-left {
                    color: #ffffff;
                    font-size: 36px;
                    background: rgba(0,0,0,0.40);
                    width: 50px;
                    height: 50px;
                    line-height: 50px;
                    border-radius: 50%;
                    text-align: center;
                }
            }
            .store-item {
                background: #FFFFFF;
                border-radius: 32px 32px 0 0;
                padding-bottom: 40px;
                padding-left: 24px;
                @include media-list();
                /*deep*/.store-type-detail {
                white-space: nowrap;
                border: 1px solid #2F69F8;
                border-radius: 8px;
                font-size: 20px;
                padding-left: 18px;
                padding-right: 18px;
                line-height: 40px;
                height: 40px;
                color: #2F69F8;
                margin-right: 10px;
            }
                .store-content-address {
                    color: #8C8C8C!important;
                    line-height: 32px!important;
                }
            }
            .button {
                padding-bottom: 68px;
                padding-left: 24px;
                padding-right: 24px;
                @include flex-start-center;
                @include space-between;
                .button-item {
                    width: 218px;
                    height: 72px;
                }
            }
        }
    }
</style>
