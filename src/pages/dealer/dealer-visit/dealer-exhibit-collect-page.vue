<!--陈列采集-->
<template>
    <link-page class="dealer-exhibit-collect-page" ref="dealerExhibitCollectPage">
        <view class="last-time-collect" v-if="lastTimeCollectFlag">
            <view class="iconfont icon-time-circle"></view>
            <view class="last-collect-text">上次采集</view>
            <view class="last-time">{{formData.created | date('YYYY-MM-DD HH:mm')}} {{formData.createdName}}</view>
        </view>
        <link-form ref="form" :value="formData" :rules="formRules" :disabled="!isEditFlag">
            <link-form-item class="display-title" label="陈列品项" disabled @tap="addDisplay">
                <!-- <link-lov :multiple="true" type="PROD_BUS_S_CLASS" v-model="formData.operateItem"/> -->
                <view class="display-line">
                    <view v-for="item of selectedDisplay" :key="item.id">{{ item.prodName }}</view>
                </view>
            </link-form-item>
            <link-form-item label="陈列方式">
                <link-lov type="DISPLAY_WAY" v-model="formData.displayWay"/>
            </link-form-item>
            <link-form-item label="陈列面(瓶)" :arrow="false">
                <!-- <link-number-keyboard placeholder="请输入陈列面" v-model="formData.displayArea"/> -->
                <link-input placeholder="请输入陈列面" v-model="formData.displayArea" disabled/>
            </link-form-item>
            <link-form-item label="是否堆头">
                <link-switch v-model="formData.duitouFlag"/>
            </link-form-item>
            <link-form-item label="促销物料" :arrow="false" >
                <view class="add-btn" v-if="pageParam.sourcePage!=='onlyView'" @tap="addMaterial">添加</view>
            </link-form-item>
            <view class="list">
                <list>
                    <link-swipe-action v-for="(item,index) in materialList" :key="index">
                        <link-swipe-option slot="option" @tap="materialDelete(item,index)" v-if="pageParam.sourcePage!=='onlyView'">删除
                        </link-swipe-option>
                        <item :arrow="false">
                            <view class="material-list" slot="note">
                                <view class="material-name">{{item.promotionMaterial | lov('PROMOTION_MATERIAL')}}</view>
                                <link-number v-model="item.promotionMaterialNum" :min="0"/>
                            </view>
                        </item>
                    </link-swipe-action>
                </list>
            </view>
            <link-form-item label="投放日期">
                <link-date view="YMD"  displayFormat="YYYY-MM-DD" v-model="formData.placementTime"/>
            </link-form-item>
            <view class="remark-content">
                <view class="text-title">备注</view>
                <textarea cols="30" rows="10"  	:disabled="!isEditFlag" class="text-area" v-model="formData.comments" placeholder="请输入备注" placeholder-style="color: #BFBFBF;"></textarea>
            </view>
            <view class="image-title">
                陈列采集照
            </view>
            <view class="img-container" v-if="isEditFlag || pageParam.sourcePage==='onlyView'">
                <lnk-img-watermark v-if="$utils.isNotEmpty(parentId)"
                                 :parentId="parentId"
                                 moduleType="display"
                                 :delFlag="delFlag && pageParam.sourcePage!=='onlyView'"
                                 :album="false"
                                 :continueFlag="true"
                                 :rootId="rootId"
                                 :pathKeyArray="attachDisplay"
                                 :useModuleName="useModuleName"
                                 moduleName="陈列采集"
                                 :displayCustomizedWatermark="true"
                                 @canDel="canDel"
                                 @uploadImgPromise="uploadImgPromise"
                                 @imgUploadSuccess="imageArrLength"
                                 @imgDeleteSuccess="imgDeleteSuccess"
                                 :newFlag="newFlag && pageParam.sourcePage!=='onlyView'"></lnk-img-watermark>
            </view>
            <view class="display-basics-column">
                <view class="image-title">
                    其他佐证材料<link-alert icon="mp-info" status="error" class="inline-tips">协议类照片建议进行竖拍</link-alert>
                </view>
                <lnk-img-watermark v-if="$utils.isNotEmpty(parentId) && isEditFlag"
                                   :parentId="parentId"
                                   moduleType="otherSupport"
                                   :delFlag="true"
                                   :album="false"
                                   :continueFlag="true"
                                   :rootId="rootId"
                                   :pathKeyArray="attachOther"
                                   :useModuleName="useModuleName"
                                   :displayCustomizedWatermark="true"
                                   moduleName="其他佐证材料"
                                   @imgUploadSuccess="otherImageArrLength"
                                   @imgDeleteSuccess="otherImageArrLength"
                                   :newFlag="newFlag"></lnk-img-watermark>
            </view>
            <!-- 扫码按钮 -->
            <view class="scan-wrap" v-if="isEditFlag">
                <view class="iconfont icon-saoma1" @tap="scanCode"></view>
            </view>
            <!-- 扫码详情 -->
            <display-scan-code :head-id="formData.id"
                               source="display"
                               ref="scanCode"
                               :disabled="!isEditFlag"
                               v-if="formData.id"
                               @updateDisScan="getDisplaySacn"/>
            <!-- 陈列明细 -->
            <display-ocr v-if="formData.id"
                         ref="disOcr"
                        :rootId="pageParam.visitId"
                        :recognitionResult="recognitionResult"
                        :head-id="formData.id"
                        :disabled="!isEditFlag"
                        :acctData="pageParam.data"
                        @confirmProd="confirmProd"
                        @changeDisplayNum="changeDisplayNum"/>
        </link-form>
        <view class="blank"></view>
        <link-sticky v-if="isEditFlag">
            <link-button block @tap="submit" size="large" :shadow="shadow" autoLoading>提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script lang="jsx">
import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark';
import {DateService, LovService} from 'link-taro-component';
import DisplayScanCode from './components/dealer-display-scan-code.vue';
import {env} from "../../../../env";
import DisplayOcr from './components/dealer-display-ocr.vue';

export default {
    name: "dealer-exhibit-collect-page",
    components: {
        LnkImgWatermark,
        DisplayScanCode,
        DisplayOcr
    },
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            const agreementOauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth(); //查询协议安全性调整
            // 促销物料
            const materialOption = new this.AutoList(this, {
                url: {
                    queryByExamplePage: 'action/link/basic/queryByExamplePage'
                },
                param: {
                    filtersRaw: [
                        {id: "type", property: "type", value: "PROMOTION_MATERIAL"},
                    ],
                },
                hooks: {
                    beforeLoad(option) {
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} class="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view class="select-left">
                                <view class="store-name1">{data.name}</view>
                            </view>
                        </item>
                    )
                }
            });
            // 陈列品项
            const displayItemOption = new this.AutoList(this, {
                url: {
                    queryByExamplePage: '/action/link/saleCategory/queryByExamplePage'
                },
                param: {
                    filtersRaw: [
                        {id: "status", property: "status", value: "Y"},
                        {id: "accntId", property: "accntId", value: this.pageParam.data.id },
                    ],
                },
                hooks: {
                    beforeLoad(option) {
                    },
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} class="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem slot="thumb"></link-checkbox>
                            <view class="select-left">
                                <view class="store-name1">{data.prodName}</view>
                            </view>
                        </item>
                    )
                }
            });
            return {
                // 陈列ai识别结果
                recognitionResult: [],
                selectedDisplay:[],
                userInfo, // 用户信息
                isDealer: false, // 是否走特定公司的经销商人员安全性
                //促销物料数组
                materialList:[],
                beforeMaterList:[],
                //促销物料选择
                materialOption,
                displayItemOption, // 陈列品项
                excludeData: this.pageParam.excludeBrandData,
                chooseProdFlag: false, // 是否开启选择产品弹框
                scannedCode: '', // 扫描的码
                addressInfo: {}, // 地址信息
                coordinate: {}, // 定位信息
                isEditFlag: this.pageParam.isEditFlag,
                shadow: true,
                lastTimeCollectFlag: false,
                parentId: '',
                attachmentList:[],                                                                           // 模块名称
                useModuleName: this.pageParam.data.acctName,                                                        // 使用模块名称
                rootId: this.pageParam.visitId,                                                                     // 来源id即拜访id
                delFlag: true,                                                                                      // 删除标志
                newFlag: true,                                                                                      // 添加标志
                formData: {},
                beforeEditData: {},
                formRules: {},
                upLoadImage: [],
                otherSupportImage: [],
                changeArr: [],
                matchTypeColor: { // 有效性状态显示颜色
                    Y: '#2F68F7', // 有效-蓝色
                    N: '#FF0000' // 无效-红色
                },
                attachOther: [], // 其他佐证材料图片
                attachDisplay: [], // 陈列采集图片
                // 已识别过的图片id
                ocrImgIds: [],
            }
        },
        async created () {
            this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
            await this.queryExhibitCollectInfo();
            await this.getAddressInfo();
        },
        watch: {
            'attachmentList': {
                deep: true,
                immediate: true,
                handler(newVal, oldVal){
                    if (!!newVal) {
                        this.attachOther = (newVal || []).filter((item) => item.moduleType === 'otherSupport') || [];
                        this.attachDisplay = (newVal || []).filter((item) => item.moduleType === 'display') || [];
                    }
                }
            }
        },
        methods: {
            /**
             * 上传图片前提示
             * <AUTHOR>
             * @date    2025/1/20 10:39
             */
            async beforeAddImg(callback) {
                callback({
                    tipFlag: false,
                    confirmText: '已确认，点击进行拍照',
                    tip: '拍照确认后就会发起AI识别，请注意以下事项：1.【陈列采集照】仅上传产品陈列照片，其他如协议合同照片等，请在【其他佐证材料照片】处上传；2.AI识别后图片不可删除，请拍照后确认照片上产品清晰无遮挡，再点击✅确认上传图片进行AI识别；3.请注意光线、无遮挡物、不重复拍照！否则影响AI识别结果；'
                });
            },
            /**
             * 修改陈列面数量
             * <AUTHOR>
             * @date    2024/12/23 17:39
             */
            changeDisplayNum(recognitionResult) {
                this.recognitionResult = recognitionResult;
                this.computedTotal();
            },
            /**
             * 确认选择品项
             * <AUTHOR>
             * @date    2024/12/23 17:39
             */
            confirmProd(prodPartCode) {
                this.recognitionResult.push({
                    row_status: 'NEW',
                    source: 'Part',
                    prodPartCode,
                    aiDisplayNum: 0
                });
            },
             /**
             * 获取已识别过的图片
             * <AUTHOR>
             * @date    2024/12/31 17:39
             */
            async getOcrResult() {
                try {
                    this.$utils.showLoading();
                    const {success, imageIds, displayItems} = await this.$http.post('action/link/accntVisitDisplayItemResult/queryVisitItemResult', {
                        visitId: this.rootId
                    });
                    if (success) {
                        if (!this.recognitionResult.length) {
                            displayItems.forEach(item => {
                                item.row_status = 'NEW';
                                item.source = 'AiOcr';
                            });
                            this.recognitionResult = displayItems;
                            this.computedTotal();
                        }
                        this.ocrImgIds = imageIds;
                    }
                } catch (e) {
                    console.log('e', e);
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 识别
             * <AUTHOR>
             * @date    2024/12/4 9:59
             */
            async recognition(imgList) {
                let images = [];
                let imagesId = [];
                imgList.forEach((item) => {
                    images.push({
                        image: item.base64,
                        id: item.id
                    });
                    imagesId.push(item.id);
                });
                try {
                    this.$utils.showLoading();
                    const {success, rows} = await this.$http.post('action/link/accntVisitDisplayItem/aiIdentity', {
                        acctId: this.pageParam.data.id,
                        visitId: this.rootId,
                        headId: this.formData.id,
                        images: images
                    });

                    if (success) {
                        this.$utils.hideLoading();
                        // 存在则更新AI识别数量，否则push
                        rows.forEach((item) => {
                            const existIndex = this.recognitionResult.findIndex(val => val.prodPartCode === item.prodPartCode);
                            if (existIndex === -1) {
                                item.row_status = 'NEW';
                                item.source = 'AiOcr';
                                this.recognitionResult.push(item);
                            } else {
                                this.recognitionResult[existIndex].aiDisplayNum = item.aiDisplayNum;
                                this.recognitionResult[existIndex].source = 'AiOcr';
                            }
                        });
                        this.computedTotal();
                        this.ocrImgIds = [...this.ocrImgIds, ...imagesId];
                    }
                } catch (e) {
                    console.log('e', e);
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 是否可以删除AI识别图片
             * <AUTHOR>
             * @date    2024/12/23 17:39
             */
            canDel(item, callback) {
                const ocrImgIds = this.ocrImgIds;
                callback({
                    canDelFlag: !ocrImgIds.includes(item.id),
                    delTip: '已识别的图片不允许删除'
                });
            },
            /**
             * 上传图片成功
             * <AUTHOR>
             * @date    2025/1/13 9:35
             */
            uploadImgPromise(data) {
                this.recognition([data.imgList[0]]);
                
            },
            /**
             * 计算总和
             * <AUTHOR>
             * @date    2024/12/24 9:35
             */
            computedTotal() {
                const recognitionResult = this.recognitionResult;
                let displayArea = recognitionResult.reduce((total, item) => total + Number(item.displayNum || 0), 0);
                let aiDisplayNum = recognitionResult.reduce((total, item) => total + Number(item.aiDisplayNum || 0), 0);
                this.$set(this.formData, 'displayArea', displayArea);
                this.$set(this.formData, 'aiDisplayNum', aiDisplayNum);
            },
            /**
             * 陈列是否达标移除
             * <AUTHOR>
             * @date	2024/05/17
             */
            removeCheck(){
            	this.formData.displayVisitCheck = null
            	this.formData.visitApprovalStatus = 'Others'
            },
            /**
             * 获取上次拜访的促销物料
             * <AUTHOR>
             * @date	2023/12/13 17:21
             * @param visitId 上次拜访的id
             * @param headId 本次拜访id
             */
            async queryMaterial(visitId, headId) {
                const {success, rows} = await this.$http.post('action/link/accntVisitDisplayPromotionMaterial/queryByExamplePage', {
                    filtersRaw: [{id: 'visitId', property: 'visitId', value: visitId}]
                });
                if (success) {
                    let materialList = [];
                    for await (let item of rows) {
                        materialList = [
                            ...materialList,
                            {
                                id: await this.$newId(),
                                row_status: 'NEW',
                                headId,
                                promotionMaterial: item.promotionMaterial,
                                promotionMaterialNum: item.promotionMaterialNum
                            }
                        ]
                    }
                    this.materialList = materialList;
                }
            },
            /**
             * @desc
             * <AUTHOR>
             * @date
             * @desc
             **/
            async  getMaterial(){
                const {success, rows} = await this.$http.post('action/link/accntVisitDisplayPromotionMaterial/queryByExamplePage', {
                    filtersRaw: [{id: 'headId', property: 'headId', value: this.parentId}]
                })
                if (success) {
                    this.beforeMaterList=rows;
                    this.materialList = rows;
                    this.materialList.forEach((item) => item.row_status = 'UPDATE');
                }
            },
            async addDisplay(){
                  const list =  await this.$object(this.displayItemOption, {
                    showInDialog: true,
                    pageTitle: '经销商陈列品项',
                    multiple: true,
                    // selected: this.selectedDisplay.map(item => item.id)
                });
                this.selectedDisplay = [...list];

            },
            /**
             * @desc 添加促销物料
             * <AUTHOR>
             * @date
             * @desc
             **/
            async addMaterial(){
                const list =  await this.$object(this.materialOption, {
                    showInDialog: true,
                    pageTitle: '促销物料',
                    multiple: true,
                    selected: this.materialList.map(item => item.id)
                });
                let materialList = this.$utils.deepcopy(this.materialList);
                list.map(async (newItem) => {
                    // 选中的产品小类里是否包含之前选过的产品小类
                    const index = this.materialList.findIndex((oldItem) => oldItem.promotionMaterial === newItem.val);
                    // 没选择过的数据则状态赋值为新建
                    if (index === -1 && newItem) {
                        const id = await this.$newId();
                        materialList.push({
                            id: id,
                            headId: this.parentId,
                            promotionMaterial: newItem.val,
                            row_status: 'NEW',
                            promotionMaterialNum: 0
                        });
                    }
                });
                this.materialList=materialList;
            },
            /**
             * @desc
             * <AUTHOR>
             * @date
             * @desc  删除促销物料
             **/
            async  materialDelete(item, index) {
                if (item.row_status === 'UPDATE') {
                    await this.$http.post('action/link/accntVisitDisplayPromotionMaterial/deleteById', item);
                }
                this.materialList.splice(index, 1);
            },
            /**
             * @desc
             * <AUTHOR>
             * @date
             * @desc
             **/
            async saveMaterial() {
                const {success, result} = await this.$http.post('action/link/accntVisitDisplayPromotionMaterial/batchUpsert', this.materialList);
                if (success) {
                    this.materialList = result;
                    this.materialList.forEach((item) => item.row_status = 'UPDATE');
                }
            },
            /**
             * 选择关联协议后
             * <AUTHOR>
             * @date	2023/7/25 19:40
             */
            async afterSelectProtocol(data) {
                this.formData.placementTime = this.$date.format(data.startTime, 'YYYY-MM-DD');
                // 获取协议关联的产品业务小类
                const {success, rows} = await this.$http.post('action/link/agreementProduct/queryByExamplePage', {
                    filtersRaw: [{id: 'headId', property: 'headId', value: data.id}]
                });
                if (success) {
                    this.formData.operateItem = rows.map((item) => item.productSmallType);
                }
            },

            /**
             * 获取当前地址
             * <AUTHOR>
             * @date	2023/4/26 18:11
             */
            async getAddressInfo() {
                const that = this;
                this.coordinate = await that.$locations.getCurrentCoordinate();
                // 校验用户是否授权地理位置
                if (!this.$utils.isEmpty(this.coordinate.latitude) && !this.$utils.isEmpty(this.coordinate.longitude)) {
                    let address = await that.$locations.reverseTMapGeocoder(this.coordinate.latitude, this.coordinate.longitude, '陈列采集');
                    this.addressInfo = {
                        province: address['originalData'].result.addressComponent['province'],
                        city: address['originalData'].result.addressComponent['city'],
                        district: address['originalData'].result.addressComponent['district'],
                        scanAddress: address['originalData'].result.formatted_address
                    }
                }
            },
            /**
             * 选择产品
             * <AUTHOR>
             * @date	2023/4/26 11:23
             */
            async chooseItem(item) {
                await this.getCodeData({
                    ...this.addressInfo,
                    headId: this.formData.id,
                    mark: this.scannedCode,
                    productId: item.id,
                    source: 'display'  // 陈列拜访
                });
            },
            /**
             * 扫码
             * <AUTHOR>
             * @date	2023/4/25 22:09
             */
            async scanCode() {
                const flag = this.formData['row_status'] !== 'NEW' || this.formData['row_status'] === 'NEW' && await this.submit(false);
                const that = this;
                if (flag) {
                    await wx.scanCode({
                        onlyFromCamera: true, // 只允许从相机扫码
                        success(res) {
                            if (res.result) {
                                that.scannedCode = res.result;
                                that.getCodeData({
                                    ...that.addressInfo,
                                    headId: that.formData.id,
                                    mark: res.result,
                                    source: 'display'  // 陈列拜访
                                });
                            }
                        }
                    });
                }
            },
            /**
             * 获取扫码数据
             * <AUTHOR>
             * @date	2023/4/25 22:54
             */
            async getCodeData(param) {
                try {
                    this.$utils.showLoading();
                    const {success, displayScan, rows} = await this.$http.post('action/link/accntVisitDisplay/visitDisplayScanVerification', param)
                    if (success) {
                        this.$set(this.formData, 'displayScan', displayScan);
                        this.$refs.scanCode && this.$refs.scanCode.scanRecordOption.list.unshift(rows);
                        this.$message.success('扫码成功！');
                        setTimeout(() => {
                            this.scanCode()
                        }, 500)
                    }
                } catch (e) {
                    console.log('扫码错误！');
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 获取陈列扫码总数
             * <AUTHOR>
             * @date	2023/4/27 19:23
             */
            async getDisplaySacn() {
                const {success, result} = await this.$http.post('action/link/accntVisitDisplay/queryById', {
                    id: this.formData.id
                });
                if (success) {
                    this.$set(this.formData, 'displayScan', result.displayScan);
                }
            },
            /**
             * 新建协议
             * <AUTHOR>
             * @date	2023/4/25 21:18
             */
            onTapCreateProtocolButton() {
                this.$nav.push('/pages/terminal/accntVisit/displays-protocol-page.vue', {
                    data: this.pageParam.data
                });
            },
            /**
             * 校验水印相机是否上传完毕
             * <AUTHOR>
             * @date 2022-7-6
             */
            checkWaterImg() {
                let check = this.$refs['dealerExhibitCollectPage'].utils.checkImages();
                if(!check) this.$showError('图片上传中!!')
                return check
            },
            changeVal (val) {
                if (val) {
                    this.changeArr = []
                    this.changeArr = val;
                }
            },

            /**
             * 获取陈列采集图片
             * <AUTHOR>
             * @date 2020-08-31
             * @param param 打卡图片长度
             */
            imageArrLength (param) {
                this.upLoadImage = param;
            },
            imgDeleteSuccess(param) {
                this.upLoadImage = param;
            },
            /**
             * 获取其他佐证材料图片
             * <AUTHOR>
             * @date	2023/4/25 21:33
             */
            otherImageArrLength(param) {
                this.otherSupportImage = param;
            },
            /**
             *  @description: 查询改拜访记录下的陈列采集数据
             *  @author: 马晓娟
             *  @date: 2020/8/26 14:17
             */
            async queryExhibitCollectInfo () {
                const param = {
                    page: 1,
                    pageFlag: true,
                    onlyCountFlag: false,
                    rows: 1,
                    oauth: 'MY_POSTN', // 职位安全性
                    sort: 'created',
                    order: 'desc',
                    attr1: 'queryItem',
                    accntId: this.pageParam.data.id,
                    visitId: this.pageParam.visitId
                };

                if (this.isDealer) param.filtersRaw = [{id: 'postnId', property: 'postnId', value: this.userInfo.postnId}];
                try {
                    this.$utils.showLoading();
                    const data = await this.$http.post('action/link/accntVisitDisplay/queryByExamplePage', param);
                    if (data.success) {
                        if (this.$utils.isEmpty(data.rows)) {
                            this.$utils.showLoading()
                            this.formData = await this.queryDefaultData();
                            this.attachmentList= this.formData.attachmentList
                            this.parentId = this.formData.id;
                            this.$utils.hideLoading();
                        } else {
                            if(data.rows[0]){
                                this.beforeEditData = JSON.parse(JSON.stringify(data.rows[0]));
                            }
                            if (data.rows[0].visitDisplayItemList && data.rows[0].visitDisplayItemList.length) {
                                data.rows[0].visitDisplayItemList.forEach(item => item.row_status = 'UPDATE');
                                this.recognitionResult = data.rows[0].visitDisplayItemList;
                                this.computedTotal()
                            }
                            
                            this.formData = data.rows[0];
                            this.attachmentList=data.rows[0].attachmentList
                            this.formData.row_status = 'UPDATE';
                            this.parentId = this.formData.id;
                            this.lastTimeCollectFlag = true;
                            await this.getMaterial();
                        }
                    }
                    if(this.formData.operateItem){
                        let list = [];
                        if(Array.isArray(this.formData.operateItem)){
                            list = this.formData.operateItem
                        } else {
                            list = JSON.parse(JSON.stringify(this.formData.operateItem));
                        }
                        if(!Array.isArray(list)){
                            list = JSON.parse(list);
                        }
                        this.selectedDisplay = list.map(item => {
                            const temp = item.split(':');
                            return {
                                id: temp[0],
                                prodName: temp[1]
                            }
                        });
                    }

                } catch (e) {
                    console.log(e,'eeee')
                } finally {
                    this.$utils.hideLoading();
                }
            },
            /**
             * 查询拜访下的陈列附件
             * <AUTHOR>
             * @date 2022-6-20
             */
            async displayHistory() {
                //页面拜访ID为空，不允许查询附件信息
                if (!this.pageParam.visitId) {
                    return [];
                }
                const params = {
                    filtersRaw: [
                        {"id": "moduleType", "property": "moduleType", "value": "display"},
                        {"id": "rootId", "property": "rootId", "value": this.pageParam.visitId}
                    ]
                }
                const {rows} = await this.$http.post('action/link/attachment/queryByExamplePage', params)
                return rows
            },
            /**
              * 获取默认值
              * <AUTHOR>
              * @date 2020-09-08
            */
            async queryDefaultData () {
                const that = this;
                return new Promise(resolve => {
                    const param = {
                        page: 1,
                        pageFlag: true,
                        onlyCountFlag: false,
                        rows: 1,
                        oauth: 'MY_POSTN', // 职位安全性
                        sort: 'created',
                        order: 'desc',
                        accntId: that.pageParam.data.id,
                    };
                    if (this.isDealer) {
                        param.filtersRaw = [{id: 'postnId', property: 'postnId', value: this.userInfo.postnId}];
                    }
                    that.$http.post('action/link/accntVisitDisplay/queryByExamplePage', param).then(async data => {
                        //lzlj-002-3229，陈列采集优化，如有未保存的陈列采集数据的附件，也要取
                        const displayList = await this.displayHistory()
                        if (data.success) {
                            if (that.$utils.isEmpty(data.rows)) {
                                let opt = {
                                    id: (displayList[0] && displayList[0].headId) || await that.$newId(),
                                    attachmentList: displayList,
                                    row_status: 'NEW',
                                    duitouFlag: 'N'
                                };
                                resolve({...opt})
                            } else {
                                delete data.rows[0].attachmentList;
                                that.$set(data.rows[0], 'row_status', 'NEW');
                                that.$set(data.rows[0], 'duitouFlag', 'N');
                                that.lastTimeCollectFlag = true;
                                    // 不带出AI识别数量
                                    that.$set(data.rows[0], 'aiDisplayNum', 0);
                                    that.$set(data.rows[0], 'displayArea', 0);
                                
                                data.rows[0]['id'] = (displayList[0] && displayList[0].headId) || await that.$newId();
                                data.rows[0]['attachmentList'] = displayList;
                                if(data.rows[0] && data.rows[0].operateItem){
                                    data.rows[0].operateItem = JSON.parse(data.rows[0].operateItem)
                                    data.rows[0].displayScan = 0
                                }
                            
                                // 获取促销物料
                                await that.queryMaterial(data.rows[0].visitId, data.rows[0].id);
                                resolve(data.rows[0])
                            }
                        } else {
                            this.$message['warn']('请求默认数据失败');
                        }
                    })
                })
            },
            /**
              * 更新数据检查
              * <AUTHOR>
              * @date 1/13/21
              * @param param
            */
            checkedEditData () {
                const result = this.beforeMaterList.length===this.materialList.length&&this.beforeMaterList.sort().toString()===this.materialList.sort().toString();
                return this.formData.operateItem === this.beforeEditData.operateItem && this.formData.displayWay === this.beforeEditData.displayWay &&
                    this.formData.displayArea === this.beforeEditData.displayArea && this.formData.duitouFlag === this.beforeEditData.duitouFlag
                    && result && this.formData.comments === this.beforeEditData.comments;
            },
            /**
             *  @description: 提交数据
             *  @author: 马晓娟
             *  @date: 2020/8/14 10:14
             */
            async submit(back = true) {
                const that = this;
                try {
                    const recognitionResult = this.recognitionResult.map(item => {
                        return {
                            ...item,
                            visitId: item.visitId ? item.visitId : this.pageParam.visitId,
                            headId: item.headId ? item.headId : this.formData.id
                        }
                    });
                    if (recognitionResult.length) {
                        for (let i = 0; i < recognitionResult.length; i++) {
                            const selectedItem = recognitionResult[i];
                            if (!selectedItem.displayNum && selectedItem.displayNum !== 0) {
                                this.$message.warn(`请维护品项${selectedItem.prodPartCode}的陈列面（瓶）！`);
                                return false
                            }
                        }
                        this.formData.visitDisplayItemList = recognitionResult;
                    }
                    
                    if(!this.checkWaterImg()) return false;
                    that.$utils.showLoading();
                    // if (that.formData['row_status'] !== 'NEW') {
                    //     if (that.checkedEditData()) {
                    //         if (back) {
                    //             let param = {refreshFlag: false};
                    //             that.$nav.back(param);
                    //             return;
                    //         } else {
                    //             return true;
                    //         }
                    //     }
                    // }
                        if (that.formData['row_status'] === 'NEW') {
                            delete that.formData['created'];
                            delete that.formData['createdBy'];
                            delete that.formData['lastUpdated'];
                            delete that.formData['lastUpdatedBy'];
                            delete that.formData['orgId'];
                            delete that.formData['postnId'];
                            that.formData.accntId = that.pageParam.data.id;
                            that.formData.visitId = that.pageParam.visitId;
                            that.formData.visitStatus = 'Y';
                            
                        }
                        if(this.selectedDisplay && this.selectedDisplay.length){
                            that.formData.operateItem = that.selectedDisplay.map(item => {
                                return `${item.id}:${item.prodName}`;
                            })
                        }
                        await that.$http.post('action/link/accntVisitDisplay/upsert', that.formData, {
                            handleFailed: (error) => {
                                that.$utils.hideLoading();
                            }
                        });
                        await that.saveMaterial();
                        if (back) {
                            let param = {refreshFlag: true};
                            that.$nav.back(param);
                        } else {
                            that.formData['row_status'] = 'UPDATE';
                            return true;
                        }
                } catch (e) {
                    that.$utils.hideLoading();
                    console.log('陈列采集提交数据报错！' + e);
                    that.$message.error('陈列采集提交数据报错！');
                } finally {
                    that.$utils.hideLoading();
                }
            }
        }
    }
</script>

<style lang="scss">
    .dealer-exhibit-collect-page {
        padding-bottom: 150px;
        font-family: PingFangSC-Regular,serif;
        .display-title {
            .link-item-title {
                height: 100%;
            }
        }
        .display-line {
            width: 100%;
            font-size: 24px;
            color:#666;
            view {
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
            }
        }
        /*deep*/.link-icon {
                    font-size: 28px;
                }
        /*deep*/.link-input-text-align-left {
                    text-align: right;
                }
        /*deep*/.link-item {
                    padding: 24px;
                }
        /*deep*/.link-button-size-large {
                    font-size: 32px!important;
                }
        .list{
            .material-list{
                display: flex;
                justify-content: space-between;
                flex-direction: row;
                align-items: center;
                .material-name{
                    color: #333333
                }
            }
        }
        .add-btn {
            font-size: 28px;
            color: #2F69F8;
        }
        .select-box {
            @include flex-start-center;
            border-bottom: 1px solid #F2F2F2;
            .select-left {
                width: 100%;
                padding-left: 24px;
                .prod-num {
                    text {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 28px;
                        background: #A6B4C7;
                        border-radius: 8px;
                        padding: 6px 12px;
                    }
                    margin-top: 6px;
                    margin-bottom: 20px;
                }
                .store-name {
                    width: 100%;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    font-weight: bold;
                }
                .store-name1 {
                    width: 100%;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                }
                .store-supplier {
                    padding-top: 8px;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                }
            }
        }
        .last-time-collect {
            @include flex-center-center();
            height: 76px;
            background: #ffffff;
            text-align: center;
            margin-bottom: 24px;
            .icon-time-circle {
                font-size: 28px;
                color: #8C8C8C;
            }
            .last-collect-text {
                font-size: 28px;
                color: #8C8C8C;
                padding-left: 8px;
            }
            .last-time {
                font-size: 28px;
                color: #000;
                padding-left: 8px;
            }
        }
        .remark-content {
            background: #ffffff;
            @include flex-center-start;
            @include direction-column;
            padding-left: 24px;
            padding-bottom: 34px;
            border-bottom: 1px solid #F2F2F2;
            .text-title {
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                padding-top: 40px;
                padding-bottom: 18px;
            }
            .text-area {
                width: 702px;
                height: 106px;
                font-size: 28px;
                color: #000;
                letter-spacing: 0;
                line-height: 19px;
            }
        }
        .image-title {
            background-color: #ffffff;
            padding-left: 24px;
            padding-top: 24px;
            font-size: 28px;
            color: #595959;
        }
        .img-container {
            background: #ffffff;
            padding-bottom: 24px;
        }
        .blank {
            height: 40px;
            width: 100%;
        }

        .display-num {
            font-size: 28px;
            display: flex;
            background-color: #ffffff;
            padding: 24px;
            justify-content: space-between;
            color: #595959;

            .view-btn {
                color: #2F69F8;
            }
        }

        .display-basics-column {
            @include flex();
            @include direction-column();
            border-bottom: 1px solid #F2F2F2;
            background: #ffffff;
            padding-bottom: 24px;

            .label {
                margin: auto 24px;
                padding-top: 40px;
                padding-bottom: 24px;
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;

                text {
                    color: #FF595A;
                    margin-left: -14px;
                }
            }

            .value {
                font-family: PingFangSC-Regular, serif;
                font-size: 28px;
                color: #BFBFBF;
                letter-spacing: 0;
                line-height: 28px;
                padding-bottom: 40px;
                margin-left: -24px;

                .text-area {
                    width: 100%;
                    color: #BFBFBF;
                }
            }
        }

        .dialog-bottom {
            .dialog-content {
                padding: 0 20px;
            }

            .model-title {
                display: flex;

                .title {
                    font-family: PingFangSC-Regular, serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    text-align: center;
                    line-height: 96px;
                    height: 96px;
                    width: 90%;
                    margin-right: 80px;
                    padding-left: 0;
                }

                .icon-close {
                    color: #BFBFBF;
                    font-size: 48px;
                    line-height: 96px;
                    height: 96px;
                }
            }
        }

        .scan-wrap {
            background: #fff;
            padding-left: 13px;
            .icon-saoma1 {
                color: #2945E8;
                font-size: 184px;
            }
        }
        .inline-tips {
            display: inline;
            background: none;
            padding: 0;
        }
        .bus-scene-row{
                .link-item-body-left{
                    width: 160px;
                    flex: none!important;
                }
                .link-item-body-right{
					.link-cover-full-item {
					     position: relative !important;
					}
					.link-input{
						position: relative !important;
					}
                    .input{
                        flex: 1!important;
                    }
                    .ent-wrap{
                        width: 80px;
                    }
                }
            }
    }
</style>
