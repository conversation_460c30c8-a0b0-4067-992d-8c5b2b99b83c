<!--库存采集-->
<template>
    <link-page class="inventory-acquisition-page">
        <view :class="!$utils.isEmpty(lastTimeCollect)? 'last-time-collect' : 'last-collect-client'" v-if="lastTimeCollectFlag">
            <view class="collect-content" v-if="!$utils.isEmpty(lastTimeCollect)">
                <view class="iconfont icon-time-circle"></view>
                <view class="last-collect-text">上次采集</view>
                <view class="last-time">{{lastTimeCollect.created | date('YYYY-MM-DD HH:mm')}} {{lastTimeCollect.createdName}}</view>
            </view>
            <view class="add-product" @tap="basicUsageOfDialog('addProd')" v-if="pageParam.sourcePage!=='onlyView'">添加产品</view>
        </view>
        <view class="last-collect-client" v-if="(pageParam.originFlag === 'clientDetails' || pageParam.originFlag === 'inventoryList' || pageParam.originFlag === 'notice') && !lastTimeCollectFlag">
            <view class="add-product" @tap="basicUsageOfDialog('addProd')" v-if="pageParam.sourcePage!=='onlyView'">添加产品</view>
        </view>
        <list>
            <item v-for="(item, index) in listData" :key="index" :arrow="false" class="perform-case-list-item">
                <view slot="note">
                    <view class="media-list">
                        <view class="num-view">
                            <view class="num">{{item.prodCode}}</view>
                        </view>
                    </view>
                    <view class="media-list">
                        <view class="store-text">
                            <view class="content-middle">
                                <view class="name">{{item.prodName}}</view>
                            </view>
<!--                            <view class="sub-total">-->
<!--                                <view class="label">总瓶数</view>-->
<!--                                <view class="value">{{item.subTotal}} {{item.retailUnit || '瓶'}}</view>-->
<!--                            </view>-->
                            <view class="store-input">
                                <view class="store-input-rows">
                                    <view class="label">系统库存</view>
                                    <view class="value">{{item.preQty1 || 0}}{{item.saleUnit | lov('PROD_UNIT') || '件'}}{{item.preQty2 || 0}}{{item.retailUnit | lov('PROD_UNIT') || '瓶'}}</view>
                                </view>
                                <view class="store-input-rows">
                                    <view class="label">当前库存</view>
                                    <view class="value-input">
                                        <input type="number" v-model="item.qty1"  	:disabled="!isEditFlag" placeholder="" placeholder-style="color: #8C8C8C;">
                                        <view class="unit">{{item.saleUnit | lov('PROD_UNIT') || '件'}}</view>
                                        <input type="number" v-model="item.qty2" placeholder="" placeholder-style="color: #8C8C8C;" :disabled="!isEditFlag">
                                        <view class="unit">{{item.retailUnit | lov('PROD_UNIT') || '瓶'}}</view>
                                    </view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
            </item>
        </list>
        <view class="blank"></view>
        <link-sticky class="button-bottom" v-if="pageParam.isEditFlag">
            <link-button size="large" :shadow="shadow" @tap="submitCheck"  autoLoading>提交</link-button>
        </link-sticky>
        <link-dialog ref="confirmDialog" disabledHideOnClickMask title="提示" :initial="true">
            {{confirmInfo}}
            <link-button slot="foot" @tap="$refs.confirmDialog.hide()" style="color: #333333">取消</link-button>
            <link-button slot="foot" @tap="submitInventory">继续提交</link-button>
        </link-dialog>

    </link-page>
</template>

<script>
import Taro from '@tarojs/taro';
    export default {
        name: "inventory-acquisition-page",
        data () {
            let salesmanCityId = this.$taro.getStorageSync('token').result.coreOrganizationTile.l6Id;
            let accntId = this.pageParam.originFlag === 'inventoryList' ? this.pageParam.accntId : this.pageParam.data.id;

            //用来判断是否为国窖公司
            let flagCompany = false;
            let userInfo = Taro.getStorageSync('token').result;
            //国窖公司
            if(userInfo.coreOrganizationTile.brandCompanyCode === '5600'){
                flagCompany = true;
            }
            const supplierProduct = new this.AutoList(this, {
                module: 'action/link/accntSaleProd',
                sortOptions: null,
                param: {
                    oauth: 'ALL',
                    sort: 'created',
                    order: 'desc',
                    filtersRaw: [
                        {id: 'salesmanCityId', property: 'salesmanCityId', value: salesmanCityId, operator: '='},
                        {id: 'status', property: 'status', value: 'Active', operator: '='},
                    ],
                    visitId: this.pageParam.visitId,
                    searchModule: 'inventoryCollect',
                    searchFilter: accntId,
                    attr1: accntId,
                    noSaleCateFlag: 'Y'
                },
                searchFields: ['prodCode','prodName'],
                filterOption: null,
                filterBar: {
                    field: 'name',
                    autoReload: false,              // 禁止点击自动刷
                    notNull: true,                  // 禁止反选为null
                },
                hooks: {
                    afterLoad (data) {
                        data.rows.forEach(async item => {
                            this.$set(item, 'channelManageModeName', await this.$lov.getNameByTypeAndVal('CHANNEL_MANAGE_MODE', item.channelManageMode))
                        })
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} class="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem></link-checkbox>
                            <view class="select-left">
                                <view class="prod-num">
                                    <text>{data.prodCode}</text>
                                </view>
                                <view class="store-name">{data.prodName}</view>
                                <view class="store-supplier">
                                    {this.$utils.isEmpty(data.channelManageModeName) ? this.$utils.isEmpty(data.accntName) ? '' : data.accntName : `${data.channelManageModeName} | ${data.accntName}`}
                                </view>
                            </view>
                        </item>
                    )
                }
            });
            return {
                isEditFlag: this.pageParam.isEditFlag,
                userInfo, // 用户信息
                isDealer: false, // 是否走特定公司的经销商人员安全性
                confirmInfo:'', //校验返回文字
                flagCompany,
                addAccntId: this.pageParam.originFlag === 'inventoryList' ? this.pageParam.accntId : this.pageParam.data.id,
                supplierProduct,
                basicOption: {},
                lastTimeCollectFlag: false,
                listData: [],
                shadow: true,
                allProductList: [],
                supplierProductArr: [],
                total: 100,
                lastTimeCollect: {},
                initData: [],
                selectData: [],
                insertList: [],
                defaultProduct: [],
                supplierDialogFlag: false,
                saleDialogFlag: false,
                terminalArr: [],
                // 分品项标志
                prodPartFlag: false,
            }
        },
        async created() {
            this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
      
            // 获取分品项企业参数配置
            const prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
            this.prodPartFlag = prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
            //库存4个场景使用 originFlag  clientDetails：终端详情-库存快捷入口 inventoryList：库存列表 visit：拜访模块的库存采集  notice：消息提醒
            const flag = ['clientDetails', 'inventoryList', 'notice'].includes(this.pageParam.originFlag)
            if (flag) {
                await this.getDefaultData();
            } else {
                await this.queryCollectData();
            }
            if (flag || this.pageParam.pageFrom === 'visit') {
                await this.queryTerminal()
            }
        },
        watch:{
            listData:{
                handler(val){
                    val.forEach((item)=>{
                        if(typeof(item.qty1) == 'string')
                            item.qty1 = item.qty1.replace(/[^0-9]/g,'')
                        if(typeof(item.qty2) == 'string')
                            item.qty2 = item.qty2.replace(/[^0-9]/g,'')
                    })
                },
                deep: true
            }
        },
        methods: {
            /**
             * 查询终端数据
             * <AUTHOR>
             * @date 2020-11-14
             */
            queryTerminal () {
                let accntId = this.pageParam.originFlag === 'inventoryList' ? this.pageParam.accntId : this.pageParam.data.id;
                this.$http.post('action/link/accnt/queryFieldsByExamplePage', {
                    multiAcctMainId: accntId,
                    filtersRaw: [
                        {id: 'acctType', property: 'acctType', value: '[Dealer]', operator: 'in'},
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                    ],
                    stayFields:"id"
                }, {
                    handleFailed (error) {
                    }
                }).then(data => {
                    this.terminalArr = data.rows;
                })
            },
            /**
             * 请求产品列表
             * <AUTHOR>
             * @date 2020-10-20
             */
            async basicUsageOfDialog(flag) {
                const that = this;
                if (flag !== 'addProd') {
                    await this.$dialog.closeAll();
                }
                let accntId = that.pageParam.originFlag === 'inventoryList' ? that.pageParam.accntId : that.pageParam.data.id;
                that.basicOption = new that.AutoList(that, {
                    module: 'action/link/saleCategory',
                    url:{
                        queryByExamplePage:'action/link/saleCategory/queryByProdIdExamplePage'
                    },
                    sortOptions: null,
                    param: {
                        oauth:'ALL', // 职位安全性
                        sort: 'created',
                        order: 'desc',
                        accntId: accntId,
                        searchModule: 'inventoryCollect',
                        searchFilter: accntId,
                        visitId: that.pageParam.visitId,
                        partOauthFlag: this.prodPartFlag ? 'Y' : '', //分品项-安全性
                        filtersRaw: [
                            {id: 'prodId', property: 'prodId', value:`[${that.defaultProduct.toString()}]`, operator: 'not in'},
                            {id: 'status', property: 'status', value:`Y`, operator: '='}
                        ]
                    },
                    // slots: {
                    //     searchRight: () => {
                    //         return this.$taro.getStorageSync('token').result.coreOrganizationTile.brandCompanyCode === '5600' || this.pageParam.data.editApprovalStatus=== 'Submitted' || ['underReview','underReview2'].includes(this.pageParam.data.auditStatus)
                    //             ? <view></view>
                    //             : <link-button mode="fill" label="添加所售产品" onTap={that.queryCityProduct} style="margin-left: 8px" size="mini"></link-button>
                    //     }
                    // },
                    searchFields: ['prodCode', 'prodName'],
                    filterOption: null,
                    filterBar: {
                        field: 'name',
                        autoReload: false,              // 禁止点击自动刷新
                        notNull: true,                  // 禁止反选为null
                    },
                    hooks: {
                        afterLoad (data) {
                            data.rows.forEach(async item => {
                                that.$set(item, 'supplierManageModeName', await that.$lov.getNameByTypeAndVal('CHANNEL_MANAGE_MODE', item.supplierManageMode))
                            })
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} class="select-box" arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem></link-checkbox>
                                <view class="select-left">
                                    <view class="prod-num">
                                        <text>{data.prodCode}</text>
                                    </view>
                                    <view class="store-name">{data.prodName}</view>
                                </view>
                            </item>
                        )
                    }
                });
                that.allProductList = await that.$object(that.basicOption, {
                    multiple: true,
                    showInDialog: true,
                    pageTitle: '请选择所售产品',
                    selected: that.allProductList.map(item => item.id),
                    beforeConfirm: async (rows) => {
                        that.selectData = await that.sureAddProduct(rows);
                    }
                });
                if (that.selectData.length !== 0) {
                    let listIndex = that.listData.map(item => item.prodId);
                    let result = that.selectData.filter(item => !listIndex.includes(item.prodId));
                    that.listData = that.listData.concat(result);
                }
            },
            /**
             * 查询城市产品
             * <AUTHOR>
             * @date 2020-11-16
             * @param param
             */
            async queryCityProduct() {
                const that = this;
                await this.$dialog.closeAll();
                let accntId = that.pageParam.originFlag === 'inventoryList' ? that.pageParam.accntId : that.pageParam.data.id;
                let obj = {id: 'prodId', property: 'prodId', value:`[${that.defaultProduct.toString()}]`, operator: 'not in'};
                let filterArr = that.supplierProduct.option.param.filtersRaw.filter(item => item.property === 'prodId');
                if (filterArr.length === 0) {
                    that.supplierProduct.option.param.filtersRaw = that.supplierProduct.option.param.filtersRaw.concat(obj);
                }
                if (this.prodPartFlag) {
                    this.supplierProduct.option.param.partOauthFlag = 'Y';  //分品项-安全性
                }
                that.supplierProductArr = await this.$object(that.supplierProduct, {
                    multiple: true,
                    showInDialog: true,
                    selected: that.supplierProductArr.map(item => item.id),
                    beforeConfirm: async (rows) => {
                        let insertArr = rows.map(item => ({
                            prodId: item.prodId,
                            supplierId: item.accntId,
                            status: 'Y',
                            accntId: accntId,
                            row_status: 'NEW',
                            supplierManageMode: item.channelManageMode
                        }));
                        //校验的产品
                        let insertProds = rows.map(item => ({
                            prodId: item.prodId,
                            supplierId: item.accntId,
                            status: 'Y',
                            accntId: accntId,
                            row_status: 'NEW',
                            supplierManageMode: item.channelManageMode,
                            supplierName: item.accntName,
                            prodCode: item.prodCode,
                            prodName: item.prodName
                        }));
                        let saleArr = [];
                        insertArr.forEach(item => {
                            that.terminalArr.forEach(val => {
                                const deepItem = JSON.parse(JSON.stringify(item));
                                deepItem.accntId = val.id;
                                saleArr.push(deepItem);
                            })
                        });
                        that.insertSaleProduct(saleArr,insertProds);
                    }
                });
            },
            /**
              * 插入所售产品
              * <AUTHOR>
              * @date 2020-11-17
              * @param array
            */
            async insertSaleProduct (array,insertProds) {
                try {
                    const checkData = this.$utils.deepcopy(insertProds);
                    checkData.forEach(item => {
                        delete item.rowId;
                        item.commonEdit = 'Y'
                    })
                    const data = await this.$http.post('action/link/saleCategory/checkSupplierUniqueForTerminal', checkData)
                    if(!data.success){
                        this.$message.warn(data.result);
                        return false;
                    }else{
                        //校验通过后
                        this.$http.post('action/link/saleCategory/batchUpsert', array, {
                            handleFailed (error) {
                            }
                        }).then(async data => {
                            if (data.success) {
                                this.basicUsageOfDialog();
                            }
                        })
                    }
                } catch (e) {
                    console.log('产品校验出错');
                }
            },
            /**
              * 检查数据
              * <AUTHOR>
              * @date 2020-09-08
            */
            async checkedData () {
                const that = this;
                let flag = true;
                for (let i = 0; i < that.listData.length; i++) {
                    let item = that.listData[i];
                    if (item.row_status === 'NEW'){
                        delete item['id'];
                        delete item['created'];
                        delete item['createdBy'];
                        delete item['lastUpdated'];
                        delete item['lastUpdatedBy'];
                        delete item['orgId'];
                        delete item['postnId'];
                    }
                    item.accntId = that.pageParam.originFlag === 'inventoryList' ? that.pageParam.accntId : that.pageParam.data.id;
                    //lzlj-002-3051根据是否有拜访id标识采集数据来源
                    item.visitId = that.pageParam.visitId;
                    that.$set(item, 'visitStatus', 'Y');
                    if (that.$utils.isEmpty(item.qty1)) {
                        let prodUnit = await this.$lov.getNameByTypeAndVal('PROD_UNIT',  item.saleUnit);
                        that.$message['warn']( `请输入当前库存${prodUnit || '件'}数`);
                        flag = false;
                        break;
                    }
                    if (that.$utils.isEmpty(item.qty2)) {
                        that.$set(item, 'qty2', 0);
                    }
                    let numFlag = (!(Number(item.preQty1) === Number(item.qty1)) || !(Number(item.preQty2) === Number(item.qty2))) && that.$utils.isEmpty(item.row_status);
                    if (numFlag) {
                        that.$set(item, 'row_status', 'UPDATE');
                    }
                }
                that.insertList = that.listData.filter(item => !that.$utils.isEmpty(item.row_status));
                return flag;
            },
            // 
            async setNowStock(list){
                let accntId = this.pageParam.originFlag === 'inventoryList' ? this.pageParam.accntId : this.pageParam.data.id;
                const res = await this.$http.post('/action/link/accntVisitInventory/getNowRealStockByProdCodes',{
                    inventoryList: list,
                    accntId:accntId,
                    companyId: this.userInfo.companyId
                });
                
                if(res.success){
                    const resMap = new Map(res.result.map(item => [item.prodCode, item.nowRealStock]));
                    this.listData = this.listData.map(item => 
                    resMap.has(item.prodCode) 
                        ? { ...item, preQty1: resMap.get(item.prodCode) } 
                        : item
                    );

                }
            },

            /**
             * 提交前校验库存 国窖
             * <AUTHOR>
             * @date 2021年11月4日17:09:07
             */
            async submitCheck(){
                this.$utils.showLoading();
                try {
                    if(!this.flagCompany){
                        await this.submitInventory();
                        return;
                    }
                    let upsetList = await this.checkedData();
                    if (this.insertList.length === 0) {
                        this.$utils.hideLoading();
                        this.$nav.back();
                        return
                    }
                    if (upsetList) {
                        const data = await this.$http.post('action/link/accntVisitInventory/checkQty', this.insertList)
                        if(data.checkFlag){
                            await   this.submitInventory();
                            return;
                        }
                        else{
                            this.$utils.hideLoading();
                            this.confirmInfo=data.result ;
                            this.$refs.confirmDialog.show();
                        }
                    } else {
                        this.$utils.hideLoading();
                    }
                } catch(e) {
                    this.$utils.hideLoading();
                }
            },

            /**
             * 提交
             * <AUTHOR>
             * @date 2020-09-08
             */
            async submitInventory() {
                const that = this;
                that.$utils.showLoading();
                try {
                    let upsetList = await this.checkedData();
                    if (that.insertList.length === 0) {
                        this.$utils.hideLoading();
                        this.$nav.back();
                        return
                    }
                    if (upsetList) {
                        await that.$http.post('action/link/accntVisitInventory/batchUpsert', that.insertList, {
                            handleFailed(error) {
                                that.$utils.hideLoading();
                            }
                        })
                        that.$utils.hideLoading();
                        if (that.pageParam.originFlag === 'inventoryList') {
                            let param = {refreshList: true};
                            that.$nav.back(param)
                        } else {
                            let param = {
                                refreshFlag: true,
                                visitApplicationStatus: 'visiting'
                            };
                            that.$nav.back(param)
                        }
                        this.$refs.confirmDialog.hide();
                    } else {
                        this.$utils.hideLoading();
                    }
                } catch (e) {
                    this.$utils.hideLoading();
                } finally {

                }
            },
            /**
              * 获取库存采集数据
              * <AUTHOR>
              * @date 2020-09-08
              * @param param visiting
            */
            queryCollectData () {
                const that = this;
                let accntId = that.pageParam.originFlag === 'inventoryList' ? that.pageParam.accntId : that.pageParam.data.id;
                // let rows = that.pageParam.visitApplicationStatus === 'visiting' ? undefined : 1;
                const param = {
                    page: 1,
                    pageFlag: true,
                    onlyCountFlag: false,
                    // rows: rows,
                    rows: 20,
                    oauth:'MY_POSTN', // 职位安全性
                    sort: 'created',
                    order: 'desc',
                    accntId: accntId,
                    visitId: that.pageParam.visitId,
                    partOauthFlag: this.prodPartFlag ? 'Y' : '', //分品项-安全性
                    filtersRaw: []
                };
                if (this.isDealer) {
                    param.filtersRaw = [{id: 'postnId', property: 'postnId', value: this.userInfo.postnId}];
                }
                this.$http.post('action/link/accntVisitInventory/queryByExamplePage', param).then(async data => {
                    if (data.success) {
                        if (that.$utils.isEmpty(data.rows)) {
                            // 新建
                            await that.getDefaultData();
                            //that.listData = that.listData.concat(that.listData);
                            that.lastTimeCollectFlag = true;
                        } else {
                            // 编辑
                            data.rows.forEach(item => {
                                if (that.pageParam.originFlag === 'inventoryList') {
                                    that.$set(item, 'row_status', 'NEW');
                                }
                            });
                            if(data.rows && data.rows.length){
                                this.setNowStock(data.rows);
                            }
                            that.listData = data.rows;
                            that.initData = data.rows;
                            that.lastTimeCollect = data.rows[0];
                            that.lastTimeCollectFlag = true;
                            this.defaultProduct = data.rows.map(item => item.prodId);
                        }
                    }
                })
            },
            /**
              * 获取默认采集数据
              * <AUTHOR>
              * @date 2020-09-07
            */
            async getDefaultData () {
                const that = this;
                let accntId = that.pageParam.originFlag === 'inventoryList' ? that.pageParam.accntId : that.pageParam.data.id;
                const filtersRaw = [{id: 'subTotal', property: 'subTotal', value: '0', operator: '>'}]
                if(this.pageParam.originFlag === 'notice') filtersRaw.push({id: 'prodCode', property: 'prodCode', value: this.pageParam.prodCode, operator: '='})
                if (this.isDealer) filtersRaw.push({id: 'postnId', property: 'postnId', value: this.userInfo.postnId});
                const data = await that.$http.post('action/link/accntVisitInventory/queryByExamplePage', {
                    page: 1,
                    pageFlag: false,
                    onlyCountFlag: false,
                    filtersRaw,
                    oauth: 'MY_POSTN', // 职位安全性
                    sort: 'created',
                    order: 'desc',
                    accntId: accntId,
                    partOauthFlag: this.prodPartFlag ? 'Y' : '', //分品项-安全性
                    tabFlag: 'Y'
                })
                if (!that.$utils.isEmpty(data.rows)) {
                    data.rows.forEach(item => {
                        that.$set(item, 'row_status', 'NEW');
                    });
                    that.lastTimeCollectFlag = true;
                    this.defaultProduct = data.rows.map(item => item.prodId);
                }
                if(data.rows && data.rows.length){
                    this.setNowStock(data.rows);
                }
                that.lastTimeCollect = data.rows[0];
                that.listData = data.rows
            },
            /**
              * 确定选中产品
              * <AUTHOR>
              * @date 2020-09-07
              * @param data
            */
            sureAddProduct (data) {
                return new Promise((resolve, reject) => {
                    const that = this;
                    let accntId = that.pageParam.originFlag === 'inventoryList' ? that.pageParam.accntId : that.pageParam.data.id;
                    that.$http.post('action/link/accntVisitInventory/multiCheckProds', {
                        accntId: accntId,
                        companyId: this.userInfo.companyId,
                        inventoryList: data
                    }).then(data => {
                        if (data.success) {
                            resolve(data.result);
                        } else {
                            reject()
                        }
                    });
                });
            }
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .inventory-acquisition-page{
        font-family: PingFangSC-Regular,serif;
        /*deep*/.link-auto-list-top-bar {
                    border-bottom: none;
                }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-item {
                    padding: 24px;
                }
        /*deep*/.link-item-active {
                    background: #ffffff;
                }
        /*deep*/.link-button-size-large {
                    font-size: 32px!important;
                    margin-right: 24px;
                    margin-left: 24px;
                }
        /*deep*/.link-button-size-large {
                    font-size: 32px!important;
                }
        /*deep*/ .link-search-input-no-padding-bottom {
                    padding-bottom: 24px!important;
                    border-bottom: 1px solid #F2F2F2;
                 }
        .last-time-collect {
            @include flex-start-center();
            @include space-between();
            height: 76px;
            background: #ffffff;
            text-align: center;
            padding-left: 24px;
            padding-right: 24px;
            .collect-content {
                @include flex-start-center();
                .icon-time-circle {
                    font-size: 28px;
                    color: #8C8C8C;
                }
                .last-collect-text {
                    font-size: 28px;
                    color: #8C8C8C;
                    padding-left: 8px;
                }
                .last-time {
                    font-size: 28px;
                    color: #000;
                    padding-left: 8px;
                }
            }
            .add-product {
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: right;
            }
        }
        .last-collect-client {
            @include flex-end-center();
            height: 76px;
            background: #ffffff;
            text-align: center;
            padding-left: 24px;
            padding-right: 24px;
            .add-product {
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: right;
            }
        }
        .perform-case-list-item {
            border-radius: 16px;
            margin: 24px auto auto auto;
            width: 702px;
            .media-list {
                @include media-list();
                .sub-total {
                    @include flex-start-center();
                    padding-top: 16px;
                    font-size: 28px;
                    .label {
                        color: #8C8C8C;
                    }
                    .value {
                        color: #000000;
                        padding-left: 8px;
                    }
                }
                .store-input {
                    padding-top: 16px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    .store-input-rows {
                        @include flex-start-center();
                        .label {
                            color: #8C8C8C;
                        }
                        .value {
                            padding-left: 8px;
                            color: #000000;
                        }
                        .value-input {
                            @include flex-start-center;
                            .unit {
                                color: #262626;
                            }
                            input {
                                color: #262626;
                                margin: auto 8px;
                                text-align: center;
                                background: #F2F2F2;
                                border-radius: 8px;
                                width: 80px;
                                height: 48px;
                            }
                        }
                    }
                }
            }
        }
        .model-title {
            display: flex;
            margin-left: 24px;
            .title {
                font-family: PingFangSC-Regular,serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 80px;
                height: 80px;
                width: 90%;
                padding-left: 40px;
            }
            .icon-close {
                color: #BFBFBF;
                font-size: 32px;
                line-height: 80px;
                height: 80px;
            }
        }
        .select-box {
            @include flex-start-center;
            border-bottom: 1px solid #F2F2F2;
            .select-left {
                width: 100%;
                padding-left: 24px;
                .prod-num {
                    text {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 28px;
                        background: #A6B4C7;
                        border-radius: 8px;
                        padding: 6px 12px;
                    }
                    margin-top: 6px;
                    margin-bottom: 20px;
                }
                .store-name {
                    width: 100%;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    font-weight: bold;
                }
                .store-supplier {
                    padding-top: 8px;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                }
            }
        }
        .blank {
            height: 204px;
            width: 100%;
        }
        .bottom-btn {
            padding-top: 16px;
            padding-bottom: 34px;
            .all-select {
                height: 96px;
                padding-left: 24px;
                @include flex-start-center();
                width: 50%;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;
                .iconfont {
                    font-size: 40px;
                    color: #BFBFBF;
                }
                .icon-yiwanchengbuzhou {
                    color: $color-primary;
                }
                .all-select-text {
                    padding-left: 16px;
                }
            }
            .sure-btn {
                width: 340px;
                height: 96px;
                margin-right: 24px;
            }
        }
        .button-bottom {
            padding-top: 16px;
            padding-bottom: 64px;
        }
    }
</style>
