<template>
    <link-page class="dealer-visit-list-page">
        <lnk-taps :taps="visitStatusOptions" v-model="visitStatusActive" :addZIndex="addZIndex" :statusLeft="true"></lnk-taps>
        <!-- <view v-if="visitStatusActive.seq === '3'" >
            <visit-today-list :visitProcessList="visitTodayList" @fresh-list="freshToday"></visit-today-list>
        </view> -->
        <view v-if="visitStatusActive.seq === '1'" >
            <visit-progress-list :visitProcessList="visitProcessList"></visit-progress-list>
        </view>
        <view v-if="visitStatusActive.seq === '2'">
            <visit-already-list :oauth="oauth" :isDealer="isDealer"></visit-already-list>
        </view>
    </link-page>
</template>

<script>
    import lnkTaps from '../../core/lnk-taps/lnk-taps'
    import visitProgressList from './components/visit-progress-list'
    import visitAlreadyList from './components/visit-already-list'
    import visitTodayList from './components/visit-today-list'
    export default {
        name: "dealer-visit-list-page",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            let oauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();
            if (oauth === 'MY_ORG') {
                oauth = 'MULTI_ORG'
            }

            const  visitTodayList=new this.AutoList(this, {

                url: {
                    queryByExamplePage: '/action/link/visitPlan/queryVisitPlanTodayPage'
                },
                param: {
                    onlyCountFlag: false
                },
                sortOptions: null,
                hooks: {
                    afterLoad(data){

                    }
                }
            });
            const visitProcessList = new this.AutoList(this, {
                module: 'action/link/accntVisit',
                param: () => {
                    return {
                        filtersRaw: [
                            {id: 'visitApplicationStatus', property: 'visitApplicationStatus', value: 'visiting', operator: '='},
                            {id: 'visitType', property: 'visitType', value: 'dailySalesCall', operator: '='},
                        ],
                        oauth: oauth
                    }
                },
                queryFields: 'visitTime,created,orieLocation,createdName,specialDescription,accntName,accntId,acctStatus,accntProvince,accntCity,accntDistrict,accntAddr,id',
                sortOptions: null,
                hooks: {
                    beforeLoad(option) {
                        // 特定公司的经销商人员只能看到自己职位创建的数据
                        if (this.isDealer) {
                            option.param.filtersRaw = [
                                ...option.param.filtersRaw,
                                {id: 'postnId', property: 'postnId', value: userInfo.postnId}
                            ]
                        }
                    }
                }
            });
            const visitStatusOptions = [
                {defaultValue: "N", id: "222323332756804060", name: "拜访中", seq: "1", type: "INTERACTION_TYPE", val: "visiting"},
                {defaultValue: "N", id: "222323332756804060", name: "已拜访", seq: "2", type: "INTERACTION_TYPE", val: "visited"},
                // {defaultValue: "N", id: "222323332756804060", name: "今日拜访", seq: "3", type: "INTERACTION_TYPE", val: "visitToday"},
            ];
            return {
                userInfo,
                isDealer: false, // 是否走特定公司的经销商人员安全性
                visitProcessList,
                visitStatusOptions,
                visitStatusActive: visitStatusOptions[0],
                addZIndex: 4,
                templateData: [],
                visitTodayList,
                oauth,
                todayTotal:0,
            }
        },
        components: {
            lnkTaps,
            visitProgressList,
            visitAlreadyList,
            visitTodayList
        },
        async created () {
            this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
            this.getVisitTotal();
            const that = this;
            this.$bus.$on('visitCreatedSuccess', () => {
                this.visitProcessList.methods.reload();
                that.getVisitTotal();
            });
        },
        methods: {
            async onBack(){
                await this.getVisitTotal();
                this.visitTodayList.methods.reload();
            },
            async  freshToday(){
                await this.getVisitTotal();
                this.visitTodayList.methods.reload();
            },
              getTodayTotal(){
                this.$http.post('action/link/accntVisitPlan/queryVisitPlanTodayPage', {
                    onlyCountFlag: true,
                }).then(data => {
                    this.todayTotal=data.rows[0].total
                })
            },
              getVisitTotal: async function () {
                const  res= await this.$http.post('action/link/accntVisitPlan/queryVisitPlanTodayPage', {
                    onlyCountFlag: true,
                });
                this.$http.post('action/link/accntVisit/getVisitCountByStatus', {
                    onlyCountFlag: true,
                    oauth: this.oauth,
                    sort: 'created',
                    order: 'desc',
                    visitType: 'dailySalesCall',
                    dealerEmp: this.isDealer ? 'Y' : undefined // 特定公司的经销商人员只能看到自己职位创建的数据
                }).then(data => {
                    if (!this.$utils.isEmpty(data)) {
                        const todayTotal=res.rows[0].total
                        const result = data.result
                        const visitedList = result.filter(item => ['visited','closed'].includes(item.visitApplicationStatus))
                        const visitedCount = visitedList && visitedList.length > 0 ? visitedList[0].total + ((visitedList[1] || {}).total || 0) : 0
                        const visitingList = result.filter(item => item.visitApplicationStatus === 'visiting')
                        const visitingCount = (visitingList[0] || {}).total || 0
                        this.visitStatusOptions.forEach(item => {
                            if (item.val === 'visiting') {
                                this.$set(item, 'num', visitingCount)
                            }
                            if (item.val === 'visited') {
                                this.$set(item, 'num', visitedCount)
                            }
                            if (item.val === 'visitToday') {
                                this.$set(item, 'num', todayTotal)
                            }
                        })
                    }
                })
            }
        }
    }
</script>

<style lang="scss">
    .dealer-visit-list-page {
        background: #F2F2F2;
        /*deep*/.link-item {
                    padding: 0;
                 }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-dropdown-content {
                    padding: 24px;
                }
        /*deep*/.link-sticky-top:before {
                    box-shadow: none!important;
                }
    }
</style>
