<!--价格采集-->
<template>
    <link-page class="dealer-price-collection-page">
        <view :class="lastTimeCollect.created !== undefined && lastTimeCollect.createdName !== undefined ? 'last-time-collect' : 'last-collect-client'"
              v-if="lastTimeCollectFlag">
            <view class="collect-content" v-if="lastTimeCollect.created !== undefined && lastTimeCollect.createdName !== undefined">
                <view class="iconfont icon-time-circle"></view>
                <view class="last-collect-text">上次采集</view>
                <view class="last-time">{{lastTimeCollect.created | date('YYYY-MM-DD HH:mm')}} {{lastTimeCollect.createdName}}</view>
            </view>
            <view class="add-product" @tap="basicUsageOfDialog('addProd')" v-if="pageParam.sourcePage!=='onlyView'">添加产品</view>
        </view>
        <link-card-list>
            <link-swipe-action v-for="(item, index) in listData" :key="index" :arrow="false" class="perform-case-list-item">
                <link-card>
                    <view class="media-list" @tap="priceAdjust(item, index)">
                        <view class="top-rows">
                            <view class="num-view">
                                <view class="num">{{item.prodCode}}</view>
                            </view>
                            <view class="store-level">价格调整 <text class="iconfont icon-right"></text></view>
                        </view>
                    </view>
                    <view class="media-list">
                        <view class="store-text">
                            <view class="content-middle middle" style="width: 90%">
                                <view class="name">{{item.prodName}}</view>
                                <view class="store-level">瓶</view>
                            </view>
                            <view class="content-price">
                                <view class="price-item">
                                    <view class="price">{{item.purchasePrice | cny}}</view>
                                    <view class="price-type">采购价</view>
                                </view>
                                <view class="parting-line"></view>
                                <view class="price-item">
                                    <view class="price">{{item.undercurrentPrice | cny}}</view>
                                    <view class="price-type">暗流价</view>
                                </view>
                                <view class="parting-line"></view>
                                <view class="price-item">
                                    <view class="price">{{item.finalPrice | cny}}</view>
                                    <view class="price-type">消费者成交价</view>
                                </view>
                            </view>
                            <!-- <view class="store-input">
                                <view class="store-input-rows">
                                    <view class="label">供货货源</view>
                                    <view class="value">{{item.supplySource | lov('SUPPLY_SOURCE')}}</view>
                                </view>
                            </view> -->
                        </view>
                    </view>
                </link-card>
                <link-swipe-option slot="option" @tap="deleteProduct(item, index)" icon="mp-trash" v-if="pageParam.sourcePage!=='onlyView'" />
            </link-swipe-action>
        </link-card-list>
        <!--调整价格弹框-->
        <link-dialog ref="priceBottom"
                     :noPadding="true"
                     v-model="priceDialogFlag"
                     position="bottom"
                     borderRadius="32rpx 32rpx 0 0">
            <view class="model-title">
                <view class="title">调整价格</view>
                <view class="iconfont icon-close" @tap="closePriceDialog"></view>
            </view>
            <link-form :disabled="flag.disabled" :readonly="flag.readonly" ref="form" :value="priceAdjustOptions" >
                <view class="product-code">
                    <view class="code">{{priceAdjustOptions.prodCode}}</view>
                </view>
                <view class="product-name">
                    <view class="name">{{priceAdjustOptions.prodName}}</view>
                    <view class="unit">瓶</view>
                </view>
                <link-form-item label="采购价" class="edit-content" :arrow="false">
                    <link-number-keyboard placeholder="请输入采购价" v-model="priceAdjustOptions.purchasePrice"/>
                </link-form-item>
                <link-form-item label="暗流价" class="edit-content" :arrow="false">
                    <link-number-keyboard placeholder="请输入暗流价" v-model="priceAdjustOptions.undercurrentPrice"/>
                </link-form-item>
                <link-form-item label="消费者成交价" class="edit-content" :arrow="false">
                    <link-number-keyboard placeholder="请输入消费者成交价" v-model="priceAdjustOptions.finalPrice"/>
                </link-form-item>
                <!-- <link-form-item label="供货货源" class="edit-content-supply">
                    <link-lov type="SUPPLY_SOURCE" v-model="priceAdjustOptions.supplySource"/>
                </link-form-item> -->
            </link-form>
            <view class="blank-product-sure"></view>
            <view class="bottom-btn">
                <link-button class="sure-btn-edit" size="normal" block @tap="closePriceDialog">确定</link-button>
            </view>
        </link-dialog>
        <view class="blank"></view>
        <link-sticky class="button-bottom" v-if="pageParam.isEditFlag">
            <link-button block @tap="submit" size="large" :shadow="shadow">提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    export default {
        name: "dealer-price-collection-page",
        data () {
            const userInfo = this.$taro.getStorageSync('token').result;
            let salesmanCityId = this.$taro.getStorageSync('token').result.coreOrganizationTile.l6Id;
            let accntId = this.pageParam.originFlag === 'inventoryList' ? this.pageParam.accntId : this.pageParam.data.id;
            const supplierProduct = new this.AutoList(this, {
                module: 'action/link/accntSaleProd',
                sortOptions: null,
                param: {
                    oauth: 'ALL',
                    sort: 'created',
                    order: 'desc',
                    filtersRaw: [
                        {id: 'salesmanCityId', property: 'salesmanCityId', value: salesmanCityId, operator: '='},
                        {id: 'status', property: 'status', value: 'Active', operator: '='},
                    ],
                    searchModule: 'inventoryCollect',
                    searchFilter: accntId,
                    noSaleCateFlag: 'Y',
                },
                searchFields: ['prodCode','prodName'],
                filterOption: null,
                filterBar: {
                    field: 'name',
                    autoReload: false,              // 禁止点击自动刷新
                    notNull: true,                  // 禁止反选为null
                },
                hooks: {
                    afterLoad (data) {
                        data.rows.forEach(async item => {
                            this.$set(item, 'channelManageModeName', await this.$lov.getNameByTypeAndVal('CHANNEL_MANAGE_MODE', item.channelManageMode))
                        })
                    }
                },
                renderFunc: (h, {data, index}) => {
                    return (
                        <item key={index} data={data} class="select-box" arrow="false">
                            <link-checkbox val={data.id} toggleOnClickItem></link-checkbox>
                            <view class="select-left">
                                <view class="prod-num">
                                    <text>{data.prodCode}</text>
                                </view>
                                <view class="store-name">{data.prodName}</view>
                                <view class="store-supplier">
                                    {this.$utils.isEmpty(data.channelManageModeName) ? this.$utils.isEmpty(data.accntName) ? '' : data.supplierName : `${data.channelManageModeName} | ${data.accntName}`}
                                </view>
                            </view>
                        </item>
                    )
                }
            });
            return {
                // 分品项公司
                isProdPartCom: '',
                userInfo, // 用户信息
                isDealer: false, // 是否走特定公司的经销商人员安全性
                supplierProduct,
                listData: [],
                supplierProductArr: [],
                lastTimeCollect: {},
                flag: {
                    disabled: false,
                    readonly: false,
                },
                priceAdjustOptions: {},
                lastTimeCollectFlag: false,
                shadow: true,
                allSelectFlag: false,
                priceDialogFlag: false,
                productDialogFlag: false,
                allProductList: [],
                selectBoxHeight: 0,
                selectData: [],
                insertArr: [],
                defaultProduct: [],
                lastPriceCollect: {},
                basicOption: {},
                terminalArr: []
            }
        },
        async created () {
            this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
            // 获取分品项企业参数配置
            const prodPartCom = await this.$utils.getCfgProperty('PROD_PART_BRANCH_COM');
            this.isProdPartCom = prodPartCom.indexOf(this.userInfo.coreOrganizationTile.brandCompanyCode) > -1;
            this.queryCollectData();
            if (this.pageParam.originFlag === 'clientDetails' || this.pageParam.pageFrom === 'visit') {
                this.queryTerminal()
            }
        },
        methods: {
            /**
             * 查询终端数据
             * <AUTHOR>
             * @date 2020-11-14
             */
            queryTerminal () {
                let accntId = this.pageParam.originFlag === 'inventoryList' ? this.pageParam.accntId : this.pageParam.data.id;
                this.$http.post('action/link/accnt/queryFieldsByExamplePage', {
                    multiAcctMainId: accntId,
                    filtersRaw: [
                        {id: 'acctType', property: 'acctType', value: '[Dealer]', operator: 'in'},
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='},
                    ],
                    stayFields:"id"
                }, {
                    handleFailed (error) {
                    }
                }).then(data => {
                    this.terminalArr = data.rows;
                })
            },
            /**
              * 删除价格记录
              * <AUTHOR>
              * @date 2020-11-21
              * @param data
              * @param index
            */
            deleteProduct (data, index) {
                const that = this;
                that.$dialog({
                    title: '提示',
                    content: '确认要删除该条价格记录？',
                    cancelButton: true,
                    confirmText: '删除',
                    onConfirm: async () => {
                        if (that.defaultProduct.includes(data.prodId)) {
                            that.$http.post('action/link/accntVisitPrice/deleteById', {
                                id: data.id
                            }).then(() => {
                                that.listData.splice(index, 1);
                                that.defaultProduct.splice(index, 1);
                            })
                        } else {
                            that.listData.splice(index, 1)
                        }
                    },
                    onCancel: () => {
                    }
                })
            },
            /**
             * 请求产品列表
             * <AUTHOR>
             * @date 2020-10-20
             */
            async basicUsageOfDialog(flag) {
                const that = this;
                if (flag !== 'addProd') {
                    await this.$dialog.closeAll();
                }
                that.basicOption = new that.AutoList(that, {
                    module: 'action/link/saleCategory',
                    sortOptions: null,
                    param: {
                        page: 1,
                        pageFlag: true,
                        oauth:'ALL', // 职位安全性
                        sort: 'created',
                        order: 'desc',
                        accntId: that.pageParam.data.id,
                        searchFilter: that.pageParam.data.id,
                        searchModule: 'priceCollect',
                        visitId: that.pageParam.visitId,
                        partOauthFlag: this.isProdPartCom ? 'Y' : '', //分品项-安全性
                        filtersRaw: [
                            {id: 'prodId', property: 'prodId', value:`[${that.defaultProduct.toString()}]`, operator: 'not in'},
                            {id: 'status', property: 'status', value:`Y`, operator: '='}
                        ]
                    },
                    // slots: {
                    //     searchRight: () => {
                    //         return  <link-button mode="fill" label="添加所售产品" onTap={that.queryCityProduct} style="margin-left: 8px" size="mini"></link-button>
                    //     }
                    // },
                    searchFields: ['prodCode', 'prodName'],
                    filterOption: null,
                    filterBar: {},
                    hooks: {
                        afterLoad (data) {
                            data.rows.forEach(async item => {
                                that.$set(item, 'supplierManageModeName', await that.$lov.getNameByTypeAndVal('CHANNEL_MANAGE_MODE', item.supplierManageMode))
                            })
                        }
                    },
                    renderFunc: (h, {data, index}) => {
                        return (
                            <item key={index} data={data} class="select-box" arrow="false">
                                <link-checkbox val={data.id} toggleOnClickItem></link-checkbox>
                                <view class="select-left">
                                    <view class="prod-num">
                                        <text>{data.prodCode}</text>
                                    </view>
                                    <view class="store-name">{data.prodName}</view>
                                    <view class="store-supplier">
                                        {that.$utils.isEmpty(data.supplierManageModeName) ? that.$utils.isEmpty(data.supplierName) ? '' : data.supplierName : `${data.supplierManageModeName} | ${data.supplierName}`}
                                    </view>
                                </view>
                            </item>
                        )
                    }
                });
                that.allProductList = await that.$object(that.basicOption, {
                    multiple: true,
                    showInDialog: true,
                    pageTitle: '请选择所售产品',
                    selected: that.allProductList.map(item => item.id),
                    beforeConfirm: async (rows) => {
                        that.selectData = await that.sureAddProduct(rows);
                    }
                });
                if (that.selectData.length !== 0) {
                    let listIndex = that.listData.map(item => item.id);
                    let result = that.selectData.filter(item => !listIndex.includes(item.id));
                    that.listData = that.listData.concat(result);
                }
            },
            /**
             * 查询城市产品
             * <AUTHOR>
             * @date 2020-11-16
             * @param param
             */
            async queryCityProduct() {
                const that = this;
                await this.$dialog.closeAll();
                let obj = {id: 'prodId', property: 'prodId', value:`[${that.defaultProduct.toString()}]`, operator: 'not in'};
                let filterArr = that.supplierProduct.option.param.filtersRaw.filter(item => item.property === 'prodId');
                if (filterArr.length === 0) {
                    that.supplierProduct.option.param.filtersRaw = that.supplierProduct.option.param.filtersRaw.concat(obj);
                }
                if (this.isProdPartCom) {
                    this.supplierProduct.option.param.partOauthFlag = 'Y';  //分品项-安全性
                }
                that.supplierProductArr = await this.$object(that.supplierProduct, {
                    multiple: true,
                    showInDialog: true,
                    selected: that.supplierProductArr.map(item => item.id),
                    beforeConfirm: async (rows) => {
                        let insertArr = rows.map(item => ({
                            prodId: item.prodId,
                            supplierId: item.accntId,
                            status: 'Y',
                            accntId: that.pageParam.data.id,
                            row_status: 'NEW',
                            supplierManageMode: item.channelManageMode
                        }));
                        //校验的产品
                        let insertProds = rows.map(item => ({
                            prodId: item.prodId,
                            supplierId: item.accntId,
                            status: 'Y',
                            accntId: that.pageParam.data.id,
                            row_status: 'NEW',
                            supplierManageMode: item.channelManageMode,
                            supplierName: item.accntName,
                            prodCode: item.prodCode,
                            prodName: item.prodName
                        }));
                        let saleArr = [];
                        insertArr.forEach(item => {
                            that.terminalArr.forEach(val => {
                                const deepItem = JSON.parse(JSON.stringify(item));
                                deepItem.accntId = val.id;
                                saleArr.push(deepItem);
                            })
                        });
                        that.insertSaleProduct(saleArr, insertProds);
                    }
                });
            },
            /**
             * 插入所售产品
             * <AUTHOR>
             * @date 2020-11-17
             * @param array
             * @param insertProds
             */
            async insertSaleProduct (array, insertProds) {
                const checkData = this.$utils.deepcopy(insertProds);
                checkData.forEach(item => {
                    delete item.rowId;
                    item.commonEdit = 'Y'
                })
                const data = await this.$http.post('action/link/saleCategory/checkSupplierUniqueForTerminal', checkData)
                if (!data.success){
                    this.$message.warn(data.result);
                    return false;
                }
                this.$http.post('action/link/saleCategory/batchUpsert', array, {
                    handleFailed (error) {
                    }
                }).then(async data => {
                    if (data.success) {
                        this.basicUsageOfDialog();
                    }
                })
            },
            /**
             * 确定选中产品
             * <AUTHOR>
             * @date 2020-09-07
             * @param data
             */
            sureAddProduct (data) {
                return new Promise((resolve, reject) => {
                    const that = this;
                    that.$http.post('action/link/accntVisitPrice/multiCheckProds', {
                        accntId: that.pageParam.data.id,
                        priceList: data
                    }).then(data => {
                        if (data.success) {
                            resolve(data.result);
                        } else {
                            reject()
                        }
                    });
                });
            },
            /**
             * 检查数据
             * <AUTHOR>
             * @date 2020-09-08
             */
            checkedData () {
                const that = this;
                let flag = true;
                for (let i = 0; i < that.listData.length; i++) {
                    let item = that.listData[i];
                    if (item.row_status === 'NEW'){
                        delete item['id'];
                        delete item['created'];
                        delete item['createdBy'];
                        delete item['lastUpdated'];
                        delete item['lastUpdatedBy'];
                        delete item['orgId'];
                        delete item['postnId'];
                    }
                    that.$set(item, 'visitStatus', 'Y');
                    item.accntId = that.pageParam.data.id;
                    item.visitId = that.pageParam.visitId;
                    if (that.$utils.isEmpty(item.purchasePrice) || item.purchasePrice === 0) {
                        that.$message['warn']( `请输入请输入采购价`);
                        flag = false;
                        break;
                    }
                    if (that.$utils.isEmpty(item.undercurrentPrice) || item.undercurrentPrice === 0) {
                        that.$message['warn']( `请输入暗流价`);
                        flag = false;
                        break;
                    }
                    if (that.$utils.isEmpty(item.finalPrice) || item.finalPrice === 0) {
                        that.$message['warn']( `请输入消费者成交价`);
                        flag = false;
                        break;
                    }
                    // if (that.$utils.isEmpty(item.supplySource)) {
                    //     that.$message['warn']( `请选择供货货源`);
                    //     flag = false;
                    //     break;
                    // }
                }
                that.insertArr = this.listData.filter(item => !this.$utils.isEmpty(item.row_status));
                return flag;
            },
            /**
             * 提交
             * <AUTHOR>
             * @date 2020-09-07
             * @param param
             */
            async submit() {
                const that = this;
                let upsetListFlag = this.checkedData();
                if (that.insertArr.length === 0) {
                    return
                }
                if (upsetListFlag) {
                    that.$utils.showLoading();
                    that.$http.post('action/link/accntVisitPrice/batchUpsert', that.insertArr, {
                        handleFailed(error) {
                            that.$utils.hideLoading();
                        }
                    }).then(data => {
                        if (data.success) {
                            that.$utils.hideLoading();
                            let param = {refreshFlag: true};
                            that.$nav.back(param);
                        }
                    })
                }
            },
            /**
             * 获取库存采集数据
             * <AUTHOR>
             * @date 2020-09-08
             * @param param
             */
            queryCollectData () {
                const that = this;
                let rows = that.pageParam.visitApplicationStatus === 'visiting' ? undefined : 1;
                const param = {
                    page: 1,
                    pageFlag: false,
                    onlyCountFlag: false,
                    rows: rows,
                    oauth:'MY_POSTN', // 职位安全性
                    sort: 'created',
                    order: 'desc',
                    accntId: that.pageParam.data.id,
                    visitId: that.pageParam.visitId,
                    partOauthFlag: this.isProdPartCom ? 'Y' : '', //分品项-安全性
                    filtersRaw: []
                };
                if (this.isDealer) param.filtersRaw = [{id: 'postnId', property: 'postnId', value: this.userInfo.postnId}];
                that.$http.post('action/link/accntVisitPrice/queryByExamplePage', param).then(async data => {
                    if (data.success) {
                        if (that.$utils.isEmpty(data.rows)) {
                            // 新建
                            let data = await that.getDefaultData();
                            that.lastTimeCollect = data[0];
                            that.listData = that.listData.concat(data);
                        } else {
                            // 编辑
                            that.listData = data.rows;
                            that.initData = that.$utils.deepcopy(data.rows);
                            that.lastTimeCollect = data.rows[0];
                            that.lastTimeCollectFlag = true;
                            that.defaultProduct = data.rows.map(item => item.prodId);
                        }
                    }
                })
            },
            /**
              * 获取默认值
              * <AUTHOR>
              * @date 2020-09-07
              * @param param
            */
            getDefaultData () {
                const that = this;
                return new Promise(resolve => {
                    const param = {
                        pageFlag: false,
                        onlyCountFlag: false,
                        rows: 1,
                        oauth: 'MY_POSTN',
                        sort: 'created',
                        order: 'desc',
                        accntId: that.pageParam.data.id,
                        tabFlag: 'Y',
                        partOauthFlag: this.isProdPartCom ? 'Y' : '', //分品项-安全性
                        filtersRaw: []
                    }
                    if (this.isDealer) param.filtersRaw.push({id: 'postnId', property: 'postnId', value: this.userInfo.postnId});
                    that.$http.post('action/link/accntVisitPrice/queryByExamplePage', param).then((data) => {
                        if (data.success) {
                            if (!that.$utils.isEmpty(data.rows)) {
                                data.rows.forEach(item => {
                                    that.$set(item, 'row_status', 'NEW');
                                });
                                resolve(data.rows);
                            }
                            that.defaultProduct = data.rows.map(item => item.prodId);
                            that.lastTimeCollectFlag = true;
                        }
                    })
                })
            },
            /**
              * 确定
              * <AUTHOR>
              * @date 2020-08-12
              * @param flag
            */
            closePriceDialog () {
                if (this.$utils.isEmpty(this.priceAdjustOptions.row_status)) {
                    this.priceAdjustOptions.row_status = 'UPDATE';
                }
                this.priceDialogFlag = !this.priceDialogFlag
            },
            /**
              * 价格调整
              * <AUTHOR>
              * @date 2020-08-12
              * @param data 调整价格对象
              * @param key 调整价格对象索引
            */
            priceAdjust (data, key) {
                if(this.pageParam.sourcePage==='onlyView'){
                    return;
                }
                this.priceAdjustOptions = data;
                this.$refs.priceBottom.show();
            },
        }
    }
</script>

<style lang="scss">
    @import "../../../styles/list-card";
    .dealer-price-collection-page {
        font-family: PingFangSC-Regular,serif;
        /*deep*/.link-auto-list-top-bar {
                    border-bottom: none;
                }
        /*deep*/.link-item-icon {
                    width: 0;
                    padding-left: 0;
                }
        /*deep*/.link-item {
                    padding: 0;
                }
        /*deep*/.link-item-active {
                    background: #ffffff;
                }
        /*deep*/.link-item-content picker {
                     margin-right: 16px;
                }
        /*deep*/.link-button-size-large {
                    font-size: 32px!important;
                }
        /*deep*/ .link-search-input-no-padding-bottom {
                     padding-bottom: 24px!important;
                     border-bottom: 1px solid #F2F2F2;
                 }
        .last-time-collect {
            @include flex-start-center();
            @include space-between();
            height: 76px;
            background: #ffffff;
            text-align: center;
            padding-left: 24px;
            padding-right: 24px;
            .collect-content {
                @include flex-start-center();
                .icon-time-circle {
                    font-size: 28px;
                    color: #8C8C8C;
                }
                .last-collect-text {
                    font-size: 28px;
                    color: #8C8C8C;
                    padding-left: 8px;
                }
                .last-time {
                    font-size: 28px;
                    color: #000;
                    padding-left: 8px;
                }
            }
            .add-product {
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: right;
            }
        }
        .last-collect-client {
            @include flex-end-center();
            height: 76px;
            background: #ffffff;
            text-align: center;
            padding-left: 24px;
            padding-right: 24px;
            .add-product {
                font-size: 28px;
                color: #2F69F8;
                letter-spacing: 0;
                text-align: right;
            }
        }
        .perform-case-list-item {
            padding: 0!important;
            /*deep*/.link-icon {
                    font-size: 40px;
                    }
            border-radius: 16px;
            margin: 24px auto auto auto;
            /*deep*/.link-card-content{
                        padding: 0!important;
                    }
            .media-list {
                @include media-list();
                font-size: 28px;
                .top-rows {
                    padding-top: 24px;
                    padding-right: 24px;
                    padding-left: 24px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    .icon-right {
                        color: #BFBFBF;
                        font-size: 32px;
                    }
                    .store-level {
                        font-size: 28px;
                        color: #595959;
                    }
                }
                .middle {
                    padding-right: 24px;
                    padding-left: 24px;
                }
                .content-price {
                    padding-bottom: 24px;
                    padding-top: 24px;
                    background: rgba(255,90,90,0.05);
                    margin-top: 16px;
                    @include flex-center-center;
                    @include space-around;
                    .parting-line {
                        width: 2px;
                        height: 64px;
                        background-image: linear-gradient(180deg, rgba(191,191,191,0.00) 0%, #BFBFBF 51%, rgba(191,191,191,0.00) 100%);
                    }
                    .price-item {
                        @include flex-center-center;
                        @include direction-column;
                        .price-type {
                            color: #8C8C8C;
                            font-size: 24px;
                        }
                        .price {
                            color: #FF5A5A;
                        }
                    }
                }
                .store-input {
                    padding: 16px 24px 32px 24px;
                    width: 100%;
                    @include flex-start-center();
                    @include space-between();
                    font-family: PingFangSC-Regular,serif;
                    font-size: 28px;
                    letter-spacing: 0;
                    .store-input-rows {
                        @include flex-start-center();
                        .label {
                            color: #8C8C8C;
                        }
                        .value {
                            padding-left: 8px;
                            color: #000000;
                        }
                    }
                }
            }
        }
        .model-title {
            display: flex;
            margin-left: 24px;
            .title {
                font-family: PingFangSC-Regular,serif;
                font-size: 32px;
                color: #262626;
                letter-spacing: 0;
                text-align: center;
                line-height: 80px;
                height: 80px;
                width: 90%;
                padding-left: 40px;
            }
            .icon-close {
                color: #BFBFBF;
                font-size: 32px;
                line-height: 80px;
                height: 80px;
            }
        }
        .product-code {
            display: flex;
            margin-left: 24px;
            .code {
                background: #A6B4C7;
                border-radius: 8px;
                font-size: 28px;
                color: #FFFFFF;
                padding: 6px 12px;
            }
        }
        .edit-content {
            padding-left: 24px;
            padding-right: 24px;
            /*deep*/.link-item-body-right {
            padding-right: 16px;
           }
        }
        .edit-content-supply {
            padding-left: 24px;
            padding-right: 24px;
        }
        .product-name {
            @include flex-start-center;
            @include space-between();
            margin-left: 24px;
            margin-right: 24px;
            margin-top: 24px;
            .name {
                font-size: 32px;
                color: #262626;
                font-weight: bold;
            }
            .unit {
                font-size: 28px;
                color: #8C8C8C;
            }
        }
        .select-box {
            @include flex-start-center;
            border-bottom: 1px solid #F2F2F2;
            padding: 24px;
            .select-left {
                width: 100%;
                padding-left: 24px;
                .prod-num {
                    text {
                        font-family: PingFangSC-Regular,serif;
                        font-size: 28px;
                        color: #FFFFFF;
                        letter-spacing: 0;
                        line-height: 28px;
                        background: #A6B4C7;
                        border-radius: 8px;
                        padding: 6px 12px;
                    }
                    margin-top: 6px;
                    margin-bottom: 20px;
                }
                .store-name {
                    width: 100%;
                    font-family: PingFangSC-Regular,serif;
                    font-size: 32px;
                    color: #262626;
                    letter-spacing: 0;
                    font-weight: bold;
                }
                .store-supplier {
                    padding-top: 8px;
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                }
            }
        }
        .blank {
            height: 95px;
            width: 100%;
        }
        .blank-product {
            height: 146px;
            width: 100%;
            background: #F2F2F2;
        }
        .blank-product-sure {
            height: 202px;
            width: 100%;
            background: #F2F2F2;
        }
        .bottom-btn {
            background: #ffffff;
            position: fixed;
            width: 100%;
            bottom: 0;
            left: 0;
            padding-top: 16px;
            padding-bottom: 34px;
            @include flex-start-center();
            .all-select {
                height: 96px;
                padding-left: 24px;
                @include flex-start-center();
                width: 50%;
                font-size: 28px;
                color: #595959;
                letter-spacing: 0;
                line-height: 28px;
                .iconfont {
                    font-size: 40px;
                    color: #BFBFBF;
                }
                .icon-yiwanchengbuzhou {
                    color: $color-primary;
                }
                .all-select-text {
                    padding-left: 16px;
                }
            }
            .sure-btn {
                width: 340px;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
                box-shadow: 0 8px 24px 0 rgba(47,105,248,0.50);
            }
            .sure-btn-edit {
                width: 702px;
                height: 96px;
                margin-right: 24px;
                margin-left: 24px;
                box-shadow: 0 8px 24px 0 rgba(47,105,248,0.50);
            }
        }
        .button-bottom {
            padding-bottom: 32px;
        }
    }
</style>
