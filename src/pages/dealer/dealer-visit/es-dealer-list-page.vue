<template>
    <link-page class="es-dealer-list-page">
        <link-auto-list :option="terminalListOption" :hideCreateButton="true"
                        :class="{'claim-style': copyTerminalFlag && !hideCreateButton && !isShowOutboundPage}"
                        :searchInputBinding="{props:{placeholder:'经销商编码/经销商名称/实时定位地址'}}">
            <view slot="searchRight" class="search-container" @tap="goMapList"
                  v-if="searchRightMap && pageParam.source !== 'terminalProtocol'">
                <link-icon icon="icon-ditu"/>
                <view>地图</view>
            </view>
            <link-filter-group slot="filterGroup">
                <view>
                    <link-filter-item label="创建时间" :param="{sort:{field:'created',desc:true}}"/>
                    <link-filter-item label="最新更新" :param="{sort:{field:'lastUpdated',desc:true}}"/>
                    <view style="font-size: 12px;">
                        <link-icon icon="icon-icon-tip"/> 此处仅展示存在实时定位地址的经销商
                    </view>
                </view>
           
            </link-filter-group>

            <template slot-scope="{data,index}">
                <item :key="index" :data="data" :arrow="false" class="terminal-list-item"
                      @tap="isSelectTerminalChange(data)">
                    <view class="terminal-list" slot="note">
                        <view class="list-cell">
                            <view class="media-list">
                                <image class="media-list-logo" :src="data.storeUrl" @tap.stop="previewStoreUrl(data)" lazy-load="true"></image>
                                <view class="store-content">
                                    <view class="store-content-representative">
                                        <view class="store-type-tag" v-if="typeList.includes(data.acctType)">{{data.acctType | lov('ACCT_TYPE')}}</view>
                                        <view class="terminal-name">{{data.acctName }}</view>
                                    </view>
                                    <view class="store-content-choose">
                                        <view class="store-content-representative">
                                            <view class="terminal-type">编码</view>
                                            <view class="terminal-name">{{data.acctCode}}</view>
                                        </view>
                                    </view>
                                    <view class="store-content-representative">
                                        <view class="terminal-type">跟进人</view>
                                        <view class="terminal-name">{{data.salesManListString}}</view>
                                    </view>
                                    <view class="store-content-address" v-if="data.orieLocation">
                                        <view class="store-address">{{data.orieLocation}}</view>
                                    </view>
                                    <view class="store-content-address" v-else>
                                        <view @tap.stop="toDeaerInfo(data)" class="store-address" style="color:red;font-size: 12px;">"缺少实时定位地址，请前往维护&nbsp;&nbsp;<link-icon icon="icon-right"/></view>
                                    </view>

                                </view>
                            </view>
                        </view>
                    </view>
                </item>
            </template>
        </link-auto-list>
    </link-page>
</template>

<script>
import lnkTaps from '../../core/lnk-taps/lnk-taps'
import { getFeature, getSecurityFeatures, parseFeature } from '../../../utils/security'
import PAGES_PATH from '../../../constants/pagesPath'
import ColorTag from '../../terminal2/components/ColorTag.vue';

export default {
    name: "es-dealer-list-page",
    components: {
        lnkTaps,
        ColorTag
    },
    data () {
		const userInfo = this.$taro.getStorageSync('token').result
        const menuId = this.pageParam.menuId;
        let detailsOauth = this.$utils.isPostnOauth() === 'MY_POSTN'? 'MULTI_POSTN': this.$utils.isPostnOauth();
         if (detailsOauth === 'MY_ORG') {
            detailsOauth = 'MULTI_ORG'
        }
        let filtersRaw = [{id:"acctType_0_auto",property:"acctType",value:"Dealer"}];
        // 是否从退货出库页面跳转
        const isShowOutboundPage = ['InactiveDistributorReturnApply', 'InactiveTerminalReturnApply'].includes(this.pageParam.source);

        const terminalListOption = new this.AutoList(this, {
            module: 'export/link/es/accnt',
            sortField: ' ',
            loadOnStart: false,
            request: async (requestConfig) => {
                if(requestConfig.param.filtersRaw){
                
                    let isSearch = false;
                    requestConfig.param.filtersRaw.forEach(item => {
                        // 搜索的时候，就不用过滤实时地址为空的数据
                        if(item.property === '[acctName,acctCode,orieLocation]'){
                            isSearch = true;
                        }
                    });
                    if(!isSearch){
                        requestConfig.param.filtersRaw.push({id: 'latitude', property: 'latitude', operator: 'not null', value: ''})
                        requestConfig.param.filtersRaw.push({id: 'longitude', property: 'longitude', operator: 'not null', value: ''})
                        requestConfig.param.filtersRaw.push({id: 'orieLocation', property: 'orieLocation', operator: 'not null', value: ''})
                    }

                    requestConfig.param.filtersRaw = JSON.stringify(requestConfig.param.filtersRaw)
                }
                const result = await this.$httpForm.post(requestConfig.url, {...requestConfig.param});
                return {
                    rows: result.rows,
                    // noMore: false,
                    success: result.success,
                    code: result.code,
                    total: result.total
                };
            },
            param: {
                filtersRaw: filtersRaw,
                oauth: detailsOauth,
                // oauth: 'MULTI_ORG',
                // brandComOrgType: 'BranchCompany',
            },
            // queryFields: 'id,acctType,censusLabels,isExclusiveShop,fourColorLabel,financingFlag,acctName,billTitle,'
            //     + 'codeMark,acctStage,acctCategory,subAcctType,acctLevel,capacityLevel,salesmanBrandCom,joinFlag,'
            //     + 'acctCode,multiAcctMainFlag,salesManListString,province,city,district,townName,address,storePicKey,'
            //     + 'storePicPreKey,tagList,judgmentFlag,doorSigns,terminalDigitization,accntPartner,orgId,editApprovalStatus,'
            //     + 'isSpringAct,displayPolicyType,salesmanAreaId,strategicFlag,mdmCompanyCode,trafficHighland,fstName,salesmanBrandComCode,'
            //     + 'starfireFlag',
            sortOptions: null,
            searchFields: ['acctName','acctCode','orieLocation'],
            hooks: {
                beforeLoad (options) {
                    if (this.$utils.isEmpty(options.param.sort.trim())) {
                        // lj2024-3424：es 使用 yearScanCode 按从大到小的顺序进行排序
                        if (this.isEs) {
                            options.param.sort = 'yearScanCode';
                            options.param.order = 'desc';
                        } else {
                            delete options.param.order;
                            delete options.param.sort;
                        }
                    }
                    this.queryParam = JSON.parse(JSON.stringify(options.param))
                    this.queryParam.onlyCountFlag = true
                    let filtersRaw = this.$utils.deepcopy(options.param.filtersRaw);
                    let displayPolicyTypeArr = []
                    this.newSearch.forEach((i, index) => {
                        const findIndex = filtersRaw.findIndex((item) => item.property === i.field);
                        if (findIndex !== -1) {
                            let newArr = []
                            if(filtersRaw[findIndex].operator === 'in'){
                                let str = filtersRaw[findIndex].value.slice(1, filtersRaw[findIndex].value.length - 1)
                                newArr = str.split(',')
                            }else{
                                newArr = filtersRaw[findIndex].value.split(',')
                            }
                            displayPolicyTypeArr = [ ...displayPolicyTypeArr, ...newArr]
                            filtersRaw.splice(findIndex, 1)
                        }
                    })

                    let filters1 = [],filters2 = []
                    if(displayPolicyTypeArr.length){
                        displayPolicyTypeArr.forEach((i) => {
                            const newArr = i.split('&&');
                            const sapCode = newArr[0]
                            const getPolicyType = newArr[1]
                            filters1.push(sapCode);
                            filters2.push(getPolicyType);
                        })
                        filters1 = [...new Set([...filters1])]
                        filters2 = [...new Set([...filters2])]
                        const newObj1 = {
                            id: "displayPolicyType",
                            property: "displayPolicyType",
                            operator: 'and or like',
                            value: `[${filters2.join(',')}]`
                        }
                        const newObj2 = {
                            id: "mdmCompanyCode",
                            property: "mdmCompanyCode",
                            operator: 'in',
                            value: `[${filters1.join(',')}]`
                        }
                        filtersRaw.push(newObj1)
                        filtersRaw.push(newObj2)
                    }else{
                        const findIndex = filtersRaw.findIndex((item) => item.property === 'mdmCompanyCode');
                        if (findIndex !== -1) {
                            filtersRaw.splice(findIndex, 1)
                        }
                    }
                    
                    options.param.filtersRaw = filtersRaw;

                },
                async afterLoad (data) {
                    if(!this.alreadyGetInfo) {
                        const chuanDongOrgId = await this.$utils.getCfgProperty('East_Sichuan_Tequ_Area_ID') || '521074619762290688'
                        this.positionChuanDong = this.userInfo.coreOrganizationTile.l5Id === chuanDongOrgId
                        this.alreadyGetInfo = true
                    }
                    this.zhanKaiHash.length = 0

                    data.rows.map(async (item) => {
                        const { fstName, salesManListString } = item;
                        let formattedSalesManList = salesManListString || '';
                        if (fstName) {
                            const filteredSalesManList = formattedSalesManList
                                .replaceAll(fstName + ',', '') // 移除以姓名+逗号开头的部分
                                .replaceAll(',' + fstName, '') // 移除以逗号+姓名结尾的部分
                                .replaceAll(fstName, ''); // 移除仅包含姓名的部分
                            formattedSalesManList = `(${fstName})${filteredSalesManList}`;
                        }
                        this.$set(item, 'formattedSalesManList', formattedSalesManList);

                        if (!this.$utils.isEmpty(item.storePicPreKey) && item.acctType === 'Terminal') {
                            let urlData = this.$image.getSignedUrl(item.storePicPreKey);
                            this.$set(item, 'storeUrl', urlData);
                        } else {
                            this.$set(item, 'storeUrl', this.$imageAssets.terminalDefaultImage);
                        }
                    })

                }
            },

        });
        return {
            // 是否使用es查询
            isEs: false,
            copyTerminalFlag: false, //终端复制
            filtersRaw: filtersRaw,
            typeList: [],
            categoryLst: [],
            sublist: [],
            levelList: [],
            caplist:[],
            positionChuanDong: false,
            terminalCount: 0,
            exclusiveFlag: userInfo.orgType==='Company' || userInfo.coreOrganizationTile.l3Id === '58929586649432064', //是否泸州老窖国窖店形象店显示 品牌公司属于国窖以及销售公司直属人员
            isDealer: false,
            listOauth: detailsOauth,
            menuId,
            hideCreateButton: true, // 是否隐藏新建按钮
            searchRightMap: null, // 是否展示搜索栏右侧地图查询入口
            terminalListOption,
            userInfo, // 用户信息
            broadCompanyCode: '', // 博大公司编码集
            zhanKaiHash: [], //记录列表中那些数据展开了终端标签
            features: [],
            newSearch: [],
            isShowOutboundPage // 是否从退货出库页面进入当前页面
        }
    },
    async created() {
        // 2024/0830 zq 分销商退货出库动态设置title
        if (this.pageParam.source === 'InactiveDistributorReturnApply') {
            this.$taro.setNavigationBarTitle({title: '分销商列表'});
        }
        // 是否使用es查询
        this.isEs = await this.queryCfgProperty('useEsQuerySimpleAccountPage') === 'Y';
        this.terminalListOption.methods.reload();
        this.getList()
        this.$bus.$on('terminalListRefresh', async () => {
            await this.terminalListOption.methods.reload();
        });
        this.isDealer = await this.$utils.getDealerOauth(this.userInfo);
        // 获取用户信息
        this.features = getSecurityFeatures('ALL', '/pages/terminal/terminal/es-terminal-list-page')
        this.hideCreateButton = ['visitListPage', 'quotaTerminal', 'advanceOrder', 'stockTaking', 'terminalProtocol', 'quickEntryVisitTerminal', 'InactiveTerminalReturnApply', 'InactiveDistributorReturnApply'].includes(this.pageParam.pageFlag) || !this.getSecurityFlag('ADDTERMINAL');
        this.searchRightMap = !['quotaTerminal', 'advanceOrder', 'stockTaking', 'quickEntryVisitTerminal', 'InactiveTerminalReturnApply', 'InactiveDistributorReturnApply'].includes(this.pageParam.source);
        // 获取博大公司编码标识参数
        this.broadCompanyCode = await this.queryCfgProperty('getPurchaseSumForOrder');
        this.getTypeArray();
    },
    methods: {
        toDeaerInfo(data){
            data.data = { id: data.id }
            this.$nav.push(PAGES_PATH.DEALER_DETAILS_PAGE, {data:data})
        },
        async getTypeArray() {
            const list = await this.$lov.getLovByTypeArray(['ACCT_TYPE', 'ACCNT_CATEGORY', 'SUB_ACCT_TYPE','ACCT_LEVEL','CAPACITY_LEVEL']);
            list[0].forEach(item => {
                this.typeList.push(item.val)
            });
            list[1].forEach(item => {
                this.categoryLst.push(item.val)
            });
            list[2].forEach(item => {
                this.sublist.push(item.val)
            });
            list[3].forEach(item => {
                this.levelList.push(item.val)
            });
            list[4].forEach(item => {
                this.caplist.push(item.val)
            });
        },
        /**
         * 数组拆分
         * <AUTHOR>
         * @date 2024-07-04
         * @param {Array} array 拆分数组源
         * @param {String} field 拆分字段
         */
        splitArrayByField(array, field) {
          return array.reduce((acc, item) => {
            const key = item[field];
            if (!acc[key]) {
              acc[key] = [];
            }
            acc[key].push(item);
            return acc;
          }, {});
        },
        /**
         * 请求陈列政策类型分类筛选数据
         * <AUTHOR>
         * @date 2024-06-19
         */
        async getList(){
            let {companyId, orgType} = this.userInfo
            let param = {
                pageFlag: false,
                onlyCountFlag: false,
                filtersRaw: [{property: 'effectiveFlag', operator: '=', value: 'Y', id: 'effectiveFlag'}],
                attr4: 'pc',
                attr6: 'sign',
                oauth: 'MY_ORG'
            }
            if(orgType !== "Company"){
                param.organizationId = companyId
            }
            const list = await this.$lov.getLovByTypeArray(['ZCXL', 'DISPLAY_POLICY_TYPE']);
            const {success, rows} = await this.$http.post('action/link/displayPolicy/queryByExamplePage',param);

            const type1 = list[0], type2 = list[1];
            let newArr = [],newObj = {}
            if(success && rows.length){
                rows.forEach((item) => {
                    item.displayMinType = item.displayMinType ? item.displayMinType : 'other'
                    item.allFiled = item.organizationId + item.displayMinType
                    const nameObj = type1.find(it=>it.val === item.displayMinType)
                    const name = nameObj ? nameObj.name : '其他'
                    item.showName = item.organizationName + '-' + name
                })
                newObj = this.splitArrayByField(rows,'allFiled')
                for(let key in newObj){
                    let newObjs = {
                        field: 'displayPolicyType_' + key,
                        type: 'select',
                        label: newObj[key][0].showName,
                        data: newObj[key].map(item=>{
                            const nameObj2 = type2.find(ite=>ite.val === item.displayPolicyType)
                            return {
                                val: (item.brandSapCode ? item.brandSapCode : 'null') + '&&' + item.displayPolicyType,
                                name: nameObj2 ? nameObj2.name : '暂无'
                            }
                        })
                    }
                    newArr.push(newObjs)
                }
                this.newSearch = newArr
            }
        },


        async onTab(active, index) {
            if (active.val === 'terClaim') {
                this.terminalListOption.option.url = { queryByExamplePage: 'action/link/accnt/queryCopyAccountPage' };
                this.terminalListOption.option.param = {
                    mdmCompanyCode: this.userInfo.coreOrganizationTile.brandCompanyCode,
                    userId: this.userInfo.id,
                    filtersRaw: [
                        {id: 'acctType', property: 'acctType', value: '[Dealer]', operator: 'in'},
                        {id: 'multiAcctMainFlag', property: 'multiAcctMainFlag', value: 'Y', operator: '='},
                        {id: 'dataSource', property: 'dataSource', value: 'WeChatWork', operator: '='},
                        {id: 'acctStatus', property: 'acctStatus', value: 'Y', operator: '='}
                    ],
                }
                this.terminalListOption.option.searchFields = ['orieLocation','acctName', 'acctCode'];
                await this.terminalListOption.methods.reload();
            } else {
                this.terminalListOption.option.url = { queryByExamplePage: 'export/link/es/accnt/queryByExamplePage'};
                this.terminalListOption.option.param = {
                    // tagListFlag: 'Y',  //是否需要终端标签字段
                    filtersRaw: this.filtersRaw,
                    oauth: this.listOauth,
                    // oauth: 'MULTI_ORG', // 应后端要求暂时定死为all
                }
                this.terminalListOption.option.searchFields = ['orieLocation','acctName', 'acctCode'];
                await this.terminalListOption.methods.reload();
            }
        },
        getValInArr(arr = []) {
            return arr.filter(item => !!item.tagValue)
        },
        /**
         * 记录列表标签展开收缩状态
         * <AUTHOR>
         * @date 2022-7-26
         */
        changeState(index) {
            this.$set(this.zhanKaiHash, index, !this.zhanKaiHash[index])
        },
        /**
         * 根据编码获取对应的值列表或表达式结果
         * <AUTHOR>
         * @date 2022-9-15
         */
        getSecurityFlag(code) {
            const feature = getFeature(this.features, code)
            const exp = parseFeature(feature)
            return exp
        },

        /**
         * 门头照片预览
         * <AUTHOR>
         * @date 2020-09-22
         * @param param
         */
        async previewStoreUrl(param) {
            const compressSuffix = '/suoluetu';
            const defaultSuffix = 'default';
            if (this.$utils.isEmpty(param.storeUrl) || param.storeUrl.indexOf(defaultSuffix) !== -1) {
                return;
            }
            const inOptions = {
                current: param.storeUrl.replaceAll(compressSuffix, ''),
                urls: [param.storeUrl]
            };
            this.$image.previewImages(inOptions);
        },
        /**
         * 监控返回函数
         * <AUTHOR>
         * @date 2020-09-15
         * @param param
         */
        onBack (param) {
            if (this.$utils.isEmpty(param)) {
                return
            }
            if (param.refreshFlag) {
                this.terminalListOption.methods.reload();
            }
        },
        goCreateProtocol(data) {
            this.$nav.push('/pages/terminal2/protocol-list/protocol-edit-page.vue', {
                terminalFlag: true,
                terminalDetail: data,
                backEdit: data.agrStatus,
                source: 'activity'
            })
        },
        /**
         * 返回上一页
         * <AUTHOR>
         * @date 2020-10-19
         * @param data 返回携带终端数据
         */
        goBack (data) {
            // lj2024-3659：国窖-贷款逾期终端限制下单现金券预订单
            if (this.pageParam.discountCouponOrder === false
                && data.mdmCompanyCode === '5600'
                && data.financingFlag === 'Overdue') {
                this.$showError('存在逾期未还贷款，暂停现金券预订单下单，请提醒客户及时还款！');
                return;
            }
            if(
                this.pageParam.source === 'advanceOrder' &&
                this.userInfo.coreOrganizationTile.brandCompanyCode === '5600' &&
                data.acctStage !== 'ykf'
            ) {
                this.$message.warn('当前终端未认证，不可下预订单')
                return
            }
            let param = {
                terminalFlag: true,
                terminalDetail: data,
            };
            this.$nav.back(param)
        },
        /**
         * 查看详情
         * <AUTHOR>
         * @date 2020-08-31
         * @param data 详情信息
         */
        async goToItem(data) {
            try {
                const resp = await this.$http.post('action/link/accnt/queryById', {
                    id: data.id,
                    // 是否需要外部系统失败原因字段
                    synExternalSysInfo: 'needSynContent'
                });
                if (!resp.success) {
                    this.$showError('查询终端详情失败！' + resp.result);
                    return;
                }
                data = resp.result;
            } catch (e) {
                this.$showError('查询终端详情出错！');
            }
            if(this.pageParam.source === 'quotaTerminal') {
                const param = {source: 'terminalList', data:data}
                this.$nav.back(param)
                return
            }
            if (this.pageParam.source === 'stockTaking') {
                this.$nav.push('/pages/dealer/dealer-visit/dealer-inventory-list-page', {data: data})
                return
            }
            if (this.pageParam.pageFlag === 'visitListPage') {
                this.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-perform-page', {data: data, createdVisitOriginal: 'terminalMapToTerminalList'})
            } else {
                this.$nav.push('/pages/dealer/dealer-visit/dealer-client-details-page', {data: data, claimList: true, newFlag: !this.hideCreateButton, listOauth: this.listOauth})
            }
        },
        /**
         * 切换地图模式
         * <AUTHOR>
         * @date 2020-09-01
         */
        goMapList () {
            if (this.pageParam.pageFlag === 'visitListPage') {
                this.$nav.redirect('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {
                    source: 'dealerList',
                    pageFlag: 'visitListPage',
                    menuId: this.menuId,
                    newFlag: !this.hideCreateButton
                })
            } else {
                this.$nav.push('/pages/dealer/dealer-visit/dealer-visit-map-list-page', {
                    source: 'dealerList',
                    menuId: this.menuId,
                    newFlag: !this.hideCreateButton
                })
            }
        },
        /**
         * 根据key值获取参数配置
         * <AUTHOR>
         * @date 8/11/21
         * @param key 参数配置健值
         */
        async queryCfgProperty(key) {
            const data = await this.$http.post('export/link/cfgProperty/queryByExamplePage', {
                filtersRaw: [{id: 'key', property: 'key', value: key}]
            });
            if (data.success && data.rows && data.rows.length) {
                return data.rows[0].value;
            } else {
                return 'noMatch';
            }
        },
        /**
         * 判定点击内容跳转逻辑
         * <AUTHOR>
         * @date 24/8/20
         */
        isSelectTerminalChange(data) {
            if (['advanceOrder', 'quickEntryVisitTerminal'].includes(this.pageParam.source)) return this.goBack(data)
            if (this.pageParam.source === 'terminalProtocol') return this.goCreateProtocol(data)
            if (this.isShowOutboundPage) return this.changScanCodePage(data)
            return this.goToItem(data)
        },
        /**
         * 退货出库选择终端跳转新建扫码页面
         * <AUTHOR>
         * @date 24/8/20
         */
        changScanCodePage(data) {
            this.$nav.push(PAGES_PATH.OUTBOUND_SCAN_CODE_MIDDLE_PAGE, {
                data,
            })
        }
    }
}
</script>

<style lang="scss">

.es-dealer-list-page {
    .claim-style{
        .link-sticky.link-sticky-top{
            top:92px !important;
        }
        .link-auto-list-wrapper{
            margin-top: 92px!important;
        }
    }

    .terminal-count {
        padding: 8px 16px;
        margin-right: 8px;
        white-space: nowrap;
        display: inline-block;
        background-color: #f2f2f2;
        color: #333333;
        border-radius: 4px;
    }
    .search-container {
        padding-left: 12px;
        color: #8C8C8C;
        font-size: 28px;
        text-align: center;
    }
    .terminal-list-item {
        background: #FFFFFF;
        width: 702px;
        margin: 24px auto auto auto;
        border-radius: 16px;
    }
    /*deep*/.link-item {
                padding: 0;
            }
    /*deep*/.link-item-icon {
                width: 0;
                padding-left: 0;
            }
    /*deep*/.link-dropdown-content {
                padding: 24px;
            }
    .terminal-list {
        .list-cell {
            .media-list {
                @include flex;
                padding: 24px 16px 24px 24px;
                .media-list-logo {
                    /*box-shadow: 0 4px 31px 0 rgba(0,44,152,0.22);*/
                    border-radius: 16px;
                    width: 128px;
                    height: 128px;
                    overflow: hidden;
                }
                .store-content {
                    width: 80%;
                    .store-content-top {
                        @include flex-start-center;
                        @include space-between;
                        margin-left: 24px;
                        .store-title {
                            font-family: PingFangSC-Semibold,serif;
                            font-size: 32px;
                            color: #262626;
                            letter-spacing: 0;
                            line-height: 36px;
                            width: 77%;
                            height: 36px;
                            overflow: hidden;
                        }
                        .store-level {
                            margin-right: -3px;
                            width: 120px;
                            height: 44px;
                            image {
                                width: 100%;
                                height: 100%;
                            }
                        }
                    }

                    .store-content-middle {
                        display: flex;
                        justify-content: space-between;
                        padding-left: 32px;
                        .left ,.right{
                            @include flex-start-center;
                            flex-wrap: wrap;
                            margin-top: 10px;
                            .store-type {
                                white-space: nowrap;
                                border: 2px solid #2F69F8;
                                border-radius: 8px;
                                font-size: 20px;
                                padding-left: 18px;
                                padding-right: 18px;
                                line-height: 40px;
                                height: 40px;
                                color: #2F69F8;
                                margin-right: 10px;
                                margin-top: 10px;
                            }
                        }
                        .item-tag {
                            width: 58px;
                            height: 40px;
                            line-height: 36px;
                            text-align: center;
                            color: #ffffff;
                            background: #2F69F8;
                            box-shadow: 0 3px 4px 0 rgba(47,105,248,0.35);
                            border-radius: 8px;
                            padding-left: 27px;
                            padding-right: 27px;
                            font-size: 20px;
                            margin-right: 8px;
                            margin-top: 10px;
                        }
                    }
                    .store-content-representative {
                        @include flex;
                        align-items: center;
                        margin-left: 24px;
                        margin-bottom: 20px;
                        width: calc(100% - 24px);
                        overflow: hidden;
                        white-space: nowrap;
                        text-overflow: ellipsis;
                        .store-type-tag {
                            padding: 4px;
                            color:#2F69F8;
                            border: 1px solid #2F69F8;
                            box-sizing: border-box;
                            margin: 0 10px;
                        }
                        .terminal-type {
                            color: #8C8C8C;
                            min-width: 50px;

                        }
                        .terminal-name {
                            font-size: 24px;
                            color: #000000;
                            letter-spacing: 0;
                            padding-left: 8px;
                            width: calc(100% - 50px);
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                        }
                    }
                    .store-content-choose {
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                    }
                    .store-content-address {
                        margin-left: 24px;
                        margin-top: 18px;
                        font-family: PingFangSC-Regular,serif;
                        font-size: 24px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 32px;
                    }
                }
            }
        }
        .content-bottom {
            display: flex;
            justify-content: space-between;
            padding: 0 24px 10px;
            .view-port {
                position: relative;
                width: 100%;
                overflow: hidden;
                .label {
                    width: 90%;
                    display: flex;
                    flex-wrap: wrap;
                    position: relative;
                    .label-item {
                        padding: 4px 10px;
                        color: #2F69F8;
                        background-color: rgb(233,242,255);
                        margin: 6px 8px;
                        border-radius: 10px;
                        height: 34px;
                    }
                }
                .label::before {
                    position: absolute;
                    right: -12%;
                    bottom: 2px;
                    background-color: white;
                    content: '';
                    z-index: 2;
                    width: 12%;
                    height: 52px;
                }
                .iconZhankai {
                    width: 10%;
                    position: absolute;
                    top: 0;
                    right: 0;
                    height: 100%;
                    text-align: right;
                }
            }
        }
    }
}
</style>
