<!--
终端普查-终端新建
<AUTHOR>
@date 2024-05-06
-->
<template>
    <link-page class="census-dealer-add">
        <link-form ref="newForm" :value="formOption" :rules="formRules">
            <link-form-item label="客户名称" required field='acctName'>
                <link-input placeholder="请输入客户名称" v-model="formOption.acctName"/>
            </link-form-item>
            <link-form-item label="客户大类">
                <link-lov type="ACCT_TYPE" v-model="formOption.acctType"/>
            </link-form-item>
            <link-form-item label="门店地址" required  field='province' v-if="formOption.id">
                <view slot="title"  @tap="getLocation('storeAddr')">
                    <text>门店地址</text>
                    <link-icon icon="icon-location" class="link-location"/>
                </view>
                <view class="address-placeholder" v-if="$utils.isEmpty(formOption.province) && $utils.isEmpty(formOption.city) && $utils.isEmpty(formOption.district)" @tap="getLocation('storeAddr')">
                    请选择门店地址
                </view>
                <view class="address-color" @tap="getLocation('storeAddr')" v-else>
                    <text v-if="!$utils.isEmpty(formOption.province)">{{formOption.province}}</text>
                    <text v-if="!$utils.isEmpty(formOption.city)">/{{formOption.city}}</text>
                    <text v-if="!$utils.isEmpty(formOption.district)">/{{formOption.district}}</text>
                    <text v-if="!$utils.isEmpty(formOption.town)">/{{formOption.town}}</text>
                </view>
            </link-form-item>
            <link-form-item  label="详细地址" required  field='addrDetaiAddr'
                            v-if="$utils.isNotEmpty(formOption.province)"
                            vertical style="border-bottom: 1px solid rgb(247,247,247);">
                <link-textarea  @done="addressDetail('address')" v-model="formOption.addrDetaiAddr" :nativeProps="{maxlength: 300}"/>
            </link-form-item>
            <link-form-item label="门头照片" required >

            </link-form-item>
            <lnk-img-watermark :parentId="formOption.id"
							 v-if="formOption.id"
                             :moduleType="moduleType"
                             :moduleName="moduleName"
                             :continueFlag="false"
                             :delFlag="true"
                             :album="true"
                             :useModuleName="formOption.frontPhoto"
                             @imgUploadSuccess="imageArrLength"
                             @imgDeleteSuccess="deleteImageArrLength"
                             :maxCount="1"
                             :newFlag="imgLength===0"/>
            <link-form-item label="竞品品牌" required   field='collaborativeBrands'>
                <link-lov type="COMPETITIVE_GOODS_TYPE" v-model="formOption.collaborativeBrands" multiple/>
            </link-form-item>
            <link-form-item label="备注">
            </link-form-item>
            <link-textarea placeholder="请输入备注项" v-model="formOption.comments"/>
        </link-form>
        <link-sticky class="bottom-btn">
            <link-button class="sure-btn" size="normal" @tap="submit">提交</link-button>
        </link-sticky>
    </link-page>
</template>

<script>
    import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark';
    import {PageCacheManager} from "../../../utils/PageCacheManager";
    definePageConfig({
        navigationBarTitleText: '经销商新增客户'
    })
    export default {
        name: 'census-dealer-add',
        components: {LnkImgWatermark},
        data(){
            const initialData = {
                formOption:{
                    'acctName': null,
                    'acctType':'Terminal',
                    'province':'',
                    'city':'',
                    'district':'',
                    'town':'',
                     id:'',
                    'acctCode':'',
                    'longitude':'',
                    'latitude':'',
                    'addrDetaiAddr':'',
                    'frontPhoto':'',
                    'collaborativeBrands':[],
                    'comments':''
                },
                moduleType: 'terminalCensus',
                 moduleName: '门头照片',
                 imgLength: 0,
                formRules:{}
            }
            return PageCacheManager.getInitialData({
                ctx: this,
                path: 'dealer/dealer-visit/parent-dealer-add-page.vue',
                title: '新建客户',
                initialData,
                initializer: () => {this.initData()}
            })
        },
        async beforeCreate() {

        },
        /**
         *  @description: 初始化所选地理信息
         *  @author: 彭杨
         *  @date: 2023/9/15
         */
          async onShow(){
              const location = this.$locations.QQGetLocation();
              if(location && this.choseLocation){
                  this.choseLocation = false
                  let addressInfo =  await this.$locations.reverseTMapGeocoder(location.latitude, location.longitude, '终端普查-终端新建');
                  await this.setAddress(addressInfo)
               }
            },
        methods: {
            /**
              * 详细地址广播事件
              * <AUTHOR>
              * @date 2024/04/29
            */
            addressDetail (addr) {
                if(addr === 'address'){
                    if (this.$utils.isEmpty(this.formOption.addrDetaiAddr.trim())) {
                        this.$message['warn']('门店地址详细地址不能为空');
                        return;
                    }
                }
            },
            /**
             *  提交
             *  @auth 谭少奇
             *  @date 2024/05/06
             */
            async submit(){
                try{
                    await this.$refs.newForm.validate()
                    if(this.imgLength>0){
                        const url = this.pageParam.edit ? 'action/link/terminalCensus/update' : 'action/link/terminalCensus/insert'
                        const data = await this.$http.post(url, this.formOption);
                        if(data.success){
                            let params = {refreshFlags: true};
                            this.$nav.back(params);
                        }
                    }else{
                        this.$message.warn('请上传门头照片！')
                    }

                }catch(e){
                    //TODO handle the exception
                }

            },
            /**
             * 获取打卡图片长度
             * <AUTHOR>
             * @date 2020-08-31
             * @param param 打卡图片长度
             */
            imageArrLength (param) {
                console.log(param,'param')
                this.imgLength =  param.length;
            },
            deleteImageArrLength(param){
                this.imgLength =  param.length;
            },
            /**
             * 设置地址编码内容
             * <AUTHOR>
             * @date 2024/04/10
             * */
            async setAddress(addressInfo){
                let address = {
                    latitude: addressInfo.wxMarkerData[0].latitude,
                    longitude: addressInfo.wxMarkerData[0].longitude,
                    province: addressInfo['originalData'].result.addressComponent['province'],
                    city: addressInfo['originalData'].result.addressComponent['city'],
                    district: addressInfo['originalData'].result.addressComponent['district'],
                    town: addressInfo['originalData'].result.addressComponent['townName'],
                    addrDetaiAddr: addressInfo.originalData.result['sematic_description']
                }
                this.formOption = Object.assign({}, this.formOption, address);
                try {
                    this.$utils.showLoading()
                    const data = await this.$http.post('/action/link/alladdress/queryEffectiveByTownCode',{addrCode:addressInfo['originalData'].result.addressComponent.town})
                    if(data.success) {
                        this.$set(this.formOption, 'cityCode', data.cityCode)
                        this.$set(this.formOption, 'provinceCode', data.provinceCode)
                        this.$set(this.formOption, 'districtCode', data.districtCode)
                        this.$set(this.formOption, 'townCode', data.townCode)
                        this.$utils.hideLoading();
                    }else{
                       this.$set(this.formOption, 'cityCode', null)
                       this.$set(this.formOption, 'provinceCode', null)
                       this.$set(this.formOption, 'districtCode', null)
                       this.$set(this.formOption, 'townCode', null)
                    }
                }catch (e) {
                    this.$set(this.formOption, 'cityCode', null)
                    this.$set(this.formOption, 'provinceCode', null)
                    this.$set(this.formOption, 'districtCode', null)
                    this.$set(this.formOption, 'townCode', null)
                    this.$utils.hideLoading();
                }
            },
            // 获取地址
            async getLocation(){
                let that = this
                const addressInfo = await this.$locations.getCurrentCoordinate();

            if (!that.$utils.isEmpty(addressInfo)) {
                await this.$locations.chooseLocation(addressInfo.latitude, addressInfo.longitude);
                this.choseLocation = true
            } else {
            //     let userLocation = await that.$locations.openSetting();
            //     if (userLocation['scope.userLocation']) {
            //         that.coordinate = await that.$locations.getCurrentCoordinate();
            //         that.$store.commit('coordinate/setCoordinate', that.coordinate);
            //         await that.initData();
            //     }
            }

            },
            /**
             * 数据初始化
             * <AUTHOR>
             * @date 2020-09-06
             */
            async initData() {
                this.coordinate =  await this.$locations.getCurrentCoordinate();
                if(this.pageParam.edit){
                    this.formOption = this.pageParam.data
					this.formOption.id = this.pageParam.data.id
					this.imgLength = this.formOption.smallFrontPhoto ? 1 : 0,
                    this.formOption.collaborativeBrands = JSON.parse(this.formOption.collaborativeBrands)
                }else{
                  await this.getDefault()
                }
            },
            async getDefault(){
                const id = await this.$newId();
                const formOption = {
                     'acctName': null,
                     'acctType':'Terminal',
                     'province':'',
                     'city':'',
                     'district':'',
                     'town':'',
                      id,
                     'acctCode':'',
                     'longitude':'',
                     'latitude':'',
                     'addrDetaiAddr':'',
                     'frontPhoto':'',
                     'collaborativeBrands':[],
                     'comments':'',
                     'cityCode':'',
                     'provinceCode':'',
                     'districtCode':'',
                     'townCode':'',
                 }
                 this.formOption = {...formOption}
            }
        }
    }
</script>

<style lang="scss">
    .census-dealer-add{
        margin-bottom: 200px;
        .bottom-btn{
            .sure-btn {
                margin: 24px;
            }
        }
    }
</style>
