<template>
    <link-page class="dealer-store-punch-card-page" ref="storePunchCardPage">
        <map id="map"
             :longitude="Number(longitude)"
             :latitude="Number(latitude)"
             scale="14"
             :circles="circles"
             show-location="true"
             show-compass="true"
             show-scale="true"
             class="map-content"
             :style="{'height': mapHeight + 'px'}"
             :setting="setting"
             @controltap="controltap"
             :markers="markers"
             @markertap="markertap"
             @regionchange="regionchange">
            <cover-view :style="{'margin-top': mapHeight - 60 +'px', float: 'right', width: '41px' }">
                <cover-view class="location-aim-container"
                            @tap="backCurrentPosition">
                    <cover-view class="location-aim">
                        <cover-image class="aim-image" :src="locationAim"></cover-image>
                    </cover-view>
                </cover-view>
            </cover-view>
        </map>
        <view class="store-details">
            <view class="store-item">
                <view class="store-content">
                    <view class="iconfont icon-location"></view>
                    <view class="address-time">{{addressDataFull}}</view>
                </view>
                <view class="store-content">
                    <view class="iconfont icon-time-circle"></view>
                    <view class="address-time">{{dateTime | date('YYYY-MM-DD HH:mm')}}</view>
                </view>
            </view>
            <view class="store-camera">
                <lnk-img-watermark :parentId="pageParam.moduleId"
                                   :goCameraFlag="!overhangFlag || (overhangFlag && distanceSelect)"
                                   moduleType="clockIn"
                                   photoPageSource="dealerStorePunchCard"
                                   @notGoCamera="notGoCamera"
                                   :moduleName="moduleName"
                                   :delFlag="delFlag"
                                   :rootId="rootId"
                                   :album="false"
                                   :useModuleName="useModuleName"
                                   @imgUploadSuccess="imageArrLength"
                                   @imgDeleteSuccess="deleteImageArrLength"
                                   :newFlag="newFlag">
                </lnk-img-watermark>
            </view>
            <view class="notice" v-if="overhangFlag" @tap="isOverhang">
                <view class="iconfont icon-check-circle-fill" v-if="distanceSelect"></view>
                <view class="iconfont icon-circle-outline" v-else></view>
                <view class="notice-text">当前位置超过误差允许范围，请确认</view>
            </view>
            <view class="button" :style="{'padding-bottom':$device.isIphoneX ? '0':'16px'}">
                <link-button class="button-item" block label="提交"  mode="fill" size="normal" @tap="submitPunchCard" autoLoading/>
            </view>
        </view>
    </link-page>
</template>

<script>
    import customCamera from "../../../components/custom-camera/custom-camera"
    import LnkImgWatermark from '../../core/lnk-img-watermark/lnk-img-watermark';

    import Taro from '@tarojs/taro'
    export default {
        name: "dealer-store-punch-card-page",
        data () {
            return {
                setting: {
                    enable3D: false,
                    enableTraffic: false,
                    showLocation: true,
                    showCompass: true,
                    showScale: true
                },
                timer: null,                                                                                        // 计时器
                timestamp: null,                                                                                    // 服务器获取时间戳
                dateTime: null,                                                                                     // 时间戳
                overhangFlag: false,                                                                                // 超距标志
                distanceSelect: false,                                                                              // 超距是否选中参数
                coordinate: null,                                                                                   // 相机定位经纬度
                useImageFlag: null,                                                                                 // 拍照完成使用照片标志
                delFlag: true,                                                                                      // 删除标志
                newFlag: true,                                                                                      // 添加标志
                punchCardArrLength: 0,                                                                              // 打卡图片数组长度
                moduleId: this.pageParam.moduleId,                                                                  // 模块Id
                rootId: this.pageParam.visitId,                                                                     // 照片来源id
                moduleType: 'clockIn',                                                                              // 模块类型
                moduleName: '到店打卡',                                                                              // 模块名称
                useModuleName: this.pageParam.data.acctName,                                                        // 使用模块名称
                storeCore: `${this.$env.imageAssetPath}/images/map/core.png`,                                       // 核心经销商图片
                noStoreCore: `${this.$env.imageAssetPath}/images/map/not-core.png`,                                 // 非核心经销商图片
                locationAim: `${this.$env.imageAssetPath}/images/map/aim.png?${Math.random() / 9999}`,              // 定位图标
                mapHeight: 0,                                                                                       // 地图高度
                longitude: 0,                                                                                       // 经销商门店经度
                latitude: 0,                                                                                        // 经销商门店纬度
                toLongitude: 0,                                                                                     // 当前自己的定位经度
                toLatitude: 0,                                                                                      // 当前自己的定位纬度
                addressDataFull: null,                                                                              // 地址（全）
                addressData: {
                    province:null,
                    city:null,
                    district:null,
                    street:null,
                    street_number:null
                },                                                                                  // 地址（省市区）
                wxMap: null,                                                                                        // 实例地图对象
                circles:[
                    {
                        latitude: '23.099994',
                        longitude: '113.324520',
                        fillColor: '#7cb5ec88',
                        color: '#7cb5ec88',
                        radius: this.pageParam.radius,
                        // radius: 1000,
                        strokeWidth: 0
                    }
                ],                                                                                      // 经销商打卡范围
                markers: [],                                                                            // 地图标记点
                onceClickFlag: true,                                                                    // 防抖控制参数
                createdRecordFlag: false,
                createdVisitRecordData: false,
                fifthGLocationFailStatus: 'N', // 是否是5G网络下定位报错【getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF getLocation:fail system permission denied  getLocation:fail:system permission denied】时录入的数据 Y是 N否
                openSettingNum: 1,//授权次数 默认为1 如果没授权的情况下 5G网络 定位问题 第一次先授权只要授过权次数加1 当次数>1 还是拿不到定位就给默认信息吧....
            }
        },
        components: {
            customCamera,
            LnkImgWatermark
        },
        async created () {
            this.wxMap = wx.createMapContext('map');
            this.goStoreDetails();
            await this.detailMarkers();
            await this.getAddress();
            this.getCircle();
            this.includePoints();
            this.dateTime = await this.getTimestamp();
            this.queryImg()
        },
        watch: {
            coordinate: {
                async handler(newVal) {
                    if (!this.$utils.isEmpty(newVal?.latitude) && !this.$utils.isEmpty(newVal?.longitude)) {
                        let distance = await this.$locations.getDistance(Number(newVal.latitude), Number(newVal.longitude), Number(this.latitude), Number(this.longitude));
                        /*this.$aegis.report({
                            msg: '到店打卡计算距离',
                            ext1: JSON.stringify(distance * 1000),
                            trace: 'log'
                        });*/
                        this.overhangFlag =  distance * 1000 > this.circles[0].radius;
                        this.toLongitude = newVal.longitude;
                        this.toLatitude = newVal.latitude;
                        let address =  await this.$locations.reverseTMapGeocoder(Number(newVal.latitude), Number(newVal.longitude), '拜访');
                        this.addressDataFull = address['originalData'].result.formatted_address;
                        this.addressData = address['originalData'].result.addressComponent;
                        this.latitude = Number(newVal.latitude);
                        this.longitude = Number(newVal.longitude);
                        let opt = {
                            latitude: newVal.latitude,
                            longitude: newVal.longitude,
                            addressDataFull: address['originalData'].result.formatted_address,
                            addressData: address['originalData'].result.addressComponent
                        };
                       /* this.$aegis.report({
                            msg: '打卡页面内当前用户定位信息',
                            ext1: JSON.stringify(opt),
                            trace: 'log'
                        });*/
                    }
                },
                deep: true
            }
        },
        onShow () {
            // 到店打卡,时间间隔是30秒
            const that = this;
            this.timer = setInterval(function(){
                that.timestamp = that.timestamp + 29000;
                that.dateTime = new Date(that.timestamp)
            },30000)
        },
        destroyed () {
            // 清除计时器
            clearInterval(this.timer);
        },
        methods: {
            /**
             * 新进页面查询一次照片
             * <AUTHOR>
             * @date 2022-7-5
             */
            async queryImg() {
                if (!this.pageParam.moduleId) {
                    return;
                }
                const params = {
                    headId: this.pageParam.moduleId,
                    moduleType: 'clockIn'
                }
                const {rows} = await this.$http.post('action/link/attachment/queryByExamplePage', params)
                this.punchCardArrLength = rows.length;
                this.newFlag = rows.length < 6;
            },
            /**
             * 校验水印相机是否上传完毕
             * <AUTHOR>
             * @date 2022-7-6
             */
            checkWaterImg() {
                let check = this.$refs['storePunchCardPage'].utils.checkImages();
                if(!check) this.$showError('图片上传中!!')
                return check
            },
            blank () {},
            /**
             * 相机拍照不能跳转的提示函数
             * <AUTHOR>
             * @date 2020-09-01
             */
            notGoCamera () {
                if (!this.distanceSelect) {
                    this.$dialog({
                        title: '提示',
                        content: '当前位置不在有效打卡范围内，若确认已到店，请重新定位经销商地址获取经纬度',
                        initial: true
                    });
                }
            },
            /**
             * 获取打卡图片长度
             * <AUTHOR>
             * @date 2020-08-31
             * @param param 打卡图片长度
             */
            imageArrLength(param) {
                this.newFlag = param.length !== 6;
                this.punchCardArrLength = param.length;
                if (param.length === 3) {
                    this.goStoreDetails()
                }
            },
            /**
             * @createdBy  张丽娟
             * @date  2021/4/6
             * @methods deleteImageArrLength
             * @para
             * @description 删掉图片成功的钩子函数
             */
            deleteImageArrLength(param) {
                this.punchCardArrLength = param.length;
                this.newFlag = param.length < 6;
                if (param.length === 2) {
                    this.goStoreDetails()
                }
            },
            /**
             * 勾选确认超距
             * <AUTHOR>
             * @date 2020-08-31
             */
            isOverhang () {
                this.distanceSelect = !this.distanceSelect
            },
            /**
             * 监控页面返回参数
             * <AUTHOR>
             * @date 2020-08-31
             * @param param
             */
            async onBack(param) {
                this.useImageFlag = param.useImageFlag;
                this.coordinate = param.coordinate;
                // this.$refs.customCamera.getImgKeyList();
                if (this.$utils.isEmpty(this.addressData)) {
                    await this.getAddress();
                }
            },
            /**
             * 校验数据
             * <AUTHOR>
             * @date 2020-08-31
             */
            checkData() {
                if(!this.pageParam.data.latitude || !this.pageParam.data.longitude) {
                    this.$dialog({
                        title: '提示',
                        content: '当前经销商经纬度信息为空，请重新定位经销商地址获取经纬度',
                        initial: true
                    });
                    return false
                }
                if (this.overhangFlag && !this.distanceSelect) {
                    this.$dialog({
                        title: '提示',
                        content: '当前位置不在有效打卡范围内，若确认已到店，请重新定位经销商地址获取经纬度',
                        initial: true
                    });
                    return false
                }
                if (this.$utils.isEmpty(this.coordinate)) {
                    this.$taro.showToast({
                        icon: 'none',
                        title: '请上传打卡照片',
                        mask: true
                    });
                    return false
                }
                if (this.punchCardArrLength === 0) {
                    this.$taro.showToast({
                        icon: 'none',
                        title: '请上传打卡照片',
                        mask: true
                    });
                    return false
                }
                return true
            },
            async insertVisitRecord () {
                const that = this;
                try {
                    this.$bus.$emit("dealerRefreshUpdateVisitStatus");
                    let insetOptions = {
                        id: that.pageParam.visitId,
                        visitModel: 'Dealer',
                        visitType: 'dailySalesCall',
                        visitTime: that.$date.format(that.dateTime, 'YYYY-MM-DD HH:mm:ss'),
                        visitApplicationStatus: 'visiting',
                        specialDescription: that.overhangFlag ? '到店定位偏移' : null,
                        accntId: that.pageParam.data.id,
                        requiredItems: that.pageParam.requiredItems,
                        fifthGLocationFailStatus: that.fifthGLocationFailStatus,//是否是5G网络下定位报错【getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF getLocation:fail system permission denied  getLocation:fail:system permission denied】时录入的数据 Y是 N否
                    };
                    let url =  'action/link/accntVisit/insert';
                    let insertData =  insetOptions;
                    const data = await this.$http.post(url, insertData, {
                        handleFailed: (error) => {
                            that.$utils.hideLoading();
                            that.onceClickFlag = true;
                        }
                    });
                    return data;
                } catch (e) {
                    that.$aegis.report({
                        msg: '创建拜访记录错误捕获',
                        ext1: JSON.stringify(e),
                        trace: 'error'
                    });
                } finally {

                }

            },
            /**
             * 提交
             * <AUTHOR>
             * @date 2020-08-28
             */
            async submitPunchCard() {
                if(!this.checkWaterImg()) return
                const that = this;
                try {
                    let checkDataFlag = that.checkData();
                    if (checkDataFlag) {
                        if (!this.onceClickFlag) {
                            return
                        }
                        this.onceClickFlag = false;
                        that.$utils.showLoading();
                        const visitRecord = await that.insertVisitRecord();
                        let insetOptions = {
                            signInType: 'toShop',
                            province: that.addressData.province,
                            city: that.addressData.city,
                            district: that.addressData.district,
                            address: `${that.addressData.street}${that.addressData.street_number}`,
                            longitude: that.coordinate.longitude,
                            latitude: that.coordinate.latitude,
                            signInStatus: that.overhangFlag ? 'abnormal' : 'normal',
                            acctId: that.pageParam.data.id,
                            id: that.pageParam.moduleId,
                            headId: that.pageParam.visitId,
                            visitStatus: 'Y',
                            fifthGLocationFailStatus: that.fifthGLocationFailStatus
                        };
                        let insetOptionsVisit = {
                            id: that.pageParam.visitId,
                            accntId: that.pageParam.data.id,
                        }
                        const signInDetails = await that.$http.post('action/link/accntSignInDetails/insert', insetOptions, {
                            handleFailed: (error) => {
                                that.$utils.hideLoading();
                                that.onceClickFlag = true;
                            }
                        });
                        await that.changePlanStatus();
                       await that.$http.post('action/link/accntVisit/queryAccntUpdateVisit',insetOptionsVisit)
                        that.$utils.hideLoading();
                        //lzlj-002-4310超距字段为空处理
                        if(signInDetails.newRow && signInDetails.newRow.beyondDistance) {
                            visitRecord.newRow.beyondDistance = signInDetails.newRow.beyondDistance
                        }
                        let param = {
                            refreshFlag: true,
                            newVisitRecord: visitRecord.newRow
                        };
                        that.$nav.back(param);
                        that.$bus.$emit('visitCreatedSuccess');
                    }
                } catch (e) {
                    that.$utils.hideLoading();
                    console.log(e)
                    that.$aegis.report({
                        msg: '打卡记录错误捕获',
                        ext1: JSON.stringify(e),
                        trace: 'error'
                    });
                }
            },
            /**
             * 更换拜访计划明细状态
             * <AUTHOR>
             * @date 2020-07-06
             */
            async   changePlanStatus(){
                let planOptions={
                    status:'new',
                    targetStatus:'Complete',
                    acctId:this.pageParam.data.id,
                    userId:this.$taro.getStorageSync('token').result.id
                }
                await this.$http.post('action/link/accntVisitPlan/updateVisitPlanStatus', planOptions);
            },
            /**
             * 获取时间戳
             * <AUTHOR>
             * @date 2020-07-06
             */
            async getTimestamp () {
                const that = this;
                return new Promise(resolve => {
                    that.$http.post('gateway/time/currentTime').then(data => {
                        if (data.success) {
                            that.timestamp = data.result;
                            let date = new Date(data.result);
                            resolve(date);
                        }
                    })
                })
            },
            /**
             * 获取当前地图的视野范围
             * <AUTHOR>
             * @date 2020-08-27
             */
            getCircle(){
                const that = this;
                this.wxMap.getRegion({
                    success: async res => {
                        let lng1 = Number(res.northeast.longitude);
                        let lat1 = Number(res.northeast.latitude);
                        let lng2 = Number(res.southwest.longitude);
                        let lat2 = Number(res.southwest.latitude);
                        let longitude = lng1 - lng2;
                        let latitude = lat1 - lat2;
                        let flag = longitude > latitude;
                        let radius = 0;
                        //计算得到短边，然后再通过*1000转变为m，除2得到半径，*0.8优化显示，让圈圈只占界面的80%
                        if (flag) {
                            let distance = await that.$locations.getDistance(lat1, lng1, lat2, lng2);
                            radius = distance * 1000 / 2;
                        } else {
                            let distance = await that.$locations.getDistance(lat1, lng1, lat2, lng2);
                            radius = distance * 1000 / 2;
                        }
                        that.circles[0].radius = radius;
                    }
                });
            },
            /**
             * 缩放视野展示所有经纬度
             * <AUTHOR>
             * @date 2020-08-28
             */
            includePoints () {
                const that = this;
                this.wxMap.includePoints({
                    padding: [50, 100, 50, 100],
                    points: [
                        {
                            latitude: Number(that.toLatitude),
                            longitude: Number(that.toLongitude)
                        },
                        {
                            latitude: Number(that.markers[0].latitude),
                            longitude: Number(that.markers[0].longitude)
                        }
                    ]
                })
            },
            /**
             * 处理地图markers数据
             * <AUTHOR>
             * @date 2020-08-26
             */
            async detailMarkers () {
                const that = this;
                that.longitude = Number(that.pageParam.data.longitude);
                that.latitude = Number(that.pageParam.data.latitude);
                let markersOption = {
                    iconPath: that.pageParam.noStoreCoreFlag ? '/static/images/map-icon/not-core.png' : '/static/images/map-icon/core.png',
                    id: that.pageParam.data.id,
                    latitude: Number(that.pageParam.data.latitude),
                    longitude: Number(that.pageParam.data.longitude),
                    width: 28,
                    height: 34,
                    label: {
                        content: that.pageParam.data.acctName,
                        padding: 0,
                        color: '#262626',
                        display:'ALWAYS',
                        textAlign:'center',
                        anchorX: -(that.pageParam.data.acctName.length * 12)/2
                    }
                };
                /*that.$aegis.report({
                    msg: '打卡页面当前门店位置',
                    ext1: JSON.stringify(markersOption),
                    trace: 'log'
                });*/
                that.markers.push(markersOption);
                that.circles[0].latitude = Number(that.pageParam.data.latitude);
                that.circles[0].longitude = Number(that.pageParam.data.longitude);
            },
            /**
             * 门店详情
             * <AUTHOR>
             * @date 2020-08-03
             */
            goStoreDetails () {
                const that = this;
                setTimeout(function () {
                    const query = wx.createSelectorQuery()
                    query.select('.store-details').boundingClientRect((ret) => {
                        that.mapHeight = that.$device.systemInfo.windowHeight - ret.height - (that.$device.systemInfo.statusBarHeight * 2);
                    }).exec()
                }, 200)
            },
            /**
             * 回到当前定位位置
             * <AUTHOR>
             * @date 2020-08-03
             */
            backCurrentPosition () {
                this.latitude = Number(this.pageParam.data.latitude);
                this.longitude = Number(this.pageParam.data.longitude);
            },
            /**
             * 获取地址
             * <AUTHOR>
             * @date 2020-07-14
             */
            async getAddress () {
                const that = this;
                let coordinate = await that.$locations.getCurrentCoordinate();//原本的逻辑
                that.coordinate = this.$utils.deepcopy(coordinate);//原本的逻辑
                // 匹配5G 某些情况下 定位获取不到的问题
                // 手机系统没开定位getLocation:fail auth deny getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF
                // 手机系统定位开了 但是企业微信没有权限 getLocation:fail system permission denied  getLocation:fail:system permission denied
                if(coordinate.errMsg === 'getLocation:fail:ERROR_NOCELL&WIFI_LOCATIONSWITCHOFF'
                    || coordinate.errMsg === 'getLocation:fail system permission denied'
                    || coordinate.errMsg === 'getLocation:fail:system permission denied'){
                    let net = "";
                    await Taro.getNetworkType({
                        success (res) {
                            net = res.networkType
                        }
                    });
                    if(net === '5g' && this.openSettingNum > 1){
                        this.fifthGLocationFailStatus = "Y";
                    } else {
                        if (that.$utils.isEmpty(coordinate.latitude) && that.$utils.isEmpty(coordinate.longitude)) {
                            this.$dialog({
                                title: '提示',
                                content: '请确认手机地理位置授权是否打开，或者【设置】-【企业微信】位置权限管理是否打开？',
                                cancelButton: false,
                                confirmText: '去开启',
                                onConfirm: async () => {
                                    let userLocation = await this.$locations.openSetting();
                                    if (userLocation['scope.userLocation']) {
                                        coordinate = await that.$locations.getCurrentCoordinate();
                                    }
                                }
                            });
                            this.openSettingNum ++;
                            return
                        }
                    }
                }
                /*that.$aegis.report({
                    msg: '到店打卡',
                    ext1: JSON.stringify(coordinate),
                    trace: 'log'
                });*/
                if (!that.$utils.isEmpty(coordinate.latitude) && !that.$utils.isEmpty(coordinate.longitude)) {
                    let distance = await that.$locations.getDistance(Number(coordinate.latitude), Number(coordinate.longitude), Number(that.latitude), Number(that.longitude));
                    /*that.$aegis.report({
                        msg: '到店打卡计算距离',
                        ext1: JSON.stringify(distance * 1000),
                        trace: 'log'
                    });*/
                    that.overhangFlag =  distance * 1000 > that.circles[0].radius;
                    that.toLongitude = coordinate.longitude;
                    that.toLatitude = coordinate.latitude;
                    let address =  await that.$locations.reverseTMapGeocoder(Number(coordinate.latitude), Number(coordinate.longitude), '到店打卡');
                    that.addressDataFull = address['originalData'].result.formatted_address;
                    that.addressData = address['originalData'].result.addressComponent;
                    that.latitude = Number(coordinate.latitude);
                    that.longitude = Number(coordinate.longitude);
                    let opt = {
                        latitude: coordinate.latitude,
                        longitude: coordinate.longitude,
                        addressDataFull: address['originalData'].result.formatted_address,
                        addressData: address['originalData'].result.addressComponent
                    };
                    /*that.$aegis.report({
                        msg: '打卡页面内当前用户定位信息',
                        ext1: JSON.stringify(opt),
                        trace: 'log'
                    });*/
                }
            },
            regionchange(e) {
            },
            markertap(e) {
            },
            controltap(e) {
            }
        }
    }
</script>

<style lang="scss">
    .dealer-store-punch-card-page {
        background-color: #ffffff;
        font-family: PingFangSC-Regular,serif;
        .map-content {
            width: 100%;
            .location-aim-container {
                @include flex-end-end;
                margin-right: 24px;
                margin-top: 16px;
                .location-aim {
                    width: 72px;
                    height: 72px;
                    border-radius: 50%;
                    background: #FFFFFF;
                    box-shadow: 0 5px 9px 0 rgba(11,33,85,0.14);
                    text-align: center;
                    .aim-image {
                        margin: 11px auto;
                        width: 50px;
                        height: 50px;
                    }
                }
            }
        }
        .store-details {
            background: #FFFFFF;
            border-radius: 32px 32px 0 0;
            overflow: hidden;
            .store-item {
                background: #FFFFFF;
                padding-top: 6px;
                padding-left: 24px;
                .store-content {
                    @include flex-start-center;
                    padding-top: 24px;
                    .iconfont {
                        color: #8C8C8C;
                        font-size: 32px;
                        width: 30px;
                    }
                    .icon-location {
                        font-size: 38px;
                    }
                    .address-time {
                        font-size: 28px;
                        color: #262626;
                        letter-spacing: 0;
                        line-height: 28px;
                        padding-left: 20px;
                    }
                }
            }
            .store-camera{
                position: relative;
            }
            .store-camera::before{
                position: absolute;
                top: 10px;
                left: 6px;
                bottom: 0;
                width: 1em;
                content: '*';
                color: #FF5A5A;
            }
            .notice {
                @include flex-start-center;
                padding-left: 24px;
                padding-top: 35px;
                .iconfont {
                    font-size: 40px;
                    color: #2F69F8;
                }
                .icon-circle-outline {
                    color: #BFBFBF;
                }
                .notice-text {
                    font-size: 28px;
                    color: #262626;
                    letter-spacing: 0;
                    line-height: 28px;
                    padding-left: 16px;
                }
            }
            .button {
                /*padding-bottom: 34px;*/
            }
        }
    }
</style>
