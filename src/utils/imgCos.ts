// 同样需要先引入COS的SDK，和配置文件

let COS = require('cos-wx-sdk-v5');
import {imgConfig} from './imgConfig';
import {$http} from "./$http";
import store from "@/store/store";
/**
  * 获取腾讯云秘钥
  * <AUTHOR>
  * @date 2020-08-13
*/
export function getCosKey () {
    console.log('getCosKey');
    return new Promise((resolve, reject) => {
        $http.post(imgConfig.getSecretIdKeyUrl, {
            bucketId: imgConfig.Bucket,
            regionId: imgConfig.Region
        }, {
            handleFailed: (error) => {
               console.log('获取腾讯云秘钥报错信息',error)
            }
        }).then(data => {
            if (data.success) {
                store.commit('cosKey/setCosKey', data);
                const nowTime = Date.now();
                // 记录上次获取临时秘钥的时间点
                store.commit('cosKey/setLastTime', nowTime);
                const expiredGap = data.expiredTime * 1000 - nowTime;
                if (expiredGap <= 3600000) {
                    // 允许提前60s重新获取秘钥，避免出现秘钥过期情况
                    store.commit('cosKey/setExpiredGap', expiredGap - 60000);
                }
                resolve(data);
            } else {
                reject(false)
            }
        });
    })
}

/*let cos = new COS({
    SecretId: env.secretId,
    SecretKey: env.secretKey,
});*/

async function getTmpSecretKey() {
    return new Promise((resolve, reject) => {
        $http.post('action/base/user/getCosToken', {
            handleFailed: (error) => {
                console.log('获取腾讯云临时秘钥报错信息', error)
            }
        }).then(data => {
            if (data.success) {
                resolve(data.result);
            } else {
                reject(false)
            }
        });
    })
}

let cos = new COS({
    // SimpleUploadMethod: 'putObject', // 强烈建议，高级上传、批量上传内部对小文件做简单上传时使用putObject,sdk版本至少需要v1.3.0
    getAuthorization: async function (options, callback) {
        const result: any = await getTmpSecretKey();
        callback({
            TmpSecretId: result.credentials.tmpSecretId,
            TmpSecretKey: result.credentials.tmpSecretKey,
            // v1.2.0之前版本的 SDK 使用 XCosSecurityToken 而不是 SecurityToken
            SecurityToken: result.credentials.sessionToken,
            // 建议返回服务器时间作为签名的开始时间，避免用户浏览器本地时间偏差过大导致签名错误
            StartTime: result.startTime, // 时间戳，单位秒，如：1580000000
            ExpiredTime: result.expiredTime, // 时间戳，单位秒，如：1580000900
            // ScopeLimit: true, // 细粒度控制权限需要设为 true，会限制密钥只在相同请求时重复使用
        });
    }
});

// 接下来可以通过 cos 实例调用 COS 请求。
// TODO
export default cos

