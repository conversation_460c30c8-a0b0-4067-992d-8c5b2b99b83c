/**
 * 生成一个对象用来管理静态资源路径
 * <AUTHOR>
 * @date    2020/9/2 15:51
 */
import {ComponentUtils} from "link-taro-component";
import {env} from "../../env";

function createAssetsManager<T extends { [k: string]: string }>(assets: T): T {
    assets = {...assets}
    Object.keys(assets).forEach(key => {
        // @ts-ignore
        assets[key] = ComponentUtils.pathJoin(env.imageAssetPath, assets[key]) as string + (key === 'darkShadowUrl' || key === 'alphaShadowUrl'? '':`?time=${env.buildTime}`);
    })
    return assets
}

export const $imageAssets = createAssetsManager({
    manImage:'images/head-portrait/man.jpeg',                                               //员工画像默认男生icon
    womanImage: 'images/head-portrait/woman.jpeg',                                          //员工画像默认女生icon
    noDataImage: 'images/components/no-data/no-data.png',                                   // 暂无数据时显示的图片
    terminalDefaultImage: 'images/store-status/terminal-default.png',                       // 终端默认图片
    qrCodeImage: 'images/client-details/qr-code.png',                                       // 二维码logo
    loseEfficacyImage: 'images/client-details/lose-efficacy.png',                           // 二维码失效图片
    storeStatusPotentialImage: 'images/store-status/store-status-potential.png',            // 终端潜在客户
    storeStatusVerifiedImage: 'images/store-status/store-status-verified.png',              // 终端认证客户
    storeStatusInvalidationImage: 'images/store-status/store-status-invalidation.png',      // 终端已失效客户
    storeStatusUnverifiedImage: 'images/store-status/store-status-unverified.png',          // 终端未认证客户
    certifyTimeBg: 'images/store-status/certify-time-bg.png',                               // 终端认证客户到期时间
    consumerListItemBg: 'images/store-status/consumer-list-item-bg.png',                    // 消费者列表item背景
    consumerFollowerBg: 'images/store-status/consumer-follower-bg.png',                     // 消费者跟进人背景
    locationAimImage: 'images/map/aim.png',                                                 // 定位目标图图标
    storeCoreImage: 'images/map/core.png',                                                  // 核定门店
    noStoreCoreImage: 'images/map/not-core.png',                                            // 非核定门店
    fileImage: 'images/marketing/cloud/file.png',                                           // 文件夹
    activityBgImage: 'images/marketing/lj-market-activity/activity-bg.png',                 // 活动背景图
    messageImage: 'images/marketing/activity/message.png',                                  // 信息图标
    phoneImage: 'images/marketing/activity/phone.png',                                      // 电话图标
    maleImage: 'images/marketing/headIcon/male.png',                                        // 男性
    femaleImage: 'images/marketing/headIcon/female.png',                                    // 女性
    userDefaultImage: 'images/marketing/headIcon/userDefault.png',                          // 未知性别
    linkLogoImage: 'images/components/login-logo/link-logo.png',                            // 登录页logo
    lzljLogoImage: 'images/components/login-logo/lzlj-logo.png',                            // 登录页logo
    noAuthImage: 'images/components/no-auth/no-auth.png',                                   // 暂无权限时显示图片
    rightImage: 'images/components/navigation-bar/right.png',                               // 右箭头
    leftImage: 'images/components/navigation-bar/left.png',                                 // 左箭头
    closeImage: 'images/components/custom-camera/close.png',                                // 闪光灯关闭图片
    devicePositionImage: 'images/components/custom-camera/device-position.png',             // 相机前后置转换图片
    lineImage: 'images/components/custom-camera/line.png',                                  // 相机竖分割线
    photoImage: 'images/components/custom-camera/photo.png',                                // 相机拍照图片
    lightImage: 'images/components/custom-camera/light.png',                                // 闪光灯打开图片
    clarityShadeImage: 'images/components/custom-camera/clarity-shade.png',                 // 遮罩蒙层
    flashAutoImage: 'images/components/custom-camera/flash-auto.png',                       // 闪光灯自动图片
    homeMenuBgImage: 'images/menu-home/home-menu-bg.png',                                   // 首页菜单
    shippedImage: 'images/order-detail/shipped.png',                                        // 终端订单详情已发货图片
    addressIconImage: 'images/order-detail/address-icon.png',                               // 终端订单详情地址图标
    salesOrgImg: 'images/dealer-data-board/stock-admin.png',                                // 经销商数据看板图片
    salesAdminImg: 'images/dealer-data-board/sale-data-org.png',                            // 经销商数据看板图片
    stockDealerImg: 'images/dealer-data-board/sale-data.admin.png',                         // 经销商数据看板图片
    stockAdminImg: 'images/dealer-data-board/stock-dealer.png',                             // 经销商数据看板图片
    fakeFleeImg: '/images/fake-terminal/fake-flee.png',                                     // 打假终端列表'窜'图片
    fakeImitateImg: '/images/fake-terminal/fake-imitate.png',                                // 打假终端列表'仿'图片
    fakeViolateImg: '/images/fake-terminal/fake-violate.png',                                // 打假终端列表'门头违规'图片
    fakeFakeImg: '/images/fake-terminal/fake-fake.png',                                      // 打假终端列表'假'图片
    agreementDefaultImg: 'images/agreementDefault/agreementDefault.png',                    //协议默认图片
    adornImg:'images/marketing/lj-market-activity/adorn.png',                               // 市场活动 互动分享背景
    interSaveImg:'images/marketing/lj-market-activity/inter-save.png',                      // 市场活动 互动分享背景 保存图片
    interShareImg:'images/marketing/lj-market-activity/inter-share.png',                    // 市场活动 互动分享背景 分享图片
    coverBgForImg:'images/card-scanning/cover-bg.png',                                      // 电子名片-名片分享-图片背景
    cardBgForImg:'images/card-scanning/card-bg.png',                                        // 电子名片-名片信息背景图
    addressIcon: '/images/card-scanning/card-icon/address.png',                             // 电子名片-分享名片-地址图标
    emailIcon: '/images/card-scanning/card-icon/email.png',                                 // 电子名片-分享名片-邮件图标
    telephoneIcon: 'images/card-scanning/card-icon/telephone.png',                          // 电子名片-分享名片-电话图标
    consumerScanBg:'/images/card-scanning/consumer-scan-bg.png',                            // 电子名片-消费者扫码-底部背景图
    dailyStamp: 'images/marketing/headIcon/dailyStamp.png',                                    // 日报-今日新增的戳
    sweepRepetitionCode:'images/marketing/lj-market-activity/sweep-repetition-code.png',    // 品鉴酒 重复扫码
    outbound: 'images/marketing/lj-market-activity/outbound.png',                           // 品鉴酒 出库
    gift: 'images/marketing/lj-market-activity/gift.png',                                   // 品鉴酒 转增
    decap: 'images/marketing/lj-market-activity/decap.png',                                 // 品鉴酒 开瓶
    effective: 'images/marketing/lj-market-activity/effective.png',                         // 品鉴酒 有效
    failure: 'images/marketing/lj-market-activity/failure.png',                             // 品鉴酒 失效
    yearReviewTrailerPage:'images/year-review/trailer-page.jpg',//年度回顾 尾图
    yearReviewTheme:'images/year-review/theme.png',//年度回顾 主题
    yearReviewLogo:'images/year-review/logo.png',//年度回顾 logo
    yearReviewHomePg:'images/year-review/home-pg.jpg',//年度回顾 首页背景
    yearReviewButton:'images/year-review/button.png',//年度回顾 按钮
    yearReviewBg:'images/year-review/bg.jpg',//年度回顾
    yearReviewButtonLeft:'images/year-review/button-left.png',//年度回顾 左按钮
    yearReviewButtonRight:'images/year-review/button-right.png',//年度回顾 左按钮
    yearReviewDefaultA:'images/year-review/default-1.jpg',//年度回顾 轮播默认-1
    yearReviewDefaultB:'images/year-review/default-2.jpg',//年度回顾 轮播默认-2
    yearReviewDefaultC:'images/year-review/default-3.jpg',//年度回顾 轮播默认-3
    yearReviewDefaultD:'images/year-review/default-4.jpg',//年度回顾 轮播默认-4
    yearReviewDefaultE:'images/year-review/default-5.jpg',//年度回顾 轮播默认-5
    yearReviewBg1:'images/year-review/2022_bg1.png',//年度回顾2022 背景图1
    yearReviewBg2:'images/year-review/2022_bg2.png',//年度回顾2022 背景图2
    yearReviewBg3:'images/year-review/2022_bg3.png',//年度回顾2022 背景图3
    yearReviewBg4:'images/year-review/2022_bg4.jpg',//年度回顾2022 背景图3
    yearReviewLogo1:'images/year-review/2022_logo.png',//年度回顾2022 logo1
    yearReviewLogo2:'images/year-review/2022_logo1.png',//年度回顾2022 logo2
    jinridongxiao:'images/dealer-data-board/jinridongxiao.png',//今日动销
    dangqiankucun:'images/dealer-data-board/dangqiankucun.png',//当前库存
    benyuedongxiao:'images/dealer-data-board/benyuedongxiao.png',//本月累计动销
    benniandongxiao:'images/dealer-data-board/benniandongxiao.png',//本年累计动销
    visitCompanyImg: 'images/corporate-relation/company.png', //企业公关公司照片
    darkShadowUrl: 'images/watermark/darkShadowUrl.jpg', //水印背景图
    alphaShadowUrl: 'images/watermark/alphaShadowUrl.png', //水印背景图
    boardBgUrl: 'images/echarts/bg.jpg', // 消费者看板背景
    consumerDay: 'images/echarts/consumer-day-bg.png', // 消费者日增背景
    consumerWeek: 'images/echarts/consumer-week-bg.png', // 消费者周增背景
    consumerMonth: 'images/echarts/consumer-month-bg.png', // 消费者月增背景
    consumerYear: 'images/echarts/consumer-year-bg.png', // 消费者年增背景
    consumerTotal: 'images/echarts/consumer-total-bg.png', // 消费者总数背景
    memberBgUrl: 'images/echarts/member.png',  // 会员总数背景
    memberVipBgUrl: 'images/echarts/vip.png',  // 会员vip背景
    homeApproval: 'images/home-board/approval.png', // 审批流提醒
    homeBirthday: 'images/home-board/birthday.png', // 生日提醒
    homePlan: 'images/home-board/plan.png', // 计划提醒
    homeDaily: 'images/home-board/daily.png', // 日报提醒
    homeStart: 'images/home-board/start.png', // 活动开始提醒
    homeFeedback: 'images/home-board/feedback.png', // 活动反馈提醒
    homeCase: 'images/home-board/case.png', // 执行案提醒
    homeVisit: 'images/home-board/visit.png', // 拜访提醒
    homeNoData: 'images/home-board/nodata.png',
    banquetForwardImage: 'images/banquet-forward/banquet-activities-indulgence.jpg/compress',   // 宴会活动-一键转发背景图
    marketingSixForwardImage: 'images/banquet-forward/banquet-activities-1573.jpg/compress', //营销6.0--一键转发背景图
    banquetShare: 'images/banquet-forward/wxImg.png', // 宴席活动一键转发-微信Icon
    btnCheckedBg: 'images/consumer-detail/btn-checked-bg.png',
    consumerCardBg: 'images/consumer-detail/consumer-card-bg.png',
    tabLeftChecked: 'images/consumer-detail/tab-left-checked.png',
    tabRightChecked: 'images/consumer-detail/tab-right-checked.png',
    statusBgRed: 'images/consumer-detail/status-bg-red.png',
    statusBgBlue: 'images/consumer-detail/status-bg-blue.svg',
    statusBgGreen: 'images/consumer-detail/status-bg-green.svg',
    statusBgGrey: 'images/consumer-detail/status-bg-grey.svg',
    statusBgOrange: 'images/consumer-detail/status-bg-orange.png',
    BecomeFan: 'images/consumer-detail/BecomeFan.png',
    RepeatBuy: 'images/consumer-detail/RepeatBuy.png',
    Buy: 'images/consumer-detail/Buy.png',
    Drink: 'images/consumer-detail/Drink.png',
    See: 'images/consumer-detail/See.png',
    Hear: 'images/consumer-detail/Hear.png',
    BecomeFanIcon: 'images/consumer-detail/BecomeFan-icon.png',
    RepeatBuyIcon: 'images/consumer-detail/RepeatBuy-icon.png',
    BuyIcon: 'images/consumer-detail/Buy-icon.png',
    DrinkIcon: 'images/consumer-detail/Drink-icon.png',
    SeeIcon: 'images/consumer-detail/See-icon.png',
    HearIcon: 'images/consumer-detail/Hear-icon.png',
    eagleBoardBg: 'images/eagle-plan/eagle-board-bg.png',  // 鹰计划看板背景
    scoreAccountBg: 'images/eagle-plan/score-account-bg.png', // 鹰计划积分账户背景
    tabLeftActiveBg: 'images/eagle-plan/tab-left-active-bg.png', // 鹰计划tab左边选中（蓝白）
    tabRightActiveBg: 'images/eagle-plan/tab-right-active-bg.png', // 鹰计划tab右边选中（蓝白）
    leftActiveBg: 'images/eagle-plan/left-active.png', // 鹰计划tab左边选中（黑灰）
    rightActiveBg: 'images/eagle-plan/right-active.png', // 鹰计划tab右边选中（黑灰）
    terminalBgBoard: 'images/eagle-plan/terminal-bg-board.png', // 鹰计划看板终端背景
    eyas: 'images/eagle-plan/eyas.png', // 鹰计划-雏鹰
    flyeagles: 'images/eagle-plan/flyeagles.png', // 鹰计划-飞鹰
    eagles: 'images/eagle-plan/eagles.png', // 鹰计划-雄鹰
    essentialeagles: 'images/eagle-plan/essentialeagles.png', // 鹰计划-精鹰
    leadeagles: 'images/eagle-plan/leadeagles.png', // 鹰计划-领鹰
    eagleIconScan: 'images/eagle-plan/icon-saoma.svg', // 添加鹰计划-扫码
    eagleIconAdd: 'images/eagle-plan/icon-add.svg', // 添加鹰计划-手动添加
    terminalBanquet: 'images/board/terminal-banquet.png', // 终端宴席坎级场次图标
    terminalBanquetReward: 'images/board/terminal-banquet-reward.png', // 终端宴席坎级奖励奖励
    cateringTitlebg: 'images/catering-ordering/bg-title.png',//餐饮终端套餐维护标题背景
    boardBlueBg: 'images/new-board/blue-bg.png', // 业务看板2.0-蓝色背景
    boardBrightOrangeBg: 'images/new-board/bright-orange-bg.png', // 业务看板2.0-亮橘色背景
    boardOrangeBg: 'images/new-board/orange-bg.png', // 业务看板2.0-橘色背景
    boardChooseTitle: 'images/new-board/choose-title.png', // 业务看板2.0-选中二级看板背景
    boardLightBlueBg: 'images/new-board/light-blue-bg.png', // 业务看板2.0-浅蓝色背景
    boardLongBlueBg: 'images/new-board/long-blue-bg.png', // 业务看板2.0-蓝色长条背景
    boardMauveBg: 'images/new-board/mauve-bg.png', // 业务看板2.0-浅紫色背景
    boardPurpleBg: 'images/new-board/purple-bg.png', // 业务看板2.0-紫色背景
    boardRedBg: 'images/new-board/red-bg.png', // 业务看板2.0-红色背景
    boardTitleBg: 'images/new-board/title-bg.png', // 业务看板2.0-模块标题背景
    boardNo1: 'images/new-board/No1.png', // 业务看板2.0-排名1
    boardNo2: 'images/new-board/No2.png', // 业务看板2.0-排名2
    boardNo3: 'images/new-board/No3.png', // 业务看板2.0-排名3
    boardWarningRight: 'images/board/early-warning-right.png', // 业务看板2.0-风险预警-右箭头
    boardWarningTips: 'images/board/early-warning-tips.png', // 业务看板2.0-风险预警-警示图标
    mktActAllNumBg: 'images/board/mkt-act-img/act-all.png', // 业务看板2.0-市场活动-活动概览-总场次背景
    mktActConductBg: 'images/board/mkt-act-img/process-act.png', // 业务看板2.0-市场活动-活动概览-进行中背景
    mktActActualBg: 'images/board/mkt-act-img/actual-act.png', // 业务看板2.0-市场活动-活动概览-已实发背景
    mktActActualAmountBg: 'images/board/mkt-act-img/actual-amount.png', // 业务看板2.0-市场活动-活动概览-总金额背景
    mktActApplyAmountBg: 'images/board/mkt-act-img/apply-amount.png', // 业务看板2.0-市场活动-活动概览-已实发总金额背景
    navBarBg: 'images/consumer-board/navBarBg.png',
    consumerTouchCmc: 'images/consumer-board/consumer-touch-cmc.png',
    consumerTouchMc: 'images/consumer-board/consumer-touch-mc.png',
    consumerTouchScene: 'images/consumer-board/consumer-touch-scene.png',
    consumerConversionOpenBottle: 'images/consumer-board/consumer-conversion-openBottle.png',
    consumerConversionOpenBox: 'images/consumer-board/consumer-conversion-openBox.png',
    consumerConversionOrder: 'images/consumer-board/consumer-conversion-order.png',
    consumerConversionReferral: 'images/consumer-board/consumer-conversion-referral.png',
    coreConsumer: 'images/consumer-board/core-consumer.png',
    followConsumer: 'images/consumer-board/follow-consumer.png',
    activeConsumer: 'images/consumer-board/active-consumer.png',
    certifyConsumer: 'images/consumer-board/certify-consumer.png',
    mktHzcyzsBg: 'images/board/mkt-six/hzcyzs.png', // 业务看板2.0-专项活动-营销6.0-合作餐饮总数背景
    mktZdrsBg: 'images/board/mkt-six/zdrs.png', // 业务看板2.0-专项活动-营销6.0-驻店人数背景
    mktHdcyrcBg: 'images/board/mkt-six/hdcyrc.png', // 业务看板2.0-专项活动-营销6.0-活动参与人次背景
    mktXjkpzsBg: 'images/board/mkt-six/xjkpzs.png', // 业务看板2.0-专项活动-营销6.0-小酒开瓶总数背景
    mktXjsmzsBg: 'images/board/mkt-six/xjsmzs.png', // 业务看板2.0-专项活动-营销6.0-小酒扫码总数背景
    mktXjkczsBg: 'images/board/mkt-six/xjkczs.png', // 业务看板2.0-专项活动-营销6.0-小酒库存总数背景
    mktCgzdxBg: 'images/board/mkt-six/cgzdx.png', // 业务看板2.0-专项活动-营销6.0-常规装动销背景
    banquetActLjyxzsBg: 'images/board/banquet-act/ljyxzs.png', // 业务看板2.0-专项活动-宴席政策-累计宴席桌数
    banquetActLjyxccBg: 'images/board/banquet-act/ljyxcc.png', // 业务看板2.0-专项活动-宴席政策-累计宴席场次
    banquetActPjyxzsBg: 'images/board/banquet-act/pjyxzs.png', // 业务看板2.0-专项活动-宴席政策-平均宴席桌数
    banquetActKplBg: 'images/board/banquet-act/kpl.png', // 业务看板2.0-专项活动-宴席政策-开瓶率
    banquetActLjckslBg: 'images/board/banquet-act/ljcksl.png', // 业务看板2.0-专项活动-宴席政策-累计出库数量
    banquetActLjthslBg: 'images/board/banquet-act/ljthsl.png', // 业务看板2.0-专项活动-宴席政策-累计退货数量
    banquetActXfzkpsBg: 'images/board/banquet-act/xfzkps.png', // 业务看板2.0-专项活动-宴席政策-消费者开瓶数
    banquetActYxzdsBg: 'images/board/banquet-act/yxzds.png', // 业务看板2.0-专项活动-宴席政策-宴席终端数
    staffTerminal: 'images/new-board/staff/icon-terminal.png', //业务看板2.0-员工目标-终端达成
    staffSales: 'images/new-board/staff/icon-dongxiao.png', //业务看板2.0-员工目标-动销达成
    staffOpen: 'images/new-board/staff/icon-open.png', //业务看板2.0-员工目标-开瓶达成
    staffCore: 'images/new-board/staff/icon-consumer.png', //业务看板2.0-员工目标-核心消费者数
    staffscan: 'images/new-board/staff/icon-scan.png', //业务看板2.0-员工目标-异地扫码率
    staffDaily: 'images/new-board/staff/icon-visit.png', //业务看板2.0-员工目标-日均有效终端拜访数量
    whiteBg: 'images/client-details/white-bg.png', //纯白色背景图片
    blueBg: 'images/client-details/blue-bg.png', //纯蓝背景图片
    prodBg: 'images/client-details/prod-bg.png', //纯色背景图片
    banquetBg: 'images/client-details/banquet-bg.png', //纯色背景图片
    markerBlue: 'images/hf-collection/marker-blue.png', // 高频点地图-蓝色采集点
    markerGreen: 'images/hf-collection/marker-green.png', // 高频点地图-绿色采集点
    markerOrange: 'images/hf-collection/marker-orange.png', // 高频点地图-橙色采集点
    markerPurple: 'images/hf-collection/marker-purple.png', // 高频点地图-紫色采集点
    markerBrown: 'images/hf-collection/marker-brown.png', // 高频点地图-棕色采集点
    markerGray: 'images/hf-collection/marker-gray.png', // 高频点地图-灰色采集点
    markerRed: 'images/hf-collection/marker-red.png', // 高频点地图-红色采集点
    markerDeepOrange: 'images/hf-collection/marker-deep-orange.png', // 高频点地图-深橙色采集点
});
